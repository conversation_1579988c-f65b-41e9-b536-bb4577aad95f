package main

import (
	"image"
	"image/draw"
	"image/png"
	"os"
)

func main() {
	originalFile, err := os.Open("./t.png")
	defer originalFile.Close()
	if nil != err {
		panic(err)
	}
	originalImg, _, err := image.Decode(originalFile)
	if nil != err {
		panic(err)
	}
	rect := image.Rect(0, 0, originalImg.Bounds().Dx(), originalImg.Bounds().Dy())
	newRgba := image.NewRGBA(rect)
	draw.Draw(newRgba, rect, originalImg, image.Pt(0, 0), draw.Src)
	watermarkFile, _ := os.Open("./d.jpg")
	watermarkImg, _, _ := image.Decode(watermarkFile)
	draw.Draw(newRgba, image.Rect(0, 0, watermarkImg.Bounds().Dx(), watermarkImg.Bounds().Dy()), watermarkImg, image.Pt(-100, -100), draw.Over)
	newFile, _ := os.Create("./test.png")
	defer newFile.Close()
	png.Encode(newFile, newRgba)
}
