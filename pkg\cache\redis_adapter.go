package cache

import (
	"context"
	redis "github.com/redis/go-redis/v9"
)

type RedisAdapter struct {
	client *redis.Client
}

func NewRedisAdapter(client *redis.Client) *RedisAdapter {
	return &RedisAdapter{client: client}
}

func (r *RedisAdapter) Set(ctx context.Context, key string, value any) error {
	return r.client.Set(ctx, key, value, 0).Err()
}

func (r *RedisAdapter) Get(ctx context.Context, key string) (any, error) {
	return r.client.Get(ctx, key).Result()
}

func (r *RedisAdapter) Increment(ctx context.Context, key string) error {
	return r.client.Incr(ctx, key).Err()
}

func (r *RedisAdapter) Decrement(ctx context.Context, key string) error {
	return r.client.Decr(ctx, key).Err()
}

func (r *RedisAdapter) Delete(ctx context.Context, key string) error {
	return r.client.Del(ctx, key).Err()
}

func (r *RedisAdapter) Flush(ctx context.Context) error {
	return r.client.FlushDB(ctx).Err()
}

func (r *RedisAdapter) DeleteMulti(ctx context.Context, keys []string) error {
	return r.client.Del(ctx, keys...).Err()
}

func (r *RedisAdapter) GetMulti(ctx context.Context, keys []string) (KeyValue, error) {
	result, err := r.client.MGet(ctx, keys...).Result()
	if err != nil {
		return nil, err
	}
	var values, i = make(KeyValue), 0
	for _, v := range result {
		values[keys[i]] = v
		i++
	}

	return values, nil
}

func (r *RedisAdapter) SetMulti(ctx context.Context, values KeyValue) error {
	return r.client.MSet(ctx, values).Err()
}
