package utils

import "golang.org/x/exp/constraints"

func Max[T constraints.Signed | constraints.Unsigned](a, b T) T {
	if a > b {
		return a
	}
	return b
}

func Min[T constraints.Signed | constraints.Unsigned](a, b T) T {
	if a > b {
		return b
	}
	return a
}

func In[T constraints.Signed | constraints.Unsigned](haystack []T, needle T) bool {
	for _, v := range haystack {
		if v == needle {
			return true
		}
	}

	return false
}
