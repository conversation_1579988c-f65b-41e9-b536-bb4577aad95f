import{a as A,s as P}from"./index-CX10IlxR.js";import{B as v,y as x,D as g,K as b,L as h,I as u,af as z,j as _,R as $,A as D,J as F,O as t,a6 as M,M as K,a9 as I,ag as y,N as w,z as q,a4 as R,a5 as E,F as N,ah as H,G as S,S as Z,H as c,u as r,ai as W}from"./index-BauktehD.js";import{s as m}from"./index-MIcNnIzF.js";var J={root:"p-inputicon"},U=v.extend({name:"inputicon",classes:J}),G={name:"BaseInputIcon",extends:x,style:U,props:{class:null},provide:function(){return{$pcInputIcon:this,$parentInstance:this}}},V={name:"InputIcon",extends:G,inheritAttrs:!1,computed:{containerClass:function(){return[this.cx("root"),this.class]}}};function Y(n,a,e,i,l,o){return g(),b("span",u({class:o.containerClass},n.ptmi("root")),[h(n.$slots,"default")],16)}V.render=Y;var Q=function(a){var e=a.dt;return`
.p-iconfield {
    position: relative;
}

.p-inputicon {
    position: absolute;
    top: 50%;
    margin-top: calc(-1 * (`.concat(e("icon.size"),` / 2));
    color: `).concat(e("iconfield.icon.color"),`;
}

.p-iconfield .p-inputicon:first-child {
    left: `).concat(e("form.field.padding.x"),`;
}

.p-iconfield .p-inputicon:last-child {
    right: `).concat(e("form.field.padding.x"),`;
}

.p-iconfield .p-inputtext:not(:first-child) {
    padding-left: calc((`).concat(e("form.field.padding.x")," * 2) + ").concat(e("icon.size"),`);
}

.p-iconfield .p-inputtext:not(:last-child) {
    padding-right: calc((`).concat(e("form.field.padding.x")," * 2) + ").concat(e("icon.size"),`);
}
`)},X={root:"p-iconfield"},tt=v.extend({name:"iconfield",theme:Q,classes:X}),et={name:"BaseIconField",extends:x,style:tt,provide:function(){return{$pcIconField:this,$parentInstance:this}}},L={name:"IconField",extends:et,inheritAttrs:!1};function nt(n,a,e,i,l,o){return g(),b("div",u({class:n.cx("root")},n.ptmi("root")),[h(n.$slots,"default")],16)}L.render=nt;var at=function(a){var e=a.dt;return`
.p-inputtext {
    font-family: inherit;
    font-feature-settings: inherit;
    font-size: 1rem;
    color: `.concat(e("inputtext.color"),`;
    background: `).concat(e("inputtext.background"),`;
    padding: `).concat(e("inputtext.padding.y")," ").concat(e("inputtext.padding.x"),`;
    border: 1px solid `).concat(e("inputtext.border.color"),`;
    transition: background `).concat(e("inputtext.transition.duration"),", color ").concat(e("inputtext.transition.duration"),", border-color ").concat(e("inputtext.transition.duration"),", outline-color ").concat(e("inputtext.transition.duration"),", box-shadow ").concat(e("inputtext.transition.duration"),`;
    appearance: none;
    border-radius: `).concat(e("inputtext.border.radius"),`;
    outline-color: transparent;
    box-shadow: `).concat(e("inputtext.shadow"),`;
}

.p-inputtext:enabled:hover {
    border-color: `).concat(e("inputtext.hover.border.color"),`;
}

.p-inputtext:enabled:focus {
    border-color: `).concat(e("inputtext.focus.border.color"),`;
    box-shadow: `).concat(e("inputtext.focus.ring.shadow"),`;
    outline: `).concat(e("inputtext.focus.ring.width")," ").concat(e("inputtext.focus.ring.style")," ").concat(e("inputtext.focus.ring.color"),`;
    outline-offset: `).concat(e("inputtext.focus.ring.offset"),`;
}

.p-inputtext.p-invalid {
    border-color: `).concat(e("inputtext.invalid.border.color"),`;
}

.p-inputtext.p-variant-filled {
    background: `).concat(e("inputtext.filled.background"),`;
}

.p-inputtext.p-variant-filled:enabled:focus {
    background: `).concat(e("inputtext.filled.focus.background"),`;
}

.p-inputtext:disabled {
    opacity: 1;
    background: `).concat(e("inputtext.disabled.background"),`;
    color: `).concat(e("inputtext.disabled.color"),`;
}

.p-inputtext::placeholder {
    color: `).concat(e("inputtext.placeholder.color"),`;
}

.p-inputtext-sm {
    font-size: `).concat(e("inputtext.sm.font.size"),`;
    padding: `).concat(e("inputtext.sm.padding.y")," ").concat(e("inputtext.sm.padding.x"),`;
}

.p-inputtext-lg {
    font-size: `).concat(e("inputtext.lg.font.size"),`;
    padding: `).concat(e("inputtext.lg.padding.y")," ").concat(e("inputtext.lg.padding.x"),`;
}

.p-inputtext-fluid {
    width: 100%;
}
`)},ot={root:function(a){var e=a.instance,i=a.props;return["p-inputtext p-component",{"p-filled":e.filled,"p-inputtext-sm":i.size==="small","p-inputtext-lg":i.size==="large","p-invalid":i.invalid,"p-variant-filled":i.variant?i.variant==="filled":e.$primevue.config.inputStyle==="filled"||e.$primevue.config.inputVariant==="filled","p-inputtext-fluid":e.hasFluid}]}},it=v.extend({name:"inputtext",theme:at,classes:ot}),st={name:"BaseInputText",extends:x,props:{modelValue:null,size:{type:String,default:null},invalid:{type:Boolean,default:!1},variant:{type:String,default:null},fluid:{type:Boolean,default:null}},style:it,provide:function(){return{$pcInputText:this,$parentInstance:this}}},B={name:"InputText",extends:st,inheritAttrs:!1,emits:["update:modelValue"],inject:{$pcFluid:{default:null}},methods:{getPTOptions:function(a){var e=a==="root"?this.ptmi:this.ptm;return e(a,{context:{filled:this.filled,disabled:this.$attrs.disabled||this.$attrs.disabled===""}})},onInput:function(a){this.$emit("update:modelValue",a.target.value)}},computed:{filled:function(){return this.modelValue!=null&&this.modelValue.toString().length>0},hasFluid:function(){return z(this.fluid)?!!this.$pcFluid:this.fluid}}},lt=["value","aria-invalid"];function ct(n,a,e,i,l,o){return g(),b("input",u({type:"text",class:n.cx("root"),value:n.modelValue,"aria-invalid":n.invalid||void 0,onInput:a[0]||(a[0]=function(){return o.onInput&&o.onInput.apply(o,arguments)})},o.getPTOptions("root")),null,16,lt)}B.render=ct;var rt=function(a){var e=a.dt;return`
.p-togglebutton {
    display: inline-flex;
    cursor: pointer;
    user-select: none;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    color: `.concat(e("togglebutton.color"),`;
    background: `).concat(e("togglebutton.background"),`;
    border: 1px solid `).concat(e("togglebutton.border.color"),`;
    padding: `).concat(e("togglebutton.padding"),`;
    font-size: 1rem;
    font-family: inherit;
    font-feature-settings: inherit;
    transition: background `).concat(e("togglebutton.transition.duration"),", color ").concat(e("togglebutton.transition.duration"),", border-color ").concat(e("togglebutton.transition.duration"),`,
        outline-color `).concat(e("togglebutton.transition.duration"),", box-shadow ").concat(e("togglebutton.transition.duration"),`;
    border-radius: `).concat(e("togglebutton.border.radius"),`;
    outline-color: transparent;
    font-weight: `).concat(e("togglebutton.font.weight"),`;
}

.p-togglebutton-content {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: `).concat(e("togglebutton.gap"),`;
}

.p-togglebutton-label,
.p-togglebutton-icon {
    position: relative;
    transition: none;
}

.p-togglebutton::before {
    content: "";
    background: transparent;
    transition: background `).concat(e("togglebutton.transition.duration"),", color ").concat(e("togglebutton.transition.duration"),", border-color ").concat(e("togglebutton.transition.duration"),`,
            outline-color `).concat(e("togglebutton.transition.duration"),", box-shadow ").concat(e("togglebutton.transition.duration"),`;
    position: absolute;
    left: `).concat(e("togglebutton.content.left"),`;
    top: `).concat(e("togglebutton.content.top"),`;
    width: calc(100% - calc(2 *  `).concat(e("togglebutton.content.left"),`));
    height: calc(100% - calc(2 *  `).concat(e("togglebutton.content.top"),`));
    border-radius: `).concat(e("togglebutton.border.radius"),`;
}

.p-togglebutton.p-togglebutton-checked::before {
    background: `).concat(e("togglebutton.content.checked.background"),`;
    box-shadow: `).concat(e("togglebutton.content.checked.shadow"),`;
}

.p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover {
    background: `).concat(e("togglebutton.hover.background"),`;
    color: `).concat(e("togglebutton.hover.color"),`;
}

.p-togglebutton.p-togglebutton-checked {
    background: `).concat(e("togglebutton.checked.background"),`;
    border-color: `).concat(e("togglebutton.checked.border.color"),`;
    color: `).concat(e("togglebutton.checked.color"),`;
}

.p-togglebutton:focus-visible {
    box-shadow: `).concat(e("togglebutton.focus.ring.shadow"),`;
    outline: `).concat(e("togglebutton.focus.ring.width")," ").concat(e("togglebutton.focus.ring.style")," ").concat(e("togglebutton.focus.ring.color"),`;
    outline-offset: `).concat(e("togglebutton.focus.ring.offset"),`;
}

.p-togglebutton.p-invalid {
    border-color: `).concat(e("togglebutton.invalid.border.color"),`;
}

.p-togglebutton:disabled {
    opacity: 1;
    cursor: default;
    background: `).concat(e("togglebutton.disabled.background"),`;
    border-color: `).concat(e("togglebutton.disabled.border.color"),`;
    color: `).concat(e("togglebutton.disabled.color"),`;
}

.p-togglebutton-icon {
    color: `).concat(e("togglebutton.icon.color"),`;
}

.p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover .p-togglebutton-icon {
    color: `).concat(e("togglebutton.icon.hover.color"),`;
}

.p-togglebutton.p-togglebutton-checked .p-togglebutton-icon {
    color: `).concat(e("togglebutton.icon.checked.color"),`;
}

.p-togglebutton:disabled .p-togglebutton-icon {
    color: `).concat(e("togglebutton.icon.disabled.color"),`;
}
`)},pt={root:function(a){var e=a.instance,i=a.props;return["p-togglebutton p-component",{"p-togglebutton-checked":e.active,"p-invalid":i.invalid}]},content:"p-togglebutton-content",icon:"p-togglebutton-icon",label:"p-togglebutton-label"},dt=v.extend({name:"togglebutton",theme:rt,classes:pt}),ut={name:"BaseToggleButton",extends:x,props:{modelValue:Boolean,onIcon:String,offIcon:String,onLabel:{type:String,default:"Yes"},offLabel:{type:String,default:"No"},iconPos:{type:String,default:"left"},invalid:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:dt,provide:function(){return{$pcToggleButton:this,$parentInstance:this}}},O={name:"ToggleButton",extends:ut,inheritAttrs:!1,emits:["update:modelValue","change"],methods:{getPTOptions:function(a){var e=a==="root"?this.ptmi:this.ptm;return e(a,{context:{active:this.active,disabled:this.disabled}})},onChange:function(a){!this.disabled&&!this.readonly&&(this.$emit("update:modelValue",!this.modelValue),this.$emit("change",a))}},computed:{active:function(){return this.modelValue===!0},hasLabel:function(){return _(this.onLabel)&&_(this.offLabel)},label:function(){return this.hasLabel?this.modelValue?this.onLabel:this.offLabel:"&nbsp;"}},directives:{ripple:$}},gt=["tabindex","disabled","aria-pressed","data-p-checked","data-p-disabled"];function mt(n,a,e,i,l,o){var p=D("ripple");return F((g(),b("button",u({type:"button",class:n.cx("root"),tabindex:n.tabindex,disabled:n.disabled,"aria-pressed":n.modelValue,onClick:a[0]||(a[0]=function(){return o.onChange&&o.onChange.apply(o,arguments)})},o.getPTOptions("root"),{"data-p-checked":o.active,"data-p-disabled":n.disabled}),[t("span",u({class:n.cx("content")},o.getPTOptions("content")),[h(n.$slots,"default",{},function(){return[h(n.$slots,"icon",{value:n.modelValue,class:M(n.cx("icon"))},function(){return[n.onIcon||n.offIcon?(g(),b("span",u({key:0,class:[n.cx("icon"),n.modelValue?n.onIcon:n.offIcon]},o.getPTOptions("icon")),null,16)):K("",!0)]}),t("span",u({class:n.cx("label")},o.getPTOptions("label")),I(o.label),17)]})],16)],16,gt)),[[p]])}O.render=mt;var bt=function(a){var e=a.dt;return`
.p-selectbutton {
    display: inline-flex;
    user-select: none;
    vertical-align: bottom;
    outline-color: transparent;
    border-radius: `.concat(e("selectbutton.border.radius"),`;
}

.p-selectbutton .p-togglebutton {
    border-radius: 0;
    border-width: 1px 1px 1px 0;
}

.p-selectbutton .p-togglebutton:focus-visible {
    position: relative;
    z-index: 1;
}

.p-selectbutton .p-togglebutton:first-child {
    border-left-width: 1px;
    border-top-left-radius: `).concat(e("selectbutton.border.radius"),`;
    border-bottom-left-radius: `).concat(e("selectbutton.border.radius"),`;
}

.p-selectbutton .p-togglebutton:last-child {
    border-top-right-radius: `).concat(e("selectbutton.border.radius"),`;
    border-bottom-right-radius: `).concat(e("selectbutton.border.radius"),`;
}

.p-selectbutton.p-invalid {
    outline: 1px solid `).concat(e("selectbutton.invalid.border.color"),`;
    outline-offset: 0;
}
`)},ft={root:function(a){var e=a.props;return["p-selectbutton p-component",{"p-invalid":e.invalid}]}},ht=v.extend({name:"selectbutton",theme:bt,classes:ft}),vt={name:"BaseSelectButton",extends:x,props:{modelValue:null,options:Array,optionLabel:null,optionValue:null,optionDisabled:null,multiple:Boolean,allowEmpty:{type:Boolean,default:!0},invalid:{type:Boolean,default:!1},disabled:Boolean,dataKey:null,ariaLabelledby:{type:String,default:null}},style:ht,provide:function(){return{$pcSelectButton:this,$parentInstance:this}}};function xt(n,a){var e=typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(!e){if(Array.isArray(n)||(e=T(n))||a){e&&(n=e);var i=0,l=function(){};return{s:l,n:function(){return i>=n.length?{done:!0}:{done:!1,value:n[i++]}},e:function(f){throw f},f:l}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var o,p=!0,s=!1;return{s:function(){e=e.call(n)},n:function(){var f=e.next();return p=f.done,f},e:function(f){s=!0,o=f},f:function(){try{p||e.return==null||e.return()}finally{if(s)throw o}}}}function yt(n){return _t(n)||kt(n)||T(n)||wt()}function wt(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function T(n,a){if(n){if(typeof n=="string")return k(n,a);var e={}.toString.call(n).slice(8,-1);return e==="Object"&&n.constructor&&(e=n.constructor.name),e==="Map"||e==="Set"?Array.from(n):e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?k(n,a):void 0}}function kt(n){if(typeof Symbol<"u"&&n[Symbol.iterator]!=null||n["@@iterator"]!=null)return Array.from(n)}function _t(n){if(Array.isArray(n))return k(n)}function k(n,a){(a==null||a>n.length)&&(a=n.length);for(var e=0,i=Array(a);e<a;e++)i[e]=n[e];return i}var C={name:"SelectButton",extends:vt,inheritAttrs:!1,emits:["update:modelValue","change"],methods:{getOptionLabel:function(a){return this.optionLabel?y(a,this.optionLabel):a},getOptionValue:function(a){return this.optionValue?y(a,this.optionValue):a},getOptionRenderKey:function(a){return this.dataKey?y(a,this.dataKey):this.getOptionLabel(a)},getPTOptions:function(a,e){return this.ptm(e,{context:{active:this.isSelected(a),disabled:this.isOptionDisabled(a),option:a}})},isOptionDisabled:function(a){return this.optionDisabled?y(a,this.optionDisabled):!1},onOptionSelect:function(a,e,i){var l=this;if(!(this.disabled||this.isOptionDisabled(e))){var o=this.isSelected(e);if(!(o&&!this.allowEmpty)){var p=this.getOptionValue(e),s;this.multiple?o?s=this.modelValue.filter(function(d){return!w(d,p,l.equalityKey)}):s=this.modelValue?[].concat(yt(this.modelValue),[p]):[p]:s=o?null:p,this.focusedIndex=i,this.$emit("update:modelValue",s),this.$emit("change",{event:a,value:s})}}},isSelected:function(a){var e=!1,i=this.getOptionValue(a);if(this.multiple){if(this.modelValue){var l=xt(this.modelValue),o;try{for(l.s();!(o=l.n()).done;){var p=o.value;if(w(p,i,this.equalityKey)){e=!0;break}}}catch(s){l.e(s)}finally{l.f()}}}else e=w(this.modelValue,i,this.equalityKey);return e}},computed:{equalityKey:function(){return this.optionValue?null:this.dataKey}},directives:{ripple:$},components:{ToggleButton:O}},$t=["aria-labelledby"];function It(n,a,e,i,l,o){var p=q("ToggleButton");return g(),b("div",u({class:n.cx("root"),role:"group","aria-labelledby":n.ariaLabelledby},n.ptmi("root")),[(g(!0),b(R,null,E(n.options,function(s,d){return g(),N(p,{key:o.getOptionRenderKey(s),modelValue:o.isSelected(s),onLabel:o.getOptionLabel(s),offLabel:o.getOptionLabel(s),disabled:n.disabled||o.isOptionDisabled(s),unstyled:n.unstyled,onChange:function(j){return o.onOptionSelect(j,s,d)},pt:n.ptm("pcButton")},H({_:2},[n.$slots.option?{name:"default",fn:S(function(){return[h(n.$slots,"option",{option:s,index:d},function(){return[t("span",u({ref_for:!0},n.ptm("pcButton").label),I(o.getOptionLabel(s)),17)]})]}),key:"0"}:void 0]),1032,["modelValue","onLabel","offLabel","disabled","unstyled","onChange","pt"])}),128))],16,$t)}C.render=It;const St={class:"flex-1 h-full overflow-y-auto overflow-x-clip overflow-hidden flex border border-surface rounded-2xl"},Vt={class:"w-4/12 xl:w-3/12 min-w-40 overflow-auto flex flex-col gap-6"},Lt=t("div",{class:"flex flex-col gap-6 pt-3 pb-2 -mb-2 px-5 sticky top-0 bg-surface-0 dark:bg-surface-950 z-10"},[t("div",{class:"flex items-center justify-between gap-6 text-color"},[t("div",{class:"text-2xl font-medium lead"},"Chats"),t("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc4501:""},[t("span",{class:"p-button-icon pi pi-plus","data-pc-section":"icon"}),t("span",{class:"p-button-label","data-pc-section":"label"}," ")])])],-1),Bt={class:"px-5"},Ot={class:"w-full px-5"},Tt=t("div",{class:"flex-1 flex flex-col"},[t("div",{class:"flex items-center gap-2 p-4 cursor-pointer hover:bg-emphasis transition-all"},[t("div",{class:"relative"},[t("div",{class:"absolute top-0 right-0 p-[1px] bg-surface-0 dark:bg-surface-950 rounded-full flex items-center justify-center"},[t("span",{class:"p-badge p-component p-badge-dot p-badge-success p-1.5","data-pc-name":"badge","data-pc-section":"root",pc4506:""})]),t("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle p-avatar-lg text-base font-medium flex","data-pc-name":"avatar","data-pc-section":"root",pc4507:""},[t("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg","data-pc-section":"image"})])]),t("div",{class:"flex-1"},[t("div",{class:"flex items-start gap-1 justify-between"},[t("div",{class:"text-color font-medium leading-6"},"Cody Fisher"),t("div",{class:"text-sm text-muted-color leading-5"},"12.30")]),t("div",{class:"flex items-center gap-5 justify-between mt-1"},[t("div",{class:"text-muted-color text-sm leading-5 line-clamp-1"}," Hey there! I've heard about PrimeVue. Any cool tips for getting started? "),t("span",{class:"p-badge p-component p-badge-circle p-badge-contrast","data-pc-name":"badge","data-pc-section":"root",pc4508:""},"8")])])]),t("div",{class:"flex items-center gap-2 p-4 cursor-pointer hover:bg-emphasis transition-all bg-emphasis"},[t("div",{class:"relative"},[t("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle p-avatar-lg text-base font-medium flex","data-pc-name":"avatar","data-pc-section":"root",pc4509:""},[t("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png","data-pc-section":"image"})])]),t("div",{class:"flex-1"},[t("div",{class:"flex items-start gap-1 justify-between"},[t("div",{class:"text-color font-medium leading-6"},"PrimeTek Team"),t("div",{class:"text-sm text-muted-color leading-5"},"11.15")]),t("div",{class:"flex items-center gap-5 justify-between mt-1"},[t("div",{class:"text-muted-color text-sm leading-5 line-clamp-1"}," Let's implement PrimeVue. Elevating our UI game! 🚀 ")])])])],-1),Ct={class:"w-8/12 xl:w-6/12 border-x border-surface flex flex-col"},jt=t("div",{class:"flex items-center p-4 gap-7 border-b border-surface"},[t("div",{class:"flex items-center"},[t("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle p-avatar-lg mr-2 av","data-pc-name":"avatar","data-pc-section":"root",pc4528:""},[t("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png","data-pc-section":"image"})]),t("div",{class:"flex-1"},[t("div",{class:"text-color leading-6 cursor-pointer hover:text-muted-color-emphasis transition-colors"}," PrimeTek "),t("div",{class:"text-muted-color leading-5 line-clamp-1 mt-1"}," Cody Fisher, Esther Howard, Jerome Bell, Kristin Watson, Ronald Richards, Darrell Steward ")])]),t("div",{class:"flex items-center gap-2"},[t("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc4529:""},[t("span",{class:"p-button-icon pi pi-phone","data-pc-section":"icon"}),t("span",{class:"p-button-label","data-pc-section":"label"}," ")]),t("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc4530:""},[t("span",{class:"p-button-icon pi pi-search","data-pc-section":"icon"}),t("span",{class:"p-button-label","data-pc-section":"label"}," ")]),t("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","aria-haspopup":"true","aria-controls":"overlay_menu","data-pc-section":"root",pc4531:""},[t("span",{class:"p-button-icon pi pi-ellipsis-h","data-pc-section":"icon"}),t("span",{class:"p-button-label","data-pc-section":"label"}," ")])])],-1),At=t("div",{class:"flex-1 overflow-y-auto flex flex-col gap-8 py-8 px-6"},[t("div",{class:"flex items-start min-w-64 w-fit max-w-[60%]"},[t("div",{class:"flex items-center gap-2 sticky top-0 transition-all"},[t("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle w-10 h-10 text-sm font-medium","data-pc-name":"avatar","data-pc-section":"root",pc4535:""},[t("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar2.png","data-pc-section":"image"})]),t("div",null,[t("svg",{class:"fill-surface-100 dark:fill-surface-800",xmlns:"http://www.w3.org/2000/svg",width:"7",height:"11",viewBox:"0 0 7 11",fill:"none"},[t("path",{d:"M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z"})])])]),t("div",{class:"flex-1 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded-lg"},[t("p",{class:"text-color leading-6 mb-0"}," It's design-neutral and compatible with Tailwind. Features accessible, high-grade components! "),t("div",{class:"bg-surface-200 dark:bg-surface-700 mt-2 w-full rounded-lg mb-0.5 hover:opacity-75 transition-all"},[t("img",{class:"w-full h-auto block cursor-pointer",src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/message-image.png",alt:"Message Image"})])])]),t("div",{class:"flex items-start min-w-64 w-fit max-w-[60%] ml-auto mr-0 flex-row-reverse"},[t("div",{class:"flex items-center gap-2 sticky top-0 transition-all flex-row-reverse"},[t("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle w-10 h-10 text-sm font-medium","data-pc-name":"avatar","data-pc-section":"root",pc4542:""},[t("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png","data-pc-section":"image"})]),t("div",null,[t("svg",{class:"fill-primary rotate-180",xmlns:"http://www.w3.org/2000/svg",width:"7",height:"11",viewBox:"0 0 7 11",fill:"none"},[t("path",{d:"M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z"})])])]),t("div",{class:"flex-1 bg-primary px-2 py-1 rounded-lg"},[t("p",{class:"text-primary-contrast leading-6 mb-0"}," I couldn't agree more. Plus, the documentation is incredibly thorough, which makes onboarding new team members a breeze. "),t("div",{class:"bg-primary-emphasis mt-2 w-full rounded-lg mb-0.5 hover:opacity-75 transition-all"},[t("img",{class:"w-full h-auto block cursor-pointer",src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/message-image.png",alt:"Message Image"})])])]),t("div",{class:"flex items-start min-w-64 w-fit max-w-[60%]"},[t("div",{class:"flex items-center gap-2 sticky top-0 transition-all"},[t("div",{class:"p-avatar p-component p-avatar-circle bg-primary-100 text-primary-950 w-10 h-10 text-sm font-medium","data-pc-name":"avatar","data-pc-section":"root",pc4545:""},[t("span",{class:"p-avatar-label","data-pc-section":"label"},"HS")]),t("div",null,[t("svg",{class:"fill-surface-100 dark:fill-surface-800",xmlns:"http://www.w3.org/2000/svg",width:"7",height:"11",viewBox:"0 0 7 11",fill:"none"},[t("path",{d:"M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z"})])])]),t("div",{class:"flex-1 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded-lg"},[t("p",{class:"text-color leading-6 mb-0"}," I've also found that leveraging the component's internal state management capabilities can help streamline data flow and improve performance. ")])]),t("div",{class:"flex items-start min-w-64 w-fit max-w-[60%] ml-auto mr-0 flex-row-reverse"},[t("div",{class:"flex items-center gap-2 sticky top-0 transition-all flex-row-reverse"},[t("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle w-10 h-10 text-sm font-medium","data-pc-name":"avatar","data-pc-section":"root",pc4546:""},[t("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png","data-pc-section":"image"})]),t("div",null,[t("svg",{class:"fill-primary rotate-180",xmlns:"http://www.w3.org/2000/svg",width:"7",height:"11",viewBox:"0 0 7 11",fill:"none"},[t("path",{d:"M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z"})])])]),t("div",{class:"flex-1 bg-primary px-2 py-1 rounded-lg"},[t("p",{class:"text-primary-contrast leading-6 mb-0"}," That's great advice. It's amazing how much detail and thought has gone into making PrimeVue such a powerful tool for developers. ")])])],-1),Pt={class:"p-4 border-t border-surface flex items-end justify-between gap-2"},zt={class:"flex items-end gap-1 flex-1"},Dt=t("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc4547:""},[t("span",{class:"p-button-icon pi pi-face-smile","data-pc-section":"icon"}),t("span",{class:"p-button-label","data-pc-section":"label"}," ")],-1),Ft=t("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc4548:""},[t("span",{class:"p-button-icon pi pi-paperclip","data-pc-section":"icon"}),t("span",{class:"p-button-label","data-pc-section":"label"}," ")],-1),Mt={class:"w-3/12 xl:block hidden min-w-40 py-6 px-3 overflow-auto"},Kt={class:"flex flex-col items-center justify-center"},qt=t("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle p-avatar-xl w-32 h-32","data-pc-name":"avatar","data-pc-section":"root",pc4551:""},[t("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png","data-pc-section":"image"})],-1),Rt=t("div",{class:"leading-6 font-medium text-color mt-4 w-full text-center"},"PrimeTek",-1),Et=t("div",{class:"leading-5 text-sm text-muted-color mt-1 w-full text-center"},"@primetek",-1),Nt={class:"flex items-center justify-center flex-wrap gap-1 mt-4"},Ht={class:"flex flex-col gap-4 mt-4"},Zt={class:"flex items-center gap-2"},Wt=t("i",{class:"pi pi-bell text-color"},null,-1),Jt=t("div",{class:"leading-6 font-medium text-color flex-1"},"Notification",-1),Ut=t("div",{class:"flex items-center gap-2"},[t("i",{class:"pi pi-volume-down text-color"}),t("div",{class:"leading-6 font-medium text-color flex-1"},"Sound"),t("div",{class:"p-toggleswitch p-component","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc4558:"",style:{position:"relative"}},[t("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input","aria-checked":"false","data-pc-section":"input"}),t("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])],-1),Gt=t("div",{class:"flex items-center gap-2"},[t("i",{class:"pi pi-download text-color"}),t("div",{class:"leading-6 font-medium text-color flex-1"},"Save to downloads"),t("div",{class:"p-toggleswitch p-component","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc4559:"",style:{position:"relative"}},[t("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input","aria-checked":"false","data-pc-section":"input"}),t("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])],-1),Yt={class:"mt-6"},Qt={class:"flex items-center gap-2"},Xt=t("div",{class:"flex-1 text-color leading-6 font-medium"},"Members",-1),te=t("div",{class:"mt-4 flex flex-col gap-4"},[t("div",{class:"flex items-center gap-2 cursor-pointer"},[t("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle font-medium text-xs","data-pc-name":"avatar","data-pc-section":"root",pc4564:""},[t("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar8.png","data-pc-section":"image"})]),t("div",{class:"text-sm text-color hover:text-muted-color-emphasis transition-colors font-medium leading-5 flex-1"}," Arlene McCoy "),t("i",{class:"pi pi-chevron-right text-xs text-muted-color"})]),t("div",{class:"flex items-center gap-2 cursor-pointer"},[t("div",{class:"p-avatar p-component p-avatar-circle bg-orange-100 text-orange-950 font-medium text-xs","data-pc-name":"avatar","data-pc-section":"root",pc4565:""},[t("span",{class:"p-avatar-label","data-pc-section":"label"},"DR")]),t("div",{class:"text-sm text-color hover:text-muted-color-emphasis transition-colors font-medium leading-5 flex-1"}," Dianne Russell "),t("i",{class:"pi pi-chevron-right text-xs text-muted-color"})])],-1),ee={class:"mt-5"},ne=t("div",{class:"p-selectbutton p-component w-full",role:"group","data-pc-name":"selectbutton","data-pc-section":"root",pc4566:""},[t("button",{type:"button",class:"p-togglebutton p-component p-togglebutton-checked flex-1","aria-pressed":"true","data-pc-name":"pcbutton","data-pc-extend":"togglebutton","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false"},[t("span",{class:"p-togglebutton-content","data-pc-section":"content"},[t("span",{class:"p-togglebutton-label","data-pc-section":"label"},"Media")])]),t("button",{type:"button",class:"p-togglebutton p-component flex-1","aria-pressed":"false","data-pc-name":"pcbutton","data-pc-extend":"togglebutton","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false"},[t("span",{class:"p-togglebutton-content","data-pc-section":"content"},[t("span",{class:"p-togglebutton-label","data-pc-section":"label"},"Link")])]),t("button",{type:"button",class:"p-togglebutton p-component flex-1","aria-pressed":"false","data-pc-name":"pcbutton","data-pc-extend":"togglebutton","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false"},[t("span",{class:"p-togglebutton-content","data-pc-section":"content"},[t("span",{class:"p-togglebutton-label","data-pc-section":"label"},"Docs")])])],-1),ae=W('<div class="mt-3 mb-5 grid grid-cols-3 gap-2"><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer"><img class="w-full h-full object-cover block" src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image1.png" alt="Media Image"></div><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer"><img class="w-full h-full object-cover block" src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image2.png" alt="Media Image"></div><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer"><img class="w-full h-full object-cover block" src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image3.png" alt="Media Image"></div><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer"><img class="w-full h-full object-cover block" src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image4.png" alt="Media Image"></div><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer"><img class="w-full h-full object-cover block" src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image5.png" alt="Media Image"></div><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer flex items-center justify-center"><span class="text-muted-color font-medium">99+</span></div></div>',1),le=Z({__name:"ChatView",setup(n){return(a,e)=>(g(),b("div",St,[t("div",Vt,[Lt,t("div",Bt,[c(r(L),null,{default:S(()=>[c(r(V),{class:"pi pi-search"}),c(r(B),{placeholder:"Search"})]),_:1})]),t("div",Ot,[c(r(C),{"model-value":"Chat",options:["Chat","Call"],"aria-labelledby":"basic",class:"w-full"})]),Tt]),t("div",Ct,[jt,At,t("div",Pt,[t("div",zt,[Dt,Ft,c(r(A),{rows:"1",autoResize:"",placeholder:"Write your message...",class:"w-full"})]),c(r(m),{icon:"pi pi-send","aria-label":"Save"})])]),t("div",Mt,[t("div",Kt,[qt,Rt,Et,t("div",Nt,[c(r(m),{icon:"pi pi-phone",severity:"secondary",text:""}),c(r(m),{icon:"pi pi-video",severity:"secondary",text:""}),c(r(m),{icon:"pi pi-sign-in",severity:"secondary",text:""}),c(r(m),{icon:"pi pi-info-circle",severity:"secondary",text:""}),c(r(m),{icon:"pi pi-ellipsis-v",severity:"secondary",text:""})])]),t("div",Ht,[t("div",Zt,[Wt,Jt,c(r(P))]),Ut,Gt]),t("div",Yt,[t("div",Qt,[Xt,c(r(m),{text:"",label:"See All",class:"text-sm py-0.5 px-2 text-muted-color"})]),te]),t("div",ee,[ne,ae,c(r(m),{icon:"pi pi-arrow-right",iconPos:"right",outlined:"",label:"Show more",class:"w-full"})])])]))}});export{le as default};
