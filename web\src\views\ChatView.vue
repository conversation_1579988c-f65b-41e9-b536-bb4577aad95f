<script setup lang="ts">
import ToggleSwitch from 'primevue/toggleswitch'
import InputIcon from 'primevue/inputicon'
import I<PERSON><PERSON><PERSON> from 'primevue/iconfield'
import Textarea from 'primevue/textarea'
import But<PERSON> from 'primevue/button'
import InputText from 'primevue/inputtext'
import <PERSON><PERSON><PERSON>on from 'primevue/selectbutton'
</script>

<template>
  <div class="flex-1 h-full overflow-y-auto overflow-x-clip overflow-hidden flex border border-surface rounded-2xl">
    <div class="w-4/12 xl:w-3/12 min-w-40 overflow-auto flex flex-col gap-6">
      <div class="flex flex-col gap-6 pt-3 pb-2 -mb-2 px-5 sticky top-0 bg-surface-0 dark:bg-surface-950 z-10">
        <div class="flex items-center justify-between gap-6 text-color">
          <div class="text-2xl font-medium lead">Chats</div>
          <button class="p-button p-component p-button-icon-only p-button-text" type="button" data-pc-name="button"
            data-p-disabled="false" data-pc-section="root" pc4501="">
            <span class="p-button-icon pi pi-plus" data-pc-section="icon"></span><span class="p-button-label"
              data-pc-section="label">&nbsp;</span><!---->
          </button>
        </div>
      </div>
      <div class="px-5">
        <IconField>
          <InputIcon class="pi pi-search" />
          <InputText placeholder="Search" />
        </IconField>
      </div>
      <div class="w-full px-5">
        <SelectButton model-value="Chat" :options="['Chat', 'Call']" aria-labelledby="basic" class="w-full" />
        <!--          <div-->
        <!--            class="p-selectbutton p-component w-full"-->
        <!--            role="group"-->
        <!--            aria-labelledby="basic"-->
        <!--            data-pc-name="selectbutton"-->
        <!--            data-pc-section="root"-->
        <!--            pc4505=""-->
        <!--          >-->
        <!--            <button-->
        <!--              type="button"-->
        <!--              class="p-togglebutton p-component p-togglebutton-checked flex-1"-->
        <!--              aria-pressed="true"-->
        <!--              data-pc-name="pcbutton"-->
        <!--              data-pc-extend="togglebutton"-->
        <!--              data-pc-section="root"-->
        <!--              data-p-checked="true"-->
        <!--              data-p-disabled="false"-->
        <!--            >-->
        <!--              <span class="p-togglebutton-content" data-pc-section="content"-->
        <!--                >&lt;!&ndash;&ndash;&gt;<span class="p-togglebutton-label" data-pc-section="label">Chat</span></span-->
        <!--              >-->
        <!--            </button>-->
        <!--            <button-->
        <!--              type="button"-->
        <!--              class="p-togglebutton p-component flex-1"-->
        <!--              aria-pressed="false"-->
        <!--              data-pc-name="pcbutton"-->
        <!--              data-pc-extend="togglebutton"-->
        <!--              data-pc-section="root"-->
        <!--              data-p-checked="false"-->
        <!--              data-p-disabled="false"-->
        <!--            >-->
        <!--              <span class="p-togglebutton-content" data-pc-section="content"-->
        <!--                >&lt;!&ndash;&ndash;&gt;<span class="p-togglebutton-label" data-pc-section="label">Call</span></span-->
        <!--              >-->
        <!--            </button>-->
        <!--          </div>-->
      </div>
      <div class="flex-1 flex flex-col">
        <div class="flex items-center gap-2 p-4 cursor-pointer hover:bg-emphasis transition-all">
          <div class="relative">
            <div
              class="absolute top-0 right-0 p-[1px] bg-surface-0 dark:bg-surface-950 rounded-full flex items-center justify-center">
              <span class="p-badge p-component p-badge-dot p-badge-success p-1.5" data-pc-name="badge"
                data-pc-section="root" pc4506=""></span>
            </div>
            <div class="p-avatar p-component p-avatar-image p-avatar-circle p-avatar-lg text-base font-medium flex"
              data-pc-name="avatar" data-pc-section="root" pc4507="">
              <img src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg"
                data-pc-section="image" />
            </div>
          </div>
          <div class="flex-1">
            <div class="flex items-start gap-1 justify-between">
              <div class="text-color font-medium leading-6">Cody Fisher</div>
              <div class="text-sm text-muted-color leading-5">12.30</div>
            </div>
            <div class="flex items-center gap-5 justify-between mt-1">
              <div class="text-muted-color text-sm leading-5 line-clamp-1">
                Hey there! I've heard about PrimeVue. Any cool tips for getting started?
              </div>
              <span class="p-badge p-component p-badge-circle p-badge-contrast" data-pc-name="badge"
                data-pc-section="root" pc4508="">8</span>
            </div>
          </div>
        </div>
        <div class="flex items-center gap-2 p-4 cursor-pointer hover:bg-emphasis transition-all bg-emphasis">
          <div class="relative">
            <!---->
            <div class="p-avatar p-component p-avatar-image p-avatar-circle p-avatar-lg text-base font-medium flex"
              data-pc-name="avatar" data-pc-section="root" pc4509="">
              <img src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png"
                data-pc-section="image" />
            </div>
          </div>
          <div class="flex-1">
            <div class="flex items-start gap-1 justify-between">
              <div class="text-color font-medium leading-6">PrimeTek Team</div>
              <div class="text-sm text-muted-color leading-5">11.15</div>
            </div>
            <div class="flex items-center gap-5 justify-between mt-1">
              <div class="text-muted-color text-sm leading-5 line-clamp-1">
                Let's implement PrimeVue. Elevating our UI game! 🚀
              </div>
              <!---->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="w-8/12 xl:w-6/12 border-x border-surface flex flex-col">
      <div class="flex items-center p-4 gap-7 border-b border-surface">
        <div class="flex items-center">
          <div class="p-avatar p-component p-avatar-image p-avatar-circle p-avatar-lg mr-2 av" data-pc-name="avatar"
            data-pc-section="root" pc4528="">
            <img src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png"
              data-pc-section="image" />
          </div>
          <div class="flex-1">
            <div class="text-color leading-6 cursor-pointer hover:text-muted-color-emphasis transition-colors">
              PrimeTek
            </div>
            <div class="text-muted-color leading-5 line-clamp-1 mt-1">
              Cody Fisher, Esther Howard, Jerome Bell, Kristin Watson, Ronald Richards, Darrell
              Steward
            </div>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <button class="p-button p-component p-button-icon-only p-button-text" type="button" data-pc-name="button"
            data-p-disabled="false" data-pc-section="root" pc4529="">
            <span class="p-button-icon pi pi-phone" data-pc-section="icon"></span><span class="p-button-label"
              data-pc-section="label">&nbsp;</span><!---->
          </button>
          <button class="p-button p-component p-button-icon-only p-button-text" type="button" data-pc-name="button"
            data-p-disabled="false" data-pc-section="root" pc4530="">
            <span class="p-button-icon pi pi-search" data-pc-section="icon"></span><span class="p-button-label"
              data-pc-section="label">&nbsp;</span><!---->
          </button>
          <button class="p-button p-component p-button-icon-only p-button-text" type="button" data-pc-name="button"
            data-p-disabled="false" aria-haspopup="true" aria-controls="overlay_menu" data-pc-section="root" pc4531="">
            <span class="p-button-icon pi pi-ellipsis-h" data-pc-section="icon"></span><span class="p-button-label"
              data-pc-section="label">&nbsp;</span><!---->
          </button>
        </div>
      </div>
      <div class="flex-1 overflow-y-auto flex flex-col gap-8 py-8 px-6">
        <div class="flex items-start min-w-64 w-fit max-w-[60%]">
          <div class="flex items-center gap-2 sticky top-0 transition-all">
            <div class="p-avatar p-component p-avatar-image p-avatar-circle w-10 h-10 text-sm font-medium"
              data-pc-name="avatar" data-pc-section="root" pc4535="">
              <img src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar2.png"
                data-pc-section="image" />
            </div>
            <div>
              <svg class="fill-surface-100 dark:fill-surface-800" xmlns="http://www.w3.org/2000/svg" width="7"
                height="11" viewBox="0 0 7 11" fill="none">
                <path
                  d="M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z">
                </path>
              </svg>
            </div>
          </div>
          <div class="flex-1 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded-lg">
            <p class="text-color leading-6 mb-0">
              It's design-neutral and compatible with Tailwind. Features accessible, high-grade
              components!
            </p>
            <div
              class="bg-surface-200 dark:bg-surface-700 mt-2 w-full rounded-lg mb-0.5 hover:opacity-75 transition-all">
              <img class="w-full h-auto block cursor-pointer"
                src="https://www.primefaces.org/cdn/primevue/images/landing/apps/message-image.png"
                alt="Message Image" />
            </div>
          </div>
        </div>
        <div class="flex items-start min-w-64 w-fit max-w-[60%] ml-auto mr-0 flex-row-reverse">
          <div class="flex items-center gap-2 sticky top-0 transition-all flex-row-reverse">
            <div class="p-avatar p-component p-avatar-image p-avatar-circle w-10 h-10 text-sm font-medium"
              data-pc-name="avatar" data-pc-section="root" pc4542="">
              <img src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png"
                data-pc-section="image" />
            </div>
            <div>
              <svg class="fill-primary rotate-180" xmlns="http://www.w3.org/2000/svg" width="7" height="11"
                viewBox="0 0 7 11" fill="none">
                <path
                  d="M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z">
                </path>
              </svg>
            </div>
          </div>
          <div class="flex-1 bg-primary px-2 py-1 rounded-lg">
            <p class="text-primary-contrast leading-6 mb-0">
              I couldn't agree more. Plus, the documentation is incredibly thorough, which makes
              onboarding new team members a breeze.
            </p>
            <div class="bg-primary-emphasis mt-2 w-full rounded-lg mb-0.5 hover:opacity-75 transition-all">
              <img class="w-full h-auto block cursor-pointer"
                src="https://www.primefaces.org/cdn/primevue/images/landing/apps/message-image.png"
                alt="Message Image" />
            </div>
          </div>
        </div>
        <div class="flex items-start min-w-64 w-fit max-w-[60%]">
          <div class="flex items-center gap-2 sticky top-0 transition-all">
            <div
              class="p-avatar p-component p-avatar-circle bg-primary-100 text-primary-950 w-10 h-10 text-sm font-medium"
              data-pc-name="avatar" data-pc-section="root" pc4545="">
              <span class="p-avatar-label" data-pc-section="label">HS</span>
            </div>
            <div>
              <svg class="fill-surface-100 dark:fill-surface-800" xmlns="http://www.w3.org/2000/svg" width="7"
                height="11" viewBox="0 0 7 11" fill="none">
                <path
                  d="M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z">
                </path>
              </svg>
            </div>
          </div>
          <div class="flex-1 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded-lg">
            <p class="text-color leading-6 mb-0">
              I've also found that leveraging the component's internal state management capabilities
              can help streamline data flow and improve performance.
            </p>
            <!---->
          </div>
        </div>
        <div class="flex items-start min-w-64 w-fit max-w-[60%] ml-auto mr-0 flex-row-reverse">
          <div class="flex items-center gap-2 sticky top-0 transition-all flex-row-reverse">
            <div class="p-avatar p-component p-avatar-image p-avatar-circle w-10 h-10 text-sm font-medium"
              data-pc-name="avatar" data-pc-section="root" pc4546="">
              <img src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png"
                data-pc-section="image" />
            </div>
            <div>
              <svg class="fill-primary rotate-180" xmlns="http://www.w3.org/2000/svg" width="7" height="11"
                viewBox="0 0 7 11" fill="none">
                <path
                  d="M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z">
                </path>
              </svg>
            </div>
          </div>
          <div class="flex-1 bg-primary px-2 py-1 rounded-lg">
            <p class="text-primary-contrast leading-6 mb-0">
              That's great advice. It's amazing how much detail and thought has gone into making
              PrimeVue such a powerful tool for developers.
            </p>
            <!---->
          </div>
        </div>
      </div>
      <div class="p-4 border-t border-surface flex items-end justify-between gap-2">
        <div class="flex items-end gap-1 flex-1">
          <button class="p-button p-component p-button-icon-only p-button-text" type="button" data-pc-name="button"
            data-p-disabled="false" data-pc-section="root" pc4547="">
            <span class="p-button-icon pi pi-face-smile" data-pc-section="icon"></span><span class="p-button-label"
              data-pc-section="label">&nbsp;</span><!---->
          </button>
          <button class="p-button p-component p-button-icon-only p-button-text" type="button" data-pc-name="button"
            data-p-disabled="false" data-pc-section="root" pc4548="">
            <span class="p-button-icon pi pi-paperclip" data-pc-section="icon"></span><span class="p-button-label"
              data-pc-section="label">&nbsp;</span><!---->
          </button>
          <Textarea rows="1" autoResize placeholder="Write your message..." class="w-full" />
        </div>
        <Button icon="pi pi-send" aria-label="Save" />
      </div>
    </div>
    <div class="w-3/12 xl:block hidden min-w-40 py-6 px-3 overflow-auto">
      <div class="flex flex-col items-center justify-center">
        <div class="p-avatar p-component p-avatar-image p-avatar-circle p-avatar-xl w-32 h-32" data-pc-name="avatar"
          data-pc-section="root" pc4551="">
          <img src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png"
            data-pc-section="image" />
        </div>
        <div class="leading-6 font-medium text-color mt-4 w-full text-center">PrimeTek</div>
        <div class="leading-5 text-sm text-muted-color mt-1 w-full text-center">@primetek</div>
        <div class="flex items-center justify-center flex-wrap gap-1 mt-4">
          <Button icon="pi pi-phone" severity="secondary" text />
          <Button icon="pi pi-video" severity="secondary" text />
          <Button icon="pi pi-sign-in" severity="secondary" text />
          <Button icon="pi pi-info-circle" severity="secondary" text />
          <Button icon="pi pi-ellipsis-v" severity="secondary" text />
        </div>
      </div>
      <div class="flex flex-col gap-4 mt-4">
        <div class="flex items-center gap-2">
          <i class="pi pi-bell text-color"></i>
          <div class="leading-6 font-medium text-color flex-1">Notification</div>
          <ToggleSwitch />
        </div>
        <div class="flex items-center gap-2">
          <i class="pi pi-volume-down text-color"></i>
          <div class="leading-6 font-medium text-color flex-1">Sound</div>
          <div class="p-toggleswitch p-component" data-pc-name="toggleswitch" data-pc-section="root"
            data-p-checked="false" data-p-disabled="false" pc4558="" style="position: relative">
            <input type="checkbox" role="switch" class="p-toggleswitch-input" aria-checked="false"
              data-pc-section="input" /><span class="p-toggleswitch-slider" data-pc-section="slider"></span>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <i class="pi pi-download text-color"></i>
          <div class="leading-6 font-medium text-color flex-1">Save to downloads</div>
          <div class="p-toggleswitch p-component" data-pc-name="toggleswitch" data-pc-section="root"
            data-p-checked="false" data-p-disabled="false" pc4559="" style="position: relative">
            <input type="checkbox" role="switch" class="p-toggleswitch-input" aria-checked="false"
              data-pc-section="input" /><span class="p-toggleswitch-slider" data-pc-section="slider"></span>
          </div>
        </div>
      </div>
      <div class="mt-6">
        <div class="flex items-center gap-2">
          <div class="flex-1 text-color leading-6 font-medium">Members</div>
          <Button text label="See All" class="text-sm py-0.5 px-2 text-muted-color" />
        </div>
        <div class="mt-4 flex flex-col gap-4">
          <div class="flex items-center gap-2 cursor-pointer">
            <div class="p-avatar p-component p-avatar-image p-avatar-circle font-medium text-xs" data-pc-name="avatar"
              data-pc-section="root" pc4564="">
              <img src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar8.png"
                data-pc-section="image" />
            </div>
            <div
              class="text-sm text-color hover:text-muted-color-emphasis transition-colors font-medium leading-5 flex-1">
              Arlene McCoy
            </div>
            <i class="pi pi-chevron-right text-xs text-muted-color"></i>
          </div>
          <div class="flex items-center gap-2 cursor-pointer">
            <div class="p-avatar p-component p-avatar-circle bg-orange-100 text-orange-950 font-medium text-xs"
              data-pc-name="avatar" data-pc-section="root" pc4565="">
              <span class="p-avatar-label" data-pc-section="label">DR</span>
            </div>
            <div
              class="text-sm text-color hover:text-muted-color-emphasis transition-colors font-medium leading-5 flex-1">
              Dianne Russell
            </div>
            <i class="pi pi-chevron-right text-xs text-muted-color"></i>
          </div>
        </div>
      </div>
      <div class="mt-5">
        <div class="p-selectbutton p-component w-full" role="group" data-pc-name="selectbutton" data-pc-section="root"
          pc4566="">
          <button type="button" class="p-togglebutton p-component p-togglebutton-checked flex-1" aria-pressed="true"
            data-pc-name="pcbutton" data-pc-extend="togglebutton" data-pc-section="root" data-p-checked="true"
            data-p-disabled="false">
            <span class="p-togglebutton-content" data-pc-section="content"><!----><span class="p-togglebutton-label"
                data-pc-section="label">Media</span></span>
          </button>
          <button type="button" class="p-togglebutton p-component flex-1" aria-pressed="false" data-pc-name="pcbutton"
            data-pc-extend="togglebutton" data-pc-section="root" data-p-checked="false" data-p-disabled="false">
            <span class="p-togglebutton-content" data-pc-section="content"><!----><span class="p-togglebutton-label"
                data-pc-section="label">Link</span></span>
          </button>
          <button type="button" class="p-togglebutton p-component flex-1" aria-pressed="false" data-pc-name="pcbutton"
            data-pc-extend="togglebutton" data-pc-section="root" data-p-checked="false" data-p-disabled="false">
            <span class="p-togglebutton-content" data-pc-section="content"><!----><span class="p-togglebutton-label"
                data-pc-section="label">Docs</span></span>
          </button>
        </div>
        <div class="mt-3 mb-5 grid grid-cols-3 gap-2">
          <div
            class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer">
            <img class="w-full h-full object-cover block"
              src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image1.png" alt="Media Image" />
          </div>
          <div
            class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer">
            <img class="w-full h-full object-cover block"
              src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image2.png" alt="Media Image" />
          </div>
          <div
            class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer">
            <img class="w-full h-full object-cover block"
              src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image3.png" alt="Media Image" />
          </div>
          <div
            class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer">
            <img class="w-full h-full object-cover block"
              src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image4.png" alt="Media Image" />
          </div>
          <div
            class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer">
            <img class="w-full h-full object-cover block"
              src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image5.png" alt="Media Image" />
          </div>
          <div
            class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer flex items-center justify-center">
            <span class="text-muted-color font-medium">99+</span>
          </div>
        </div>
        <Button icon="pi pi-arrow-right" iconPos="right" outlined label="Show more" class="w-full" />

        <!--          <button-->
        <!--            class="p-button p-component p-button-outlined w-full text-left justify-between"-->
        <!--            type="button"-->
        <!--            aria-label="Show more"-->
        <!--            data-pc-name="button"-->
        <!--            data-p-disabled="false"-->
        <!--            data-pc-section="root"-->
        <!--            pc4567=""-->
        <!--          >-->
        <!--            <span-->
        <!--              class="p-button-icon p-button-icon-right pi pi-arrow-right"-->
        <!--              data-pc-section="icon"-->
        <!--            ></span-->
        <!--            ><span class="p-button-label" data-pc-section="label">Show more</span-->
        <!--            >&lt;!&ndash;&ndash;&gt;-->
        <!--          </button>-->
      </div>
    </div>
  </div>
</template>

<style scoped></style>
