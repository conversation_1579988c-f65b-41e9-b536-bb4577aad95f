[App]
URL = "http://127.0.0.1:8989"
Host = "127.0.0.1"
Port = 8989
PIDFile = "./meeting.pid"
LogFile = "storage/logs/app.log"
UploadDir = "public/upload"
JWTSecret = "your-jwt-secret-key-here-change-in-production"
SessionSecret = "your-session-secret-key-here-change-in-production"
Secure = false

[Im]
AppID = "AppID"
SecretKey = "SecretKey"

[Mysql]
DSN = "root:123456@tcp(127.0.0.1:3305)/meeting?charset=utf8mb4&parseTime=True&loc=Local"

[Passport]
URL = "http://cms.localhost"
ClientID = "9ec64326-6608-4fc4-b6c2-2851ea1afed3"
ClientSecret = "43hx6hCbh0dCbcNVYi5D0HVZnn2m1YXjeDPyHTZc"
RedirectURI = "http://127.0.0.1:8989/login/callback"
ResponseType = "code"
Scope = ["base_info"]
GrantType = "authorization_code"