import{B as _,y as S,D as n,K as r,I as d,O as i,S as w,V as b,W as $,a as u,aa as B,a0 as P,F as h,u as a,a3 as m,a4 as C,ab as D,ac as k,a8 as L,H as V,G as I,ad as N}from"./index-CuD5g1K5.js";import{s as y}from"./index-BGAKD1C9.js";var W=function(o){var s=o.dt;return`
.p-progressspinner {
    position: relative;
    margin: 0 auto;
    width: 100px;
    height: 100px;
    display: inline-block;
}

.p-progressspinner::before {
    content: "";
    display: block;
    padding-top: 100%;
}

.p-progressspinner-spin {
    height: 100%;
    transform-origin: center center;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    animation: p-progressspinner-rotate 2s linear infinite;
}

.p-progressspinner-circle {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: 0;
    stroke: `.concat(s("progressspinner.color.1"),`;
    animation: p-progressspinner-dash 1.5s ease-in-out infinite, p-progressspinner-color 6s ease-in-out infinite;
    stroke-linecap: round;
}

@keyframes p-progressspinner-rotate {
    100% {
        transform: rotate(360deg);
    }
}
@keyframes p-progressspinner-dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35px;
    }
    100% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124px;
    }
}
@keyframes p-progressspinner-color {
    100%,
    0% {
        stroke: `).concat(s("progressspinner.color.1"),`;
    }
    40% {
        stroke: `).concat(s("progressspinner.color.2"),`;
    }
    66% {
        stroke: `).concat(s("progressspinner.color.3"),`;
    }
    80%,
    90% {
        stroke: `).concat(s("progressspinner.color.4"),`;
    }
}
`)},j={root:"p-progressspinner",spin:"p-progressspinner-spin",circle:"p-progressspinner-circle"},q=_.extend({name:"progressspinner",theme:W,classes:j}),M={name:"BaseProgressSpinner",extends:S,props:{strokeWidth:{type:String,default:"2"},fill:{type:String,default:"none"},animationDuration:{type:String,default:"2s"}},style:q,provide:function(){return{$pcProgressSpinner:this,$parentInstance:this}}},v={name:"ProgressSpinner",extends:M,inheritAttrs:!1,computed:{svgStyle:function(){return{"animation-duration":this.animationDuration}}}},R=["fill","stroke-width"];function T(e,o,s,p,c,l){return n(),r("div",d({class:e.cx("root"),role:"progressbar"},e.ptmi("root")),[(n(),r("svg",d({class:e.cx("spin"),viewBox:"25 25 50 50",style:l.svgStyle},e.ptm("spin")),[i("circle",d({class:e.cx("circle"),cx:"50",cy:"50",r:"20",fill:e.fill,"stroke-width":e.strokeWidth,strokeMiterlimit:"10"},e.ptm("circle")),null,16,R)],16))],16)}v.render=T;const U={class:"w-full h-full flex justify-center items-center flex-col gap-6"},F=i("p",{class:"text-2xl"},"多人实时音视频在线会议",-1),A=i("p",{class:"text-sm text-gray-500"}," 多人会议需使用多账号！测试账号密码***********，其他账号请自行注册 ",-1),E=i("p",{class:"text-sm text-gray-500"},"点击下方会议加入",-1),G={key:1,class:"flex justify-center items-center gap-2"},H={class:"flex flex-col justify-between bg-gray-100 w-48 h-32 rounded-md p-4"},z=w({__name:"LoginView",setup(e){b();const o=$(),s=D();u("");const p=u(!0),c=u([]);B().then(t=>{c.value=t}),P(()=>{setTimeout(()=>{p.value=!1},200)});const l=()=>{const g=new URL(window.location.href).searchParams.get("redirect_uri");N(g)};function x(t){s.push(`/meeting/${t}`)}return(t,g)=>(n(),r("div",U,[F,A,E,p.value?(n(),h(a(v),{key:0,style:{width:"50px",height:"50px"},strokeWidth:"4",fill:"transparent",animationDuration:".5s","aria-label":"Custom ProgressSpinner"})):(n(),r(m,{key:1},[a(o).info.id?(n(),r("div",G,[(n(!0),r(m,null,C(c.value,f=>(n(),r("div",H,[k(L(f.Name)+" ",1),V(a(y),{onClick:()=>x(f.ID)},{default:I(()=>[k("加入")]),_:2},1032,["onClick"])]))),256))])):(n(),h(a(y),{key:0,label:"登录",severity:"secondary",onClick:l}))],64))]))}});export{z as default};
