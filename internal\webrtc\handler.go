package webrtc

import (
	"bytes"
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

const (
	// Time allowed to write a message to the peer
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer
	pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer
	maxMessageSize = 4096
)

var (
	newline = []byte{'\n'}
	space   = []byte{' '}
)

var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Allow connections from any origin
		return true
	},
}

// ClientManager manages WebSocket clients
type ClientManager struct {
	hub *Hub
}

// NewClientManager creates a new ClientManager
func NewClientManager(hub *Hub) *ClientManager {
	return &ClientManager{
		hub: hub,
	}
}

// HandleWebSocket handles WebSocket connections
func (cm *ClientManager) HandleWebSocket(c *gin.Context) {
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Failed to upgrade connection: %v", err)
		return
	}
	defer conn.Close()

	// Generate client ID
	clientID := "client_" + uuid.New().String()[:8]
	clientName := "Device " + clientID[:6]

	// Create client
	client := &Client{
		ID:     clientID,
		Name:   clientName,
		Conn:   conn,
		RoomID: "",
	}

	// Register client
	cm.hub.register <- client

	// Set up pong handler
	conn.SetReadLimit(maxMessageSize)
	conn.SetReadDeadline(time.Now().Add(pongWait))
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(pongWait))
		return nil
	})

	// Handle messages
	for {
		_, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		message = bytes.TrimSpace(bytes.Replace(message, newline, space, -1))

		// Parse the message
		var msg *Message
		if err := json.Unmarshal(message, &msg); err != nil {
			log.Printf("Error parsing message: %v", err)
			continue
		}

		// Set the client ID
		msg.ClientID = client.ID

		// Send to hub for processing
		cm.hub.broadcast <- msg
	}

	// Unregister client
	cm.hub.unregister <- client
}

// ServeWebSocket serves the WebSocket endpoint
func ServeWebSocket(hub *Hub) gin.HandlerFunc {
	manager := NewClientManager(hub)
	return manager.HandleWebSocket
}
