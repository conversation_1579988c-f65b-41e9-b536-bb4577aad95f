import axios from 'axios'
import { apiBaseURI, appId } from '@/config'
export function getWebsocketUrl(params: any) {
  const usp = new URLSearchParams(params)
  return `${apiBaseURI}/api/websocket?${usp}`
}

export function loginToken() {
  return axios.get(`/login/token`)
}

export function getMeetingList() {
  return axios.get(`/api/meetings`)
}

export function getMeetingInfo(id: any) {
  return axios.get(`/api/meetings/${id}`)
}

export function userSignature() {
  return axios.post(`/api/user/signature?AppID=${appId}`)
}
