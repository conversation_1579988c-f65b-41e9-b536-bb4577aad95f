# WebRTC Meeting Functionality

This document explains how to use the WebRTC meeting functionality that has been integrated into the meeting server.

## Overview

The WebRTC functionality allows users to create and join real-time video meetings directly through their web browsers. It follows a hub-based architecture similar to the JavaScript signaling server implementation, with:

- WebSocket signaling server for connection negotiation
- Meeting room management
- Participant management
- Real-time messaging within meetings

## Architecture

The implementation follows a hub-based pattern:
- **Hub**: Central message router that manages all clients and rooms
- **ClientManager**: Handles WebSocket connections and client lifecycle
- **Service**: Manages the overall WebRTC service lifecycle

## WebSocket Endpoint

The WebRTC signaling server is available at:
```
ws://[server-address]/ws
```

For local development, this would be:
```
ws://localhost:8989/ws
```

## Message Types

The WebSocket server supports the following message types:

### Connection Management
- `discover` - Get list of available devices
- `connection-request` - Request connection to another device
- `offer` - WebRTC offer
- `answer` - WebRTC answer
- `ice-candidate` - ICE candidate for connection

### Meeting Management
- `create-meeting` - Create a new meeting
- `join-meeting` - Join an existing meeting
- `leave-room` - Leave current meeting
- `meeting-message` - Send message to all meeting participants

### Room Management (Legacy)
- `create-room` - Create a new room
- `join-room` - Join an existing room
- `room-message` - Send message to all room participants

### Meeting Actions (Host Only)
- `meeting-action` - Perform actions like kicking participants or muting

## Example Usage

### 1. Connecting to the Server

```javascript
const ws = new WebSocket('ws://localhost:8989/ws');

ws.onopen = function(event) {
    console.log('Connected to WebRTC signaling server');
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    // Handle incoming messages
};
```

### 2. Creating a Meeting

```javascript
const createMeetingMessage = {
    type: 'create-meeting',
    data: {
        displayName: 'Participant Name'
    }
};

ws.send(JSON.stringify(createMeetingMessage));
```

### 3. Joining a Meeting

```javascript
const joinMeetingMessage = {
    type: 'join-meeting',
    data: {
        meetingId: 'meeting-room-id',
        displayName: 'Participant Name'
    }
};

ws.send(JSON.stringify(joinMeetingMessage));
```

### 4. WebRTC Signaling

After joining a meeting, participants need to exchange WebRTC offers, answers, and ICE candidates:

```javascript
// Send offer
const offerMessage = {
    type: 'room-offer',
    data: {
        sdp: offer.sdp
    }
};
ws.send(JSON.stringify(offerMessage));

// Send answer
const answerMessage = {
    type: 'room-answer',
    data: {
        sdp: answer.sdp
    }
};
ws.send(JSON.stringify(answerMessage));

// Send ICE candidate
const iceCandidateMessage = {
    type: 'room-ice-candidate',
    data: {
        candidate: candidate
    }
};
ws.send(JSON.stringify(iceCandidateMessage));
```

## Testing

A test HTML page is available at:
```
http://localhost:8989/webrtc-test.html
```

This page provides a simple interface to test the WebRTC functionality.

## API Reference

### Message Format

All messages follow this JSON format:
```json
{
    "type": "message-type",
    "data": {
        // Message-specific data
    }
}
```

### Response Messages

The server sends various response messages:
- `id` - Client's assigned ID
- `devices` - List of available devices
- `meeting-created` - Confirmation of meeting creation
- `meeting-joined` - Confirmation of joining a meeting
- `participant-joined` - Notification when a participant joins
- `participant-left` - Notification when a participant leaves

## Security Considerations

- All WebSocket connections are currently allowed (CORS is disabled)
- In production, you may want to implement authentication for WebSocket connections
- Meeting IDs are randomly generated and should be shared securely

## Troubleshooting

### Common Issues

1. **WebSocket connection fails**
   - Check that the server is running
   - Verify the WebSocket URL is correct
   - Ensure firewall is not blocking the connection

2. **WebRTC connection fails**
   - Check that STUN servers are configured correctly
   - Verify that camera/microphone permissions are granted
   - Ensure NAT traversal is working (TURN server may be needed)

### Logs

Check the server logs for debugging information:
```
2025/08/31 14:52:14 Client client_11dc4063 connected
```

## Refactored Implementation Details

The refactored implementation aligns more closely with the original JavaScript signaling server:

1. **Simplified Data Structures**: Removed unnecessary fields from Client, Participant, and Room structs
2. **Streamlined Message Handling**: Simplified the message routing logic
3. **Consistent Naming**: Aligned naming conventions with the JavaScript implementation
4. **Improved Error Handling**: Better error handling and logging
5. **Cleaner Code Organization**: More modular and maintainable code structure

## Future Improvements

Possible enhancements to the WebRTC functionality:
- Add authentication for WebSocket connections
- Implement TURN server support for better NAT traversal
- Add recording capabilities
- Implement screen sharing
- Add chat functionality within meetings