import{t as Pe,r as Ie,a as j,c as ze,i as Be,w as le,g as <PERSON><PERSON>,o as He,u as F,B as $e,b as Ne,d as Ke,f as ae,e as ie,h as Ue,j as we,k as Ve,E as We,s as Ge,R as Ye,l as Ze,m as Je,n as qe,C as Qe,p as Xe,q as et,v as Se,$ as tt,x as nt,Z as he,y as ot,z as it,A as Me,D as rt,F as H,G as ye,H as P,T as at,I as ee,J as se,K as st,L as U,M as Ce,N as lt,O as ut,P as g,Q as dt,S as ct,U as ft,V as pt,W as bt,X as vt,Y as ht,_ as mt,a0 as yt,a1 as gt,a2 as $t,a3 as kt,a4 as xe,a5 as re,a6 as fe,a7 as Le,a8 as _e,a9 as pe}from"./index-uycSxWK8.js";import{s as X}from"./index-DONUS5C5.js";import{s as wt,a as Oe,b as St}from"./index-D9uK78IG.js";function ve(t){return Ae()?(He(t),!0):!1}function ke(t){return typeof t=="function"?t():F(t)}const ne=typeof window<"u"&&typeof document<"u",Ct=typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope,xt=Object.prototype.toString,Lt=t=>xt.call(t)==="[object Object]",Fe=()=>{};function _t(...t){if(t.length!==1)return Pe(...t);const e=t[0];return typeof e=="function"?Ie(ze(()=>({get:e,set:Fe}))):j(e)}function Ot(t,e=1e3,n={}){const{immediate:a=!0,immediateCallback:m=!1}=n;let u=null;const _=j(!1);function O(){u&&(clearInterval(u),u=null)}function $(){_.value=!1,O()}function x(){const v=ke(e);v<=0||(_.value=!0,m&&t(),O(),u=setInterval(t,v))}if(a&&ne&&x(),Be(e)||typeof e=="function"){const v=le(e,()=>{_.value&&ne&&x()});ve(v)}return ve($),{isActive:_,pause:$,resume:x}}function Et(t){var e;const n=ke(t);return(e=n==null?void 0:n.$el)!=null?e:n}const Tt=ne?window:void 0;function Dt(...t){let e,n,a,m;if(typeof t[0]=="string"||Array.isArray(t[0])?([n,a,m]=t,e=Tt):[e,n,a,m]=t,!e)return Fe;Array.isArray(n)||(n=[n]),Array.isArray(a)||(a=[a]);const u=[],_=()=>{u.forEach(v=>v()),u.length=0},O=(v,b,M,E)=>(v.addEventListener(b,M,E),()=>v.removeEventListener(b,M,E)),$=le(()=>[Et(e),ke(m)],([v,b])=>{if(_(),!v)return;const M=Lt(b)?{...b}:b;u.push(...n.flatMap(E=>a.map(I=>O(v,E,I,M))))},{immediate:!0,flush:"post"}),x=()=>{$(),_()};return ve(x),x}const Ee="ping";function me(t){return t===!0?{}:t}function Mt(t,e={}){const{onConnected:n,onDisconnected:a,onError:m,onMessage:u,immediate:_=!0,autoClose:O=!0,protocols:$=[]}=e,x=j(null),v=j("CLOSED"),b=j(),M=_t(t);let E,I,z=!1,W=0,J=[],q;const G=()=>{if(J.length&&b.value&&v.value==="OPEN"){for(const T of J)b.value.send(T);J=[]}},te=()=>{clearTimeout(q),q=void 0},A=(T=1e3,k)=>{!ne||!b.value||(z=!0,te(),E==null||E(),b.value.close(T,k),b.value=void 0)},B=(T,k=!0)=>!b.value||v.value!=="OPEN"?(k&&J.push(T),!1):(G(),b.value.send(T),!0),Q=()=>{if(z||typeof M.value>"u")return;const T=new WebSocket(M.value,$);b.value=T,v.value="CONNECTING",T.onopen=()=>{v.value="OPEN",n==null||n(T),I==null||I(),G()},T.onclose=k=>{if(v.value="CLOSED",a==null||a(T,k),!z&&e.autoReconnect){const{retries:y=-1,delay:N=1e3,onFailed:Y}=me(e.autoReconnect);W+=1,typeof y=="number"&&(y<0||W<y)||typeof y=="function"&&y()?setTimeout(Q,N):Y==null||Y()}},T.onerror=k=>{m==null||m(T,k)},T.onmessage=k=>{if(e.heartbeat){te();const{message:y=Ee}=me(e.heartbeat);if(k.data===y)return}x.value=k.data,u==null||u(T,k)}};if(e.heartbeat){const{message:T=Ee,interval:k=1e3,pongTimeout:y=1e3}=me(e.heartbeat),{pause:N,resume:Y}=Ot(()=>{B(T,!1),q==null&&(q=setTimeout(()=>{A(),z=!1},y))},k,{immediate:!1});E=N,I=Y}O&&(ne&&Dt("beforeunload",()=>A()),ve(A));const Z=()=>{!ne&&!Ct||(A(),z=!1,W=0,Q())};return _&&Z(),le(M,Z),{data:x,status:v,close:A,send:B,open:Z,ws:b}}var Ft=$e.extend({name:"focustrap-directive"}),jt=Ne.extend({style:Ft});function ue(t){"@babel/helpers - typeof";return ue=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ue(t)}function Te(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter(function(m){return Object.getOwnPropertyDescriptor(t,m).enumerable})),n.push.apply(n,a)}return n}function De(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Te(Object(n),!0).forEach(function(a){Rt(t,a,n[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Te(Object(n)).forEach(function(a){Object.defineProperty(t,a,Object.getOwnPropertyDescriptor(n,a))})}return t}function Rt(t,e,n){return(e=Pt(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Pt(t){var e=It(t,"string");return ue(e)=="symbol"?e:e+""}function It(t,e){if(ue(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var a=n.call(t,e);if(ue(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var zt=jt.extend("focustrap",{mounted:function(e,n){var a=n.value||{},m=a.disabled;m||(this.createHiddenFocusableElements(e,n),this.bind(e,n),this.autoElementFocus(e,n)),e.setAttribute("data-pd-focustrap",!0),this.$el=e},updated:function(e,n){var a=n.value||{},m=a.disabled;m&&this.unbind(e)},unmounted:function(e){this.unbind(e)},methods:{getComputedSelector:function(e){return':not(.p-hidden-focusable):not([data-p-hidden-focusable="true"])'.concat(e??"")},bind:function(e,n){var a=this,m=n.value||{},u=m.onFocusIn,_=m.onFocusOut;e.$_pfocustrap_mutationobserver=new MutationObserver(function(O){O.forEach(function($){if($.type==="childList"&&!e.contains(document.activeElement)){var x=function(b){var M=we(b)?we(b,a.getComputedSelector(e.$_pfocustrap_focusableselector))?b:ie(e,a.getComputedSelector(e.$_pfocustrap_focusableselector)):ie(b);return Ve(M)?M:b.nextSibling&&x(b.nextSibling)};ae(x($.nextSibling))}})}),e.$_pfocustrap_mutationobserver.disconnect(),e.$_pfocustrap_mutationobserver.observe(e,{childList:!0}),e.$_pfocustrap_focusinlistener=function(O){return u&&u(O)},e.$_pfocustrap_focusoutlistener=function(O){return _&&_(O)},e.addEventListener("focusin",e.$_pfocustrap_focusinlistener),e.addEventListener("focusout",e.$_pfocustrap_focusoutlistener)},unbind:function(e){e.$_pfocustrap_mutationobserver&&e.$_pfocustrap_mutationobserver.disconnect(),e.$_pfocustrap_focusinlistener&&e.removeEventListener("focusin",e.$_pfocustrap_focusinlistener)&&(e.$_pfocustrap_focusinlistener=null),e.$_pfocustrap_focusoutlistener&&e.removeEventListener("focusout",e.$_pfocustrap_focusoutlistener)&&(e.$_pfocustrap_focusoutlistener=null)},autoFocus:function(e){this.autoElementFocus(this.$el,{value:De(De({},e),{},{autoFocus:!0})})},autoElementFocus:function(e,n){var a=n.value||{},m=a.autoFocusSelector,u=m===void 0?"":m,_=a.firstFocusableSelector,O=_===void 0?"":_,$=a.autoFocus,x=$===void 0?!1:$,v=ie(e,"[autofocus]".concat(this.getComputedSelector(u)));x&&!v&&(v=ie(e,this.getComputedSelector(O))),ae(v)},onFirstHiddenElementFocus:function(e){var n,a=e.currentTarget,m=e.relatedTarget,u=m===a.$_pfocustrap_lasthiddenfocusableelement||!((n=this.$el)!==null&&n!==void 0&&n.contains(m))?ie(a.parentElement,this.getComputedSelector(a.$_pfocustrap_focusableselector)):a.$_pfocustrap_lasthiddenfocusableelement;ae(u)},onLastHiddenElementFocus:function(e){var n,a=e.currentTarget,m=e.relatedTarget,u=m===a.$_pfocustrap_firsthiddenfocusableelement||!((n=this.$el)!==null&&n!==void 0&&n.contains(m))?Ke(a.parentElement,this.getComputedSelector(a.$_pfocustrap_focusableselector)):a.$_pfocustrap_firsthiddenfocusableelement;ae(u)},createHiddenFocusableElements:function(e,n){var a=this,m=n.value||{},u=m.tabIndex,_=u===void 0?0:u,O=m.firstFocusableSelector,$=O===void 0?"":O,x=m.lastFocusableSelector,v=x===void 0?"":x,b=function(z){return Ue("span",{class:"p-hidden-accessible p-hidden-focusable",tabIndex:_,role:"presentation","aria-hidden":!0,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0,onFocus:z==null?void 0:z.bind(a)})},M=b(this.onFirstHiddenElementFocus),E=b(this.onLastHiddenElementFocus);M.$_pfocustrap_lasthiddenfocusableelement=E,M.$_pfocustrap_focusableselector=$,M.setAttribute("data-pc-section","firstfocusableelement"),E.$_pfocustrap_firsthiddenfocusableelement=M,E.$_pfocustrap_focusableselector=v,E.setAttribute("data-pc-section","lastfocusableelement"),e.prepend(M),e.append(E)}}}),be=We(),Bt=({dt:t})=>`
.p-popover {
    margin-block-start: ${t("popover.gutter")};
    background: ${t("popover.background")};
    color: ${t("popover.color")};
    border: 1px solid ${t("popover.border.color")};
    border-radius: ${t("popover.border.radius")};
    box-shadow: ${t("popover.shadow")};
}

.p-popover-content {
    padding: ${t("popover.content.padding")};
}

.p-popover-flipped {
    margin-block-start: calc(${t("popover.gutter")} * -1);
    margin-block-end: ${t("popover.gutter")};
}

.p-popover-enter-from {
    opacity: 0;
    transform: scaleY(0.8);
}

.p-popover-leave-to {
    opacity: 0;
}

.p-popover-enter-active {
    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.p-popover-leave-active {
    transition: opacity 0.1s linear;
}

.p-popover:after,
.p-popover:before {
    bottom: 100%;
    left: calc(${t("popover.arrow.offset")} + ${t("popover.arrow.left")});
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.p-popover:after {
    border-width: calc(${t("popover.gutter")} - 2px);
    margin-left: calc(-1 * (${t("popover.gutter")} - 2px));
    border-style: solid;
    border-color: transparent;
    border-bottom-color: ${t("popover.background")};
}

.p-popover:before {
    border-width: ${t("popover.gutter")};
    margin-left: calc(-1 * ${t("popover.gutter")});
    border-style: solid;
    border-color: transparent;
    border-bottom-color: ${t("popover.border.color")};
}

.p-popover-flipped:after,
.p-popover-flipped:before {
    bottom: auto;
    top: 100%;
}

.p-popover.p-popover-flipped:after {
    border-bottom-color: transparent;
    border-top-color: ${t("popover.background")};
}

.p-popover.p-popover-flipped:before {
    border-bottom-color: transparent;
    border-top-color: ${t("popover.border.color")};
}
`,At={root:"p-popover p-component",content:"p-popover-content"},Ht=$e.extend({name:"popover",style:Bt,classes:At}),Nt={name:"BasePopover",extends:Ze,props:{dismissable:{type:Boolean,default:!0},appendTo:{type:[String,Object],default:"body"},baseZIndex:{type:Number,default:0},autoZIndex:{type:Boolean,default:!0},breakpoints:{type:Object,default:null},closeOnEscape:{type:Boolean,default:!0}},style:Ht,provide:function(){return{$pcPopover:this,$parentInstance:this}}},je={name:"Popover",extends:Nt,inheritAttrs:!1,emits:["show","hide"],data:function(){return{visible:!1}},watch:{dismissable:{immediate:!0,handler:function(e){e?this.bindOutsideClickListener():this.unbindOutsideClickListener()}}},selfClick:!1,target:null,eventTarget:null,outsideClickListener:null,scrollHandler:null,resizeListener:null,container:null,styleElement:null,overlayEventListener:null,documentKeydownListener:null,beforeUnmount:function(){this.dismissable&&this.unbindOutsideClickListener(),this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.destroyStyle(),this.unbindResizeListener(),this.target=null,this.container&&this.autoZIndex&&he.clear(this.container),this.overlayEventListener&&(be.off("overlay-click",this.overlayEventListener),this.overlayEventListener=null),this.container=null},mounted:function(){this.breakpoints&&this.createStyle()},methods:{toggle:function(e,n){this.visible?this.hide():this.show(e,n)},show:function(e,n){this.visible=!0,this.eventTarget=e.currentTarget,this.target=n||e.currentTarget},hide:function(){this.visible=!1},onContentClick:function(){this.selfClick=!0},onEnter:function(e){var n=this;ot(e,{position:"absolute",top:"0"}),this.alignOverlay(),this.dismissable&&this.bindOutsideClickListener(),this.bindScrollListener(),this.bindResizeListener(),this.autoZIndex&&he.set("overlay",e,this.baseZIndex+this.$primevue.config.zIndex.overlay),this.overlayEventListener=function(a){n.container.contains(a.target)&&(n.selfClick=!0)},this.focus(),be.on("overlay-click",this.overlayEventListener),this.$emit("show"),this.closeOnEscape&&this.bindDocumentKeyDownListener()},onLeave:function(){this.unbindOutsideClickListener(),this.unbindScrollListener(),this.unbindResizeListener(),this.unbindDocumentKeyDownListener(),be.off("overlay-click",this.overlayEventListener),this.overlayEventListener=null,this.$emit("hide")},onAfterLeave:function(e){this.autoZIndex&&he.clear(e)},alignOverlay:function(){et(this.container,this.target,!1);var e=Se(this.container),n=Se(this.target),a=0;e.left<n.left&&(a=n.left-e.left),this.container.style.setProperty(tt("popover.arrow.left").name,"".concat(a,"px")),e.top<n.top&&(this.container.setAttribute("data-p-popover-flipped","true"),!this.isUnstyled&&nt(this.container,"p-popover-flipped"))},onContentKeydown:function(e){e.code==="Escape"&&this.closeOnEscape&&(this.hide(),ae(this.target))},onButtonKeydown:function(e){switch(e.code){case"ArrowDown":case"ArrowUp":case"ArrowLeft":case"ArrowRight":e.preventDefault()}},focus:function(){var e=this.container.querySelector("[autofocus]");e&&e.focus()},onKeyDown:function(e){e.code==="Escape"&&this.closeOnEscape&&(this.visible=!1)},bindDocumentKeyDownListener:function(){this.documentKeydownListener||(this.documentKeydownListener=this.onKeyDown.bind(this),window.document.addEventListener("keydown",this.documentKeydownListener))},unbindDocumentKeyDownListener:function(){this.documentKeydownListener&&(window.document.removeEventListener("keydown",this.documentKeydownListener),this.documentKeydownListener=null)},bindOutsideClickListener:function(){var e=this;!this.outsideClickListener&&Xe()&&(this.outsideClickListener=function(n){e.visible&&!e.selfClick&&!e.isTargetClicked(n)&&(e.visible=!1),e.selfClick=!1},document.addEventListener("click",this.outsideClickListener))},unbindOutsideClickListener:function(){this.outsideClickListener&&(document.removeEventListener("click",this.outsideClickListener),this.outsideClickListener=null,this.selfClick=!1)},bindScrollListener:function(){var e=this;this.scrollHandler||(this.scrollHandler=new Qe(this.target,function(){e.visible&&(e.visible=!1)})),this.scrollHandler.bindScrollListener()},unbindScrollListener:function(){this.scrollHandler&&this.scrollHandler.unbindScrollListener()},bindResizeListener:function(){var e=this;this.resizeListener||(this.resizeListener=function(){e.visible&&!qe()&&(e.visible=!1)},window.addEventListener("resize",this.resizeListener))},unbindResizeListener:function(){this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null)},isTargetClicked:function(e){return this.eventTarget&&(this.eventTarget===e.target||this.eventTarget.contains(e.target))},containerRef:function(e){this.container=e},createStyle:function(){if(!this.styleElement&&!this.isUnstyled){var e;this.styleElement=document.createElement("style"),this.styleElement.type="text/css",Je(this.styleElement,"nonce",(e=this.$primevue)===null||e===void 0||(e=e.config)===null||e===void 0||(e=e.csp)===null||e===void 0?void 0:e.nonce),document.head.appendChild(this.styleElement);var n="";for(var a in this.breakpoints)n+=`
                        @media screen and (max-width: `.concat(a,`) {
                            .p-popover[`).concat(this.$attrSelector,`] {
                                width: `).concat(this.breakpoints[a],` !important;
                            }
                        }
                    `);this.styleElement.innerHTML=n}},destroyStyle:function(){this.styleElement&&(document.head.removeChild(this.styleElement),this.styleElement=null)},onOverlayClick:function(e){be.emit("overlay-click",{originalEvent:e,target:this.target})}},directives:{focustrap:zt,ripple:Ye},components:{Portal:Ge}},Kt=["aria-modal"];function Ut(t,e,n,a,m,u){var _=it("Portal"),O=Me("focustrap");return H(),rt(_,{appendTo:t.appendTo},{default:ye(function(){return[P(at,ee({name:"p-popover",onEnter:u.onEnter,onLeave:u.onLeave,onAfterLeave:u.onAfterLeave},t.ptm("transition")),{default:ye(function(){return[m.visible?se((H(),U("div",ee({key:0,ref:u.containerRef,role:"dialog","aria-modal":m.visible,onClick:e[3]||(e[3]=function(){return u.onOverlayClick&&u.onOverlayClick.apply(u,arguments)}),class:t.cx("root")},t.ptmi("root")),[t.$slots.container?Ce(t.$slots,"container",{key:0,closeCallback:u.hide,keydownCallback:function(x){return u.onButtonKeydown(x)}}):(H(),U("div",ee({key:1,class:t.cx("content"),onClick:e[0]||(e[0]=function(){return u.onContentClick&&u.onContentClick.apply(u,arguments)}),onMousedown:e[1]||(e[1]=function(){return u.onContentClick&&u.onContentClick.apply(u,arguments)}),onKeydown:e[2]||(e[2]=function(){return u.onContentKeydown&&u.onContentKeydown.apply(u,arguments)})},t.ptm("content")),[Ce(t.$slots,"default")],16))],16,Kt)),[[O]]):st("",!0)]}),_:3},16,["onEnter","onLeave","onAfterLeave"])]}),_:3},8,["appendTo"])}je.render=Ut;var Vt=({dt:t})=>`
.p-radiobutton {
    position: relative;
    display: inline-flex;
    user-select: none;
    vertical-align: bottom;
    width: ${t("radiobutton.width")};
    height: ${t("radiobutton.height")};
}

.p-radiobutton-input {
    cursor: pointer;
    appearance: none;
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    opacity: 0;
    z-index: 1;
    outline: 0 none;
    border: 1px solid transparent;
    border-radius: 50%;
}

.p-radiobutton-box {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    border: 1px solid ${t("radiobutton.border.color")};
    background: ${t("radiobutton.background")};
    width: ${t("radiobutton.width")};
    height: ${t("radiobutton.height")};
    transition: background ${t("radiobutton.transition.duration")}, color ${t("radiobutton.transition.duration")}, border-color ${t("radiobutton.transition.duration")}, box-shadow ${t("radiobutton.transition.duration")}, outline-color ${t("radiobutton.transition.duration")};
    outline-color: transparent;
    box-shadow: ${t("radiobutton.shadow")};
}

.p-radiobutton-icon {
    transition-duration: ${t("radiobutton.transition.duration")};
    background: transparent;
    font-size: ${t("radiobutton.icon.size")};
    width: ${t("radiobutton.icon.size")};
    height: ${t("radiobutton.icon.size")};
    border-radius: 50%;
    backface-visibility: hidden;
    transform: translateZ(0) scale(0.1);
}

.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {
    border-color: ${t("radiobutton.hover.border.color")};
}

.p-radiobutton-checked .p-radiobutton-box {
    border-color: ${t("radiobutton.checked.border.color")};
    background: ${t("radiobutton.checked.background")};
}

.p-radiobutton-checked .p-radiobutton-box .p-radiobutton-icon {
    background: ${t("radiobutton.icon.checked.color")};
    transform: translateZ(0) scale(1, 1);
    visibility: visible;
}

.p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {
    border-color: ${t("radiobutton.checked.hover.border.color")};
    background: ${t("radiobutton.checked.hover.background")};
}

.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:hover).p-radiobutton-checked .p-radiobutton-box .p-radiobutton-icon {
    background: ${t("radiobutton.icon.checked.hover.color")};
}

.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:focus-visible) .p-radiobutton-box {
    border-color: ${t("radiobutton.focus.border.color")};
    box-shadow: ${t("radiobutton.focus.ring.shadow")};
    outline: ${t("radiobutton.focus.ring.width")} ${t("radiobutton.focus.ring.style")} ${t("radiobutton.focus.ring.color")};
    outline-offset: ${t("radiobutton.focus.ring.offset")};
}

.p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:focus-visible) .p-radiobutton-box {
    border-color: ${t("radiobutton.checked.focus.border.color")};
}

.p-radiobutton.p-invalid > .p-radiobutton-box {
    border-color: ${t("radiobutton.invalid.border.color")};
}

.p-radiobutton.p-variant-filled .p-radiobutton-box {
    background: ${t("radiobutton.filled.background")};
}

.p-radiobutton.p-variant-filled.p-radiobutton-checked .p-radiobutton-box {
    background: ${t("radiobutton.checked.background")};
}

.p-radiobutton.p-variant-filled:not(.p-disabled):has(.p-radiobutton-input:hover).p-radiobutton-checked .p-radiobutton-box {
    background: ${t("radiobutton.checked.hover.background")};
}

.p-radiobutton.p-disabled {
    opacity: 1;
}

.p-radiobutton.p-disabled .p-radiobutton-box {
    background: ${t("radiobutton.disabled.background")};
    border-color: ${t("radiobutton.checked.disabled.border.color")};
}

.p-radiobutton-checked.p-disabled .p-radiobutton-box .p-radiobutton-icon {
    background: ${t("radiobutton.icon.disabled.color")};
}

.p-radiobutton-sm,
.p-radiobutton-sm .p-radiobutton-box {
    width: ${t("radiobutton.sm.width")};
    height: ${t("radiobutton.sm.height")};
}

.p-radiobutton-sm .p-radiobutton-icon {
    font-size: ${t("radiobutton.icon.sm.size")};
    width: ${t("radiobutton.icon.sm.size")};
    height: ${t("radiobutton.icon.sm.size")};
}

.p-radiobutton-lg,
.p-radiobutton-lg .p-radiobutton-box {
    width: ${t("radiobutton.lg.width")};
    height: ${t("radiobutton.lg.height")};
}

.p-radiobutton-lg .p-radiobutton-icon {
    font-size: ${t("radiobutton.icon.lg.size")};
    width: ${t("radiobutton.icon.lg.size")};
    height: ${t("radiobutton.icon.lg.size")};
}
`,Wt={root:function(e){var n=e.instance,a=e.props;return["p-radiobutton p-component",{"p-radiobutton-checked":n.checked,"p-disabled":a.disabled,"p-invalid":n.$pcRadioButtonGroup?n.$pcRadioButtonGroup.$invalid:n.$invalid,"p-variant-filled":n.$variant==="filled","p-radiobutton-sm p-inputfield-sm":a.size==="small","p-radiobutton-lg p-inputfield-lg":a.size==="large"}]},box:"p-radiobutton-box",input:"p-radiobutton-input",icon:"p-radiobutton-icon"},Gt=$e.extend({name:"radiobutton",style:Vt,classes:Wt}),Yt={name:"BaseRadioButton",extends:wt,props:{value:null,binary:Boolean,readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},inputId:{type:String,default:null},inputClass:{type:[String,Object],default:null},inputStyle:{type:Object,default:null},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:Gt,provide:function(){return{$pcRadioButton:this,$parentInstance:this}}};function de(t){"@babel/helpers - typeof";return de=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(t)}function Zt(t,e,n){return(e=Jt(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Jt(t){var e=qt(t,"string");return de(e)=="symbol"?e:e+""}function qt(t,e){if(de(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var a=n.call(t,e);if(de(a)!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var ge={name:"RadioButton",extends:Yt,inheritAttrs:!1,emits:["change","focus","blur"],inject:{$pcRadioButtonGroup:{default:void 0}},methods:{getPTOptions:function(e){var n=e==="root"?this.ptmi:this.ptm;return n(e,{context:{checked:this.checked,disabled:this.disabled}})},onChange:function(e){if(!this.disabled&&!this.readonly){var n=this.binary?!this.checked:this.value;this.$pcRadioButtonGroup?this.$pcRadioButtonGroup.writeValue(n,e):this.writeValue(n,e),this.$emit("change",e)}},onFocus:function(e){this.$emit("focus",e)},onBlur:function(e){var n,a;this.$emit("blur",e),(n=(a=this.formField).onBlur)===null||n===void 0||n.call(a,e)}},computed:{groupName:function(){return this.$pcRadioButtonGroup?this.$pcRadioButtonGroup.groupName:this.$formName},checked:function(){var e=this.$pcRadioButtonGroup?this.$pcRadioButtonGroup.d_value:this.d_value;return e!=null&&(this.binary?!!e:ut(e,this.value))},dataP:function(){return lt(Zt({invalid:this.$invalid,checked:this.checked,disabled:this.disabled,filled:this.$variant==="filled"},this.size,this.size))}}},Qt=["data-p-checked","data-p-disabled","data-p"],Xt=["id","value","name","checked","tabindex","disabled","readonly","aria-labelledby","aria-label","aria-invalid"],en=["data-p"],tn=["data-p"];function nn(t,e,n,a,m,u){return H(),U("div",ee({class:t.cx("root")},u.getPTOptions("root"),{"data-p-checked":u.checked,"data-p-disabled":t.disabled,"data-p":u.dataP}),[g("input",ee({id:t.inputId,type:"radio",class:[t.cx("input"),t.inputClass],style:t.inputStyle,value:t.value,name:u.groupName,checked:u.checked,tabindex:t.tabindex,disabled:t.disabled,readonly:t.readonly,"aria-labelledby":t.ariaLabelledby,"aria-label":t.ariaLabel,"aria-invalid":t.invalid||void 0,onFocus:e[0]||(e[0]=function(){return u.onFocus&&u.onFocus.apply(u,arguments)}),onBlur:e[1]||(e[1]=function(){return u.onBlur&&u.onBlur.apply(u,arguments)}),onChange:e[2]||(e[2]=function(){return u.onChange&&u.onChange.apply(u,arguments)})},u.getPTOptions("input")),null,16,Xt),g("div",ee({class:t.cx("box")},u.getPTOptions("box"),{"data-p":u.dataP}),[g("div",ee({class:t.cx("icon")},u.getPTOptions("icon"),{"data-p":u.dataP}),null,16,tn)],16,en)],16,Qt)}ge.render=nn;var Re={exports:{}};(function(t,e){(function(n,a){t.exports=a()})(dt,function(){var n=1e3,a=6e4,m=36e5,u="millisecond",_="second",O="minute",$="hour",x="day",v="week",b="month",M="quarter",E="year",I="date",z="Invalid Date",W=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,J=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,q={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(f){var s=["th","st","nd","rd"],r=f%100;return"["+f+(s[(r-20)%10]||s[r]||s[0])+"]"}},G=function(f,s,r){var c=String(f);return!c||c.length>=s?f:""+Array(s+1-c.length).join(r)+f},te={s:G,z:function(f){var s=-f.utcOffset(),r=Math.abs(s),c=Math.floor(r/60),d=r%60;return(s<=0?"+":"-")+G(c,2,"0")+":"+G(d,2,"0")},m:function f(s,r){if(s.date()<r.date())return-f(r,s);var c=12*(r.year()-s.year())+(r.month()-s.month()),d=s.clone().add(c,b),h=r-d<0,i=s.clone().add(c+(h?-1:1),b);return+(-(c+(r-d)/(h?d-i:i-d))||0)},a:function(f){return f<0?Math.ceil(f)||0:Math.floor(f)},p:function(f){return{M:b,y:E,w:v,d:x,D:I,h:$,m:O,s:_,ms:u,Q:M}[f]||String(f||"").toLowerCase().replace(/s$/,"")},u:function(f){return f===void 0}},A="en",B={};B[A]=q;var Q="$isDayjsObject",Z=function(f){return f instanceof N||!(!f||!f[Q])},T=function f(s,r,c){var d;if(!s)return A;if(typeof s=="string"){var h=s.toLowerCase();B[h]&&(d=h),r&&(B[h]=r,d=h);var i=s.split("-");if(!d&&i.length>1)return f(i[0])}else{var o=s.name;B[o]=s,d=o}return!c&&d&&(A=d),d||!c&&A},k=function(f,s){if(Z(f))return f.clone();var r=typeof s=="object"?s:{};return r.date=f,r.args=arguments,new N(r)},y=te;y.l=T,y.i=Z,y.w=function(f,s){return k(f,{locale:s.$L,utc:s.$u,x:s.$x,$offset:s.$offset})};var N=function(){function f(r){this.$L=T(r.locale,null,!0),this.parse(r),this.$x=this.$x||r.x||{},this[Q]=!0}var s=f.prototype;return s.parse=function(r){this.$d=function(c){var d=c.date,h=c.utc;if(d===null)return new Date(NaN);if(y.u(d))return new Date;if(d instanceof Date)return new Date(d);if(typeof d=="string"&&!/Z$/i.test(d)){var i=d.match(W);if(i){var o=i[2]-1||0,p=(i[7]||"0").substring(0,3);return h?new Date(Date.UTC(i[1],o,i[3]||1,i[4]||0,i[5]||0,i[6]||0,p)):new Date(i[1],o,i[3]||1,i[4]||0,i[5]||0,i[6]||0,p)}}return new Date(d)}(r),this.init()},s.init=function(){var r=this.$d;this.$y=r.getFullYear(),this.$M=r.getMonth(),this.$D=r.getDate(),this.$W=r.getDay(),this.$H=r.getHours(),this.$m=r.getMinutes(),this.$s=r.getSeconds(),this.$ms=r.getMilliseconds()},s.$utils=function(){return y},s.isValid=function(){return this.$d.toString()!==z},s.isSame=function(r,c){var d=k(r);return this.startOf(c)<=d&&d<=this.endOf(c)},s.isAfter=function(r,c){return k(r)<this.startOf(c)},s.isBefore=function(r,c){return this.endOf(c)<k(r)},s.$g=function(r,c,d){return y.u(r)?this[c]:this.set(d,r)},s.unix=function(){return Math.floor(this.valueOf()/1e3)},s.valueOf=function(){return this.$d.getTime()},s.startOf=function(r,c){var d=this,h=!!y.u(c)||c,i=y.p(r),o=function(C,L){var K=y.w(d.$u?Date.UTC(d.$y,L,C):new Date(d.$y,L,C),d);return h?K:K.endOf(x)},p=function(C,L){return y.w(d.toDate()[C].apply(d.toDate("s"),(h?[0,0,0,0]:[23,59,59,999]).slice(L)),d)},l=this.$W,w=this.$M,D=this.$D,S="set"+(this.$u?"UTC":"");switch(i){case E:return h?o(1,0):o(31,11);case b:return h?o(1,w):o(0,w+1);case v:var R=this.$locale().weekStart||0,V=(l<R?l+7:l)-R;return o(h?D-V:D+(6-V),w);case x:case I:return p(S+"Hours",0);case $:return p(S+"Minutes",1);case O:return p(S+"Seconds",2);case _:return p(S+"Milliseconds",3);default:return this.clone()}},s.endOf=function(r){return this.startOf(r,!1)},s.$set=function(r,c){var d,h=y.p(r),i="set"+(this.$u?"UTC":""),o=(d={},d[x]=i+"Date",d[I]=i+"Date",d[b]=i+"Month",d[E]=i+"FullYear",d[$]=i+"Hours",d[O]=i+"Minutes",d[_]=i+"Seconds",d[u]=i+"Milliseconds",d)[h],p=h===x?this.$D+(c-this.$W):c;if(h===b||h===E){var l=this.clone().set(I,1);l.$d[o](p),l.init(),this.$d=l.set(I,Math.min(this.$D,l.daysInMonth())).$d}else o&&this.$d[o](p);return this.init(),this},s.set=function(r,c){return this.clone().$set(r,c)},s.get=function(r){return this[y.p(r)]()},s.add=function(r,c){var d,h=this;r=Number(r);var i=y.p(c),o=function(w){var D=k(h);return y.w(D.date(D.date()+Math.round(w*r)),h)};if(i===b)return this.set(b,this.$M+r);if(i===E)return this.set(E,this.$y+r);if(i===x)return o(1);if(i===v)return o(7);var p=(d={},d[O]=a,d[$]=m,d[_]=n,d)[i]||1,l=this.$d.getTime()+r*p;return y.w(l,this)},s.subtract=function(r,c){return this.add(-1*r,c)},s.format=function(r){var c=this,d=this.$locale();if(!this.isValid())return d.invalidDate||z;var h=r||"YYYY-MM-DDTHH:mm:ssZ",i=y.z(this),o=this.$H,p=this.$m,l=this.$M,w=d.weekdays,D=d.months,S=d.meridiem,R=function(L,K,oe,ce){return L&&(L[K]||L(c,h))||oe[K].slice(0,ce)},V=function(L){return y.s(o%12||12,L,"0")},C=S||function(L,K,oe){var ce=L<12?"AM":"PM";return oe?ce.toLowerCase():ce};return h.replace(J,function(L,K){return K||function(oe){switch(oe){case"YY":return String(c.$y).slice(-2);case"YYYY":return y.s(c.$y,4,"0");case"M":return l+1;case"MM":return y.s(l+1,2,"0");case"MMM":return R(d.monthsShort,l,D,3);case"MMMM":return R(D,l);case"D":return c.$D;case"DD":return y.s(c.$D,2,"0");case"d":return String(c.$W);case"dd":return R(d.weekdaysMin,c.$W,w,2);case"ddd":return R(d.weekdaysShort,c.$W,w,3);case"dddd":return w[c.$W];case"H":return String(o);case"HH":return y.s(o,2,"0");case"h":return V(1);case"hh":return V(2);case"a":return C(o,p,!0);case"A":return C(o,p,!1);case"m":return String(p);case"mm":return y.s(p,2,"0");case"s":return String(c.$s);case"ss":return y.s(c.$s,2,"0");case"SSS":return y.s(c.$ms,3,"0");case"Z":return i}return null}(L)||i.replace(":","")})},s.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},s.diff=function(r,c,d){var h,i=this,o=y.p(c),p=k(r),l=(p.utcOffset()-this.utcOffset())*a,w=this-p,D=function(){return y.m(i,p)};switch(o){case E:h=D()/12;break;case b:h=D();break;case M:h=D()/3;break;case v:h=(w-l)/6048e5;break;case x:h=(w-l)/864e5;break;case $:h=w/m;break;case O:h=w/a;break;case _:h=w/n;break;default:h=w}return d?h:y.a(h)},s.daysInMonth=function(){return this.endOf(b).$D},s.$locale=function(){return B[this.$L]},s.locale=function(r,c){if(!r)return this.$L;var d=this.clone(),h=T(r,c,!0);return h&&(d.$L=h),d},s.clone=function(){return y.w(this.$d,this)},s.toDate=function(){return new Date(this.valueOf())},s.toJSON=function(){return this.isValid()?this.toISOString():null},s.toISOString=function(){return this.$d.toISOString()},s.toString=function(){return this.$d.toUTCString()},f}(),Y=N.prototype;return k.prototype=Y,[["$ms",u],["$s",_],["$m",O],["$H",$],["$W",x],["$M",b],["$y",E],["$D",I]].forEach(function(f){Y[f[1]]=function(s){return this.$g(s,f[0],f[1])}}),k.extend=function(f,s){return f.$i||(f(s,N,k),f.$i=!0),k},k.locale=T,k.isDayjs=Z,k.unix=function(f){return k(1e3*f)},k.en=B[A],k.Ls=B,k.p={},k})})(Re);var on=Re.exports;const rn=ct(on),an={class:"hidden flex justify-center items-center w-full h-full"},sn={class:"rounded-md flex flex-col gap-2 justify-between bg-gray-100 w-1/2 h-96"},ln=[".srcObject"],un={class:"flex justify-between items-center w-full bg-gray-50 p-2"},dn={class:"flex justify-between items-center gap-2"},cn={class:"w-full h-full relative border border-surface rounded-2xl overflow-hidden flex"},fn={class:"w-full sm:w-9/12 border-r border-surface flex flex-col relative p-4"},pn={class:"flex h-full w-full user-list flex-wrap justify-center items-center content-center"},bn={class:"box bg-gray-100 rounded-md overflow-hidden relative"},vn=[".srcObject"],hn={class:"absolute w-full bottom-0 flex gap-1 items-center justify-between py-2 px-4"},mn=["onResize",".srcObject","id"],yn={class:"absolute w-full bottom-0 flex gap-1 items-center justify-between py-2 px-4"},gn={class:"absolute top-8 left-0 right-0 flex items-center justify-center"},$n={class:"bg-gray-200 flex gap-2 rounded-md h-10 p-1 opacity-90"},kn={class:"flex flex-col gap-4 w-fit"},wn=["for"],Sn=["for"],Cn={class:"hidden sm:flex w-3/12 min-w-40 flex-col gap-6 p-4"},xn={class:"flex-1 overflow-auto"},Ln={target:"_blank",class:"block my-0 mx-2",href:""},_n=["src"],On={class:"text-sm w-fit mb-2"},En={class:"flex justify-between items-end gap-2"},Fn=ft({__name:"MeetingView",setup(t){const e=pt(),n=bt(),a=j([]),m=j([]),u=j([]),_=vt(),O=_.params.id,$=j(),x=j(!0),v=j([]),b=ht({video:!1,audio:{echoCancellation:!0}}),M=j([]),E=j(""),I=j(),z=j();let W;mt(_.params.id).catch(i=>{window.location.href="/"});const J=i=>{const o=document.getElementById(i);o&&console.log(`Remote video ${i} size changed to ${o.videoWidth}x${o.videoHeight}`)},q={offerToReceiveAudio:!0,offerToReceiveVideo:!0},G=j({video:"",audio:""}),te={iceServers:[{urls:["turn:47.109.86.108:3478"],username:"new",credential:"new"}]};yt().then(i=>{const[o,p,l]=i;m.value.push(...o),a.value.push(...p),u.value.push(...l)}),gt(()=>{$t().then(i=>{W=Mt(kt({MeetingId:O,Signature:i.signature,...i.parameters}),{autoReconnect:!0,autoClose:!0,heartbeat:{message:"ping",interval:15e3,pongTimeout:1e3},onMessage(p,l){l.data.split(`
`).forEach(async w=>{var V;if(w==="pong")return;const{cmd:D,payload:S}=JSON.parse(w);let R;switch(D){case 10001:M.value.push(S);break;case 10002:S.id===n.info.id?W(JSON.stringify({cmd:10004})):v.value.push(A(S));break;case 10003:v.value=v.value.filter(C=>{const L=C.id===S.id;return L&&(C.stream&&s(C.stream),C.pc&&C.pc.close()),!L});break;case 10005:if(R=v.value.find(C=>C.id===S.FromUser),R){const C=R.pc;switch(S.Event){case"icecandidate":await C.addIceCandidate(S.Payload);break;case"offer":await C.setRemoteDescription(new RTCSessionDescription(S.Payload)),C.createAnswer().then(L=>{C.setLocalDescription(L).then(()=>B(R.id,"answer",L))});break;case"answer":await C.setRemoteDescription(new RTCSessionDescription(S.Payload));break}}break;case 10008:if(S.id===n.info.id)(V=$.value)!=null&&V.getAudioTracks()&&(x.value=$.value.getAudioTracks()[0].enabled=S.muted),x.value=S.muted;else{let C=v.value.find(L=>L.id===S.id);C&&Object.assign(C,S)}break;case 10004:for(let C=0;C<S.length;C++){let L=S[C];if(L.id!==n.info.id)try{L=A(L);const K=await L.pc.createOffer(q);await L.pc.setLocalDescription(K),B(L.id,"offer",K),v.value.push(L)}catch(K){alert(`getUserMedia() error: ${K}`)}}break}})}}).send})});function A(i){return i.pc=new RTCPeerConnection(te),i.pc.addEventListener("icecandidate",o=>{o.candidate&&B(i.id,"icecandidate",o.candidate)}),i.pc.addEventListener("track",o=>{console.log("track",o),i.stream!==o.streams[0]&&(v.value=v.value.map(p=>(p.id===i.id&&(p.stream=o.streams[0]),p)))}),$.value&&$.value.getTracks().forEach(o=>{i.pc.addTrack(o,$.value)}),i}function B(i,o,p){W(JSON.stringify({cmd:10005,payload:{Event:o,ToUser:i,Payload:p,FromUser:n.info.id}}))}function Q(){W(JSON.stringify({cmd:10001,payload:{user:n.info,message:E.value}})),E.value=""}async function Z(i){const o=await i.pc.createOffer();await i.pc.setLocalDescription(o),B(i.id,"offer",o)}le(b,async(i,o,p)=>{try{if(!i.video&&!i.audio){s($.value),$.value=void 0;return}const l=await navigator.mediaDevices.getUserMedia({video:i.video,audio:i.audio?{echoCancellation:!0}:!1});s($.value),$.value=l;const[w]=l.getVideoTracks(),[D]=l.getAudioTracks();v.value.forEach(S=>{if(w){console.log("Got video track ",w);const R=S.pc.getSenders().find(V=>{var C;return((C=V.track)==null?void 0:C.kind)===w.kind});R?(console.log("Got video sender"),R.replaceTrack(w)):(S.pc.addTrack(w,l),Z(S))}if(D){console.log("Got audio track ",D);const R=S.pc.getSenders().find(V=>{var C;return((C=V.track)==null?void 0:C.kind)===D.kind});R&&(console.log("Got audio sender ",D),R.replaceTrack(D))}})}catch(l){e.add({severity:"error",summary:"出错了",detail:l.message,group:"br",life:3e3})}}),j({Camera:1}.Camera);const k=j(!1),y=()=>{k.value?(s($.value),$.value=void 0,k.value=!1):(console.log("share screen"),navigator.mediaDevices.getDisplayMedia({video:!0,audio:b.audio}).then(i=>{s($.value),$.value=i,i.addEventListener("inactive",o=>{s(i),$.value=void 0}),v.value.forEach(o=>{const[p]=i.getVideoTracks();if(p){const w=o.pc.getSenders().find(D=>{var S;return((S=D.track)==null?void 0:S.kind)===p.kind});w?w.replaceTrack(p):o.pc.addTrack(p,i)}const[l]=i.getAudioTracks();if(l){const w=o.pc.getSenders().find(D=>{var S;return((S=D.track)==null?void 0:S.kind)===l.kind});w&&w.replaceTrack(l)}Z(o)}),k.value=!0}))},N=j([]),Y=j(!1),f=async()=>{if(Y.value){const i=new Blob(N.value,{type:"video/webm"}),o=URL.createObjectURL(i),p=document.createElement("a");p.href=o,p.style.display="none",p.download=`屏幕录制${rn().format("YYYYMMDDHHmm")}.webm`,p.click()}else{N.value=[],z.value=await navigator.mediaDevices.getDisplayMedia({video:!0,audio:{echoCancellation:!0}});const i=new MediaRecorder(z.value,{mimeType:"video/webm"});i.ondataavailable=o=>N.value.push(o.data),i.start(100),e.add({severity:"success",summary:"录制中...",detail:"再次点击录制按钮结束录制",life:3e3})}Y.value=!Y.value},s=i=>{i&&i.getTracks().forEach(function(o){o.readyState==="live"&&o.stop()})},r=j(),c=j({video:!1,audio:!1});le(c.value,async i=>{if(!i.audio&&!i.video)s(r.value),r.value=void 0;else{const o=r.value;r.value=await navigator.mediaDevices.getUserMedia(i),s(o)}});const d=j(!1),h=()=>{$.value=r.value,r.value=void 0,d.value=!0};return(i,o)=>{const p=Me("tooltip");return H(),U(re,null,[g("div",an,[g("div",sn,[g("video",{class:"flex-1 h-80",".srcObject":r.value,playsinline:"",autoplay:""},null,40,ln),g("div",un,[g("div",dn,[o[8]||(o[8]=g("i",{class:"text-lg pi pi-video"},null,-1)),P(F(Oe),{modelValue:c.value.video,"onUpdate:modelValue":o[0]||(o[0]=l=>c.value.video=l)},null,8,["modelValue"]),o[9]||(o[9]=g("i",{class:"text-lg pi pi-microphone"},null,-1)),P(F(Oe),{modelValue:c.value.audio,"onUpdate:modelValue":o[1]||(o[1]=l=>c.value.audio=l)},null,8,["modelValue"])]),g("div",null,[P(F(X),{onClick:h,class:"px-3 py-1 text-sm",label:"进入会议"})])])])]),g("div",cn,[g("div",fn,[g("div",pn,[g("div",bn,[g("video",{".srcObject":$.value,playsinline:"",muted:"",autoplay:""},null,40,vn),g("div",hn,[P(F(xe),{image:F(n).info.avatar,shape:"circle"},null,8,["image"]),o[10]||(o[10]=g("div",{class:"flex gap-2"},[g("i",{class:"pi pi-video"}),g("i",{class:"pi pi-microphone"})],-1))])]),(H(!0),U(re,null,fe(v.value,l=>(H(),U("div",{class:"box bg-gray-100 rounded-md overflow-hidden relative",key:l.id},[(H(),U("video",{onResize:()=>J(l.id),".srcObject":l.stream,id:l.id,key:l.id,playsinline:"",autoplay:""},null,40,mn)),g("div",yn,[P(F(xe),{image:l.avatar,shape:"circle"},null,8,["image"]),o[11]||(o[11]=g("div",{class:"flex gap-2"},[g("i",{class:"pi pi-video"}),g("i",{class:"pi pi-microphone"})],-1))])]))),128))]),g("div",gn,[g("div",$n,[se(P(F(X),{onClick:f,icon:"pi pi-stop-circle",text:"",class:"py-0 hidden sm:block"},null,512),[[p,{value:"屏幕录制"},void 0,{bottom:!0}]]),se(P(F(X),{icon:"pi pi-share-alt",class:"py-0 hidden sm:block",text:"",onClick:y},null,512),[[p,{value:"分享屏幕"},void 0,{bottom:!0}]]),se(P(F(X),{class:"py-0",icon:"pi pi-video",text:"",onClick:o[2]||(o[2]=()=>b.video=!b.video)},null,512),[[p,{value:"摄像头"},void 0,{bottom:!0}]]),se(P(F(X),{icon:"pi pi-microphone",class:Le(["py-0",x.value?"":"bg-green-100"]),text:"",onClick:o[3]||(o[3]=()=>F(W)(JSON.stringify({cmd:10006,payload:{id:F(n).info.id,muted:!x.value}})))},null,8,["class"]),[[p,{value:"麦克风"},void 0,{bottom:!0}]]),P(F(X),{type:"button",icon:"pi pi-cog",text:"",onClick:o[4]||(o[4]=l=>I.value.toggle(l))}),P(F(je),{ref_key:"op",ref:I},{default:ye(()=>[g("div",kn,[g("div",null,[o[12]||(o[12]=g("span",{class:"font-medium block mb-2"},"麦克风",-1)),(H(!0),U(re,null,fe(a.value,l=>(H(),U("div",{key:l.deviceId,class:"flex items-center"},[P(F(ge),{onClick:()=>b.audio={deviceId:l.deviceId},modelValue:G.value.audio,"onUpdate:modelValue":o[5]||(o[5]=w=>G.value.audio=w),inputId:l.deviceId,name:"audio",value:l.deviceId},null,8,["onClick","modelValue","inputId","value"]),g("label",{for:l.deviceId,class:"ml-2"},pe(l.label),9,wn)]))),128)),o[13]||(o[13]=g("span",{class:"font-medium block mb-2"},"摄像头",-1)),(H(!0),U(re,null,fe(m.value,l=>(H(),U("div",{key:l.deviceId,class:"flex items-center"},[P(F(ge),{onClick:()=>b.video={deviceId:l.deviceId},modelValue:G.value.video,"onUpdate:modelValue":o[6]||(o[6]=w=>G.value.video=w),inputId:l.deviceId,name:"video",value:l.deviceId},null,8,["onClick","modelValue","inputId","value"]),g("label",{for:l.deviceId,class:"ml-2"},pe(l.label),9,Sn)]))),128))])])]),_:1},512)])])]),g("div",Cn,[g("div",xn,[(H(!0),U(re,null,fe(M.value,l=>(H(),U("div",{key:l.id,class:"flex py-1.5 px-0",style:_e({flexDirection:l.user.id===F(n).info.id?"row-reverse":"row"})},[g("a",Ln,[g("img",{src:l.user.avatar,alt:"",class:"h-10 w-10 border-2 border-white rounded-full"},null,8,_n)]),g("div",{class:Le(["flex flex-col w-fit",l.user.id===F(n).info.id?"items-end":"items-start"])},[g("div",On,pe(l.user.nickname),1),g("div",{class:"p-3 w-fit text-sm bg-gray-100",style:_e({borderRadius:l.user.id===F(n).info.id?"1.8em 0.1em 1.8em 1.8em":"0.1em 1.8em 1.8em"})},pe(l.message),5)],2)],4))),128))]),g("div",En,[P(F(St),{modelValue:E.value,"onUpdate:modelValue":o[7]||(o[7]=l=>E.value=l),autoResize:"",rows:"1",class:"w-full max-h-20 min-h-8"},null,8,["modelValue"]),P(F(X),{icon:"pi pi-face-smile",text:""}),P(F(X),{icon:"pi pi-send",text:"",class:"",onClick:Q})])])])],64)}}});export{Fn as default};
