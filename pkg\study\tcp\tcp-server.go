package tcp

import (
	"bufio"
	"fmt"
	"github.com/google/uuid"
	"hash/crc32"
	"log"
	"net"
	"os"
	"time"
)

const (
	heartbeatInterval = 5 * time.Second
	heartbeatTimeout  = 10 * time.Second
)

var nodes = make(map[string]*Node)

type Node struct {
	ID   string
	Conn net.Conn
}

type Conn struct {
	conn          net.Conn
	lastWriteTime time.Time
}

func (c *Conn) Write(p []byte) (int, error) {
	n, err := c.conn.Write(p)
	if err == nil {
		c.lastWriteTime = time.Now()
	}
	return n, err
}

func (c *Conn) HeartBeat() (int, error) {
	keepAlive := make(chan bool)
	go func() {
		ticker := time.NewTicker(heartbeatInterval)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				fmt.Println(c.lastWriteTime.String())
				if c.lastWriteTime.IsZero() || time.Since(c.lastWriteTime) >= heartbeatInterval {
					if _, err := c.Write([]byte("Heartbeat\n")); err != nil {
						fmt.Println("Error sending heartbeat:", err.Error())
						close(keepAlive)
						return
					}
				}

			case <-keepAlive:
				// Keep the connection alive
			}
		}
	}()

	return 0, nil
}
func (c *Conn) handleConnection() {
	log.Printf("remote addr %s, hash %d", c.conn.RemoteAddr().String(), crc32.ChecksumIEEE([]byte(c.conn.RemoteAddr().String())))
	node := &Node{ID: uuid.NewString(), Conn: c.conn}
	c.HeartBeat()
	nodes[node.ID] = node
	keepAlive := make(chan bool)
	defer func() {
		delete(nodes, node.ID)
		_ = c.conn.Close()
	}()
	go func() {
		ticker := time.NewTicker(heartbeatInterval)
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				_, err := c.Write([]byte("Heartbeat\n"))
				if err != nil {
					fmt.Println("Error sending heartbeat:", err.Error())
					close(keepAlive)
					return
				}
			case <-keepAlive:
				// Keep the connection alive
			}
		}
	}()
	reader := bufio.NewReader(c.conn)
	for {
		message, err := reader.ReadString('\n')
		if err != nil {
			fmt.Println("Error reading:", err.Error())
			return
		}
		fmt.Print("Received:", string(message))
		c.Write([]byte("Ack: " + message)) // 回应客户端
	}
}

func main() {
	listener, err := net.Listen("tcp", "localhost:9999")
	if err != nil {
		fmt.Println("Error listening:", err.Error())
		os.Exit(1)
	}
	defer listener.Close()
	fmt.Println("Listening on localhost:9999")
	//go func() {
	//	for {
	//		time.Sleep(time.Second)
	//		fmt.Println("nodes len" + strconv.Itoa(len(nodes)))
	//	}
	//}()
	for {
		conn, err := listener.Accept()
		if err != nil {
			fmt.Println("Error accepting: ", err.Error())
			continue
		}
		c := &Conn{conn: conn}
		c.handleConnection()
	}
}
