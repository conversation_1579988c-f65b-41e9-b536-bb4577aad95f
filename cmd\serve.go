package cmd

import (
	"context"
	"errors"
	"fmt"
	"meeting/internal/service"
	"net/http"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"

	log "github.com/sirupsen/logrus"
	"github.com/urfave/cli/v2"
)

// Serve @title Serve
// @version 0.1
// @description Restful API Document
// @termsOfService  http://swagger.io/terms/
// @host 127.0.0.1:8090
// @BasePath /
// @contact.name   API Support
// @contact.url    http://www.swagger.io/support
// @contact.email  <EMAIL>
// @license.name  Apache 2.0
// @license.url   http://www.apache.org/licenses/LICENSE-2.0.html
// @securityDefinitions.basic  BasicAuth
// @externalDocs.description  OpenAPI
// @externalDocs.url          https://swagger.io/resources/open-api/
func Serve() *cli.Command {
	return &cli.Command{
		Name:  "serve",
		Usage: "Serve",
		Flags: []cli.Flag{
			&cli.StringFlag{Name: "l", Value: "0.0.0.0", Usage: "host"},
			&cli.StringFlag{Name: "p", Value: "8989", Usage: "port"},
			&cli.StringFlag{Name: "c", Value: "./config.toml", Usage: "config file"},
		},
		Action: func(ctx *cli.Context) error {
			runtime.SetMutexProfileFraction(1) // (非必需)开启对锁调用的跟踪
			runtime.SetBlockProfileRate(1)     // (非必需)开启对阻塞操作的跟踪

			// 使用 Wire 初始化应用程序
			app, err := InitializeApplication(ctx.String("c"))
			if err != nil {
				return fmt.Errorf("failed to initialize application: %w", err)
			}

			// Start WebRTC service
			if app.WebRTC != nil {
				app.WebRTC.Start()
				defer app.WebRTC.Stop()
			}

			go service.RunGrpcServer()

			go func() {
				if err := app.Server.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
					log.Fatalf("listen: %s\n", err)
				}
			}()

			quit := make(chan os.Signal, 1)
			signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
			<-quit
			log.Println("Shutdown Server ...")

			//创建超时上下文，Shutdown可以让未处理的连接在这个时间内关闭
			ch, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			return app.Server.Shutdown(ch)
		},
	}
}
