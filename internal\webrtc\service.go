package webrtc

import (
	"context"
	"log"
	"sync"
)

// Service manages the WebRTC signaling service
type Service struct {
	hub    *Hub
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewService creates a new WebRTC service
func NewService(hub *Hub) *Service {
	ctx, cancel := context.WithCancel(context.Background())

	return &Service{
		hub:    hub,
		ctx:    ctx,
		cancel: cancel,
	}
}

// Start starts the WebRTC service
func (s *Service) Start() {
	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		log.Println("Starting WebRTC hub...")
		s.hub.Run()
	}()

	log.Println("WebRTC service started")
}

// Stop stops the WebRTC service
func (s *Service) Stop() {
	log.Println("Stopping WebRTC service...")
	//s.cancel() // todo 为啥注释
	s.hub.Stop() // Stop the hub as well
	//s.wg.Wait()  // todo 为啥注释
	log.Println("WebRTC service stopped")
}

// GetHub returns the hub instance
func (s *Service) GetHub() *Hub {
	return s.hub
}
