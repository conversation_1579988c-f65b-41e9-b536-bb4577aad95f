package main

import (
	"context"
	"fmt"
	"github.com/smallnest/rpcx/server"
	"github.com/smallnest/rpcx/share"
	"log"
)

type Args struct {
	A int
	B int
}
type Reply struct {
	C int
}

type Arith int

func (*Arith) Mul(ctx context.Context, args *Args, reply *Reply) error {
	reply.C = args.A + args.B
	fmt.Printf("%#v", ctx.Value(share.ReqMetaDataKey))
	return nil
}

func main() {
	s := server.NewServer()
	s.Register(new(Arith), "state=inactive")
	log.Fatalln(s.<PERSON>("tcp", ":8972"))
}
