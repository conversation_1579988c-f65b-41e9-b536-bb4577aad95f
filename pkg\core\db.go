package core

import (
	"context"
	"fmt"
	"log"
	"meeting/pkg/config"
	"os"
	"sync"
	"time"

	"github.com/google/wire"
	"github.com/opentracing/opentracing-go"
	tracerLog "github.com/opentracing/opentracing-go/log"
	"github.com/redis/go-redis/v9"
	clientv3 "go.etcd.io/etcd/client/v3"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// NewDB 创建数据库连接
func NewDB(cfg *config.TomlConfig) (*gorm.DB, error) {
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  false,
		},
	)

	db, err := gorm.Open(mysql.Open(cfg.Mysql.DSN), &gorm.Config{
		Logger: newLogger,
	})
	if err != nil {
		return nil, err
	}

	// 注册插件
	if err := db.Use(&OpentracingPlugin{}); err != nil {
		return nil, err
	}

	return db, nil
}

// NewRedis 创建Redis连接
func NewRedis() *redis.Client {
	return redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   0,
	})
}

// ProviderSet Wire 提供者集合
var ProviderSet = wire.NewSet(NewDB, NewRedis)

// 保持向后兼容的全局变量和函数
var globalDB *gorm.DB
var Redis *redis.Client
var once sync.Once

func InitializeDB() {
	once.Do(initMysql)
}

const gormSpanKey = "__gorm_span"

func DB(ctx context.Context) *gorm.DB {
	span, _ := opentracing.StartSpanFromContext(ctx, "gorm")
	db := globalDB.WithContext(ctx)
	db.InstanceSet(gormSpanKey, span)

	return db
}

func Subscribe(channel ...string) {
	ctx := context.TODO()
	subscriber := Redis.Subscribe(ctx, channel...)
	defer subscriber.Close()
	c := subscriber.Channel()
	for msg := range c {
		fmt.Println(msg.Channel, msg.Payload)
	}
}

func Publish(channel string, message any) {
	Redis.Publish(context.TODO(), channel, message)
}

const (
	callBackBeforeName = "opentracing:before"
	callBackAfterName  = "opentracing:after"
)

type OpentracingPlugin struct{}

func (op *OpentracingPlugin) Name() string {
	return "opentracingPlugin"
}

func before(db *gorm.DB) {
	// 先从父级spans生成子span ---> 这里命名为gorm，但实际上可以自定义
	// 自己喜欢的operationName
	span, _ := opentracing.StartSpanFromContext(db.Statement.Context, "gorm")

	// 利用db实例去传递span
	db.InstanceSet(gormSpanKey, span)

	return
}

func after(db *gorm.DB) {
	// 从GORM的DB实例中取出span
	_span, isExist := db.InstanceGet(gormSpanKey)
	if !isExist {
		// 不存在就直接抛弃掉
		return
	}

	// 断言进行类型转换
	span, ok := _span.(opentracing.Span)
	if !ok {
		return
	}
	// <---- 一定一定一定要Finsih掉！！！
	defer span.Finish()

	// Error
	if db.Error != nil {
		span.LogFields(tracerLog.Error(db.Error))
	}

	// sql --> 写法来源GORM V2的日志
	span.LogFields(tracerLog.String("sql", db.Dialector.Explain(db.Statement.SQL.String(), db.Statement.Vars...)))
	return
}

func (op *OpentracingPlugin) Initialize(db *gorm.DB) (err error) {
	// 开始前 - 并不是都用相同的方法，可以自己自定义
	db.Callback().Create().Before("gorm:before_create").Register(callBackBeforeName, before)
	db.Callback().Query().Before("gorm:query").Register(callBackBeforeName, before)
	db.Callback().Delete().Before("gorm:before_delete").Register(callBackBeforeName, before)
	db.Callback().Update().Before("gorm:setup_reflect_value").Register(callBackBeforeName, before)
	db.Callback().Row().Before("gorm:row").Register(callBackBeforeName, before)
	db.Callback().Raw().Before("gorm:raw").Register(callBackBeforeName, before)

	// 结束后 - 并不是都用相同的方法，可以自己自定义
	db.Callback().Create().After("gorm:after_create").Register(callBackAfterName, after)
	db.Callback().Query().After("gorm:after_query").Register(callBackAfterName, after)
	db.Callback().Delete().After("gorm:after_delete").Register(callBackAfterName, after)
	db.Callback().Update().After("gorm:after_update").Register(callBackAfterName, after)
	db.Callback().Row().After("gorm:row").Register(callBackAfterName, after)
	db.Callback().Raw().After("gorm:raw").Register(callBackAfterName, after)
	return
}

// Plugin GORM plugin interface
type Plugin interface {
	Name() string
	Initialize(*gorm.DB) error
}

// 告诉编译器这个结构体实现了gorm.Plugin接口
var _ gorm.Plugin = &OpentracingPlugin{}

func initMysql() {
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second, // 慢 SQL 阈值
			LogLevel:                  logger.Info, // 日志级别
			IgnoreRecordNotFoundError: true,        // 忽略ErrRecordNotFound（记录未找到）错误
			Colorful:                  false,       // 禁用彩色打印
		},
	)
	var err error
	if globalDB, err = gorm.Open(mysql.Open(config.GetConfig().Mysql.DSN), &gorm.Config{
		Logger: newLogger,
	}); err != nil {
		panic(err)
	}
	//globalDB.Initialize(globalDB)
}

func initRedis() {
	Redis = redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   0,
	})
}

func initEtcd() {
	config := clientv3.Config{
		Endpoints:   []string{"127.0.0.1:2379"},
		DialTimeout: 5 * time.Second,
	}
	client, err := clientv3.New(config)
	if err != nil {
		panic(err)
	}

	put, err := client.Put(context.Background(), "User", "value", clientv3.WithPrefix())
	if err != nil {
		panic(err)
	}
	fmt.Println(put)
}
