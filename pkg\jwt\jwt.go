package jwt

import (
	"context"
	"errors"
	"meeting/internal/entity"
	"meeting/pkg/config"
	"meeting/pkg/core"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

var ttl = time.Hour * 24 * 7

// getJWTSecret 获取JWT密钥
func getJWTSecret() []byte {
	cfg := config.GetConfig()
	secret := cfg.App.JWTSecret
	if secret == "" {
		secret = "meeting-jwt-secret-key-change-in-production"
		core.Logger().Warn("使用默认JWT密钥，生产环境请配置App.JWTSecret")
	}
	return []byte(secret)
}

func FromUser(user *entity.User) (token string, expires int64, err error) {
	expiresAt := time.Now().Add(ttl)
	expires = expiresAt.Unix()
	j := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.RegisteredClaims{
		Subject:   strconv.Itoa(int(user.ID)),
		ExpiresAt: jwt.NewNumericDate(expiresAt),
		IssuedAt:  jwt.NewNumericDate(time.Now()),
		Issuer:    "meeting-app",
	})
	token, err = j.SignedString(getJWTSecret())

	return
}

func ParseToken(ctx *gin.Context) string {
	var tokenString string
	if tokenString = ctx.Query("token"); tokenString == "" {
		if as := ctx.GetHeader("Authorization"); as != "" {
			ass := strings.SplitN(as, " ", 2)
			if len(ass) == 2 {
				tokenString = ass[1]
			}
		}
	}

	return tokenString
}

func Authenticate(token string) (*entity.User, error) {
	if token == "" {
		return nil, errors.New("token不正确")
	}

	parsedToken, err := jwt.ParseWithClaims(token, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名方法")
		}
		return getJWTSecret(), nil
	})

	if err != nil {
		return nil, err
	}

	if !parsedToken.Valid {
		return nil, errors.New("token无效")
	}

	claims, ok := parsedToken.Claims.(*jwt.RegisteredClaims)
	if !ok {
		return nil, errors.New("无法解析token claims")
	}

	var user entity.User
	err = core.DB(context.Background()).Where("id = ?", claims.Subject).First(&user).Error
	if err != nil {
		return nil, errors.New("用户不存在")
	}

	return &user, nil
}

func ParseClaims(tokenString string) *jwt.RegisteredClaims {
	parsedToken, err := jwt.ParseWithClaims(tokenString, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名方法")
		}
		return getJWTSecret(), nil
	})
	if err != nil {
		return nil
	}
	claims, _ := parsedToken.Claims.(*jwt.RegisteredClaims)
	return claims
}
