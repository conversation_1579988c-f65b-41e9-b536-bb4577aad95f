package middleware

import (
	"crypto/sha256"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"golang.org/x/net/context"
	"meeting/pkg/api"
	"meeting/pkg/config"
	"meeting/pkg/core"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
)

// EnhancedAPIProtection 增强API保护中间件，实现类似PHP版本的功能
func EnhancedAPIProtection() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 本地环境跳过检查
		if config.GetConfig().App.Host == "localhost" || config.GetConfig().App.Host == "127.0.0.1" {
			ctx.Next()
			return
		}

		// 检查app_id参数和小程序是否存在
		appID := ctx.Query("app_id")
		if appID == "" {
			ctx.JSON(http.StatusForbidden, api.Fail(api.WithMessage("app不存在")))
			ctx.Abort()
			return
		}

		// TODO: 验证WechatApp是否存在
		// 这里需要根据实际数据库结构和查询方式来实现
		// if !WechatApp.MiniProgram().Where("app_id", appID).Exists() {
		//     ctx.JSON(http.StatusForbidden, api.Fail(api.WithMessage("app不存在")))
		//     ctx.Abort()
		//     return
		// }

		// 1. UA验证 - 仅允许微信小程序访问
		if !validateUserAgent(ctx) {
			ctx.JSON(http.StatusForbidden, api.Fail(api.WithMessage("非法客户端")))
			ctx.Abort()
			return
		}

		// 2. 频率限制 - 每分钟最多60次请求
		if tooManyRequests(ctx) {
			ctx.JSON(http.StatusTooManyRequests, api.Fail(api.WithMessage("请求频率过高")))
			ctx.Abort()
			return
		}

		// 3. 签名验证 - 确保请求来自可信客户端
		if !validateSignature(ctx) {
			ctx.JSON(http.StatusForbidden, api.Fail(api.WithMessage("无效签名")))
			ctx.Abort()
			return
		}

		// 所有检查通过，继续处理请求
		ctx.Next()
	}
}

// validateUserAgent 验证User-Agent
func validateUserAgent(ctx *gin.Context) bool {
	userAgent := ctx.GetHeader("User-Agent")
	return userAgent != "" && (strings.Contains(userAgent, "miniProgram") || strings.Contains(userAgent, "MicroMessenger"))
}

// tooManyRequests 频率限制检查
func tooManyRequests(ctx *gin.Context) bool {
	clientIP := ctx.ClientIP()
	key := fmt.Sprintf("api-throttle:%s", clientIP)

	// 使用Redis进行频率限制
	rdb := core.Redis
	// 每分钟最多60次请求
	limiter := NewRateLimiter(rdb, key, 60, time.Minute)
	return !limiter.Allow()
}

// validateSignature 签名验证
func validateSignature(ctx *gin.Context) bool {
	// 检查请求是否包含签名所需参数
	timestampStr := ctx.Query("timestamp")
	signature := ctx.Query("signature")

	if timestampStr == "" || signature == "" {
		return false
	}

	// 检查时间戳是否过期 (5分钟内有效)
	timestamp, err := strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return false
	}

	if time.Now().UnixMilli()-timestamp > 5*60*1000 {
		return false
	}

	// 检查签名是否已使用
	cacheKey := fmt.Sprintf("api-signature:%s", signature)
	rdb := core.Redis
	exists := rdb.Exists(context.Background(), cacheKey)
	if exists.Val() > 0 {
		return false
	}

	// 生成签名并验证
	params := make(url.Values)
	queryParams := ctx.Request.URL.Query()
	for k, v := range queryParams {
		if k != "signature" {
			params[k] = v
		}
	}

	secret := config.GetConfig().App.JWTSecret // 使用JWTSecret作为API密钥
	expectedSignature := generateSignature(params, secret)

	if signature != expectedSignature {
		return false
	}

	// 缓存签名，防止重放攻击
	ttl := 5 * time.Minute
	err = rdb.Set(context.Background(), cacheKey, "1", ttl).Err()
	if err != nil {
		// 日志记录错误，但不中断验证流程
		fmt.Printf("Failed to cache signature: %v\n", err)
	}

	return true
}

// generateSignature 生成签名
func generateSignature(params url.Values, secret string) string {
	// 参数排序
	var keys []string
	data := make(url.Values)
	for k, v := range params {
		keys = append(keys, k)
		data[k] = v
	}
	sort.Strings(keys)

	// 构造待签名字符串
	var strParts []string
	for _, k := range keys {
		strParts = append(strParts, fmt.Sprintf("%s=%s", k, strings.Join(data[k], ",")))
	}
	str := strings.Join(strParts, "&") + secret

	// SHA256哈希
	hash := sha256.Sum256([]byte(str))
	return fmt.Sprintf("%x", hash)
}

// RateLimiter 限流器
type RateLimiter struct {
	rdb   *redis.Client
	key   string
	limit int64
	ttl   time.Duration
}

// NewRateLimiter 创建新的限流器
func NewRateLimiter(rdb *redis.Client, key string, limit int64, ttl time.Duration) *RateLimiter {
	return &RateLimiter{
		rdb:   rdb,
		key:   key,
		limit: limit,
		ttl:   ttl,
	}
}

// Allow 检查是否允许通过
func (rl *RateLimiter) Allow() bool {
	ctx := context.Background()
	
	// 使用Redis的INCR命令实现计数
	count, err := rl.rdb.Incr(ctx, rl.key).Result()
	if err != nil {
		return true // 如果Redis出错，允许通过
	}

	// 如果是第一次设置key，设置过期时间
	if count == 1 {
		rl.rdb.Expire(ctx, rl.key, rl.ttl)
	}

	// 检查是否超过限制
	return count <= rl.limit
}