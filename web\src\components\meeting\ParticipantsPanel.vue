<template>
  <div class="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col">
    <!-- Participants Header -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
      <h3 class="font-semibold text-gray-900 dark:text-white">
        {{ $t('tools.webRtcMeeting.participants.title') }} ({{ participants.length }})
      </h3>
      <button
        @click="$emit('close')"
        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
      >
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path
            fill-rule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
    </div>

    <!-- Host Controls (if user is host) -->
    <div v-if="isHost" class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="space-y-2">
        <button
          @click="$emit('muteAll')"
          class="w-full px-3 py-2 text-sm bg-red-100 hover:bg-red-200 text-red-700 rounded-lg flex items-center justify-center space-x-2"
        >
          <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path
              d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793z"
            />
            <path
              d="M15.293 7.293a1 1 0 011.414 0L18 8.586l1.293-1.293a1 1 0 111.414 1.414L19.414 10l1.293 1.293a1 1 0 01-1.414 1.414L18 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L16.586 10l-1.293-1.293a1 1 0 010-1.414z"
            />
          </svg>
          <span>{{ $t('tools.webRtcMeeting.controls.muteAll') }}</span>
        </button>
      </div>
    </div>

    <!-- Participants List -->
    <div class="flex-1 p-4 overflow-y-auto">
      <div class="space-y-2">
        <div
          v-for="participant in participants"
          :key="participant.id"
          class="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <div class="flex items-center space-x-3">
            <!-- Avatar -->
            <div class="relative">
              <div
                class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium"
              >
                {{ getInitials(participant.name || participant.id) }}
              </div>
              <!-- Connection Status Indicator -->
              <div
                :class="[
                  'absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white',
                  getConnectionStatusColor(participant.connectionState),
                ]"
                :title="getConnectionStatusText(participant.connectionState)"
              ></div>
            </div>

            <!-- Participant Info -->
            <div class="flex-1 min-w-0">
              <div class="font-medium text-gray-900 dark:text-white truncate">
                {{ participant.name || participant.id }}
                <span
                  v-if="participant.id === localUserId"
                  class="text-xs text-gray-500 ml-1"
                >
                  ({{ $t('tools.webRtcMeeting.you') }})
                </span>
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400 flex items-center space-x-2">
                <!-- Role -->
                <span v-if="participant.role === 'host'" class="text-yellow-500 flex items-center">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                    />
                  </svg>
                  {{ $t('tools.webRtcMeeting.participants.host') }}
                </span>
                <span v-else>{{ $t('tools.webRtcMeeting.participants.member') }}</span>

                <!-- Media Status -->
                <div class="flex items-center space-x-1">
                  <!-- Microphone Status -->
                  <div
                    v-if="!participant.mediaState.microphone"
                    class="w-3 h-3 bg-red-500 rounded-full flex items-center justify-center"
                    :title="$t('tools.webRtcMeeting.controls.muted')"
                  >
                    <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        d="M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06L3.28 2.22z"
                      />
                    </svg>
                  </div>

                  <!-- Camera Status -->
                  <div
                    v-if="!participant.mediaState.camera"
                    class="w-3 h-3 bg-red-500 rounded-full flex items-center justify-center"
                    :title="$t('tools.webRtcMeeting.controls.cameraOff')"
                  >
                    <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        d="M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06L3.28 2.22z"
                      />
                    </svg>
                  </div>

                  <!-- Screen Sharing Status -->
                  <div
                    v-if="participant.mediaState.screenSharing"
                    class="w-3 h-3 bg-purple-500 rounded-full flex items-center justify-center"
                    :title="$t('tools.webRtcMeeting.video.sharingScreen')"
                  >
                    <svg class="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        d="M2 3a1 1 0 011-1h14a1 1 0 011 1v11a1 1 0 01-1 1H3a1 1 0 01-1-1V3zm2 2v7h12V5H4z"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Participant Controls (only for host and not self) -->
          <div v-if="isHost && participant.id !== localUserId" class="flex space-x-1">
            <!-- Mute Button -->
            <button
              @click="$emit('muteParticipant', participant.id)"
              class="p-2 text-gray-400 hover:text-red-500 rounded transition-colors"
              :title="$t('tools.webRtcMeeting.controls.mute')"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793z"
                />
                <path
                  d="M15.293 7.293a1 1 0 011.414 0L18 8.586l1.293-1.293a1 1 0 111.414 1.414L19.414 10l1.293 1.293a1 1 0 01-1.414 1.414L18 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L16.586 10l-1.293-1.293a1 1 0 010-1.414z"
                />
              </svg>
            </button>

            <!-- Remove Button -->
            <button
              @click="$emit('kickParticipant', participant.id)"
              class="p-2 text-gray-400 hover:text-red-500 rounded transition-colors"
              :title="$t('tools.webRtcMeeting.controls.remove')"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3z"
                  clip-rule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Invite Section -->
    <div class="p-4 border-t border-gray-200 dark:border-gray-700">
      <button
        @click="$emit('copyInviteLink')"
        class="w-full px-3 py-2 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg flex items-center justify-center space-x-2"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
          <path
            d="M3 5a2 2 0 012-2 3 3 0 003 3h6a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L12.586 15H17v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"
          />
        </svg>
        <span>{{ $t('tools.webRtcMeeting.meeting.copyLink') }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import type { Participant } from '@/types/meeting'

interface Props {
  participants: Participant[]
  localUserId: string
  isHost: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'muteParticipant', participantId: string): void
  (e: 'kickParticipant', participantId: string): void
  (e: 'muteAll'): void
  (e: 'copyInviteLink'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

// Methods
const getInitials = (name: string): string => {
  return name.charAt(0).toUpperCase()
}

const getConnectionStatusColor = (status: string): string => {
  switch (status) {
    case 'connected':
      return 'bg-green-500'
    case 'connecting':
      return 'bg-yellow-500'
    case 'disconnected':
    default:
      return 'bg-red-500'
  }
}

const getConnectionStatusText = (status: string): string => {
  switch (status) {
    case 'connected':
      return t('tools.webRtcMeeting.status.connected')
    case 'connecting':
      return t('tools.webRtcMeeting.status.connecting')
    case 'disconnected':
    default:
      return t('tools.webRtcMeeting.status.disconnected')
  }
}
</script>
