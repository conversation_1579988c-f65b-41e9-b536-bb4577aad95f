<template>
  <div class="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col animate-slide-in-right">
    <!-- Panel Header -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
      <h3 class="font-semibold text-gray-900 dark:text-white">
        {{ $t('tools.webRtcMeeting.participants.title') }} ({{ participants.length }})
      </h3>
      <Button
        icon="pi pi-times"
        severity="secondary"
        text
        rounded
        @click="$emit('close')"
        class="!p-2"
      />
    </div>

    <!-- Host Controls (if user is host) -->
    <div v-if="isHost" class="p-4 border-b border-gray-200 dark:border-gray-700">
      <Button
        @click="$emit('muteAll')"
        icon="pi pi-volume-off"
        :label="$t('tools.webRtcMeeting.controls.muteAll')"
        severity="danger"
        outlined
        size="small"
        class="w-full animate-fade-in"
      />
    </div>

    <!-- Participants List -->
    <div class="flex-1 p-4 overflow-y-auto">
      <div class="space-y-2">
        <div
          v-for="(participant, index) in participants"
          :key="participant.id"
          class="flex items-center justify-between p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 animate-fade-in"
          :style="{ animationDelay: `${index * 100}ms` }"
        >
          <div class="flex items-center space-x-2">
            <!-- Connection Status Indicator -->
            <div
              :class="[
                'w-2 h-2 rounded-full transition-colors duration-300',
                participant.id === localUserId ? 'bg-blue-500' : getConnectionStatusColor(participant.connectionState),
              ]"
            ></div>

            <!-- Participant Name -->
            <span class="text-sm font-medium text-gray-900 dark:text-white">
              {{ participant.name || participant.id }}
              <span v-if="participant.id === localUserId" class="text-xs text-gray-500">
                ({{ $t('tools.webRtcMeeting.you') }})
              </span>
              <span v-if="participant.role === 'host'" class="text-xs text-yellow-500 ml-1">
                👑
              </span>
            </span>

            <!-- Media Status Icons -->
            <div class="flex items-center space-x-1">
              <i
                v-if="!participant.mediaState.microphone"
                class="pi pi-microphone-slash text-red-500 text-xs"
                :title="$t('tools.webRtcMeeting.controls.muted')"
              ></i>
              <i
                v-if="!participant.mediaState.camera"
                class="pi pi-video-slash text-red-500 text-xs"
                :title="$t('tools.webRtcMeeting.controls.cameraOff')"
              ></i>
              <i
                v-if="participant.mediaState.screenSharing"
                class="pi pi-desktop text-purple-500 text-xs"
                :title="$t('tools.webRtcMeeting.video.sharingScreen')"
              ></i>
            </div>
          </div>

          <!-- Participant Controls (only for host and not self) -->
          <div v-if="isHost && participant.id !== localUserId" class="flex space-x-1">
            <Button
              icon="pi pi-volume-off"
              severity="danger"
              text
              rounded
              size="small"
              @click="$emit('muteParticipant', participant.id)"
              :title="$t('tools.webRtcMeeting.controls.mute')"
              class="!p-1 hover:scale-110 transition-transform duration-200"
            />
            <Button
              icon="pi pi-user-minus"
              severity="danger"
              text
              rounded
              size="small"
              @click="$emit('kickParticipant', participant.id)"
              :title="$t('tools.webRtcMeeting.controls.remove')"
              class="!p-1 hover:scale-110 transition-transform duration-200"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Invite Section -->
    <div class="p-4 border-t border-gray-200 dark:border-gray-700">
      <button
        @click="$emit('copyInviteLink')"
        class="w-full px-3 py-2 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-lg flex items-center justify-center space-x-2"
      >
        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
          <path
            d="M3 5a2 2 0 012-2 3 3 0 003 3h6a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L12.586 15H17v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"
          />
        </svg>
        <span>{{ $t('tools.webRtcMeeting.meeting.copyLink') }}</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import type { Participant } from '@/types/meeting'

interface Props {
  participants: Participant[]
  localUserId: string
  isHost: boolean
}

interface Emits {
  (e: 'close'): void
  (e: 'muteParticipant', participantId: string): void
  (e: 'kickParticipant', participantId: string): void
  (e: 'muteAll'): void
  (e: 'copyInviteLink'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

// Methods
const getInitials = (name: string): string => {
  return name.charAt(0).toUpperCase()
}

const getConnectionStatusColor = (status: string): string => {
  switch (status) {
    case 'connected':
      return 'bg-green-500'
    case 'connecting':
      return 'bg-yellow-500'
    case 'disconnected':
    default:
      return 'bg-red-500'
  }
}

const getConnectionStatusText = (status: string): string => {
  switch (status) {
    case 'connected':
      return t('tools.webRtcMeeting.status.connected')
    case 'connecting':
      return t('tools.webRtcMeeting.status.connecting')
    case 'disconnected':
    default:
      return t('tools.webRtcMeeting.status.disconnected')
  }
}
</script>
