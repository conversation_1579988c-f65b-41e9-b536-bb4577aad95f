package entity

import (
	"gorm.io/gorm"
	"time"
)

// Favorite 收藏实体
type Favorite struct {
	ID        uint            `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID    uint            `gorm:"type:bigint(20) unsigned;not null" json:"user_id"`
	ModelType string          `gorm:"type:varchar(255);not null" json:"model_type"`
	ModelID   uint            `gorm:"type:bigint(20) unsigned;not null" json:"model_id"`
	CreatedAt *time.Time      `gorm:"type:timestamp" json:"created_at,omitempty"`
	UpdatedAt *time.Time      `gorm:"type:timestamp" json:"updated_at,omitempty"`
	DeletedAt *gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at,omitempty"`

	// 关联用户
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}