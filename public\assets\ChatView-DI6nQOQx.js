import{s as M,c as L,b as F,a as R}from"./index-D9uK78IG.js";import{B as y,l as z,L as f,F as g,M as v,I as u,N as S,R as B,k as I,A as q,J as E,P as e,a7 as N,K as _,a9 as P,O as $,ae as w,z as H,a5 as Z,a6 as U,D as W,af as J,G as O,U as G,H as r,u as p,ag as Y}from"./index-uycSxWK8.js";import{s as b}from"./index-DONUS5C5.js";var Q={root:"p-inputicon"},X=y.extend({name:"inputicon",classes:Q}),tt={name:"BaseInputIcon",extends:z,style:X,props:{class:null},provide:function(){return{$pcInputIcon:this,$parentInstance:this}}},T={name:"InputIcon",extends:tt,inheritAttrs:!1,computed:{containerClass:function(){return[this.cx("root"),this.class]}}};function et(t,a,n,i,s,o){return g(),f("span",u({class:o.containerClass},t.ptmi("root")),[v(t.$slots,"default")],16)}T.render=et;var nt=({dt:t})=>`
.p-iconfield {
    position: relative;
}

.p-inputicon {
    position: absolute;
    top: 50%;
    margin-top: calc(-1 * (${t("icon.size")} / 2));
    color: ${t("iconfield.icon.color")};
    line-height: 1;
    z-index: 1;
}

.p-iconfield .p-inputicon:first-child {
    inset-inline-start: ${t("form.field.padding.x")};
}

.p-iconfield .p-inputicon:last-child {
    inset-inline-end: ${t("form.field.padding.x")};
}

.p-iconfield .p-inputtext:not(:first-child),
.p-iconfield .p-inputwrapper:not(:first-child) .p-inputtext {
    padding-inline-start: calc((${t("form.field.padding.x")} * 2) + ${t("icon.size")});
}

.p-iconfield .p-inputtext:not(:last-child) {
    padding-inline-end: calc((${t("form.field.padding.x")} * 2) + ${t("icon.size")});
}

.p-iconfield:has(.p-inputfield-sm) .p-inputicon {
    font-size: ${t("form.field.sm.font.size")};
    width: ${t("form.field.sm.font.size")};
    height: ${t("form.field.sm.font.size")};
    margin-top: calc(-1 * (${t("form.field.sm.font.size")} / 2));
}

.p-iconfield:has(.p-inputfield-lg) .p-inputicon {
    font-size: ${t("form.field.lg.font.size")};
    width: ${t("form.field.lg.font.size")};
    height: ${t("form.field.lg.font.size")};
    margin-top: calc(-1 * (${t("form.field.lg.font.size")} / 2));
}
`,at={root:"p-iconfield"},ot=y.extend({name:"iconfield",style:nt,classes:at}),it={name:"BaseIconField",extends:z,style:ot,provide:function(){return{$pcIconField:this,$parentInstance:this}}},C={name:"IconField",extends:it,inheritAttrs:!1};function lt(t,a,n,i,s,o){return g(),f("div",u({class:t.cx("root")},t.ptmi("root")),[v(t.$slots,"default")],16)}C.render=lt;var st=({dt:t})=>`
.p-inputtext {
    font-family: inherit;
    font-feature-settings: inherit;
    font-size: 1rem;
    color: ${t("inputtext.color")};
    background: ${t("inputtext.background")};
    padding-block: ${t("inputtext.padding.y")};
    padding-inline: ${t("inputtext.padding.x")};
    border: 1px solid ${t("inputtext.border.color")};
    transition: background ${t("inputtext.transition.duration")}, color ${t("inputtext.transition.duration")}, border-color ${t("inputtext.transition.duration")}, outline-color ${t("inputtext.transition.duration")}, box-shadow ${t("inputtext.transition.duration")};
    appearance: none;
    border-radius: ${t("inputtext.border.radius")};
    outline-color: transparent;
    box-shadow: ${t("inputtext.shadow")};
}

.p-inputtext:enabled:hover {
    border-color: ${t("inputtext.hover.border.color")};
}

.p-inputtext:enabled:focus {
    border-color: ${t("inputtext.focus.border.color")};
    box-shadow: ${t("inputtext.focus.ring.shadow")};
    outline: ${t("inputtext.focus.ring.width")} ${t("inputtext.focus.ring.style")} ${t("inputtext.focus.ring.color")};
    outline-offset: ${t("inputtext.focus.ring.offset")};
}

.p-inputtext.p-invalid {
    border-color: ${t("inputtext.invalid.border.color")};
}

.p-inputtext.p-variant-filled {
    background: ${t("inputtext.filled.background")};
}

.p-inputtext.p-variant-filled:enabled:hover {
    background: ${t("inputtext.filled.hover.background")};
}

.p-inputtext.p-variant-filled:enabled:focus {
    background: ${t("inputtext.filled.focus.background")};
}

.p-inputtext:disabled {
    opacity: 1;
    background: ${t("inputtext.disabled.background")};
    color: ${t("inputtext.disabled.color")};
}

.p-inputtext::placeholder {
    color: ${t("inputtext.placeholder.color")};
}

.p-inputtext.p-invalid::placeholder {
    color: ${t("inputtext.invalid.placeholder.color")};
}

.p-inputtext-sm {
    font-size: ${t("inputtext.sm.font.size")};
    padding-block: ${t("inputtext.sm.padding.y")};
    padding-inline: ${t("inputtext.sm.padding.x")};
}

.p-inputtext-lg {
    font-size: ${t("inputtext.lg.font.size")};
    padding-block: ${t("inputtext.lg.padding.y")};
    padding-inline: ${t("inputtext.lg.padding.x")};
}

.p-inputtext-fluid {
    width: 100%;
}
`,rt={root:function(a){var n=a.instance,i=a.props;return["p-inputtext p-component",{"p-filled":n.$filled,"p-inputtext-sm p-inputfield-sm":i.size==="small","p-inputtext-lg p-inputfield-lg":i.size==="large","p-invalid":n.$invalid,"p-variant-filled":n.$variant==="filled","p-inputtext-fluid":n.$fluid}]}},pt=y.extend({name:"inputtext",style:st,classes:rt}),ct={name:"BaseInputText",extends:M,style:pt,provide:function(){return{$pcInputText:this,$parentInstance:this}}};function h(t){"@babel/helpers - typeof";return h=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},h(t)}function dt(t,a,n){return(a=ut(a))in t?Object.defineProperty(t,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[a]=n,t}function ut(t){var a=gt(t,"string");return h(a)=="symbol"?a:a+""}function gt(t,a){if(h(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var i=n.call(t,a);if(h(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(a==="string"?String:Number)(t)}var j={name:"InputText",extends:ct,inheritAttrs:!1,methods:{onInput:function(a){this.writeValue(a.target.value,a)}},computed:{attrs:function(){return u(this.ptmi("root",{context:{filled:this.$filled,disabled:this.disabled}}),this.formField)},dataP:function(){return S(dt({invalid:this.$invalid,fluid:this.$fluid,filled:this.$variant==="filled"},this.size,this.size))}}},bt=["value","name","disabled","aria-invalid","data-p"];function ft(t,a,n,i,s,o){return g(),f("input",u({type:"text",class:t.cx("root"),value:t.d_value,name:t.name,disabled:t.disabled,"aria-invalid":t.$invalid||void 0,"data-p":o.dataP,onInput:a[0]||(a[0]=function(){return o.onInput&&o.onInput.apply(o,arguments)})},o.attrs),null,16,bt)}j.render=ft;var mt=({dt:t})=>`
.p-togglebutton {
    display: inline-flex;
    cursor: pointer;
    user-select: none;
    overflow: hidden;
    position: relative;
    color: ${t("togglebutton.color")};
    background: ${t("togglebutton.background")};
    border: 1px solid ${t("togglebutton.border.color")};
    padding: ${t("togglebutton.padding")};
    font-size: 1rem;
    font-family: inherit;
    font-feature-settings: inherit;
    transition: background ${t("togglebutton.transition.duration")}, color ${t("togglebutton.transition.duration")}, border-color ${t("togglebutton.transition.duration")},
        outline-color ${t("togglebutton.transition.duration")}, box-shadow ${t("togglebutton.transition.duration")};
    border-radius: ${t("togglebutton.border.radius")};
    outline-color: transparent;
    font-weight: ${t("togglebutton.font.weight")};
}

.p-togglebutton-content {
    display: inline-flex;
    flex: 1 1 auto;
    align-items: center;
    justify-content: center;
    gap: ${t("togglebutton.gap")};
    padding: ${t("togglebutton.content.padding")};
    background: transparent;
    border-radius: ${t("togglebutton.content.border.radius")};
    transition: background ${t("togglebutton.transition.duration")}, color ${t("togglebutton.transition.duration")}, border-color ${t("togglebutton.transition.duration")},
            outline-color ${t("togglebutton.transition.duration")}, box-shadow ${t("togglebutton.transition.duration")};
}

.p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover {
    background: ${t("togglebutton.hover.background")};
    color: ${t("togglebutton.hover.color")};
}

.p-togglebutton.p-togglebutton-checked {
    background: ${t("togglebutton.checked.background")};
    border-color: ${t("togglebutton.checked.border.color")};
    color: ${t("togglebutton.checked.color")};
}

.p-togglebutton-checked .p-togglebutton-content {
    background: ${t("togglebutton.content.checked.background")};
    box-shadow: ${t("togglebutton.content.checked.shadow")};
}

.p-togglebutton:focus-visible {
    box-shadow: ${t("togglebutton.focus.ring.shadow")};
    outline: ${t("togglebutton.focus.ring.width")} ${t("togglebutton.focus.ring.style")} ${t("togglebutton.focus.ring.color")};
    outline-offset: ${t("togglebutton.focus.ring.offset")};
}

.p-togglebutton.p-invalid {
    border-color: ${t("togglebutton.invalid.border.color")};
}

.p-togglebutton:disabled {
    opacity: 1;
    cursor: default;
    background: ${t("togglebutton.disabled.background")};
    border-color: ${t("togglebutton.disabled.border.color")};
    color: ${t("togglebutton.disabled.color")};
}

.p-togglebutton-label,
.p-togglebutton-icon {
    position: relative;
    transition: none;
}

.p-togglebutton-icon {
    color: ${t("togglebutton.icon.color")};
}

.p-togglebutton:not(:disabled):not(.p-togglebutton-checked):hover .p-togglebutton-icon {
    color: ${t("togglebutton.icon.hover.color")};
}

.p-togglebutton.p-togglebutton-checked .p-togglebutton-icon {
    color: ${t("togglebutton.icon.checked.color")};
}

.p-togglebutton:disabled .p-togglebutton-icon {
    color: ${t("togglebutton.icon.disabled.color")};
}

.p-togglebutton-sm {
    padding: ${t("togglebutton.sm.padding")};
    font-size: ${t("togglebutton.sm.font.size")};
}

.p-togglebutton-sm .p-togglebutton-content {
    padding: ${t("togglebutton.content.sm.padding")};
}

.p-togglebutton-lg {
    padding: ${t("togglebutton.lg.padding")};
    font-size: ${t("togglebutton.lg.font.size")};
}

.p-togglebutton-lg .p-togglebutton-content {
    padding: ${t("togglebutton.content.lg.padding")};
}
`,vt={root:function(a){var n=a.instance,i=a.props;return["p-togglebutton p-component",{"p-togglebutton-checked":n.active,"p-invalid":n.$invalid,"p-togglebutton-sm p-inputfield-sm":i.size==="small","p-togglebutton-lg p-inputfield-lg":i.size==="large"}]},content:"p-togglebutton-content",icon:"p-togglebutton-icon",label:"p-togglebutton-label"},ht=y.extend({name:"togglebutton",style:mt,classes:vt}),xt={name:"BaseToggleButton",extends:L,props:{onIcon:String,offIcon:String,onLabel:{type:String,default:"Yes"},offLabel:{type:String,default:"No"},iconPos:{type:String,default:"left"},readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null},size:{type:String,default:null}},style:ht,provide:function(){return{$pcToggleButton:this,$parentInstance:this}}};function x(t){"@babel/helpers - typeof";return x=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},x(t)}function yt(t,a,n){return(a=wt(a))in t?Object.defineProperty(t,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[a]=n,t}function wt(t){var a=$t(t,"string");return x(a)=="symbol"?a:a+""}function $t(t,a){if(x(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var i=n.call(t,a);if(x(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(a==="string"?String:Number)(t)}var A={name:"ToggleButton",extends:xt,inheritAttrs:!1,emits:["change"],methods:{getPTOptions:function(a){var n=a==="root"?this.ptmi:this.ptm;return n(a,{context:{active:this.active,disabled:this.disabled}})},onChange:function(a){!this.disabled&&!this.readonly&&(this.writeValue(!this.d_value,a),this.$emit("change",a))},onBlur:function(a){var n,i;(n=(i=this.formField).onBlur)===null||n===void 0||n.call(i,a)}},computed:{active:function(){return this.d_value===!0},hasLabel:function(){return I(this.onLabel)&&I(this.offLabel)},label:function(){return this.hasLabel?this.d_value?this.onLabel:this.offLabel:" "},dataP:function(){return S(yt({checked:this.active,invalid:this.$invalid},this.size,this.size))}},directives:{ripple:B}},kt=["tabindex","disabled","aria-pressed","aria-label","aria-labelledby","data-p-checked","data-p-disabled","data-p"],St=["data-p"];function It(t,a,n,i,s,o){var c=q("ripple");return E((g(),f("button",u({type:"button",class:t.cx("root"),tabindex:t.tabindex,disabled:t.disabled,"aria-pressed":t.d_value,onClick:a[0]||(a[0]=function(){return o.onChange&&o.onChange.apply(o,arguments)}),onBlur:a[1]||(a[1]=function(){return o.onBlur&&o.onBlur.apply(o,arguments)})},o.getPTOptions("root"),{"aria-label":t.ariaLabel,"aria-labelledby":t.ariaLabelledby,"data-p-checked":o.active,"data-p-disabled":t.disabled,"data-p":o.dataP}),[e("span",u({class:t.cx("content")},o.getPTOptions("content"),{"data-p":o.dataP}),[v(t.$slots,"default",{},function(){return[v(t.$slots,"icon",{value:t.d_value,class:N(t.cx("icon"))},function(){return[t.onIcon||t.offIcon?(g(),f("span",u({key:0,class:[t.cx("icon"),t.d_value?t.onIcon:t.offIcon]},o.getPTOptions("icon")),null,16)):_("",!0)]}),e("span",u({class:t.cx("label")},o.getPTOptions("label")),P(o.label),17)]})],16,St)],16,kt)),[[c]])}A.render=It;var Lt=({dt:t})=>`
.p-selectbutton {
    display: inline-flex;
    user-select: none;
    vertical-align: bottom;
    outline-color: transparent;
    border-radius: ${t("selectbutton.border.radius")};
}

.p-selectbutton .p-togglebutton {
    border-radius: 0;
    border-width: 1px 1px 1px 0;
}

.p-selectbutton .p-togglebutton:focus-visible {
    position: relative;
    z-index: 1;
}

.p-selectbutton .p-togglebutton:first-child {
    border-inline-start-width: 1px;
    border-start-start-radius: ${t("selectbutton.border.radius")};
    border-end-start-radius: ${t("selectbutton.border.radius")};
}

.p-selectbutton .p-togglebutton:last-child {
    border-start-end-radius: ${t("selectbutton.border.radius")};
    border-end-end-radius: ${t("selectbutton.border.radius")};
}

.p-selectbutton.p-invalid {
    outline: 1px solid ${t("selectbutton.invalid.border.color")};
    outline-offset: 0;
}
`,zt={root:function(a){var n=a.instance;return["p-selectbutton p-component",{"p-invalid":n.$invalid}]}},Bt=y.extend({name:"selectbutton",style:Lt,classes:zt}),Pt={name:"BaseSelectButton",extends:L,props:{options:Array,optionLabel:null,optionValue:null,optionDisabled:null,multiple:Boolean,allowEmpty:{type:Boolean,default:!0},dataKey:null,ariaLabelledby:{type:String,default:null},size:{type:String,default:null}},style:Bt,provide:function(){return{$pcSelectButton:this,$parentInstance:this}}};function Ot(t,a){var n=typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=V(t))||a){n&&(t=n);var i=0,s=function(){};return{s,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(m){throw m},f:s}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var o,c=!0,l=!1;return{s:function(){n=n.call(t)},n:function(){var m=n.next();return c=m.done,m},e:function(m){l=!0,o=m},f:function(){try{c||n.return==null||n.return()}finally{if(l)throw o}}}}function Tt(t){return At(t)||jt(t)||V(t)||Ct()}function Ct(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function V(t,a){if(t){if(typeof t=="string")return k(t,a);var n={}.toString.call(t).slice(8,-1);return n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set"?Array.from(t):n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?k(t,a):void 0}}function jt(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function At(t){if(Array.isArray(t))return k(t)}function k(t,a){(a==null||a>t.length)&&(a=t.length);for(var n=0,i=Array(a);n<a;n++)i[n]=t[n];return i}var D={name:"SelectButton",extends:Pt,inheritAttrs:!1,emits:["change"],methods:{getOptionLabel:function(a){return this.optionLabel?w(a,this.optionLabel):a},getOptionValue:function(a){return this.optionValue?w(a,this.optionValue):a},getOptionRenderKey:function(a){return this.dataKey?w(a,this.dataKey):this.getOptionLabel(a)},isOptionDisabled:function(a){return this.optionDisabled?w(a,this.optionDisabled):!1},isOptionReadonly:function(a){if(this.allowEmpty)return!1;var n=this.isSelected(a);return this.multiple?n&&this.d_value.length===1:n},onOptionSelect:function(a,n,i){var s=this;if(!(this.disabled||this.isOptionDisabled(n)||this.isOptionReadonly(n))){var o=this.isSelected(n),c=this.getOptionValue(n),l;if(this.multiple)if(o){if(l=this.d_value.filter(function(d){return!$(d,c,s.equalityKey)}),!this.allowEmpty&&l.length===0)return}else l=this.d_value?[].concat(Tt(this.d_value),[c]):[c];else{if(o&&!this.allowEmpty)return;l=o?null:c}this.writeValue(l,a),this.$emit("change",{event:a,value:l})}},isSelected:function(a){var n=!1,i=this.getOptionValue(a);if(this.multiple){if(this.d_value){var s=Ot(this.d_value),o;try{for(s.s();!(o=s.n()).done;){var c=o.value;if($(c,i,this.equalityKey)){n=!0;break}}}catch(l){s.e(l)}finally{s.f()}}}else n=$(this.d_value,i,this.equalityKey);return n}},computed:{equalityKey:function(){return this.optionValue?null:this.dataKey},dataP:function(){return S({invalid:this.$invalid})}},directives:{ripple:B},components:{ToggleButton:A}},Vt=["aria-labelledby","data-p"];function Dt(t,a,n,i,s,o){var c=H("ToggleButton");return g(),f("div",u({class:t.cx("root"),role:"group","aria-labelledby":t.ariaLabelledby},t.ptmi("root"),{"data-p":o.dataP}),[(g(!0),f(Z,null,U(t.options,function(l,d){return g(),W(c,{key:o.getOptionRenderKey(l),modelValue:o.isSelected(l),onLabel:o.getOptionLabel(l),offLabel:o.getOptionLabel(l),disabled:t.disabled||o.isOptionDisabled(l),unstyled:t.unstyled,size:t.size,readonly:o.isOptionReadonly(l),onChange:function(K){return o.onOptionSelect(K,l,d)},pt:t.ptm("pcToggleButton")},J({_:2},[t.$slots.option?{name:"default",fn:O(function(){return[v(t.$slots,"option",{option:l,index:d},function(){return[e("span",u({ref_for:!0},t.ptm("pcToggleButton").label),P(o.getOptionLabel(l)),17)]})]}),key:"0"}:void 0]),1032,["modelValue","onLabel","offLabel","disabled","unstyled","size","readonly","onChange","pt"])}),128))],16,Vt)}D.render=Dt;const Kt={class:"flex-1 h-full overflow-y-auto overflow-x-clip overflow-hidden flex border border-surface rounded-2xl"},Mt={class:"w-4/12 xl:w-3/12 min-w-40 overflow-auto flex flex-col gap-6"},Ft={class:"px-5"},Rt={class:"w-full px-5"},qt={class:"w-8/12 xl:w-6/12 border-x border-surface flex flex-col"},Et={class:"p-4 border-t border-surface flex items-end justify-between gap-2"},Nt={class:"flex items-end gap-1 flex-1"},_t={class:"w-3/12 xl:block hidden min-w-40 py-6 px-3 overflow-auto"},Ht={class:"flex flex-col items-center justify-center"},Zt={class:"flex items-center justify-center flex-wrap gap-1 mt-4"},Ut={class:"flex flex-col gap-4 mt-4"},Wt={class:"flex items-center gap-2"},Jt={class:"mt-6"},Gt={class:"flex items-center gap-2"},Yt={class:"mt-5"},ee=G({__name:"ChatView",setup(t){return(a,n)=>(g(),f("div",Kt,[e("div",Mt,[n[0]||(n[0]=e("div",{class:"flex flex-col gap-6 pt-3 pb-2 -mb-2 px-5 sticky top-0 bg-surface-0 dark:bg-surface-950 z-10"},[e("div",{class:"flex items-center justify-between gap-6 text-color"},[e("div",{class:"text-2xl font-medium lead"},"Chats"),e("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc4501:""},[e("span",{class:"p-button-icon pi pi-plus","data-pc-section":"icon"}),e("span",{class:"p-button-label","data-pc-section":"label"}," ")])])],-1)),e("div",Ft,[r(p(C),null,{default:O(()=>[r(p(T),{class:"pi pi-search"}),r(p(j),{placeholder:"Search"})]),_:1})]),e("div",Rt,[r(p(D),{"model-value":"Chat",options:["Chat","Call"],"aria-labelledby":"basic",class:"w-full"})]),n[1]||(n[1]=e("div",{class:"flex-1 flex flex-col"},[e("div",{class:"flex items-center gap-2 p-4 cursor-pointer hover:bg-emphasis transition-all"},[e("div",{class:"relative"},[e("div",{class:"absolute top-0 right-0 p-[1px] bg-surface-0 dark:bg-surface-950 rounded-full flex items-center justify-center"},[e("span",{class:"p-badge p-component p-badge-dot p-badge-success p-1.5","data-pc-name":"badge","data-pc-section":"root",pc4506:""})]),e("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle p-avatar-lg text-base font-medium flex","data-pc-name":"avatar","data-pc-section":"root",pc4507:""},[e("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg","data-pc-section":"image"})])]),e("div",{class:"flex-1"},[e("div",{class:"flex items-start gap-1 justify-between"},[e("div",{class:"text-color font-medium leading-6"},"Cody Fisher"),e("div",{class:"text-sm text-muted-color leading-5"},"12.30")]),e("div",{class:"flex items-center gap-5 justify-between mt-1"},[e("div",{class:"text-muted-color text-sm leading-5 line-clamp-1"}," Hey there! I've heard about PrimeVue. Any cool tips for getting started? "),e("span",{class:"p-badge p-component p-badge-circle p-badge-contrast","data-pc-name":"badge","data-pc-section":"root",pc4508:""},"8")])])]),e("div",{class:"flex items-center gap-2 p-4 cursor-pointer hover:bg-emphasis transition-all bg-emphasis"},[e("div",{class:"relative"},[e("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle p-avatar-lg text-base font-medium flex","data-pc-name":"avatar","data-pc-section":"root",pc4509:""},[e("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png","data-pc-section":"image"})])]),e("div",{class:"flex-1"},[e("div",{class:"flex items-start gap-1 justify-between"},[e("div",{class:"text-color font-medium leading-6"},"PrimeTek Team"),e("div",{class:"text-sm text-muted-color leading-5"},"11.15")]),e("div",{class:"flex items-center gap-5 justify-between mt-1"},[e("div",{class:"text-muted-color text-sm leading-5 line-clamp-1"}," Let's implement PrimeVue. Elevating our UI game! 🚀 ")])])])],-1))]),e("div",qt,[n[4]||(n[4]=e("div",{class:"flex items-center p-4 gap-7 border-b border-surface"},[e("div",{class:"flex items-center"},[e("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle p-avatar-lg mr-2 av","data-pc-name":"avatar","data-pc-section":"root",pc4528:""},[e("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png","data-pc-section":"image"})]),e("div",{class:"flex-1"},[e("div",{class:"text-color leading-6 cursor-pointer hover:text-muted-color-emphasis transition-colors"}," PrimeTek "),e("div",{class:"text-muted-color leading-5 line-clamp-1 mt-1"}," Cody Fisher, Esther Howard, Jerome Bell, Kristin Watson, Ronald Richards, Darrell Steward ")])]),e("div",{class:"flex items-center gap-2"},[e("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc4529:""},[e("span",{class:"p-button-icon pi pi-phone","data-pc-section":"icon"}),e("span",{class:"p-button-label","data-pc-section":"label"}," ")]),e("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc4530:""},[e("span",{class:"p-button-icon pi pi-search","data-pc-section":"icon"}),e("span",{class:"p-button-label","data-pc-section":"label"}," ")]),e("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","aria-haspopup":"true","aria-controls":"overlay_menu","data-pc-section":"root",pc4531:""},[e("span",{class:"p-button-icon pi pi-ellipsis-h","data-pc-section":"icon"}),e("span",{class:"p-button-label","data-pc-section":"label"}," ")])])],-1)),n[5]||(n[5]=e("div",{class:"flex-1 overflow-y-auto flex flex-col gap-8 py-8 px-6"},[e("div",{class:"flex items-start min-w-64 w-fit max-w-[60%]"},[e("div",{class:"flex items-center gap-2 sticky top-0 transition-all"},[e("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle w-10 h-10 text-sm font-medium","data-pc-name":"avatar","data-pc-section":"root",pc4535:""},[e("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar2.png","data-pc-section":"image"})]),e("div",null,[e("svg",{class:"fill-surface-100 dark:fill-surface-800",xmlns:"http://www.w3.org/2000/svg",width:"7",height:"11",viewBox:"0 0 7 11",fill:"none"},[e("path",{d:"M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z"})])])]),e("div",{class:"flex-1 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded-lg"},[e("p",{class:"text-color leading-6 mb-0"}," It's design-neutral and compatible with Tailwind. Features accessible, high-grade components! "),e("div",{class:"bg-surface-200 dark:bg-surface-700 mt-2 w-full rounded-lg mb-0.5 hover:opacity-75 transition-all"},[e("img",{class:"w-full h-auto block cursor-pointer",src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/message-image.png",alt:"Message Image"})])])]),e("div",{class:"flex items-start min-w-64 w-fit max-w-[60%] ml-auto mr-0 flex-row-reverse"},[e("div",{class:"flex items-center gap-2 sticky top-0 transition-all flex-row-reverse"},[e("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle w-10 h-10 text-sm font-medium","data-pc-name":"avatar","data-pc-section":"root",pc4542:""},[e("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png","data-pc-section":"image"})]),e("div",null,[e("svg",{class:"fill-primary rotate-180",xmlns:"http://www.w3.org/2000/svg",width:"7",height:"11",viewBox:"0 0 7 11",fill:"none"},[e("path",{d:"M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z"})])])]),e("div",{class:"flex-1 bg-primary px-2 py-1 rounded-lg"},[e("p",{class:"text-primary-contrast leading-6 mb-0"}," I couldn't agree more. Plus, the documentation is incredibly thorough, which makes onboarding new team members a breeze. "),e("div",{class:"bg-primary-emphasis mt-2 w-full rounded-lg mb-0.5 hover:opacity-75 transition-all"},[e("img",{class:"w-full h-auto block cursor-pointer",src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/message-image.png",alt:"Message Image"})])])]),e("div",{class:"flex items-start min-w-64 w-fit max-w-[60%]"},[e("div",{class:"flex items-center gap-2 sticky top-0 transition-all"},[e("div",{class:"p-avatar p-component p-avatar-circle bg-primary-100 text-primary-950 w-10 h-10 text-sm font-medium","data-pc-name":"avatar","data-pc-section":"root",pc4545:""},[e("span",{class:"p-avatar-label","data-pc-section":"label"},"HS")]),e("div",null,[e("svg",{class:"fill-surface-100 dark:fill-surface-800",xmlns:"http://www.w3.org/2000/svg",width:"7",height:"11",viewBox:"0 0 7 11",fill:"none"},[e("path",{d:"M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z"})])])]),e("div",{class:"flex-1 bg-surface-100 dark:bg-surface-800 px-2 py-1 rounded-lg"},[e("p",{class:"text-color leading-6 mb-0"}," I've also found that leveraging the component's internal state management capabilities can help streamline data flow and improve performance. ")])]),e("div",{class:"flex items-start min-w-64 w-fit max-w-[60%] ml-auto mr-0 flex-row-reverse"},[e("div",{class:"flex items-center gap-2 sticky top-0 transition-all flex-row-reverse"},[e("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle w-10 h-10 text-sm font-medium","data-pc-name":"avatar","data-pc-section":"root",pc4546:""},[e("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png","data-pc-section":"image"})]),e("div",null,[e("svg",{class:"fill-primary rotate-180",xmlns:"http://www.w3.org/2000/svg",width:"7",height:"11",viewBox:"0 0 7 11",fill:"none"},[e("path",{d:"M1.79256 7.09551C0.516424 6.31565 0.516426 4.46224 1.79256 3.68238L7 0.500055L7 10.2778L1.79256 7.09551Z"})])])]),e("div",{class:"flex-1 bg-primary px-2 py-1 rounded-lg"},[e("p",{class:"text-primary-contrast leading-6 mb-0"}," That's great advice. It's amazing how much detail and thought has gone into making PrimeVue such a powerful tool for developers. ")])])],-1)),e("div",Et,[e("div",Nt,[n[2]||(n[2]=e("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc4547:""},[e("span",{class:"p-button-icon pi pi-face-smile","data-pc-section":"icon"}),e("span",{class:"p-button-label","data-pc-section":"label"}," ")],-1)),n[3]||(n[3]=e("button",{class:"p-button p-component p-button-icon-only p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc4548:""},[e("span",{class:"p-button-icon pi pi-paperclip","data-pc-section":"icon"}),e("span",{class:"p-button-label","data-pc-section":"label"}," ")],-1)),r(p(F),{rows:"1",autoResize:"",placeholder:"Write your message...",class:"w-full"})]),r(p(b),{icon:"pi pi-send","aria-label":"Save"})])]),e("div",_t,[e("div",Ht,[n[6]||(n[6]=e("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle p-avatar-xl w-32 h-32","data-pc-name":"avatar","data-pc-section":"root",pc4551:""},[e("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar-primetek.png","data-pc-section":"image"})],-1)),n[7]||(n[7]=e("div",{class:"leading-6 font-medium text-color mt-4 w-full text-center"},"PrimeTek",-1)),n[8]||(n[8]=e("div",{class:"leading-5 text-sm text-muted-color mt-1 w-full text-center"},"@primetek",-1)),e("div",Zt,[r(p(b),{icon:"pi pi-phone",severity:"secondary",text:""}),r(p(b),{icon:"pi pi-video",severity:"secondary",text:""}),r(p(b),{icon:"pi pi-sign-in",severity:"secondary",text:""}),r(p(b),{icon:"pi pi-info-circle",severity:"secondary",text:""}),r(p(b),{icon:"pi pi-ellipsis-v",severity:"secondary",text:""})])]),e("div",Ut,[e("div",Wt,[n[9]||(n[9]=e("i",{class:"pi pi-bell text-color"},null,-1)),n[10]||(n[10]=e("div",{class:"leading-6 font-medium text-color flex-1"},"Notification",-1)),r(p(R))]),n[11]||(n[11]=e("div",{class:"flex items-center gap-2"},[e("i",{class:"pi pi-volume-down text-color"}),e("div",{class:"leading-6 font-medium text-color flex-1"},"Sound"),e("div",{class:"p-toggleswitch p-component","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc4558:"",style:{position:"relative"}},[e("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input","aria-checked":"false","data-pc-section":"input"}),e("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])],-1)),n[12]||(n[12]=e("div",{class:"flex items-center gap-2"},[e("i",{class:"pi pi-download text-color"}),e("div",{class:"leading-6 font-medium text-color flex-1"},"Save to downloads"),e("div",{class:"p-toggleswitch p-component","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc4559:"",style:{position:"relative"}},[e("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input","aria-checked":"false","data-pc-section":"input"}),e("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])],-1))]),e("div",Jt,[e("div",Gt,[n[13]||(n[13]=e("div",{class:"flex-1 text-color leading-6 font-medium"},"Members",-1)),r(p(b),{text:"",label:"See All",class:"text-sm py-0.5 px-2 text-muted-color"})]),n[14]||(n[14]=e("div",{class:"mt-4 flex flex-col gap-4"},[e("div",{class:"flex items-center gap-2 cursor-pointer"},[e("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle font-medium text-xs","data-pc-name":"avatar","data-pc-section":"root",pc4564:""},[e("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar8.png","data-pc-section":"image"})]),e("div",{class:"text-sm text-color hover:text-muted-color-emphasis transition-colors font-medium leading-5 flex-1"}," Arlene McCoy "),e("i",{class:"pi pi-chevron-right text-xs text-muted-color"})]),e("div",{class:"flex items-center gap-2 cursor-pointer"},[e("div",{class:"p-avatar p-component p-avatar-circle bg-orange-100 text-orange-950 font-medium text-xs","data-pc-name":"avatar","data-pc-section":"root",pc4565:""},[e("span",{class:"p-avatar-label","data-pc-section":"label"},"DR")]),e("div",{class:"text-sm text-color hover:text-muted-color-emphasis transition-colors font-medium leading-5 flex-1"}," Dianne Russell "),e("i",{class:"pi pi-chevron-right text-xs text-muted-color"})])],-1))]),e("div",Yt,[n[15]||(n[15]=e("div",{class:"p-selectbutton p-component w-full",role:"group","data-pc-name":"selectbutton","data-pc-section":"root",pc4566:""},[e("button",{type:"button",class:"p-togglebutton p-component p-togglebutton-checked flex-1","aria-pressed":"true","data-pc-name":"pcbutton","data-pc-extend":"togglebutton","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false"},[e("span",{class:"p-togglebutton-content","data-pc-section":"content"},[e("span",{class:"p-togglebutton-label","data-pc-section":"label"},"Media")])]),e("button",{type:"button",class:"p-togglebutton p-component flex-1","aria-pressed":"false","data-pc-name":"pcbutton","data-pc-extend":"togglebutton","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false"},[e("span",{class:"p-togglebutton-content","data-pc-section":"content"},[e("span",{class:"p-togglebutton-label","data-pc-section":"label"},"Link")])]),e("button",{type:"button",class:"p-togglebutton p-component flex-1","aria-pressed":"false","data-pc-name":"pcbutton","data-pc-extend":"togglebutton","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false"},[e("span",{class:"p-togglebutton-content","data-pc-section":"content"},[e("span",{class:"p-togglebutton-label","data-pc-section":"label"},"Docs")])])],-1)),n[16]||(n[16]=Y('<div class="mt-3 mb-5 grid grid-cols-3 gap-2"><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer"><img class="w-full h-full object-cover block" src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image1.png" alt="Media Image"></div><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer"><img class="w-full h-full object-cover block" src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image2.png" alt="Media Image"></div><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer"><img class="w-full h-full object-cover block" src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image3.png" alt="Media Image"></div><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer"><img class="w-full h-full object-cover block" src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image4.png" alt="Media Image"></div><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer"><img class="w-full h-full object-cover block" src="https://www.primefaces.org/cdn/primevue/images/landing/apps/chat-image5.png" alt="Media Image"></div><div class="bg-emphasis hover:opacity-70 transition-all flex-1 aspect-square rounded-lg border border-surface cursor-pointer flex items-center justify-center"><span class="text-muted-color font-medium">99+</span></div></div>',1)),r(p(b),{icon:"pi pi-arrow-right",iconPos:"right",outlined:"",label:"Show more",class:"w-full"})])])]))}});export{ee as default};
