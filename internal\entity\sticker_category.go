package entity

import (
	"time"
)

// StickerCategory 贴纸与分类的关联实体，映射到 sticker_categories 表
type StickerCategory struct {
	ID         uint       `gorm:"primaryKey;autoIncrement" json:"id"`
	StickerID  uint       `gorm:"type:bigint(20) unsigned;not null" json:"sticker_id"`
	CategoryID uint       `gorm:"type:bigint(20) unsigned;not null" json:"category_id"`
	CreatedAt  *time.Time `gorm:"type:timestamp" json:"created_at,omitempty"`
	UpdatedAt  *time.Time `gorm:"type:timestamp" json:"updated_at,omitempty"`
	
	// 关联贴纸
	Sticker Sticker `gorm:"foreignKey:StickerID" json:"sticker"`
	
	// 关联分类
	Category Category `gorm:"foreignKey:CategoryID" json:"category"`
}