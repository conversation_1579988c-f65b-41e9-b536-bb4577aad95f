package geocode

import (
	"context"
	"fmt"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"log"
)

func tool() mcp.Tool {
	return mcp.NewTool("geocoding",
		mcp.WithDescription("查询地址的经纬度，Get the latitude and longitude of an address"),
		mcp.WithString("address",
			mcp.Required(),
			mcp.Description("要查询的地址"),
		),
	)
}

func handler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	args := request.GetArguments()
	address, ok := args["address"].(string)
	if !ok {
		return mcp.NewToolResultError("地址参数无效"), nil
	}

	longitude, latitude, err := getCoordinatesFromAmap(address)
	if err != nil {
		return mcp.NewToolResultErrorFromErr("经纬度查询失败", err), nil
	}

	resultText := fmt.Sprintf("地址: %s, 纬度: %.6f, 经度: %.6f", address, latitude, longitude)
	log.Println(resultText)
	return mcp.NewToolResultText(resultText), nil
}

func Tool() (mcp.Tool, server.ToolHandlerFunc) {
	return tool(), handler
}
