const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/MeetingView-2VMLXPJN.js","assets/index-C4camBIb.js","assets/index-DOZg_TSE.js","assets/MeetingView-q7SXXlYZ.css","assets/LoginView-CLzqqMm5.js","assets/ChatView-Eab6ittC.js","assets/InboxView-BY1tYbrT.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/CustomersView-P3IxWO33.js","assets/CardsView-BwqfqlAb.js","assets/MoviesView-DUABXTdB.js"])))=>i.map(i=>d[i]);
function hf(e,t){for(var r=0;r<t.length;r++){const o=t[r];if(typeof o!="string"&&!Array.isArray(o)){for(const n in o)if(n!=="default"&&!(n in e)){const i=Object.getOwnPropertyDescriptor(o,n);i&&Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:()=>o[n]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))o(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&o(s)}).observe(document,{childList:!0,subtree:!0});function r(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(n){if(n.ep)return;n.ep=!0;const i=r(n);fetch(n.href,i)}})();let Gl=!0,Jl=!0;function Qo(e,t,r){const o=e.match(t);return o&&o.length>=r&&parseInt(o[r],10)}function vr(e,t,r){if(!e.RTCPeerConnection)return;const o=e.RTCPeerConnection.prototype,n=o.addEventListener;o.addEventListener=function(s,a){if(s!==t)return n.apply(this,arguments);const l=u=>{const c=r(u);c&&(a.handleEvent?a.handleEvent(c):a(c))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(a,l),n.apply(this,[s,l])};const i=o.removeEventListener;o.removeEventListener=function(s,a){if(s!==t||!this._eventMap||!this._eventMap[t])return i.apply(this,arguments);if(!this._eventMap[t].has(a))return i.apply(this,arguments);const l=this._eventMap[t].get(a);return this._eventMap[t].delete(a),this._eventMap[t].size===0&&delete this._eventMap[t],Object.keys(this._eventMap).length===0&&delete this._eventMap,i.apply(this,[s,l])},Object.defineProperty(o,"on"+t,{get(){return this["_on"+t]},set(s){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),s&&this.addEventListener(t,this["_on"+t]=s)},enumerable:!0,configurable:!0})}function mf(e){return typeof e!="boolean"?new Error("Argument type: "+typeof e+". Please use a boolean."):(Gl=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function bf(e){return typeof e!="boolean"?new Error("Argument type: "+typeof e+". Please use a boolean."):(Jl=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function Yl(){if(typeof window=="object"){if(Gl)return;typeof console<"u"&&typeof console.log=="function"&&console.log.apply(console,arguments)}}function gs(e,t){Jl&&console.warn(e+" is deprecated, please use "+t+" instead.")}function vf(e){const t={browser:null,version:null};if(typeof e>"u"||!e.navigator||!e.navigator.userAgent)return t.browser="Not a browser.",t;const{navigator:r}=e;if(r.userAgentData&&r.userAgentData.brands){const o=r.userAgentData.brands.find(n=>n.brand==="Chromium");if(o)return{browser:"chrome",version:parseInt(o.version,10)}}if(r.mozGetUserMedia)t.browser="firefox",t.version=Qo(r.userAgent,/Firefox\/(\d+)\./,1);else if(r.webkitGetUserMedia||e.isSecureContext===!1&&e.webkitRTCPeerConnection)t.browser="chrome",t.version=Qo(r.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(e.RTCPeerConnection&&r.userAgent.match(/AppleWebKit\/(\d+)\./))t.browser="safari",t.version=Qo(r.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype;else return t.browser="Not a supported browser.",t;return t}function Ks(e){return Object.prototype.toString.call(e)==="[object Object]"}function Xl(e){return Ks(e)?Object.keys(e).reduce(function(t,r){const o=Ks(e[r]),n=o?Xl(e[r]):e[r],i=o&&!Object.keys(n).length;return n===void 0||i?t:Object.assign(t,{[r]:n})},{}):e}function wi(e,t,r){!t||r.has(t.id)||(r.set(t.id,t),Object.keys(t).forEach(o=>{o.endsWith("Id")?wi(e,e.get(t[o]),r):o.endsWith("Ids")&&t[o].forEach(n=>{wi(e,e.get(n),r)})}))}function qs(e,t,r){const o=r?"outbound-rtp":"inbound-rtp",n=new Map;if(t===null)return n;const i=[];return e.forEach(s=>{s.type==="track"&&s.trackIdentifier===t.id&&i.push(s)}),i.forEach(s=>{e.forEach(a=>{a.type===o&&a.trackId===s.id&&wi(e,a,n)})}),n}const Gs=Yl;function Zl(e,t){const r=e&&e.navigator;if(!r.mediaDevices)return;const o=function(a){if(typeof a!="object"||a.mandatory||a.optional)return a;const l={};return Object.keys(a).forEach(u=>{if(u==="require"||u==="advanced"||u==="mediaSource")return;const c=typeof a[u]=="object"?a[u]:{ideal:a[u]};c.exact!==void 0&&typeof c.exact=="number"&&(c.min=c.max=c.exact);const d=function(f,p){return f?f+p.charAt(0).toUpperCase()+p.slice(1):p==="deviceId"?"sourceId":p};if(c.ideal!==void 0){l.optional=l.optional||[];let f={};typeof c.ideal=="number"?(f[d("min",u)]=c.ideal,l.optional.push(f),f={},f[d("max",u)]=c.ideal,l.optional.push(f)):(f[d("",u)]=c.ideal,l.optional.push(f))}c.exact!==void 0&&typeof c.exact!="number"?(l.mandatory=l.mandatory||{},l.mandatory[d("",u)]=c.exact):["min","max"].forEach(f=>{c[f]!==void 0&&(l.mandatory=l.mandatory||{},l.mandatory[d(f,u)]=c[f])})}),a.advanced&&(l.optional=(l.optional||[]).concat(a.advanced)),l},n=function(a,l){if(t.version>=61)return l(a);if(a=JSON.parse(JSON.stringify(a)),a&&typeof a.audio=="object"){const u=function(c,d,f){d in c&&!(f in c)&&(c[f]=c[d],delete c[d])};a=JSON.parse(JSON.stringify(a)),u(a.audio,"autoGainControl","googAutoGainControl"),u(a.audio,"noiseSuppression","googNoiseSuppression"),a.audio=o(a.audio)}if(a&&typeof a.video=="object"){let u=a.video.facingMode;u=u&&(typeof u=="object"?u:{ideal:u});const c=t.version<66;if(u&&(u.exact==="user"||u.exact==="environment"||u.ideal==="user"||u.ideal==="environment")&&!(r.mediaDevices.getSupportedConstraints&&r.mediaDevices.getSupportedConstraints().facingMode&&!c)){delete a.video.facingMode;let d;if(u.exact==="environment"||u.ideal==="environment"?d=["back","rear"]:(u.exact==="user"||u.ideal==="user")&&(d=["front"]),d)return r.mediaDevices.enumerateDevices().then(f=>{f=f.filter(h=>h.kind==="videoinput");let p=f.find(h=>d.some(b=>h.label.toLowerCase().includes(b)));return!p&&f.length&&d.includes("back")&&(p=f[f.length-1]),p&&(a.video.deviceId=u.exact?{exact:p.deviceId}:{ideal:p.deviceId}),a.video=o(a.video),Gs("chrome: "+JSON.stringify(a)),l(a)})}a.video=o(a.video)}return Gs("chrome: "+JSON.stringify(a)),l(a)},i=function(a){return t.version>=64?a:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[a.name]||a.name,message:a.message,constraint:a.constraint||a.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}},s=function(a,l,u){n(a,c=>{r.webkitGetUserMedia(c,l,d=>{u&&u(i(d))})})};if(r.getUserMedia=s.bind(r),r.mediaDevices.getUserMedia){const a=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(l){return n(l,u=>a(u).then(c=>{if(u.audio&&!c.getAudioTracks().length||u.video&&!c.getVideoTracks().length)throw c.getTracks().forEach(d=>{d.stop()}),new DOMException("","NotFoundError");return c},c=>Promise.reject(i(c))))}}}function Ql(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function ec(e){if(typeof e=="object"&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(r){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=r)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=o=>{o.stream.addEventListener("addtrack",n=>{let i;e.RTCPeerConnection.prototype.getReceivers?i=this.getReceivers().find(a=>a.track&&a.track.id===n.track.id):i={track:n.track};const s=new Event("track");s.track=n.track,s.receiver=i,s.transceiver={receiver:i},s.streams=[o.stream],this.dispatchEvent(s)}),o.stream.getTracks().forEach(n=>{let i;e.RTCPeerConnection.prototype.getReceivers?i=this.getReceivers().find(a=>a.track&&a.track.id===n.id):i={track:n};const s=new Event("track");s.track=n,s.receiver=i,s.transceiver={receiver:i},s.streams=[o.stream],this.dispatchEvent(s)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else vr(e,"track",t=>(t.transceiver||Object.defineProperty(t,"transceiver",{value:{receiver:t.receiver}}),t))}function tc(e){if(typeof e=="object"&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(n,i){return{track:i,get dtmf(){return this._dtmf===void 0&&(i.kind==="audio"?this._dtmf=n.createDTMFSender(i):this._dtmf=null),this._dtmf},_pc:n}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(a,l){let u=n.apply(this,arguments);return u||(u=t(this,a),this._senders.push(u)),u};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(a){i.apply(this,arguments);const l=this._senders.indexOf(a);l!==-1&&this._senders.splice(l,1)}}const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(i){this._senders=this._senders||[],r.apply(this,[i]),i.getTracks().forEach(s=>{this._senders.push(t(this,s))})};const o=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(i){this._senders=this._senders||[],o.apply(this,[i]),i.getTracks().forEach(s=>{const a=this._senders.find(l=>l.track===s);a&&this._senders.splice(this._senders.indexOf(a),1)})}}else if(typeof e=="object"&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const o=t.apply(this,[]);return o.forEach(n=>n._pc=this),o},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return this._dtmf===void 0&&(this.track.kind==="audio"?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function rc(e){if(!(typeof e=="object"&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const r=e.RTCPeerConnection.prototype.getSenders;r&&(e.RTCPeerConnection.prototype.getSenders=function(){const i=r.apply(this,[]);return i.forEach(s=>s._pc=this),i});const o=e.RTCPeerConnection.prototype.addTrack;o&&(e.RTCPeerConnection.prototype.addTrack=function(){const i=o.apply(this,arguments);return i._pc=this,i}),e.RTCRtpSender.prototype.getStats=function(){const i=this;return this._pc.getStats().then(s=>qs(s,i.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const r=e.RTCPeerConnection.prototype.getReceivers;r&&(e.RTCPeerConnection.prototype.getReceivers=function(){const n=r.apply(this,[]);return n.forEach(i=>i._pc=this),n}),vr(e,"track",o=>(o.receiver._pc=o.srcElement,o)),e.RTCRtpReceiver.prototype.getStats=function(){const n=this;return this._pc.getStats().then(i=>qs(i,n.track,!1))}}if(!("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const o=arguments[0];let n,i,s;return this.getSenders().forEach(a=>{a.track===o&&(n?s=!0:n=a)}),this.getReceivers().forEach(a=>(a.track===o&&(i?s=!0:i=a),a.track===o)),s||n&&i?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):n?n.getStats():i?i.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function oc(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(s=>this._shimmedLocalStreams[s][0])};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(s,a){if(!a)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const l=t.apply(this,arguments);return this._shimmedLocalStreams[a.id]?this._shimmedLocalStreams[a.id].indexOf(l)===-1&&this._shimmedLocalStreams[a.id].push(l):this._shimmedLocalStreams[a.id]=[a,l],l};const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(s){this._shimmedLocalStreams=this._shimmedLocalStreams||{},s.getTracks().forEach(u=>{if(this.getSenders().find(d=>d.track===u))throw new DOMException("Track already exists.","InvalidAccessError")});const a=this.getSenders();r.apply(this,arguments);const l=this.getSenders().filter(u=>a.indexOf(u)===-1);this._shimmedLocalStreams[s.id]=[s].concat(l)};const o=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(s){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[s.id],o.apply(this,arguments)};const n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(s){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},s&&Object.keys(this._shimmedLocalStreams).forEach(a=>{const l=this._shimmedLocalStreams[a].indexOf(s);l!==-1&&this._shimmedLocalStreams[a].splice(l,1),this._shimmedLocalStreams[a].length===1&&delete this._shimmedLocalStreams[a]}),n.apply(this,arguments)}}function nc(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return oc(e);const r=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const c=r.apply(this);return this._reverseStreams=this._reverseStreams||{},c.map(d=>this._reverseStreams[d.id])};const o=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(c){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},c.getTracks().forEach(d=>{if(this.getSenders().find(p=>p.track===d))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[c.id]){const d=new e.MediaStream(c.getTracks());this._streams[c.id]=d,this._reverseStreams[d.id]=c,c=d}o.apply(this,[c])};const n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(c){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},n.apply(this,[this._streams[c.id]||c]),delete this._reverseStreams[this._streams[c.id]?this._streams[c.id].id:c.id],delete this._streams[c.id]},e.RTCPeerConnection.prototype.addTrack=function(c,d){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const f=[].slice.call(arguments,1);if(f.length!==1||!f[0].getTracks().find(b=>b===c))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(b=>b.track===c))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const h=this._streams[d.id];if(h)h.addTrack(c),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const b=new e.MediaStream([c]);this._streams[d.id]=b,this._reverseStreams[b.id]=d,this.addStream(b)}return this.getSenders().find(b=>b.track===c)};function i(u,c){let d=c.sdp;return Object.keys(u._reverseStreams||[]).forEach(f=>{const p=u._reverseStreams[f],h=u._streams[p.id];d=d.replace(new RegExp(h.id,"g"),p.id)}),new RTCSessionDescription({type:c.type,sdp:d})}function s(u,c){let d=c.sdp;return Object.keys(u._reverseStreams||[]).forEach(f=>{const p=u._reverseStreams[f],h=u._streams[p.id];d=d.replace(new RegExp(p.id,"g"),h.id)}),new RTCSessionDescription({type:c.type,sdp:d})}["createOffer","createAnswer"].forEach(function(u){const c=e.RTCPeerConnection.prototype[u],d={[u](){const f=arguments;return arguments.length&&typeof arguments[0]=="function"?c.apply(this,[h=>{const b=i(this,h);f[0].apply(null,[b])},h=>{f[1]&&f[1].apply(null,h)},arguments[2]]):c.apply(this,arguments).then(h=>i(this,h))}};e.RTCPeerConnection.prototype[u]=d[u]});const a=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return!arguments.length||!arguments[0].type?a.apply(this,arguments):(arguments[0]=s(this,arguments[0]),a.apply(this,arguments))};const l=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const u=l.get.apply(this);return u.type===""?u:i(this,u)}}),e.RTCPeerConnection.prototype.removeTrack=function(c){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!c._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(c._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};let f;Object.keys(this._streams).forEach(p=>{this._streams[p].getTracks().find(b=>c.track===b)&&(f=this._streams[p])}),f&&(f.getTracks().length===1?this.removeStream(this._reverseStreams[f.id]):f.removeTrack(c.track),this.dispatchEvent(new Event("negotiationneeded")))}}function Ti(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(r){const o=e.RTCPeerConnection.prototype[r],n={[r](){return arguments[0]=new(r==="addIceCandidate"?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),o.apply(this,arguments)}};e.RTCPeerConnection.prototype[r]=n[r]})}function ic(e,t){vr(e,"negotiationneeded",r=>{const o=r.target;if(!((t.version<72||o.getConfiguration&&o.getConfiguration().sdpSemantics==="plan-b")&&o.signalingState!=="stable"))return r})}const Js=Object.freeze(Object.defineProperty({__proto__:null,fixNegotiationNeeded:ic,shimAddTrackRemoveTrack:nc,shimAddTrackRemoveTrackWithNative:oc,shimGetSendersWithDtmf:tc,shimGetUserMedia:Zl,shimMediaStream:Ql,shimOnTrack:ec,shimPeerConnection:Ti,shimSenderReceiverGetStats:rc},Symbol.toStringTag,{value:"Module"}));function sc(e,t){const r=e&&e.navigator,o=e&&e.MediaStreamTrack;if(r.getUserMedia=function(n,i,s){gs("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(n).then(i,s)},!(t.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){const n=function(s,a,l){a in s&&!(l in s)&&(s[l]=s[a],delete s[a])},i=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(s){return typeof s=="object"&&typeof s.audio=="object"&&(s=JSON.parse(JSON.stringify(s)),n(s.audio,"autoGainControl","mozAutoGainControl"),n(s.audio,"noiseSuppression","mozNoiseSuppression")),i(s)},o&&o.prototype.getSettings){const s=o.prototype.getSettings;o.prototype.getSettings=function(){const a=s.apply(this,arguments);return n(a,"mozAutoGainControl","autoGainControl"),n(a,"mozNoiseSuppression","noiseSuppression"),a}}if(o&&o.prototype.applyConstraints){const s=o.prototype.applyConstraints;o.prototype.applyConstraints=function(a){return this.kind==="audio"&&typeof a=="object"&&(a=JSON.parse(JSON.stringify(a)),n(a,"autoGainControl","mozAutoGainControl"),n(a,"noiseSuppression","mozNoiseSuppression")),s.apply(this,[a])}}}}function yf(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(o){if(!(o&&o.video)){const n=new DOMException("getDisplayMedia without video constraints is undefined");return n.name="NotFoundError",n.code=8,Promise.reject(n)}return o.video===!0?o.video={mediaSource:t}:o.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(o)})}function ac(e){typeof e=="object"&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Pi(e,t){if(typeof e!="object"||!(e.RTCPeerConnection||e.mozRTCPeerConnection))return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(n){const i=e.RTCPeerConnection.prototype[n],s={[n](){return arguments[0]=new(n==="addIceCandidate"?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),i.apply(this,arguments)}};e.RTCPeerConnection.prototype[n]=s[n]});const r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},o=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[i,s,a]=arguments;return o.apply(this,[i||null]).then(l=>{if(t.version<53&&!s)try{l.forEach(u=>{u.type=r[u.type]||u.type})}catch(u){if(u.name!=="TypeError")throw u;l.forEach((c,d)=>{l.set(d,Object.assign({},c,{type:r[c.type]||c.type}))})}return l}).then(s,a)}}function lc(e){if(!(typeof e=="object"&&e.RTCPeerConnection&&e.RTCRtpSender)||e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const n=t.apply(this,[]);return n.forEach(i=>i._pc=this),n});const r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){const n=r.apply(this,arguments);return n._pc=this,n}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function cc(e){if(!(typeof e=="object"&&e.RTCPeerConnection&&e.RTCRtpSender)||e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const o=t.apply(this,[]);return o.forEach(n=>n._pc=this),o}),vr(e,"track",r=>(r.receiver._pc=r.srcElement,r)),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function uc(e){!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(r){gs("removeStream","removeTrack"),this.getSenders().forEach(o=>{o.track&&r.getTracks().includes(o.track)&&this.removeTrack(o)})})}function dc(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function fc(e){if(!(typeof e=="object"&&e.RTCPeerConnection))return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let o=arguments[1]&&arguments[1].sendEncodings;o===void 0&&(o=[]),o=[...o];const n=o.length>0;n&&o.forEach(s=>{if("rid"in s&&!/^[a-z0-9]{0,16}$/i.test(s.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in s&&!(parseFloat(s.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in s&&!(parseFloat(s.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});const i=t.apply(this,arguments);if(n){const{sender:s}=i,a=s.getParameters();(!("encodings"in a)||a.encodings.length===1&&Object.keys(a.encodings[0]).length===0)&&(a.encodings=o,s.sendEncodings=o,this.setParametersPromises.push(s.setParameters(a).then(()=>{delete s.sendEncodings}).catch(()=>{delete s.sendEncodings})))}return i})}function pc(e){if(!(typeof e=="object"&&e.RTCRtpSender))return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const o=t.apply(this,arguments);return"encodings"in o||(o.encodings=[].concat(this.sendEncodings||[{}])),o})}function gc(e){if(!(typeof e=="object"&&e.RTCPeerConnection))return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}function hc(e){if(!(typeof e=="object"&&e.RTCPeerConnection))return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}const Ys=Object.freeze(Object.defineProperty({__proto__:null,shimAddTransceiver:fc,shimCreateAnswer:hc,shimCreateOffer:gc,shimGetDisplayMedia:yf,shimGetParameters:pc,shimGetUserMedia:sc,shimOnTrack:ac,shimPeerConnection:Pi,shimRTCDataChannel:dc,shimReceiverGetStats:cc,shimRemoveStream:uc,shimSenderGetStats:lc},Symbol.toStringTag,{value:"Module"}));function mc(e){if(!(typeof e!="object"||!e.RTCPeerConnection)){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(o){this._localStreams||(this._localStreams=[]),this._localStreams.includes(o)||this._localStreams.push(o),o.getAudioTracks().forEach(n=>t.call(this,n,o)),o.getVideoTracks().forEach(n=>t.call(this,n,o))},e.RTCPeerConnection.prototype.addTrack=function(o,...n){return n&&n.forEach(i=>{this._localStreams?this._localStreams.includes(i)||this._localStreams.push(i):this._localStreams=[i]}),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(r){this._localStreams||(this._localStreams=[]);const o=this._localStreams.indexOf(r);if(o===-1)return;this._localStreams.splice(o,1);const n=r.getTracks();this.getSenders().forEach(i=>{n.includes(i.track)&&this.removeTrack(i)})})}}function bc(e){if(!(typeof e!="object"||!e.RTCPeerConnection)&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(r){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=r),this.addEventListener("track",this._onaddstreampoly=o=>{o.streams.forEach(n=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(n))return;this._remoteStreams.push(n);const i=new Event("addstream");i.stream=n,this.dispatchEvent(i)})})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const o=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(n){n.streams.forEach(i=>{if(o._remoteStreams||(o._remoteStreams=[]),o._remoteStreams.indexOf(i)>=0)return;o._remoteStreams.push(i);const s=new Event("addstream");s.stream=i,o.dispatchEvent(s)})}),t.apply(o,arguments)}}}function vc(e){if(typeof e!="object"||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,r=t.createOffer,o=t.createAnswer,n=t.setLocalDescription,i=t.setRemoteDescription,s=t.addIceCandidate;t.createOffer=function(u,c){const d=arguments.length>=2?arguments[2]:arguments[0],f=r.apply(this,[d]);return c?(f.then(u,c),Promise.resolve()):f},t.createAnswer=function(u,c){const d=arguments.length>=2?arguments[2]:arguments[0],f=o.apply(this,[d]);return c?(f.then(u,c),Promise.resolve()):f};let a=function(l,u,c){const d=n.apply(this,[l]);return c?(d.then(u,c),Promise.resolve()):d};t.setLocalDescription=a,a=function(l,u,c){const d=i.apply(this,[l]);return c?(d.then(u,c),Promise.resolve()):d},t.setRemoteDescription=a,a=function(l,u,c){const d=s.apply(this,[l]);return c?(d.then(u,c),Promise.resolve()):d},t.addIceCandidate=a}function yc(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const r=t.mediaDevices,o=r.getUserMedia.bind(r);t.mediaDevices.getUserMedia=n=>o(Cc(n))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=(function(o,n,i){t.mediaDevices.getUserMedia(o).then(n,i)}).bind(t))}function Cc(e){return e&&e.video!==void 0?Object.assign({},e,{video:Xl(e.video)}):e}function Sc(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(o,n){if(o&&o.iceServers){const i=[];for(let s=0;s<o.iceServers.length;s++){let a=o.iceServers[s];a.urls===void 0&&a.url?(gs("RTCIceServer.url","RTCIceServer.urls"),a=JSON.parse(JSON.stringify(a)),a.urls=a.url,delete a.url,i.push(a)):i.push(o.iceServers[s])}o.iceServers=i}return new t(o,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get(){return t.generateCertificate}})}function kc(e){typeof e=="object"&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function _c(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(o){if(o){typeof o.offerToReceiveAudio<"u"&&(o.offerToReceiveAudio=!!o.offerToReceiveAudio);const n=this.getTransceivers().find(s=>s.receiver.track.kind==="audio");o.offerToReceiveAudio===!1&&n?n.direction==="sendrecv"?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":n.direction==="recvonly"&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):o.offerToReceiveAudio===!0&&!n&&this.addTransceiver("audio",{direction:"recvonly"}),typeof o.offerToReceiveVideo<"u"&&(o.offerToReceiveVideo=!!o.offerToReceiveVideo);const i=this.getTransceivers().find(s=>s.receiver.track.kind==="video");o.offerToReceiveVideo===!1&&i?i.direction==="sendrecv"?i.setDirection?i.setDirection("sendonly"):i.direction="sendonly":i.direction==="recvonly"&&(i.setDirection?i.setDirection("inactive"):i.direction="inactive"):o.offerToReceiveVideo===!0&&!i&&this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function $c(e){typeof e!="object"||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}const Xs=Object.freeze(Object.defineProperty({__proto__:null,shimAudioContext:$c,shimCallbacksAPI:vc,shimConstraints:Cc,shimCreateOfferLegacy:_c,shimGetUserMedia:yc,shimLocalStreamsAPI:mc,shimRTCIceServerUrls:Sc,shimRemoteStreamsAPI:bc,shimTrackEventTransceiver:kc},Symbol.toStringTag,{value:"Module"}));var vw=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Cf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var xc={exports:{}};(function(e){const t={};t.generateIdentifier=function(){return Math.random().toString(36).substring(2,12)},t.localCName=t.generateIdentifier(),t.splitLines=function(r){return r.trim().split(`
`).map(o=>o.trim())},t.splitSections=function(r){return r.split(`
m=`).map((n,i)=>(i>0?"m="+n:n).trim()+`\r
`)},t.getDescription=function(r){const o=t.splitSections(r);return o&&o[0]},t.getMediaSections=function(r){const o=t.splitSections(r);return o.shift(),o},t.matchPrefix=function(r,o){return t.splitLines(r).filter(n=>n.indexOf(o)===0)},t.parseCandidate=function(r){let o;r.indexOf("a=candidate:")===0?o=r.substring(12).split(" "):o=r.substring(10).split(" ");const n={foundation:o[0],component:{1:"rtp",2:"rtcp"}[o[1]]||o[1],protocol:o[2].toLowerCase(),priority:parseInt(o[3],10),ip:o[4],address:o[4],port:parseInt(o[5],10),type:o[7]};for(let i=8;i<o.length;i+=2)switch(o[i]){case"raddr":n.relatedAddress=o[i+1];break;case"rport":n.relatedPort=parseInt(o[i+1],10);break;case"tcptype":n.tcpType=o[i+1];break;case"ufrag":n.ufrag=o[i+1],n.usernameFragment=o[i+1];break;default:n[o[i]]===void 0&&(n[o[i]]=o[i+1]);break}return n},t.writeCandidate=function(r){const o=[];o.push(r.foundation);const n=r.component;n==="rtp"?o.push(1):n==="rtcp"?o.push(2):o.push(n),o.push(r.protocol.toUpperCase()),o.push(r.priority),o.push(r.address||r.ip),o.push(r.port);const i=r.type;return o.push("typ"),o.push(i),i!=="host"&&r.relatedAddress&&r.relatedPort&&(o.push("raddr"),o.push(r.relatedAddress),o.push("rport"),o.push(r.relatedPort)),r.tcpType&&r.protocol.toLowerCase()==="tcp"&&(o.push("tcptype"),o.push(r.tcpType)),(r.usernameFragment||r.ufrag)&&(o.push("ufrag"),o.push(r.usernameFragment||r.ufrag)),"candidate:"+o.join(" ")},t.parseIceOptions=function(r){return r.substring(14).split(" ")},t.parseRtpMap=function(r){let o=r.substring(9).split(" ");const n={payloadType:parseInt(o.shift(),10)};return o=o[0].split("/"),n.name=o[0],n.clockRate=parseInt(o[1],10),n.channels=o.length===3?parseInt(o[2],10):1,n.numChannels=n.channels,n},t.writeRtpMap=function(r){let o=r.payloadType;r.preferredPayloadType!==void 0&&(o=r.preferredPayloadType);const n=r.channels||r.numChannels||1;return"a=rtpmap:"+o+" "+r.name+"/"+r.clockRate+(n!==1?"/"+n:"")+`\r
`},t.parseExtmap=function(r){const o=r.substring(9).split(" ");return{id:parseInt(o[0],10),direction:o[0].indexOf("/")>0?o[0].split("/")[1]:"sendrecv",uri:o[1],attributes:o.slice(2).join(" ")}},t.writeExtmap=function(r){return"a=extmap:"+(r.id||r.preferredId)+(r.direction&&r.direction!=="sendrecv"?"/"+r.direction:"")+" "+r.uri+(r.attributes?" "+r.attributes:"")+`\r
`},t.parseFmtp=function(r){const o={};let n;const i=r.substring(r.indexOf(" ")+1).split(";");for(let s=0;s<i.length;s++)n=i[s].trim().split("="),o[n[0].trim()]=n[1];return o},t.writeFmtp=function(r){let o="",n=r.payloadType;if(r.preferredPayloadType!==void 0&&(n=r.preferredPayloadType),r.parameters&&Object.keys(r.parameters).length){const i=[];Object.keys(r.parameters).forEach(s=>{r.parameters[s]!==void 0?i.push(s+"="+r.parameters[s]):i.push(s)}),o+="a=fmtp:"+n+" "+i.join(";")+`\r
`}return o},t.parseRtcpFb=function(r){const o=r.substring(r.indexOf(" ")+1).split(" ");return{type:o.shift(),parameter:o.join(" ")}},t.writeRtcpFb=function(r){let o="",n=r.payloadType;return r.preferredPayloadType!==void 0&&(n=r.preferredPayloadType),r.rtcpFeedback&&r.rtcpFeedback.length&&r.rtcpFeedback.forEach(i=>{o+="a=rtcp-fb:"+n+" "+i.type+(i.parameter&&i.parameter.length?" "+i.parameter:"")+`\r
`}),o},t.parseSsrcMedia=function(r){const o=r.indexOf(" "),n={ssrc:parseInt(r.substring(7,o),10)},i=r.indexOf(":",o);return i>-1?(n.attribute=r.substring(o+1,i),n.value=r.substring(i+1)):n.attribute=r.substring(o+1),n},t.parseSsrcGroup=function(r){const o=r.substring(13).split(" ");return{semantics:o.shift(),ssrcs:o.map(n=>parseInt(n,10))}},t.getMid=function(r){const o=t.matchPrefix(r,"a=mid:")[0];if(o)return o.substring(6)},t.parseFingerprint=function(r){const o=r.substring(14).split(" ");return{algorithm:o[0].toLowerCase(),value:o[1].toUpperCase()}},t.getDtlsParameters=function(r,o){return{role:"auto",fingerprints:t.matchPrefix(r+o,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(r,o){let n="a=setup:"+o+`\r
`;return r.fingerprints.forEach(i=>{n+="a=fingerprint:"+i.algorithm+" "+i.value+`\r
`}),n},t.parseCryptoLine=function(r){const o=r.substring(9).split(" ");return{tag:parseInt(o[0],10),cryptoSuite:o[1],keyParams:o[2],sessionParams:o.slice(3)}},t.writeCryptoLine=function(r){return"a=crypto:"+r.tag+" "+r.cryptoSuite+" "+(typeof r.keyParams=="object"?t.writeCryptoKeyParams(r.keyParams):r.keyParams)+(r.sessionParams?" "+r.sessionParams.join(" "):"")+`\r
`},t.parseCryptoKeyParams=function(r){if(r.indexOf("inline:")!==0)return null;const o=r.substring(7).split("|");return{keyMethod:"inline",keySalt:o[0],lifeTime:o[1],mkiValue:o[2]?o[2].split(":")[0]:void 0,mkiLength:o[2]?o[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(r){return r.keyMethod+":"+r.keySalt+(r.lifeTime?"|"+r.lifeTime:"")+(r.mkiValue&&r.mkiLength?"|"+r.mkiValue+":"+r.mkiLength:"")},t.getCryptoParameters=function(r,o){return t.matchPrefix(r+o,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(r,o){const n=t.matchPrefix(r+o,"a=ice-ufrag:")[0],i=t.matchPrefix(r+o,"a=ice-pwd:")[0];return n&&i?{usernameFragment:n.substring(12),password:i.substring(10)}:null},t.writeIceParameters=function(r){let o="a=ice-ufrag:"+r.usernameFragment+`\r
a=ice-pwd:`+r.password+`\r
`;return r.iceLite&&(o+=`a=ice-lite\r
`),o},t.parseRtpParameters=function(r){const o={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},i=t.splitLines(r)[0].split(" ");o.profile=i[2];for(let a=3;a<i.length;a++){const l=i[a],u=t.matchPrefix(r,"a=rtpmap:"+l+" ")[0];if(u){const c=t.parseRtpMap(u),d=t.matchPrefix(r,"a=fmtp:"+l+" ");switch(c.parameters=d.length?t.parseFmtp(d[0]):{},c.rtcpFeedback=t.matchPrefix(r,"a=rtcp-fb:"+l+" ").map(t.parseRtcpFb),o.codecs.push(c),c.name.toUpperCase()){case"RED":case"ULPFEC":o.fecMechanisms.push(c.name.toUpperCase());break}}}t.matchPrefix(r,"a=extmap:").forEach(a=>{o.headerExtensions.push(t.parseExtmap(a))});const s=t.matchPrefix(r,"a=rtcp-fb:* ").map(t.parseRtcpFb);return o.codecs.forEach(a=>{s.forEach(l=>{a.rtcpFeedback.find(c=>c.type===l.type&&c.parameter===l.parameter)||a.rtcpFeedback.push(l)})}),o},t.writeRtpDescription=function(r,o){let n="";n+="m="+r+" ",n+=o.codecs.length>0?"9":"0",n+=" "+(o.profile||"UDP/TLS/RTP/SAVPF")+" ",n+=o.codecs.map(s=>s.preferredPayloadType!==void 0?s.preferredPayloadType:s.payloadType).join(" ")+`\r
`,n+=`c=IN IP4 0.0.0.0\r
`,n+=`a=rtcp:9 IN IP4 0.0.0.0\r
`,o.codecs.forEach(s=>{n+=t.writeRtpMap(s),n+=t.writeFmtp(s),n+=t.writeRtcpFb(s)});let i=0;return o.codecs.forEach(s=>{s.maxptime>i&&(i=s.maxptime)}),i>0&&(n+="a=maxptime:"+i+`\r
`),o.headerExtensions&&o.headerExtensions.forEach(s=>{n+=t.writeExtmap(s)}),n},t.parseRtpEncodingParameters=function(r){const o=[],n=t.parseRtpParameters(r),i=n.fecMechanisms.indexOf("RED")!==-1,s=n.fecMechanisms.indexOf("ULPFEC")!==-1,a=t.matchPrefix(r,"a=ssrc:").map(f=>t.parseSsrcMedia(f)).filter(f=>f.attribute==="cname"),l=a.length>0&&a[0].ssrc;let u;const c=t.matchPrefix(r,"a=ssrc-group:FID").map(f=>f.substring(17).split(" ").map(h=>parseInt(h,10)));c.length>0&&c[0].length>1&&c[0][0]===l&&(u=c[0][1]),n.codecs.forEach(f=>{if(f.name.toUpperCase()==="RTX"&&f.parameters.apt){let p={ssrc:l,codecPayloadType:parseInt(f.parameters.apt,10)};l&&u&&(p.rtx={ssrc:u}),o.push(p),i&&(p=JSON.parse(JSON.stringify(p)),p.fec={ssrc:l,mechanism:s?"red+ulpfec":"red"},o.push(p))}}),o.length===0&&l&&o.push({ssrc:l});let d=t.matchPrefix(r,"b=");return d.length&&(d[0].indexOf("b=TIAS:")===0?d=parseInt(d[0].substring(7),10):d[0].indexOf("b=AS:")===0?d=parseInt(d[0].substring(5),10)*1e3*.95-50*40*8:d=void 0,o.forEach(f=>{f.maxBitrate=d})),o},t.parseRtcpParameters=function(r){const o={},n=t.matchPrefix(r,"a=ssrc:").map(a=>t.parseSsrcMedia(a)).filter(a=>a.attribute==="cname")[0];n&&(o.cname=n.value,o.ssrc=n.ssrc);const i=t.matchPrefix(r,"a=rtcp-rsize");o.reducedSize=i.length>0,o.compound=i.length===0;const s=t.matchPrefix(r,"a=rtcp-mux");return o.mux=s.length>0,o},t.writeRtcpParameters=function(r){let o="";return r.reducedSize&&(o+=`a=rtcp-rsize\r
`),r.mux&&(o+=`a=rtcp-mux\r
`),r.ssrc!==void 0&&r.cname&&(o+="a=ssrc:"+r.ssrc+" cname:"+r.cname+`\r
`),o},t.parseMsid=function(r){let o;const n=t.matchPrefix(r,"a=msid:");if(n.length===1)return o=n[0].substring(7).split(" "),{stream:o[0],track:o[1]};const i=t.matchPrefix(r,"a=ssrc:").map(s=>t.parseSsrcMedia(s)).filter(s=>s.attribute==="msid");if(i.length>0)return o=i[0].value.split(" "),{stream:o[0],track:o[1]}},t.parseSctpDescription=function(r){const o=t.parseMLine(r),n=t.matchPrefix(r,"a=max-message-size:");let i;n.length>0&&(i=parseInt(n[0].substring(19),10)),isNaN(i)&&(i=65536);const s=t.matchPrefix(r,"a=sctp-port:");if(s.length>0)return{port:parseInt(s[0].substring(12),10),protocol:o.fmt,maxMessageSize:i};const a=t.matchPrefix(r,"a=sctpmap:");if(a.length>0){const l=a[0].substring(10).split(" ");return{port:parseInt(l[0],10),protocol:l[1],maxMessageSize:i}}},t.writeSctpDescription=function(r,o){let n=[];return r.protocol!=="DTLS/SCTP"?n=["m="+r.kind+" 9 "+r.protocol+" "+o.protocol+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctp-port:"+o.port+`\r
`]:n=["m="+r.kind+" 9 "+r.protocol+" "+o.port+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctpmap:"+o.port+" "+o.protocol+` 65535\r
`],o.maxMessageSize!==void 0&&n.push("a=max-message-size:"+o.maxMessageSize+`\r
`),n.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,22)},t.writeSessionBoilerplate=function(r,o,n){let i;const s=o!==void 0?o:2;return r?i=r:i=t.generateSessionId(),`v=0\r
o=`+(n||"thisisadapterortc")+" "+i+" "+s+` IN IP4 127.0.0.1\r
s=-\r
t=0 0\r
`},t.getDirection=function(r,o){const n=t.splitLines(r);for(let i=0;i<n.length;i++)switch(n[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[i].substring(2)}return o?t.getDirection(o):"sendrecv"},t.getKind=function(r){return t.splitLines(r)[0].split(" ")[0].substring(2)},t.isRejected=function(r){return r.split(" ",2)[1]==="0"},t.parseMLine=function(r){const n=t.splitLines(r)[0].substring(2).split(" ");return{kind:n[0],port:parseInt(n[1],10),protocol:n[2],fmt:n.slice(3).join(" ")}},t.parseOLine=function(r){const n=t.matchPrefix(r,"o=")[0].substring(2).split(" ");return{username:n[0],sessionId:n[1],sessionVersion:parseInt(n[2],10),netType:n[3],addressType:n[4],address:n[5]}},t.isValidSDP=function(r){if(typeof r!="string"||r.length===0)return!1;const o=t.splitLines(r);for(let n=0;n<o.length;n++)if(o[n].length<2||o[n].charAt(1)!=="=")return!1;return!0},e.exports=t})(xc);var Rc=xc.exports;const wr=Cf(Rc),Sf=hf({__proto__:null,default:wr},[Rc]);function en(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(o){if(typeof o=="object"&&o.candidate&&o.candidate.indexOf("a=")===0&&(o=JSON.parse(JSON.stringify(o)),o.candidate=o.candidate.substring(2)),o.candidate&&o.candidate.length){const n=new t(o),i=wr.parseCandidate(o.candidate);for(const s in i)s in n||Object.defineProperty(n,s,{value:i[s]});return n.toJSON=function(){return{candidate:n.candidate,sdpMid:n.sdpMid,sdpMLineIndex:n.sdpMLineIndex,usernameFragment:n.usernameFragment}},n}return new t(o)},e.RTCIceCandidate.prototype=t.prototype,vr(e,"icecandidate",r=>(r.candidate&&Object.defineProperty(r,"candidate",{value:new e.RTCIceCandidate(r.candidate),writable:"false"}),r))}function Ei(e){!e.RTCIceCandidate||e.RTCIceCandidate&&"relayProtocol"in e.RTCIceCandidate.prototype||vr(e,"icecandidate",t=>{if(t.candidate){const r=wr.parseCandidate(t.candidate.candidate);r.type==="relay"&&(t.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[r.priority>>24])}return t})}function tn(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp}});const r=function(a){if(!a||!a.sdp)return!1;const l=wr.splitSections(a.sdp);return l.shift(),l.some(u=>{const c=wr.parseMLine(u);return c&&c.kind==="application"&&c.protocol.indexOf("SCTP")!==-1})},o=function(a){const l=a.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(l===null||l.length<2)return-1;const u=parseInt(l[1],10);return u!==u?-1:u},n=function(a){let l=65536;return t.browser==="firefox"&&(t.version<57?a===-1?l=16384:l=2147483637:t.version<60?l=t.version===57?65535:65536:l=2147483637),l},i=function(a,l){let u=65536;t.browser==="firefox"&&t.version===57&&(u=65535);const c=wr.matchPrefix(a.sdp,"a=max-message-size:");return c.length>0?u=parseInt(c[0].substring(19),10):t.browser==="firefox"&&l!==-1&&(u=2147483637),u},s=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,t.browser==="chrome"&&t.version>=76){const{sdpSemantics:l}=this.getConfiguration();l==="plan-b"&&Object.defineProperty(this,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp},enumerable:!0,configurable:!0})}if(r(arguments[0])){const l=o(arguments[0]),u=n(l),c=i(arguments[0],l);let d;u===0&&c===0?d=Number.POSITIVE_INFINITY:u===0||c===0?d=Math.max(u,c):d=Math.min(u,c);const f={};Object.defineProperty(f,"maxMessageSize",{get(){return d}}),this._sctp=f}return s.apply(this,arguments)}}function rn(e){if(!(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype))return;function t(o,n){const i=o.send;o.send=function(){const a=arguments[0],l=a.length||a.size||a.byteLength;if(o.readyState==="open"&&n.sctp&&l>n.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+n.sctp.maxMessageSize+" bytes)");return i.apply(o,arguments)}}const r=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const n=r.apply(this,arguments);return t(n,this),n},vr(e,"datachannel",o=>(t(o.channel,o.target),o))}function Oi(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(r){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),r&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=r)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(r=>{const o=t[r];t[r]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=n=>{const i=n.target;if(i._lastConnectionState!==i.connectionState){i._lastConnectionState=i.connectionState;const s=new Event("connectionstatechange",n);i.dispatchEvent(s)}return n},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),o.apply(this,arguments)}})}function Bi(e,t){if(!e.RTCPeerConnection||t.browser==="chrome"&&t.version>=71||t.browser==="safari"&&t.version>=605)return;const r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(n){if(n&&n.sdp&&n.sdp.indexOf(`
a=extmap-allow-mixed`)!==-1){const i=n.sdp.split(`
`).filter(s=>s.trim()!=="a=extmap-allow-mixed").join(`
`);e.RTCSessionDescription&&n instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:n.type,sdp:i}):n.sdp=i}return r.apply(this,arguments)}}function on(e,t){if(!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype))return;const r=e.RTCPeerConnection.prototype.addIceCandidate;!r||r.length===0||(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?(t.browser==="chrome"&&t.version<78||t.browser==="firefox"&&t.version<68||t.browser==="safari")&&arguments[0]&&arguments[0].candidate===""?Promise.resolve():r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function nn(e,t){if(!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype))return;const r=e.RTCPeerConnection.prototype.setLocalDescription;!r||r.length===0||(e.RTCPeerConnection.prototype.setLocalDescription=function(){let n=arguments[0]||{};if(typeof n!="object"||n.type&&n.sdp)return r.apply(this,arguments);if(n={type:n.type,sdp:n.sdp},!n.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":n.type="offer";break;default:n.type="answer";break}return n.sdp||n.type!=="offer"&&n.type!=="answer"?r.apply(this,[n]):(n.type==="offer"?this.createOffer:this.createAnswer).apply(this).then(s=>r.apply(this,[s]))})}const kf=Object.freeze(Object.defineProperty({__proto__:null,removeExtmapAllowMixed:Bi,shimAddIceCandidateNullOrEmpty:on,shimConnectionState:Oi,shimMaxMessageSize:tn,shimParameterlessSetLocalDescription:nn,shimRTCIceCandidate:en,shimRTCIceCandidateRelayProtocol:Ei,shimSendThrowTypeError:rn},Symbol.toStringTag,{value:"Module"}));function _f({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const r=Yl,o=vf(e),n={browserDetails:o,commonShim:kf,extractVersion:Qo,disableLog:mf,disableWarnings:bf,sdp:Sf};switch(o.browser){case"chrome":if(!Js||!Ti||!t.shimChrome)return r("Chrome shim is not included in this adapter release."),n;if(o.version===null)return r("Chrome shim can not determine version, not shimming."),n;r("adapter.js shimming chrome."),n.browserShim=Js,on(e,o),nn(e),Zl(e,o),Ql(e),Ti(e,o),ec(e),nc(e,o),tc(e),rc(e),ic(e,o),en(e),Ei(e),Oi(e),tn(e,o),rn(e),Bi(e,o);break;case"firefox":if(!Ys||!Pi||!t.shimFirefox)return r("Firefox shim is not included in this adapter release."),n;r("adapter.js shimming firefox."),n.browserShim=Ys,on(e,o),nn(e),sc(e,o),Pi(e,o),ac(e),uc(e),lc(e),cc(e),dc(e),fc(e),pc(e),gc(e),hc(e),en(e),Oi(e),tn(e,o),rn(e);break;case"safari":if(!Xs||!t.shimSafari)return r("Safari shim is not included in this adapter release."),n;r("adapter.js shimming safari."),n.browserShim=Xs,on(e,o),nn(e),Sc(e),_c(e),vc(e),mc(e),bc(e),kc(e),yc(e),$c(e),en(e),Ei(e),tn(e,o),rn(e),Bi(e,o);break;default:r("Unsupported browser!");break}return n}const $f=_f({window:typeof window>"u"?void 0:window});/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function hs(e){const t=Object.create(null);for(const r of e.split(","))t[r]=1;return r=>r in t}const me={},Tr=[],wt=()=>{},xf=()=>!1,An=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ms=e=>e.startsWith("onUpdate:"),Ee=Object.assign,bs=(e,t)=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)},Rf=Object.prototype.hasOwnProperty,le=(e,t)=>Rf.call(e,t),q=Array.isArray,Pr=e=>Ln(e)==="[object Map]",wc=e=>Ln(e)==="[object Set]",Y=e=>typeof e=="function",ke=e=>typeof e=="string",jt=e=>typeof e=="symbol",Ce=e=>e!==null&&typeof e=="object",Tc=e=>(Ce(e)||Y(e))&&Y(e.then)&&Y(e.catch),Pc=Object.prototype.toString,Ln=e=>Pc.call(e),wf=e=>Ln(e).slice(8,-1),Ec=e=>Ln(e)==="[object Object]",vs=e=>ke(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Yr=hs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),In=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},Tf=/-(\w)/g,lt=In(e=>e.replace(Tf,(t,r)=>r?r.toUpperCase():"")),Pf=/\B([A-Z])/g,yr=In(e=>e.replace(Pf,"-$1").toLowerCase()),Dn=In(e=>e.charAt(0).toUpperCase()+e.slice(1)),ii=In(e=>e?`on${Dn(e)}`:""),Xt=(e,t)=>!Object.is(e,t),si=(e,...t)=>{for(let r=0;r<e.length;r++)e[r](...t)},Oc=(e,t,r,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:r})},Ef=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Of=e=>{const t=ke(e)?Number(e):NaN;return isNaN(t)?e:t};let Zs;const jn=()=>Zs||(Zs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Mn(e){if(q(e)){const t={};for(let r=0;r<e.length;r++){const o=e[r],n=ke(o)?If(o):Mn(o);if(n)for(const i in n)t[i]=n[i]}return t}else if(ke(e)||Ce(e))return e}const Bf=/;(?![^(]*\))/g,Af=/:([^]+)/,Lf=/\/\*[^]*?\*\//g;function If(e){const t={};return e.replace(Lf,"").split(Bf).forEach(r=>{if(r){const o=r.split(Af);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function Ir(e){let t="";if(ke(e))t=e;else if(q(e))for(let r=0;r<e.length;r++){const o=Ir(e[r]);o&&(t+=o+" ")}else if(Ce(e))for(const r in e)e[r]&&(t+=r+" ");return t.trim()}function Df(e){if(!e)return null;let{class:t,style:r}=e;return t&&!ke(t)&&(e.class=Ir(t)),r&&(e.style=Mn(r)),e}const jf="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Mf=hs(jf);function Bc(e){return!!e||e===""}const Ac=e=>!!(e&&e.__v_isRef===!0),lo=e=>ke(e)?e:e==null?"":q(e)||Ce(e)&&(e.toString===Pc||!Y(e.toString))?Ac(e)?lo(e.value):JSON.stringify(e,Lc,2):String(e),Lc=(e,t)=>Ac(t)?Lc(e,t.value):Pr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((r,[o,n],i)=>(r[ai(o,i)+" =>"]=n,r),{})}:wc(t)?{[`Set(${t.size})`]:[...t.values()].map(r=>ai(r))}:jt(t)?ai(t):Ce(t)&&!q(t)&&!Ec(t)?String(t):t,ai=(e,t="")=>{var r;return jt(e)?`Symbol(${(r=e.description)!=null?r:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ue;class Ic{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ue,!t&&Ue&&(this.index=(Ue.scopes||(Ue.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].pause();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,r;if(this.scopes)for(t=0,r=this.scopes.length;t<r;t++)this.scopes[t].resume();for(t=0,r=this.effects.length;t<r;t++)this.effects[t].resume()}}run(t){if(this._active){const r=Ue;try{return Ue=this,t()}finally{Ue=r}}}on(){Ue=this}off(){Ue=this.parent}stop(t){if(this._active){this._active=!1;let r,o;for(r=0,o=this.effects.length;r<o;r++)this.effects[r].stop();for(this.effects.length=0,r=0,o=this.cleanups.length;r<o;r++)this.cleanups[r]();if(this.cleanups.length=0,this.scopes){for(r=0,o=this.scopes.length;r<o;r++)this.scopes[r].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function Dc(e){return new Ic(e)}function jc(){return Ue}function Nf(e,t=!1){Ue&&Ue.cleanups.push(e)}let ve;const li=new WeakSet;class Mc{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ue&&Ue.active&&Ue.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,li.has(this)&&(li.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Fc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Qs(this),zc(this);const t=ve,r=pt;ve=this,pt=!0;try{return this.fn()}finally{Hc(this),ve=t,pt=r,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ss(t);this.deps=this.depsTail=void 0,Qs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?li.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ai(this)&&this.run()}get dirty(){return Ai(this)}}let Nc=0,Xr,Zr;function Fc(e,t=!1){if(e.flags|=8,t){e.next=Zr,Zr=e;return}e.next=Xr,Xr=e}function ys(){Nc++}function Cs(){if(--Nc>0)return;if(Zr){let t=Zr;for(Zr=void 0;t;){const r=t.next;t.next=void 0,t.flags&=-9,t=r}}let e;for(;Xr;){let t=Xr;for(Xr=void 0;t;){const r=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(o){e||(e=o)}t=r}}if(e)throw e}function zc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Hc(e){let t,r=e.depsTail,o=r;for(;o;){const n=o.prevDep;o.version===-1?(o===r&&(r=n),Ss(o),Ff(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=n}e.deps=t,e.depsTail=r}function Ai(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Uc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Uc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===co))return;e.globalVersion=co;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Ai(e)){e.flags&=-3;return}const r=ve,o=pt;ve=e,pt=!0;try{zc(e);const n=e.fn(e._value);(t.version===0||Xt(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{ve=r,pt=o,Hc(e),e.flags&=-3}}function Ss(e,t=!1){const{dep:r,prevSub:o,nextSub:n}=e;if(o&&(o.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=o,e.nextSub=void 0),r.subs===e&&(r.subs=o,!o&&r.computed)){r.computed.flags&=-5;for(let i=r.computed.deps;i;i=i.nextDep)Ss(i,!0)}!t&&!--r.sc&&r.map&&r.map.delete(r.key)}function Ff(e){const{prevDep:t,nextDep:r}=e;t&&(t.nextDep=r,e.prevDep=void 0),r&&(r.prevDep=t,e.nextDep=void 0)}let pt=!0;const Vc=[];function rr(){Vc.push(pt),pt=!1}function or(){const e=Vc.pop();pt=e===void 0?!0:e}function Qs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const r=ve;ve=void 0;try{t()}finally{ve=r}}}let co=0;class zf{constructor(t,r){this.sub=t,this.dep=r,this.version=r.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Nn{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ve||!pt||ve===this.computed)return;let r=this.activeLink;if(r===void 0||r.sub!==ve)r=this.activeLink=new zf(ve,this),ve.deps?(r.prevDep=ve.depsTail,ve.depsTail.nextDep=r,ve.depsTail=r):ve.deps=ve.depsTail=r,Wc(r);else if(r.version===-1&&(r.version=this.version,r.nextDep)){const o=r.nextDep;o.prevDep=r.prevDep,r.prevDep&&(r.prevDep.nextDep=o),r.prevDep=ve.depsTail,r.nextDep=void 0,ve.depsTail.nextDep=r,ve.depsTail=r,ve.deps===r&&(ve.deps=o)}return r}trigger(t){this.version++,co++,this.notify(t)}notify(t){ys();try{for(let r=this.subs;r;r=r.prevSub)r.sub.notify()&&r.sub.dep.notify()}finally{Cs()}}}function Wc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let o=t.deps;o;o=o.nextDep)Wc(o)}const r=e.dep.subs;r!==e&&(e.prevSub=r,r&&(r.nextSub=e)),e.dep.subs=e}}const bn=new WeakMap,fr=Symbol(""),Li=Symbol(""),uo=Symbol("");function je(e,t,r){if(pt&&ve){let o=bn.get(e);o||bn.set(e,o=new Map);let n=o.get(r);n||(o.set(r,n=new Nn),n.map=o,n.key=r),n.track()}}function Lt(e,t,r,o,n,i){const s=bn.get(e);if(!s){co++;return}const a=l=>{l&&l.trigger()};if(ys(),t==="clear")s.forEach(a);else{const l=q(e),u=l&&vs(r);if(l&&r==="length"){const c=Number(o);s.forEach((d,f)=>{(f==="length"||f===uo||!jt(f)&&f>=c)&&a(d)})}else switch((r!==void 0||s.has(void 0))&&a(s.get(r)),u&&a(s.get(uo)),t){case"add":l?u&&a(s.get("length")):(a(s.get(fr)),Pr(e)&&a(s.get(Li)));break;case"delete":l||(a(s.get(fr)),Pr(e)&&a(s.get(Li)));break;case"set":Pr(e)&&a(s.get(fr));break}}Cs()}function Hf(e,t){const r=bn.get(e);return r&&r.get(t)}function Sr(e){const t=ne(e);return t===e?t:(je(t,"iterate",uo),at(e)?t:t.map(Me))}function Fn(e){return je(e=ne(e),"iterate",uo),e}const Uf={__proto__:null,[Symbol.iterator](){return ci(this,Symbol.iterator,Me)},concat(...e){return Sr(this).concat(...e.map(t=>q(t)?Sr(t):t))},entries(){return ci(this,"entries",e=>(e[1]=Me(e[1]),e))},every(e,t){return Et(this,"every",e,t,void 0,arguments)},filter(e,t){return Et(this,"filter",e,t,r=>r.map(Me),arguments)},find(e,t){return Et(this,"find",e,t,Me,arguments)},findIndex(e,t){return Et(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Et(this,"findLast",e,t,Me,arguments)},findLastIndex(e,t){return Et(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Et(this,"forEach",e,t,void 0,arguments)},includes(...e){return ui(this,"includes",e)},indexOf(...e){return ui(this,"indexOf",e)},join(e){return Sr(this).join(e)},lastIndexOf(...e){return ui(this,"lastIndexOf",e)},map(e,t){return Et(this,"map",e,t,void 0,arguments)},pop(){return zr(this,"pop")},push(...e){return zr(this,"push",e)},reduce(e,...t){return ea(this,"reduce",e,t)},reduceRight(e,...t){return ea(this,"reduceRight",e,t)},shift(){return zr(this,"shift")},some(e,t){return Et(this,"some",e,t,void 0,arguments)},splice(...e){return zr(this,"splice",e)},toReversed(){return Sr(this).toReversed()},toSorted(e){return Sr(this).toSorted(e)},toSpliced(...e){return Sr(this).toSpliced(...e)},unshift(...e){return zr(this,"unshift",e)},values(){return ci(this,"values",Me)}};function ci(e,t,r){const o=Fn(e),n=o[t]();return o!==e&&!at(e)&&(n._next=n.next,n.next=()=>{const i=n._next();return i.value&&(i.value=r(i.value)),i}),n}const Vf=Array.prototype;function Et(e,t,r,o,n,i){const s=Fn(e),a=s!==e&&!at(e),l=s[t];if(l!==Vf[t]){const d=l.apply(e,i);return a?Me(d):d}let u=r;s!==e&&(a?u=function(d,f){return r.call(this,Me(d),f,e)}:r.length>2&&(u=function(d,f){return r.call(this,d,f,e)}));const c=l.call(s,u,o);return a&&n?n(c):c}function ea(e,t,r,o){const n=Fn(e);let i=r;return n!==e&&(at(e)?r.length>3&&(i=function(s,a,l){return r.call(this,s,a,l,e)}):i=function(s,a,l){return r.call(this,s,Me(a),l,e)}),n[t](i,...o)}function ui(e,t,r){const o=ne(e);je(o,"iterate",uo);const n=o[t](...r);return(n===-1||n===!1)&&xs(r[0])?(r[0]=ne(r[0]),o[t](...r)):n}function zr(e,t,r=[]){rr(),ys();const o=ne(e)[t].apply(e,r);return Cs(),or(),o}const Wf=hs("__proto__,__v_isRef,__isVue"),Kc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(jt));function Kf(e){jt(e)||(e=String(e));const t=ne(this);return je(t,"has",e),t.hasOwnProperty(e)}class qc{constructor(t=!1,r=!1){this._isReadonly=t,this._isShallow=r}get(t,r,o){if(r==="__v_skip")return t.__v_skip;const n=this._isReadonly,i=this._isShallow;if(r==="__v_isReactive")return!n;if(r==="__v_isReadonly")return n;if(r==="__v_isShallow")return i;if(r==="__v_raw")return o===(n?i?rp:Xc:i?Yc:Jc).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const s=q(t);if(!n){let l;if(s&&(l=Uf[r]))return l;if(r==="hasOwnProperty")return Kf}const a=Reflect.get(t,r,xe(t)?t:o);return(jt(r)?Kc.has(r):Wf(r))||(n||je(t,"get",r),i)?a:xe(a)?s&&vs(r)?a:a.value:Ce(a)?n?_s(a):Dr(a):a}}class Gc extends qc{constructor(t=!1){super(!1,t)}set(t,r,o,n){let i=t[r];if(!this._isShallow){const l=hr(i);if(!at(o)&&!hr(o)&&(i=ne(i),o=ne(o)),!q(t)&&xe(i)&&!xe(o))return l?!1:(i.value=o,!0)}const s=q(t)&&vs(r)?Number(r)<t.length:le(t,r),a=Reflect.set(t,r,o,xe(t)?t:n);return t===ne(n)&&(s?Xt(o,i)&&Lt(t,"set",r,o):Lt(t,"add",r,o)),a}deleteProperty(t,r){const o=le(t,r);t[r];const n=Reflect.deleteProperty(t,r);return n&&o&&Lt(t,"delete",r,void 0),n}has(t,r){const o=Reflect.has(t,r);return(!jt(r)||!Kc.has(r))&&je(t,"has",r),o}ownKeys(t){return je(t,"iterate",q(t)?"length":fr),Reflect.ownKeys(t)}}class qf extends qc{constructor(t=!1){super(!0,t)}set(t,r){return!0}deleteProperty(t,r){return!0}}const Gf=new Gc,Jf=new qf,Yf=new Gc(!0);const Ii=e=>e,Wo=e=>Reflect.getPrototypeOf(e);function Xf(e,t,r){return function(...o){const n=this.__v_raw,i=ne(n),s=Pr(i),a=e==="entries"||e===Symbol.iterator&&s,l=e==="keys"&&s,u=n[e](...o),c=r?Ii:t?Di:Me;return!t&&je(i,"iterate",l?Li:fr),{next(){const{value:d,done:f}=u.next();return f?{value:d,done:f}:{value:a?[c(d[0]),c(d[1])]:c(d),done:f}},[Symbol.iterator](){return this}}}}function Ko(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Zf(e,t){const r={get(n){const i=this.__v_raw,s=ne(i),a=ne(n);e||(Xt(n,a)&&je(s,"get",n),je(s,"get",a));const{has:l}=Wo(s),u=t?Ii:e?Di:Me;if(l.call(s,n))return u(i.get(n));if(l.call(s,a))return u(i.get(a));i!==s&&i.get(n)},get size(){const n=this.__v_raw;return!e&&je(ne(n),"iterate",fr),Reflect.get(n,"size",n)},has(n){const i=this.__v_raw,s=ne(i),a=ne(n);return e||(Xt(n,a)&&je(s,"has",n),je(s,"has",a)),n===a?i.has(n):i.has(n)||i.has(a)},forEach(n,i){const s=this,a=s.__v_raw,l=ne(a),u=t?Ii:e?Di:Me;return!e&&je(l,"iterate",fr),a.forEach((c,d)=>n.call(i,u(c),u(d),s))}};return Ee(r,e?{add:Ko("add"),set:Ko("set"),delete:Ko("delete"),clear:Ko("clear")}:{add(n){!t&&!at(n)&&!hr(n)&&(n=ne(n));const i=ne(this);return Wo(i).has.call(i,n)||(i.add(n),Lt(i,"add",n,n)),this},set(n,i){!t&&!at(i)&&!hr(i)&&(i=ne(i));const s=ne(this),{has:a,get:l}=Wo(s);let u=a.call(s,n);u||(n=ne(n),u=a.call(s,n));const c=l.call(s,n);return s.set(n,i),u?Xt(i,c)&&Lt(s,"set",n,i):Lt(s,"add",n,i),this},delete(n){const i=ne(this),{has:s,get:a}=Wo(i);let l=s.call(i,n);l||(n=ne(n),l=s.call(i,n)),a&&a.call(i,n);const u=i.delete(n);return l&&Lt(i,"delete",n,void 0),u},clear(){const n=ne(this),i=n.size!==0,s=n.clear();return i&&Lt(n,"clear",void 0,void 0),s}}),["keys","values","entries",Symbol.iterator].forEach(n=>{r[n]=Xf(n,e,t)}),r}function ks(e,t){const r=Zf(e,t);return(o,n,i)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?o:Reflect.get(le(r,n)&&n in o?r:o,n,i)}const Qf={get:ks(!1,!1)},ep={get:ks(!1,!0)},tp={get:ks(!0,!1)};const Jc=new WeakMap,Yc=new WeakMap,Xc=new WeakMap,rp=new WeakMap;function op(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function np(e){return e.__v_skip||!Object.isExtensible(e)?0:op(wf(e))}function Dr(e){return hr(e)?e:$s(e,!1,Gf,Qf,Jc)}function Zc(e){return $s(e,!1,Yf,ep,Yc)}function _s(e){return $s(e,!0,Jf,tp,Xc)}function $s(e,t,r,o,n){if(!Ce(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=n.get(e);if(i)return i;const s=np(e);if(s===0)return e;const a=new Proxy(e,s===2?o:r);return n.set(e,a),a}function Zt(e){return hr(e)?Zt(e.__v_raw):!!(e&&e.__v_isReactive)}function hr(e){return!!(e&&e.__v_isReadonly)}function at(e){return!!(e&&e.__v_isShallow)}function xs(e){return e?!!e.__v_raw:!1}function ne(e){const t=e&&e.__v_raw;return t?ne(t):e}function Rs(e){return!le(e,"__v_skip")&&Object.isExtensible(e)&&Oc(e,"__v_skip",!0),e}const Me=e=>Ce(e)?Dr(e):e,Di=e=>Ce(e)?_s(e):e;function xe(e){return e?e.__v_isRef===!0:!1}function Tt(e){return Qc(e,!1)}function ip(e){return Qc(e,!0)}function Qc(e,t){return xe(e)?e:new sp(e,t)}class sp{constructor(t,r){this.dep=new Nn,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=r?t:ne(t),this._value=r?t:Me(t),this.__v_isShallow=r}get value(){return this.dep.track(),this._value}set value(t){const r=this._rawValue,o=this.__v_isShallow||at(t)||hr(t);t=o?t:ne(t),Xt(t,r)&&(this._rawValue=t,this._value=o?t:Me(t),this.dep.trigger())}}function ft(e){return xe(e)?e.value:e}const ap={get:(e,t,r)=>t==="__v_raw"?e:ft(Reflect.get(e,t,r)),set:(e,t,r,o)=>{const n=e[t];return xe(n)&&!xe(r)?(n.value=r,!0):Reflect.set(e,t,r,o)}};function eu(e){return Zt(e)?e:new Proxy(e,ap)}class lp{constructor(t){this.__v_isRef=!0,this._value=void 0;const r=this.dep=new Nn,{get:o,set:n}=t(r.track.bind(r),r.trigger.bind(r));this._get=o,this._set=n}get value(){return this._value=this._get()}set value(t){this._set(t)}}function yw(e){return new lp(e)}function cp(e){const t=q(e)?new Array(e.length):{};for(const r in e)t[r]=tu(e,r);return t}class up{constructor(t,r,o){this._object=t,this._key=r,this._defaultValue=o,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Hf(ne(this._object),this._key)}}class dp{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Cw(e,t,r){return xe(e)?e:Y(e)?new dp(e):Ce(e)&&arguments.length>1?tu(e,t,r):Tt(e)}function tu(e,t,r){const o=e[t];return xe(o)?o:new up(e,t,r)}class fp{constructor(t,r,o){this.fn=t,this.setter=r,this._value=void 0,this.dep=new Nn(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=co-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!r,this.isSSR=o}notify(){if(this.flags|=16,!(this.flags&8)&&ve!==this)return Fc(this,!0),!0}get value(){const t=this.dep.track();return Uc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function pp(e,t,r=!1){let o,n;return Y(e)?o=e:(o=e.get,n=e.set),new fp(o,n,r)}const qo={},vn=new WeakMap;let lr;function gp(e,t=!1,r=lr){if(r){let o=vn.get(r);o||vn.set(r,o=[]),o.push(e)}}function hp(e,t,r=me){const{immediate:o,deep:n,once:i,scheduler:s,augmentJob:a,call:l}=r,u=y=>n?y:at(y)||n===!1||n===0?It(y,1):It(y);let c,d,f,p,h=!1,b=!1;if(xe(e)?(d=()=>e.value,h=at(e)):Zt(e)?(d=()=>u(e),h=!0):q(e)?(b=!0,h=e.some(y=>Zt(y)||at(y)),d=()=>e.map(y=>{if(xe(y))return y.value;if(Zt(y))return u(y);if(Y(y))return l?l(y,2):y()})):Y(e)?t?d=l?()=>l(e,2):e:d=()=>{if(f){rr();try{f()}finally{or()}}const y=lr;lr=c;try{return l?l(e,3,[p]):e(p)}finally{lr=y}}:d=wt,t&&n){const y=d,P=n===!0?1/0:n;d=()=>It(y(),P)}const C=jc(),k=()=>{c.stop(),C&&C.active&&bs(C.effects,c)};if(i&&t){const y=t;t=(...P)=>{y(...P),k()}}let _=b?new Array(e.length).fill(qo):qo;const w=y=>{if(!(!(c.flags&1)||!c.dirty&&!y))if(t){const P=c.run();if(n||h||(b?P.some((F,z)=>Xt(F,_[z])):Xt(P,_))){f&&f();const F=lr;lr=c;try{const z=[P,_===qo?void 0:b&&_[0]===qo?[]:_,p];l?l(t,3,z):t(...z),_=P}finally{lr=F}}}else c.run()};return a&&a(w),c=new Mc(d),c.scheduler=s?()=>s(w,!1):w,p=y=>gp(y,!1,c),f=c.onStop=()=>{const y=vn.get(c);if(y){if(l)l(y,4);else for(const P of y)P();vn.delete(c)}},t?o?w(!0):_=c.run():s?s(w.bind(null,!0),!0):c.run(),k.pause=c.pause.bind(c),k.resume=c.resume.bind(c),k.stop=k,k}function It(e,t=1/0,r){if(t<=0||!Ce(e)||e.__v_skip||(r=r||new Set,r.has(e)))return e;if(r.add(e),t--,xe(e))It(e.value,t,r);else if(q(e))for(let o=0;o<e.length;o++)It(e[o],t,r);else if(wc(e)||Pr(e))e.forEach(o=>{It(o,t,r)});else if(Ec(e)){for(const o in e)It(e[o],t,r);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&It(e[o],t,r)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Mo(e,t,r,o){try{return o?e(...o):e()}catch(n){zn(n,t,r)}}function gt(e,t,r,o){if(Y(e)){const n=Mo(e,t,r,o);return n&&Tc(n)&&n.catch(i=>{zn(i,t,r)}),n}if(q(e)){const n=[];for(let i=0;i<e.length;i++)n.push(gt(e[i],t,r,o));return n}}function zn(e,t,r,o=!0){const n=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:s}=t&&t.appContext.config||me;if(t){let a=t.parent;const l=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${r}`;for(;a;){const c=a.ec;if(c){for(let d=0;d<c.length;d++)if(c[d](e,l,u)===!1)return}a=a.parent}if(i){rr(),Mo(i,null,10,[e,l,u]),or();return}}mp(e,r,n,o,s)}function mp(e,t,r,o=!0,n=!1){if(n)throw e;console.error(e)}const Ve=[];let _t=-1;const Er=[];let Wt=null,_r=0;const ru=Promise.resolve();let yn=null;function Hn(e){const t=yn||ru;return e?t.then(this?e.bind(this):e):t}function bp(e){let t=_t+1,r=Ve.length;for(;t<r;){const o=t+r>>>1,n=Ve[o],i=fo(n);i<e||i===e&&n.flags&2?t=o+1:r=o}return t}function ws(e){if(!(e.flags&1)){const t=fo(e),r=Ve[Ve.length-1];!r||!(e.flags&2)&&t>=fo(r)?Ve.push(e):Ve.splice(bp(t),0,e),e.flags|=1,ou()}}function ou(){yn||(yn=ru.then(iu))}function vp(e){q(e)?Er.push(...e):Wt&&e.id===-1?Wt.splice(_r+1,0,e):e.flags&1||(Er.push(e),e.flags|=1),ou()}function ta(e,t,r=_t+1){for(;r<Ve.length;r++){const o=Ve[r];if(o&&o.flags&2){if(e&&o.id!==e.uid)continue;Ve.splice(r,1),r--,o.flags&4&&(o.flags&=-2),o(),o.flags&4||(o.flags&=-2)}}}function nu(e){if(Er.length){const t=[...new Set(Er)].sort((r,o)=>fo(r)-fo(o));if(Er.length=0,Wt){Wt.push(...t);return}for(Wt=t,_r=0;_r<Wt.length;_r++){const r=Wt[_r];r.flags&4&&(r.flags&=-2),r.flags&8||r(),r.flags&=-2}Wt=null,_r=0}}const fo=e=>e.id==null?e.flags&2?-1:1/0:e.id;function iu(e){try{for(_t=0;_t<Ve.length;_t++){const t=Ve[_t];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Mo(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;_t<Ve.length;_t++){const t=Ve[_t];t&&(t.flags&=-2)}_t=-1,Ve.length=0,nu(),yn=null,(Ve.length||Er.length)&&iu()}}let Pe=null,su=null;function Cn(e){const t=Pe;return Pe=e,su=e&&e.type.__scopeId||null,t}function Sn(e,t=Pe,r){if(!t||e._n)return e;const o=(...n)=>{o._d&&ha(-1);const i=Cn(t);let s;try{s=e(...n)}finally{Cn(i),o._d&&ha(1)}return s};return o._n=!0,o._c=!0,o._d=!0,o}function yp(e,t){if(Pe===null)return e;const r=qn(Pe),o=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[i,s,a,l=me]=t[n];i&&(Y(i)&&(i={mounted:i,updated:i}),i.deep&&It(s),o.push({dir:i,instance:r,value:s,oldValue:void 0,arg:a,modifiers:l}))}return e}function ir(e,t,r,o){const n=e.dirs,i=t&&t.dirs;for(let s=0;s<n.length;s++){const a=n[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(rr(),gt(l,r,8,[e.el,a,e,t]),or())}}const au=Symbol("_vte"),lu=e=>e.__isTeleport,Qr=e=>e&&(e.disabled||e.disabled===""),ra=e=>e&&(e.defer||e.defer===""),oa=e=>typeof SVGElement<"u"&&e instanceof SVGElement,na=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,ji=(e,t)=>{const r=e&&e.to;return ke(r)?t?t(r):null:r},cu={name:"Teleport",__isTeleport:!0,process(e,t,r,o,n,i,s,a,l,u){const{mc:c,pc:d,pbc:f,o:{insert:p,querySelector:h,createText:b,createComment:C}}=u,k=Qr(t.props);let{shapeFlag:_,children:w,dynamicChildren:y}=t;if(e==null){const P=t.el=b(""),F=t.anchor=b("");p(P,r,o),p(F,r,o);const z=(O,U)=>{_&16&&(n&&n.isCE&&(n.ce._teleportTarget=O),c(w,O,U,n,i,s,a,l))},N=()=>{const O=t.target=ji(t.props,h),U=uu(O,t,b,p);O&&(s!=="svg"&&oa(O)?s="svg":s!=="mathml"&&na(O)&&(s="mathml"),k||(z(O,U),sn(t,!1)))};k&&(z(r,F),sn(t,!0)),ra(t.props)?He(()=>{N(),t.el.__isMounted=!0},i):N()}else{if(ra(t.props)&&!e.el.__isMounted){He(()=>{cu.process(e,t,r,o,n,i,s,a,l,u),delete e.el.__isMounted},i);return}t.el=e.el,t.targetStart=e.targetStart;const P=t.anchor=e.anchor,F=t.target=e.target,z=t.targetAnchor=e.targetAnchor,N=Qr(e.props),O=N?r:F,U=N?P:z;if(s==="svg"||oa(F)?s="svg":(s==="mathml"||na(F))&&(s="mathml"),y?(f(e.dynamicChildren,y,O,n,i,s,a),Ls(e,t,!0)):l||d(e,t,O,U,n,i,s,a,!1),k)N?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Go(t,r,P,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const G=t.target=ji(t.props,h);G&&Go(t,G,null,u,0)}else N&&Go(t,F,z,u,1);sn(t,k)}},remove(e,t,r,{um:o,o:{remove:n}},i){const{shapeFlag:s,children:a,anchor:l,targetStart:u,targetAnchor:c,target:d,props:f}=e;if(d&&(n(u),n(c)),i&&n(l),s&16){const p=i||!Qr(f);for(let h=0;h<a.length;h++){const b=a[h];o(b,t,r,p,!!b.dynamicChildren)}}},move:Go,hydrate:Cp};function Go(e,t,r,{o:{insert:o},m:n},i=2){i===0&&o(e.targetAnchor,t,r);const{el:s,anchor:a,shapeFlag:l,children:u,props:c}=e,d=i===2;if(d&&o(s,t,r),(!d||Qr(c))&&l&16)for(let f=0;f<u.length;f++)n(u[f],t,r,2);d&&o(a,t,r)}function Cp(e,t,r,o,n,i,{o:{nextSibling:s,parentNode:a,querySelector:l,insert:u,createText:c}},d){const f=t.target=ji(t.props,l);if(f){const p=Qr(t.props),h=f._lpa||f.firstChild;if(t.shapeFlag&16)if(p)t.anchor=d(s(e),t,a(e),r,o,n,i),t.targetStart=h,t.targetAnchor=h&&s(h);else{t.anchor=s(e);let b=h;for(;b;){if(b&&b.nodeType===8){if(b.data==="teleport start anchor")t.targetStart=b;else if(b.data==="teleport anchor"){t.targetAnchor=b,f._lpa=t.targetAnchor&&s(t.targetAnchor);break}}b=s(b)}t.targetAnchor||uu(f,t,c,u),d(h&&s(h),t,f,r,o,n,i)}sn(t,p)}return t.anchor&&s(t.anchor)}const Sp=cu;function sn(e,t){const r=e.ctx;if(r&&r.ut){let o,n;for(t?(o=e.el,n=e.anchor):(o=e.targetStart,n=e.targetAnchor);o&&o!==n;)o.nodeType===1&&o.setAttribute("data-v-owner",r.uid),o=o.nextSibling;r.ut()}}function uu(e,t,r,o){const n=t.targetStart=r(""),i=t.targetAnchor=r("");return n[au]=i,e&&(o(n,e),o(i,e)),i}const Kt=Symbol("_leaveCb"),Jo=Symbol("_enterCb");function du(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Es(()=>{e.isMounted=!0}),yu(()=>{e.isUnmounting=!0}),e}const tt=[Function,Array],fu={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:tt,onEnter:tt,onAfterEnter:tt,onEnterCancelled:tt,onBeforeLeave:tt,onLeave:tt,onAfterLeave:tt,onLeaveCancelled:tt,onBeforeAppear:tt,onAppear:tt,onAfterAppear:tt,onAppearCancelled:tt},pu=e=>{const t=e.subTree;return t.component?pu(t.component):t},kp={name:"BaseTransition",props:fu,setup(e,{slots:t}){const r=bo(),o=du();return()=>{const n=t.default&&Ts(t.default(),!0);if(!n||!n.length)return;const i=gu(n),s=ne(e),{mode:a}=s;if(o.isLeaving)return di(i);const l=ia(i);if(!l)return di(i);let u=po(l,s,o,r,d=>u=d);l.type!==We&&mr(l,u);let c=r.subTree&&ia(r.subTree);if(c&&c.type!==We&&!ur(l,c)&&pu(r).type!==We){let d=po(c,s,o,r);if(mr(c,d),a==="out-in"&&l.type!==We)return o.isLeaving=!0,d.afterLeave=()=>{o.isLeaving=!1,r.job.flags&8||r.update(),delete d.afterLeave,c=void 0},di(i);a==="in-out"&&l.type!==We?d.delayLeave=(f,p,h)=>{const b=hu(o,c);b[String(c.key)]=c,f[Kt]=()=>{p(),f[Kt]=void 0,delete u.delayedLeave,c=void 0},u.delayedLeave=()=>{h(),delete u.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return i}}};function gu(e){let t=e[0];if(e.length>1){for(const r of e)if(r.type!==We){t=r;break}}return t}const _p=kp;function hu(e,t){const{leavingVNodes:r}=e;let o=r.get(t.type);return o||(o=Object.create(null),r.set(t.type,o)),o}function po(e,t,r,o,n){const{appear:i,mode:s,persisted:a=!1,onBeforeEnter:l,onEnter:u,onAfterEnter:c,onEnterCancelled:d,onBeforeLeave:f,onLeave:p,onAfterLeave:h,onLeaveCancelled:b,onBeforeAppear:C,onAppear:k,onAfterAppear:_,onAppearCancelled:w}=t,y=String(e.key),P=hu(r,e),F=(O,U)=>{O&&gt(O,o,9,U)},z=(O,U)=>{const G=U[1];F(O,U),q(O)?O.every(L=>L.length<=1)&&G():O.length<=1&&G()},N={mode:s,persisted:a,beforeEnter(O){let U=l;if(!r.isMounted)if(i)U=C||l;else return;O[Kt]&&O[Kt](!0);const G=P[y];G&&ur(e,G)&&G.el[Kt]&&G.el[Kt](),F(U,[O])},enter(O){let U=u,G=c,L=d;if(!r.isMounted)if(i)U=k||u,G=_||c,L=w||d;else return;let X=!1;const ce=O[Jo]=_e=>{X||(X=!0,_e?F(L,[O]):F(G,[O]),N.delayedLeave&&N.delayedLeave(),O[Jo]=void 0)};U?z(U,[O,ce]):ce()},leave(O,U){const G=String(e.key);if(O[Jo]&&O[Jo](!0),r.isUnmounting)return U();F(f,[O]);let L=!1;const X=O[Kt]=ce=>{L||(L=!0,U(),ce?F(b,[O]):F(h,[O]),O[Kt]=void 0,P[G]===e&&delete P[G])};P[G]=e,p?z(p,[O,X]):X()},clone(O){const U=po(O,t,r,o,n);return n&&n(U),U}};return N}function di(e){if(Un(e))return e=er(e),e.children=null,e}function ia(e){if(!Un(e))return lu(e.type)&&e.children?gu(e.children):e;const{shapeFlag:t,children:r}=e;if(r){if(t&16)return r[0];if(t&32&&Y(r.default))return r.default()}}function mr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,mr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ts(e,t=!1,r){let o=[],n=0;for(let i=0;i<e.length;i++){let s=e[i];const a=r==null?s.key:String(r)+String(s.key!=null?s.key:i);s.type===Le?(s.patchFlag&128&&n++,o=o.concat(Ts(s.children,t,a))):(t||s.type!==We)&&o.push(a!=null?er(s,{key:a}):s)}if(n>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}/*! #__NO_SIDE_EFFECTS__ */function Ps(e,t){return Y(e)?Ee({name:e.name},t,{setup:e}):e}function $p(){const e=bo();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function mu(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function kn(e,t,r,o,n=!1){if(q(e)){e.forEach((h,b)=>kn(h,t&&(q(t)?t[b]:t),r,o,n));return}if(Or(o)&&!n){o.shapeFlag&512&&o.type.__asyncResolved&&o.component.subTree.component&&kn(e,t,r,o.component.subTree);return}const i=o.shapeFlag&4?qn(o.component):o.el,s=n?null:i,{i:a,r:l}=e,u=t&&t.r,c=a.refs===me?a.refs={}:a.refs,d=a.setupState,f=ne(d),p=d===me?()=>!1:h=>le(f,h);if(u!=null&&u!==l&&(ke(u)?(c[u]=null,p(u)&&(d[u]=null)):xe(u)&&(u.value=null)),Y(l))Mo(l,a,12,[s,c]);else{const h=ke(l),b=xe(l);if(h||b){const C=()=>{if(e.f){const k=h?p(l)?d[l]:c[l]:l.value;n?q(k)&&bs(k,i):q(k)?k.includes(i)||k.push(i):h?(c[l]=[i],p(l)&&(d[l]=c[l])):(l.value=[i],e.k&&(c[e.k]=l.value))}else h?(c[l]=s,p(l)&&(d[l]=s)):b&&(l.value=s,e.k&&(c[e.k]=s))};s?(C.id=-1,He(C,r)):C()}}}jn().requestIdleCallback;jn().cancelIdleCallback;const Or=e=>!!e.type.__asyncLoader,Un=e=>e.type.__isKeepAlive;function xp(e,t){bu(e,"a",t)}function Rp(e,t){bu(e,"da",t)}function bu(e,t,r=Be){const o=e.__wdc||(e.__wdc=()=>{let n=r;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Vn(t,o,r),r){let n=r.parent;for(;n&&n.parent;)Un(n.parent.vnode)&&wp(o,t,r,n),n=n.parent}}function wp(e,t,r,o){const n=Vn(t,e,o,!0);Cu(()=>{bs(o[t],n)},r)}function Vn(e,t,r=Be,o=!1){if(r){const n=r[e]||(r[e]=[]),i=t.__weh||(t.__weh=(...s)=>{rr();const a=No(r),l=gt(t,r,e,s);return a(),or(),l});return o?n.unshift(i):n.push(i),i}}const Mt=e=>(t,r=Be)=>{(!vo||e==="sp")&&Vn(e,(...o)=>t(...o),r)},Tp=Mt("bm"),Es=Mt("m"),Pp=Mt("bu"),vu=Mt("u"),yu=Mt("bum"),Cu=Mt("um"),Ep=Mt("sp"),Op=Mt("rtg"),Bp=Mt("rtc");function Ap(e,t=Be){Vn("ec",e,t)}const Os="components",Lp="directives";function Mi(e,t){return Bs(Os,e,!0,t)||e}const Su=Symbol.for("v-ndc");function Kr(e){return ke(e)?Bs(Os,e,!1)||e:e||Su}function Ip(e){return Bs(Lp,e)}function Bs(e,t,r=!0,o=!1){const n=Pe||Be;if(n){const i=n.type;if(e===Os){const a=kg(i,!1);if(a&&(a===t||a===lt(t)||a===Dn(lt(t))))return i}const s=sa(n[e]||i[e],t)||sa(n.appContext[e],t);return!s&&o?i:s}}function sa(e,t){return e&&(e[t]||e[lt(t)]||e[Dn(lt(t))])}function ku(e,t,r,o){let n;const i=r,s=q(e);if(s||ke(e)){const a=s&&Zt(e);let l=!1;a&&(l=!at(e),e=Fn(e)),n=new Array(e.length);for(let u=0,c=e.length;u<c;u++)n[u]=t(l?Me(e[u]):e[u],u,void 0,i)}else if(typeof e=="number"){n=new Array(e);for(let a=0;a<e;a++)n[a]=t(a+1,a,void 0,i)}else if(Ce(e))if(e[Symbol.iterator])n=Array.from(e,(a,l)=>t(a,l,void 0,i));else{const a=Object.keys(e);n=new Array(a.length);for(let l=0,u=a.length;l<u;l++){const c=a[l];n[l]=t(e[c],c,l,i)}}else n=[];return n}function Sw(e,t){for(let r=0;r<t.length;r++){const o=t[r];if(q(o))for(let n=0;n<o.length;n++)e[o[n].name]=o[n].fn;else o&&(e[o.name]=o.key?(...n)=>{const i=o.fn(...n);return i&&(i.key=o.key),i}:o.fn)}return e}function _n(e,t,r={},o,n){if(Pe.ce||Pe.parent&&Or(Pe.parent)&&Pe.parent.ce)return t!=="default"&&(r.name=t),se(),it(Le,null,[we("slot",r,o&&o())],64);let i=e[t];i&&i._c&&(i._d=!1),se();const s=i&&_u(i(r)),a=r.key||s&&s.key,l=it(Le,{key:(a&&!jt(a)?a:`_${t}`)+(!s&&o?"_fb":"")},s||(o?o():[]),s&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),i&&i._c&&(i._d=!0),l}function _u(e){return e.some(t=>ho(t)?!(t.type===We||t.type===Le&&!_u(t.children)):!0)?e:null}const Ni=e=>e?Uu(e)?qn(e):Ni(e.parent):null,eo=Ee(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ni(e.parent),$root:e=>Ni(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>xu(e),$forceUpdate:e=>e.f||(e.f=()=>{ws(e.update)}),$nextTick:e=>e.n||(e.n=Hn.bind(e.proxy)),$watch:e=>og.bind(e)}),fi=(e,t)=>e!==me&&!e.__isScriptSetup&&le(e,t),Dp={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:r,setupState:o,data:n,props:i,accessCache:s,type:a,appContext:l}=e;let u;if(t[0]!=="$"){const p=s[t];if(p!==void 0)switch(p){case 1:return o[t];case 2:return n[t];case 4:return r[t];case 3:return i[t]}else{if(fi(o,t))return s[t]=1,o[t];if(n!==me&&le(n,t))return s[t]=2,n[t];if((u=e.propsOptions[0])&&le(u,t))return s[t]=3,i[t];if(r!==me&&le(r,t))return s[t]=4,r[t];Fi&&(s[t]=0)}}const c=eo[t];let d,f;if(c)return t==="$attrs"&&je(e.attrs,"get",""),c(e);if((d=a.__cssModules)&&(d=d[t]))return d;if(r!==me&&le(r,t))return s[t]=4,r[t];if(f=l.config.globalProperties,le(f,t))return f[t]},set({_:e},t,r){const{data:o,setupState:n,ctx:i}=e;return fi(n,t)?(n[t]=r,!0):o!==me&&le(o,t)?(o[t]=r,!0):le(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=r,!0)},has({_:{data:e,setupState:t,accessCache:r,ctx:o,appContext:n,propsOptions:i}},s){let a;return!!r[s]||e!==me&&le(e,s)||fi(t,s)||(a=i[0])&&le(a,s)||le(o,s)||le(eo,s)||le(n.config.globalProperties,s)},defineProperty(e,t,r){return r.get!=null?e._.accessCache[t]=0:le(r,"value")&&this.set(e,t,r.value,null),Reflect.defineProperty(e,t,r)}};function aa(e){return q(e)?e.reduce((t,r)=>(t[r]=null,t),{}):e}let Fi=!0;function jp(e){const t=xu(e),r=e.proxy,o=e.ctx;Fi=!1,t.beforeCreate&&la(t.beforeCreate,e,"bc");const{data:n,computed:i,methods:s,watch:a,provide:l,inject:u,created:c,beforeMount:d,mounted:f,beforeUpdate:p,updated:h,activated:b,deactivated:C,beforeDestroy:k,beforeUnmount:_,destroyed:w,unmounted:y,render:P,renderTracked:F,renderTriggered:z,errorCaptured:N,serverPrefetch:O,expose:U,inheritAttrs:G,components:L,directives:X,filters:ce}=t;if(u&&Mp(u,o,null),s)for(const K in s){const ee=s[K];Y(ee)&&(o[K]=ee.bind(r))}if(n){const K=n.call(r,r);Ce(K)&&(e.data=Dr(K))}if(Fi=!0,i)for(const K in i){const ee=i[K],Ie=Y(ee)?ee.bind(r,r):Y(ee.get)?ee.get.bind(r,r):wt,Oe=!Y(ee)&&Y(ee.set)?ee.set.bind(r):wt,Re=nt({get:Ie,set:Oe});Object.defineProperty(o,K,{enumerable:!0,configurable:!0,get:()=>Re.value,set:$e=>Re.value=$e})}if(a)for(const K in a)$u(a[K],o,r,K);if(l){const K=Y(l)?l.call(r):l;Reflect.ownKeys(K).forEach(ee=>{an(ee,K[ee])})}c&&la(c,e,"c");function re(K,ee){q(ee)?ee.forEach(Ie=>K(Ie.bind(r))):ee&&K(ee.bind(r))}if(re(Tp,d),re(Es,f),re(Pp,p),re(vu,h),re(xp,b),re(Rp,C),re(Ap,N),re(Bp,F),re(Op,z),re(yu,_),re(Cu,y),re(Ep,O),q(U))if(U.length){const K=e.exposed||(e.exposed={});U.forEach(ee=>{Object.defineProperty(K,ee,{get:()=>r[ee],set:Ie=>r[ee]=Ie})})}else e.exposed||(e.exposed={});P&&e.render===wt&&(e.render=P),G!=null&&(e.inheritAttrs=G),L&&(e.components=L),X&&(e.directives=X),O&&mu(e)}function Mp(e,t,r=wt){q(e)&&(e=zi(e));for(const o in e){const n=e[o];let i;Ce(n)?"default"in n?i=Qe(n.from||o,n.default,!0):i=Qe(n.from||o):i=Qe(n),xe(i)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>i.value,set:s=>i.value=s}):t[o]=i}}function la(e,t,r){gt(q(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,r)}function $u(e,t,r,o){let n=o.includes(".")?Mu(r,o):()=>r[o];if(ke(e)){const i=t[e];Y(i)&&Rt(n,i)}else if(Y(e))Rt(n,e.bind(r));else if(Ce(e))if(q(e))e.forEach(i=>$u(i,t,r,o));else{const i=Y(e.handler)?e.handler.bind(r):t[e.handler];Y(i)&&Rt(n,i,e)}}function xu(e){const t=e.type,{mixins:r,extends:o}=t,{mixins:n,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:!n.length&&!r&&!o?l=t:(l={},n.length&&n.forEach(u=>$n(l,u,s,!0)),$n(l,t,s)),Ce(t)&&i.set(t,l),l}function $n(e,t,r,o=!1){const{mixins:n,extends:i}=t;i&&$n(e,i,r,!0),n&&n.forEach(s=>$n(e,s,r,!0));for(const s in t)if(!(o&&s==="expose")){const a=Np[s]||r&&r[s];e[s]=a?a(e[s],t[s]):t[s]}return e}const Np={data:ca,props:ua,emits:ua,methods:qr,computed:qr,beforeCreate:ze,created:ze,beforeMount:ze,mounted:ze,beforeUpdate:ze,updated:ze,beforeDestroy:ze,beforeUnmount:ze,destroyed:ze,unmounted:ze,activated:ze,deactivated:ze,errorCaptured:ze,serverPrefetch:ze,components:qr,directives:qr,watch:zp,provide:ca,inject:Fp};function ca(e,t){return t?e?function(){return Ee(Y(e)?e.call(this,this):e,Y(t)?t.call(this,this):t)}:t:e}function Fp(e,t){return qr(zi(e),zi(t))}function zi(e){if(q(e)){const t={};for(let r=0;r<e.length;r++)t[e[r]]=e[r];return t}return e}function ze(e,t){return e?[...new Set([].concat(e,t))]:t}function qr(e,t){return e?Ee(Object.create(null),e,t):t}function ua(e,t){return e?q(e)&&q(t)?[...new Set([...e,...t])]:Ee(Object.create(null),aa(e),aa(t??{})):t}function zp(e,t){if(!e)return t;if(!t)return e;const r=Ee(Object.create(null),e);for(const o in t)r[o]=ze(e[o],t[o]);return r}function Ru(){return{app:null,config:{isNativeTag:xf,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Hp=0;function Up(e,t){return function(o,n=null){Y(o)||(o=Ee({},o)),n!=null&&!Ce(n)&&(n=null);const i=Ru(),s=new WeakSet,a=[];let l=!1;const u=i.app={_uid:Hp++,_component:o,_props:n,_container:null,_context:i,_instance:null,version:$g,get config(){return i.config},set config(c){},use(c,...d){return s.has(c)||(c&&Y(c.install)?(s.add(c),c.install(u,...d)):Y(c)&&(s.add(c),c(u,...d))),u},mixin(c){return i.mixins.includes(c)||i.mixins.push(c),u},component(c,d){return d?(i.components[c]=d,u):i.components[c]},directive(c,d){return d?(i.directives[c]=d,u):i.directives[c]},mount(c,d,f){if(!l){const p=u._ceVNode||we(o,n);return p.appContext=i,f===!0?f="svg":f===!1&&(f=void 0),e(p,c,f),l=!0,u._container=c,c.__vue_app__=u,qn(p.component)}},onUnmount(c){a.push(c)},unmount(){l&&(gt(a,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,d){return i.provides[c]=d,u},runWithContext(c){const d=pr;pr=u;try{return c()}finally{pr=d}}};return u}}let pr=null;function an(e,t){if(Be){let r=Be.provides;const o=Be.parent&&Be.parent.provides;o===r&&(r=Be.provides=Object.create(o)),r[e]=t}}function Qe(e,t,r=!1){const o=Be||Pe;if(o||pr){const n=pr?pr._context.provides:o?o.parent==null?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return r&&Y(t)?t.call(o&&o.proxy):t}}function Vp(){return!!(Be||Pe||pr)}const wu={},Tu=()=>Object.create(wu),Pu=e=>Object.getPrototypeOf(e)===wu;function Wp(e,t,r,o=!1){const n={},i=Tu();e.propsDefaults=Object.create(null),Eu(e,t,n,i);for(const s in e.propsOptions[0])s in n||(n[s]=void 0);r?e.props=o?n:Zc(n):e.type.props?e.props=n:e.props=i,e.attrs=i}function Kp(e,t,r,o){const{props:n,attrs:i,vnode:{patchFlag:s}}=e,a=ne(n),[l]=e.propsOptions;let u=!1;if((o||s>0)&&!(s&16)){if(s&8){const c=e.vnode.dynamicProps;for(let d=0;d<c.length;d++){let f=c[d];if(Wn(e.emitsOptions,f))continue;const p=t[f];if(l)if(le(i,f))p!==i[f]&&(i[f]=p,u=!0);else{const h=lt(f);n[h]=Hi(l,a,h,p,e,!1)}else p!==i[f]&&(i[f]=p,u=!0)}}}else{Eu(e,t,n,i)&&(u=!0);let c;for(const d in a)(!t||!le(t,d)&&((c=yr(d))===d||!le(t,c)))&&(l?r&&(r[d]!==void 0||r[c]!==void 0)&&(n[d]=Hi(l,a,d,void 0,e,!0)):delete n[d]);if(i!==a)for(const d in i)(!t||!le(t,d))&&(delete i[d],u=!0)}u&&Lt(e.attrs,"set","")}function Eu(e,t,r,o){const[n,i]=e.propsOptions;let s=!1,a;if(t)for(let l in t){if(Yr(l))continue;const u=t[l];let c;n&&le(n,c=lt(l))?!i||!i.includes(c)?r[c]=u:(a||(a={}))[c]=u:Wn(e.emitsOptions,l)||(!(l in o)||u!==o[l])&&(o[l]=u,s=!0)}if(i){const l=ne(r),u=a||me;for(let c=0;c<i.length;c++){const d=i[c];r[d]=Hi(n,l,d,u[d],e,!le(u,d))}}return s}function Hi(e,t,r,o,n,i){const s=e[r];if(s!=null){const a=le(s,"default");if(a&&o===void 0){const l=s.default;if(s.type!==Function&&!s.skipFactory&&Y(l)){const{propsDefaults:u}=n;if(r in u)o=u[r];else{const c=No(n);o=u[r]=l.call(null,t),c()}}else o=l;n.ce&&n.ce._setProp(r,o)}s[0]&&(i&&!a?o=!1:s[1]&&(o===""||o===yr(r))&&(o=!0))}return o}const qp=new WeakMap;function Ou(e,t,r=!1){const o=r?qp:t.propsCache,n=o.get(e);if(n)return n;const i=e.props,s={},a=[];let l=!1;if(!Y(e)){const c=d=>{l=!0;const[f,p]=Ou(d,t,!0);Ee(s,f),p&&a.push(...p)};!r&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!i&&!l)return Ce(e)&&o.set(e,Tr),Tr;if(q(i))for(let c=0;c<i.length;c++){const d=lt(i[c]);da(d)&&(s[d]=me)}else if(i)for(const c in i){const d=lt(c);if(da(d)){const f=i[c],p=s[d]=q(f)||Y(f)?{type:f}:Ee({},f),h=p.type;let b=!1,C=!0;if(q(h))for(let k=0;k<h.length;++k){const _=h[k],w=Y(_)&&_.name;if(w==="Boolean"){b=!0;break}else w==="String"&&(C=!1)}else b=Y(h)&&h.name==="Boolean";p[0]=b,p[1]=C,(b||le(p,"default"))&&a.push(d)}}const u=[s,a];return Ce(e)&&o.set(e,u),u}function da(e){return e[0]!=="$"&&!Yr(e)}const Bu=e=>e[0]==="_"||e==="$stable",As=e=>q(e)?e.map($t):[$t(e)],Gp=(e,t,r)=>{if(t._n)return t;const o=Sn((...n)=>As(t(...n)),r);return o._c=!1,o},Au=(e,t,r)=>{const o=e._ctx;for(const n in e){if(Bu(n))continue;const i=e[n];if(Y(i))t[n]=Gp(n,i,o);else if(i!=null){const s=As(i);t[n]=()=>s}}},Lu=(e,t)=>{const r=As(t);e.slots.default=()=>r},Iu=(e,t,r)=>{for(const o in t)(r||o!=="_")&&(e[o]=t[o])},Jp=(e,t,r)=>{const o=e.slots=Tu();if(e.vnode.shapeFlag&32){const n=t._;n?(Iu(o,t,r),r&&Oc(o,"_",n,!0)):Au(t,o)}else t&&Lu(e,t)},Yp=(e,t,r)=>{const{vnode:o,slots:n}=e;let i=!0,s=me;if(o.shapeFlag&32){const a=t._;a?r&&a===1?i=!1:Iu(n,t,r):(i=!t.$stable,Au(t,n)),s=t}else t&&(Lu(e,t),s={default:1});if(i)for(const a in n)!Bu(a)&&s[a]==null&&delete n[a]},He=ug;function Xp(e){return Zp(e)}function Zp(e,t){const r=jn();r.__VUE__=!0;const{insert:o,remove:n,patchProp:i,createElement:s,createText:a,createComment:l,setText:u,setElementText:c,parentNode:d,nextSibling:f,setScopeId:p=wt,insertStaticContent:h}=e,b=(g,m,v,$=null,T=null,R=null,I=void 0,A=null,B=!!m.dynamicChildren)=>{if(g===m)return;g&&!ur(g,m)&&($=x(g),$e(g,T,R,!0),g=null),m.patchFlag===-2&&(B=!1,m.dynamicChildren=null);const{type:E,ref:W,shapeFlag:j}=m;switch(E){case Kn:C(g,m,v,$);break;case We:k(g,m,v,$);break;case ln:g==null&&_(m,v,$,I);break;case Le:L(g,m,v,$,T,R,I,A,B);break;default:j&1?P(g,m,v,$,T,R,I,A,B):j&6?X(g,m,v,$,T,R,I,A,B):(j&64||j&128)&&E.process(g,m,v,$,T,R,I,A,B,H)}W!=null&&T&&kn(W,g&&g.ref,R,m||g,!m)},C=(g,m,v,$)=>{if(g==null)o(m.el=a(m.children),v,$);else{const T=m.el=g.el;m.children!==g.children&&u(T,m.children)}},k=(g,m,v,$)=>{g==null?o(m.el=l(m.children||""),v,$):m.el=g.el},_=(g,m,v,$)=>{[g.el,g.anchor]=h(g.children,m,v,$,g.el,g.anchor)},w=({el:g,anchor:m},v,$)=>{let T;for(;g&&g!==m;)T=f(g),o(g,v,$),g=T;o(m,v,$)},y=({el:g,anchor:m})=>{let v;for(;g&&g!==m;)v=f(g),n(g),g=v;n(m)},P=(g,m,v,$,T,R,I,A,B)=>{m.type==="svg"?I="svg":m.type==="math"&&(I="mathml"),g==null?F(m,v,$,T,R,I,A,B):O(g,m,T,R,I,A,B)},F=(g,m,v,$,T,R,I,A)=>{let B,E;const{props:W,shapeFlag:j,transition:V,dirs:J}=g;if(B=g.el=s(g.type,R,W&&W.is,W),j&8?c(B,g.children):j&16&&N(g.children,B,null,$,T,pi(g,R),I,A),J&&ir(g,null,$,"created"),z(B,g,g.scopeId,I,$),W){for(const be in W)be!=="value"&&!Yr(be)&&i(B,be,null,W[be],R,$);"value"in W&&i(B,"value",null,W.value,R),(E=W.onVnodeBeforeMount)&&Ct(E,$,g)}J&&ir(g,null,$,"beforeMount");const oe=Qp(T,V);oe&&V.beforeEnter(B),o(B,m,v),((E=W&&W.onVnodeMounted)||oe||J)&&He(()=>{E&&Ct(E,$,g),oe&&V.enter(B),J&&ir(g,null,$,"mounted")},T)},z=(g,m,v,$,T)=>{if(v&&p(g,v),$)for(let R=0;R<$.length;R++)p(g,$[R]);if(T){let R=T.subTree;if(m===R||Fu(R.type)&&(R.ssContent===m||R.ssFallback===m)){const I=T.vnode;z(g,I,I.scopeId,I.slotScopeIds,T.parent)}}},N=(g,m,v,$,T,R,I,A,B=0)=>{for(let E=B;E<g.length;E++){const W=g[E]=A?qt(g[E]):$t(g[E]);b(null,W,m,v,$,T,R,I,A)}},O=(g,m,v,$,T,R,I)=>{const A=m.el=g.el;let{patchFlag:B,dynamicChildren:E,dirs:W}=m;B|=g.patchFlag&16;const j=g.props||me,V=m.props||me;let J;if(v&&sr(v,!1),(J=V.onVnodeBeforeUpdate)&&Ct(J,v,m,g),W&&ir(m,g,v,"beforeUpdate"),v&&sr(v,!0),(j.innerHTML&&V.innerHTML==null||j.textContent&&V.textContent==null)&&c(A,""),E?U(g.dynamicChildren,E,A,v,$,pi(m,T),R):I||ee(g,m,A,null,v,$,pi(m,T),R,!1),B>0){if(B&16)G(A,j,V,v,T);else if(B&2&&j.class!==V.class&&i(A,"class",null,V.class,T),B&4&&i(A,"style",j.style,V.style,T),B&8){const oe=m.dynamicProps;for(let be=0;be<oe.length;be++){const de=oe[be],Ye=j[de],Ke=V[de];(Ke!==Ye||de==="value")&&i(A,de,Ye,Ke,T,v)}}B&1&&g.children!==m.children&&c(A,m.children)}else!I&&E==null&&G(A,j,V,v,T);((J=V.onVnodeUpdated)||W)&&He(()=>{J&&Ct(J,v,m,g),W&&ir(m,g,v,"updated")},$)},U=(g,m,v,$,T,R,I)=>{for(let A=0;A<m.length;A++){const B=g[A],E=m[A],W=B.el&&(B.type===Le||!ur(B,E)||B.shapeFlag&70)?d(B.el):v;b(B,E,W,null,$,T,R,I,!0)}},G=(g,m,v,$,T)=>{if(m!==v){if(m!==me)for(const R in m)!Yr(R)&&!(R in v)&&i(g,R,m[R],null,T,$);for(const R in v){if(Yr(R))continue;const I=v[R],A=m[R];I!==A&&R!=="value"&&i(g,R,A,I,T,$)}"value"in v&&i(g,"value",m.value,v.value,T)}},L=(g,m,v,$,T,R,I,A,B)=>{const E=m.el=g?g.el:a(""),W=m.anchor=g?g.anchor:a("");let{patchFlag:j,dynamicChildren:V,slotScopeIds:J}=m;J&&(A=A?A.concat(J):J),g==null?(o(E,v,$),o(W,v,$),N(m.children||[],v,W,T,R,I,A,B)):j>0&&j&64&&V&&g.dynamicChildren?(U(g.dynamicChildren,V,v,T,R,I,A),(m.key!=null||T&&m===T.subTree)&&Ls(g,m,!0)):ee(g,m,v,W,T,R,I,A,B)},X=(g,m,v,$,T,R,I,A,B)=>{m.slotScopeIds=A,g==null?m.shapeFlag&512?T.ctx.activate(m,v,$,I,B):ce(m,v,$,T,R,I,B):_e(g,m,B)},ce=(g,m,v,$,T,R,I)=>{const A=g.component=bg(g,$,T);if(Un(g)&&(A.ctx.renderer=H),vg(A,!1,I),A.asyncDep){if(T&&T.registerDep(A,re,I),!g.el){const B=A.subTree=we(We);k(null,B,m,v)}}else re(A,g,m,v,T,R,I)},_e=(g,m,v)=>{const $=m.component=g.component;if(lg(g,m,v))if($.asyncDep&&!$.asyncResolved){K($,m,v);return}else $.next=m,$.update();else m.el=g.el,$.vnode=m},re=(g,m,v,$,T,R,I)=>{const A=()=>{if(g.isMounted){let{next:j,bu:V,u:J,parent:oe,vnode:be}=g;{const vt=Du(g);if(vt){j&&(j.el=be.el,K(g,j,I)),vt.asyncDep.then(()=>{g.isUnmounted||A()});return}}let de=j,Ye;sr(g,!1),j?(j.el=be.el,K(g,j,I)):j=be,V&&si(V),(Ye=j.props&&j.props.onVnodeBeforeUpdate)&&Ct(Ye,oe,j,be),sr(g,!0);const Ke=pa(g),bt=g.subTree;g.subTree=Ke,b(bt,Ke,d(bt.el),x(bt),g,T,R),j.el=Ke.el,de===null&&cg(g,Ke.el),J&&He(J,T),(Ye=j.props&&j.props.onVnodeUpdated)&&He(()=>Ct(Ye,oe,j,be),T)}else{let j;const{el:V,props:J}=m,{bm:oe,m:be,parent:de,root:Ye,type:Ke}=g,bt=Or(m);sr(g,!1),oe&&si(oe),!bt&&(j=J&&J.onVnodeBeforeMount)&&Ct(j,de,m),sr(g,!0);{Ye.ce&&Ye.ce._injectChildStyle(Ke);const vt=g.subTree=pa(g);b(null,vt,v,$,g,T,R),m.el=vt.el}if(be&&He(be,T),!bt&&(j=J&&J.onVnodeMounted)){const vt=m;He(()=>Ct(j,de,vt),T)}(m.shapeFlag&256||de&&Or(de.vnode)&&de.vnode.shapeFlag&256)&&g.a&&He(g.a,T),g.isMounted=!0,m=v=$=null}};g.scope.on();const B=g.effect=new Mc(A);g.scope.off();const E=g.update=B.run.bind(B),W=g.job=B.runIfDirty.bind(B);W.i=g,W.id=g.uid,B.scheduler=()=>ws(W),sr(g,!0),E()},K=(g,m,v)=>{m.component=g;const $=g.vnode.props;g.vnode=m,g.next=null,Kp(g,m.props,$,v),Yp(g,m.children,v),rr(),ta(g),or()},ee=(g,m,v,$,T,R,I,A,B=!1)=>{const E=g&&g.children,W=g?g.shapeFlag:0,j=m.children,{patchFlag:V,shapeFlag:J}=m;if(V>0){if(V&128){Oe(E,j,v,$,T,R,I,A,B);return}else if(V&256){Ie(E,j,v,$,T,R,I,A,B);return}}J&8?(W&16&&Fe(E,T,R),j!==E&&c(v,j)):W&16?J&16?Oe(E,j,v,$,T,R,I,A,B):Fe(E,T,R,!0):(W&8&&c(v,""),J&16&&N(j,v,$,T,R,I,A,B))},Ie=(g,m,v,$,T,R,I,A,B)=>{g=g||Tr,m=m||Tr;const E=g.length,W=m.length,j=Math.min(E,W);let V;for(V=0;V<j;V++){const J=m[V]=B?qt(m[V]):$t(m[V]);b(g[V],J,v,null,T,R,I,A,B)}E>W?Fe(g,T,R,!0,!1,j):N(m,v,$,T,R,I,A,B,j)},Oe=(g,m,v,$,T,R,I,A,B)=>{let E=0;const W=m.length;let j=g.length-1,V=W-1;for(;E<=j&&E<=V;){const J=g[E],oe=m[E]=B?qt(m[E]):$t(m[E]);if(ur(J,oe))b(J,oe,v,null,T,R,I,A,B);else break;E++}for(;E<=j&&E<=V;){const J=g[j],oe=m[V]=B?qt(m[V]):$t(m[V]);if(ur(J,oe))b(J,oe,v,null,T,R,I,A,B);else break;j--,V--}if(E>j){if(E<=V){const J=V+1,oe=J<W?m[J].el:$;for(;E<=V;)b(null,m[E]=B?qt(m[E]):$t(m[E]),v,oe,T,R,I,A,B),E++}}else if(E>V)for(;E<=j;)$e(g[E],T,R,!0),E++;else{const J=E,oe=E,be=new Map;for(E=oe;E<=V;E++){const Xe=m[E]=B?qt(m[E]):$t(m[E]);Xe.key!=null&&be.set(Xe.key,E)}let de,Ye=0;const Ke=V-oe+1;let bt=!1,vt=0;const Fr=new Array(Ke);for(E=0;E<Ke;E++)Fr[E]=0;for(E=J;E<=j;E++){const Xe=g[E];if(Ye>=Ke){$e(Xe,T,R,!0);continue}let yt;if(Xe.key!=null)yt=be.get(Xe.key);else for(de=oe;de<=V;de++)if(Fr[de-oe]===0&&ur(Xe,m[de])){yt=de;break}yt===void 0?$e(Xe,T,R,!0):(Fr[yt-oe]=E+1,yt>=vt?vt=yt:bt=!0,b(Xe,m[yt],v,null,T,R,I,A,B),Ye++)}const Vs=bt?eg(Fr):Tr;for(de=Vs.length-1,E=Ke-1;E>=0;E--){const Xe=oe+E,yt=m[Xe],Ws=Xe+1<W?m[Xe+1].el:$;Fr[E]===0?b(null,yt,v,Ws,T,R,I,A,B):bt&&(de<0||E!==Vs[de]?Re(yt,v,Ws,2):de--)}}},Re=(g,m,v,$,T=null)=>{const{el:R,type:I,transition:A,children:B,shapeFlag:E}=g;if(E&6){Re(g.component.subTree,m,v,$);return}if(E&128){g.suspense.move(m,v,$);return}if(E&64){I.move(g,m,v,H);return}if(I===Le){o(R,m,v);for(let j=0;j<B.length;j++)Re(B[j],m,v,$);o(g.anchor,m,v);return}if(I===ln){w(g,m,v);return}if($!==2&&E&1&&A)if($===0)A.beforeEnter(R),o(R,m,v),He(()=>A.enter(R),T);else{const{leave:j,delayLeave:V,afterLeave:J}=A,oe=()=>o(R,m,v),be=()=>{j(R,()=>{oe(),J&&J()})};V?V(R,oe,be):be()}else o(R,m,v)},$e=(g,m,v,$=!1,T=!1)=>{const{type:R,props:I,ref:A,children:B,dynamicChildren:E,shapeFlag:W,patchFlag:j,dirs:V,cacheIndex:J}=g;if(j===-2&&(T=!1),A!=null&&kn(A,null,v,g,!0),J!=null&&(m.renderCache[J]=void 0),W&256){m.ctx.deactivate(g);return}const oe=W&1&&V,be=!Or(g);let de;if(be&&(de=I&&I.onVnodeBeforeUnmount)&&Ct(de,m,g),W&6)nr(g.component,v,$);else{if(W&128){g.suspense.unmount(v,$);return}oe&&ir(g,null,m,"beforeUnmount"),W&64?g.type.remove(g,m,v,H,$):E&&!E.hasOnce&&(R!==Le||j>0&&j&64)?Fe(E,m,v,!1,!0):(R===Le&&j&384||!T&&W&16)&&Fe(B,m,v),$&&ct(g)}(be&&(de=I&&I.onVnodeUnmounted)||oe)&&He(()=>{de&&Ct(de,m,g),oe&&ir(g,null,m,"unmounted")},v)},ct=g=>{const{type:m,el:v,anchor:$,transition:T}=g;if(m===Le){Je(v,$);return}if(m===ln){y(g);return}const R=()=>{n(v),T&&!T.persisted&&T.afterLeave&&T.afterLeave()};if(g.shapeFlag&1&&T&&!T.persisted){const{leave:I,delayLeave:A}=T,B=()=>I(v,R);A?A(g.el,R,B):B()}else R()},Je=(g,m)=>{let v;for(;g!==m;)v=f(g),n(g),g=v;n(m)},nr=(g,m,v)=>{const{bum:$,scope:T,job:R,subTree:I,um:A,m:B,a:E}=g;fa(B),fa(E),$&&si($),T.stop(),R&&(R.flags|=8,$e(I,g,m,v)),A&&He(A,m),He(()=>{g.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&g.asyncDep&&!g.asyncResolved&&g.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},Fe=(g,m,v,$=!1,T=!1,R=0)=>{for(let I=R;I<g.length;I++)$e(g[I],m,v,$,T)},x=g=>{if(g.shapeFlag&6)return x(g.component.subTree);if(g.shapeFlag&128)return g.suspense.next();const m=f(g.anchor||g.el),v=m&&m[au];return v?f(v):m};let M=!1;const D=(g,m,v)=>{g==null?m._vnode&&$e(m._vnode,null,null,!0):b(m._vnode||null,g,m,null,null,null,v),m._vnode=g,M||(M=!0,ta(),nu(),M=!1)},H={p:b,um:$e,m:Re,r:ct,mt:ce,mc:N,pc:ee,pbc:U,n:x,o:e};return{render:D,hydrate:void 0,createApp:Up(D)}}function pi({type:e,props:t},r){return r==="svg"&&e==="foreignObject"||r==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:r}function sr({effect:e,job:t},r){r?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Qp(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ls(e,t,r=!1){const o=e.children,n=t.children;if(q(o)&&q(n))for(let i=0;i<o.length;i++){const s=o[i];let a=n[i];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=n[i]=qt(n[i]),a.el=s.el),!r&&a.patchFlag!==-2&&Ls(s,a)),a.type===Kn&&(a.el=s.el)}}function eg(e){const t=e.slice(),r=[0];let o,n,i,s,a;const l=e.length;for(o=0;o<l;o++){const u=e[o];if(u!==0){if(n=r[r.length-1],e[n]<u){t[o]=n,r.push(o);continue}for(i=0,s=r.length-1;i<s;)a=i+s>>1,e[r[a]]<u?i=a+1:s=a;u<e[r[i]]&&(i>0&&(t[o]=r[i-1]),r[i]=o)}}for(i=r.length,s=r[i-1];i-- >0;)r[i]=s,s=t[s];return r}function Du(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Du(t)}function fa(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const tg=Symbol.for("v-scx"),rg=()=>Qe(tg);function Rt(e,t,r){return ju(e,t,r)}function ju(e,t,r=me){const{immediate:o,deep:n,flush:i,once:s}=r,a=Ee({},r),l=t&&o||!t&&i!=="post";let u;if(vo){if(i==="sync"){const p=rg();u=p.__watcherHandles||(p.__watcherHandles=[])}else if(!l){const p=()=>{};return p.stop=wt,p.resume=wt,p.pause=wt,p}}const c=Be;a.call=(p,h,b)=>gt(p,c,h,b);let d=!1;i==="post"?a.scheduler=p=>{He(p,c&&c.suspense)}:i!=="sync"&&(d=!0,a.scheduler=(p,h)=>{h?p():ws(p)}),a.augmentJob=p=>{t&&(p.flags|=4),d&&(p.flags|=2,c&&(p.id=c.uid,p.i=c))};const f=hp(e,t,a);return vo&&(u?u.push(f):l&&f()),f}function og(e,t,r){const o=this.proxy,n=ke(e)?e.includes(".")?Mu(o,e):()=>o[e]:e.bind(o,o);let i;Y(t)?i=t:(i=t.handler,r=t);const s=No(this),a=ju(n,i.bind(o),r);return s(),a}function Mu(e,t){const r=t.split(".");return()=>{let o=e;for(let n=0;n<r.length&&o;n++)o=o[r[n]];return o}}const ng=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${lt(t)}Modifiers`]||e[`${yr(t)}Modifiers`];function ig(e,t,...r){if(e.isUnmounted)return;const o=e.vnode.props||me;let n=r;const i=t.startsWith("update:"),s=i&&ng(o,t.slice(7));s&&(s.trim&&(n=r.map(c=>ke(c)?c.trim():c)),s.number&&(n=r.map(Ef)));let a,l=o[a=ii(t)]||o[a=ii(lt(t))];!l&&i&&(l=o[a=ii(yr(t))]),l&&gt(l,e,6,n);const u=o[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,gt(u,e,6,n)}}function Nu(e,t,r=!1){const o=t.emitsCache,n=o.get(e);if(n!==void 0)return n;const i=e.emits;let s={},a=!1;if(!Y(e)){const l=u=>{const c=Nu(u,t,!0);c&&(a=!0,Ee(s,c))};!r&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!i&&!a?(Ce(e)&&o.set(e,null),null):(q(i)?i.forEach(l=>s[l]=null):Ee(s,i),Ce(e)&&o.set(e,s),s)}function Wn(e,t){return!e||!An(t)?!1:(t=t.slice(2).replace(/Once$/,""),le(e,t[0].toLowerCase()+t.slice(1))||le(e,yr(t))||le(e,t))}function pa(e){const{type:t,vnode:r,proxy:o,withProxy:n,propsOptions:[i],slots:s,attrs:a,emit:l,render:u,renderCache:c,props:d,data:f,setupState:p,ctx:h,inheritAttrs:b}=e,C=Cn(e);let k,_;try{if(r.shapeFlag&4){const y=n||o,P=y;k=$t(u.call(P,y,c,d,p,f,h)),_=a}else{const y=t;k=$t(y.length>1?y(d,{attrs:a,slots:s,emit:l}):y(d,null)),_=t.props?a:sg(a)}}catch(y){to.length=0,zn(y,e,1),k=we(We)}let w=k;if(_&&b!==!1){const y=Object.keys(_),{shapeFlag:P}=w;y.length&&P&7&&(i&&y.some(ms)&&(_=ag(_,i)),w=er(w,_,!1,!0))}return r.dirs&&(w=er(w,null,!1,!0),w.dirs=w.dirs?w.dirs.concat(r.dirs):r.dirs),r.transition&&mr(w,r.transition),k=w,Cn(C),k}const sg=e=>{let t;for(const r in e)(r==="class"||r==="style"||An(r))&&((t||(t={}))[r]=e[r]);return t},ag=(e,t)=>{const r={};for(const o in e)(!ms(o)||!(o.slice(9)in t))&&(r[o]=e[o]);return r};function lg(e,t,r){const{props:o,children:n,component:i}=e,{props:s,children:a,patchFlag:l}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(r&&l>=0){if(l&1024)return!0;if(l&16)return o?ga(o,s,u):!!s;if(l&8){const c=t.dynamicProps;for(let d=0;d<c.length;d++){const f=c[d];if(s[f]!==o[f]&&!Wn(u,f))return!0}}}else return(n||a)&&(!a||!a.$stable)?!0:o===s?!1:o?s?ga(o,s,u):!0:!!s;return!1}function ga(e,t,r){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let n=0;n<o.length;n++){const i=o[n];if(t[i]!==e[i]&&!Wn(r,i))return!0}return!1}function cg({vnode:e,parent:t},r){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=r,t=t.parent;else break}}const Fu=e=>e.__isSuspense;function ug(e,t){t&&t.pendingBranch?q(e)?t.effects.push(...e):t.effects.push(e):vp(e)}const Le=Symbol.for("v-fgt"),Kn=Symbol.for("v-txt"),We=Symbol.for("v-cmt"),ln=Symbol.for("v-stc"),to=[];let Ze=null;function se(e=!1){to.push(Ze=e?null:[])}function dg(){to.pop(),Ze=to[to.length-1]||null}let go=1;function ha(e,t=!1){go+=e,e<0&&Ze&&t&&(Ze.hasOnce=!0)}function zu(e){return e.dynamicChildren=go>0?Ze||Tr:null,dg(),go>0&&Ze&&Ze.push(e),e}function Te(e,t,r,o,n,i){return zu(ge(e,t,r,o,n,i,!0))}function it(e,t,r,o,n){return zu(we(e,t,r,o,n,!0))}function ho(e){return e?e.__v_isVNode===!0:!1}function ur(e,t){return e.type===t.type&&e.key===t.key}const Hu=({key:e})=>e??null,cn=({ref:e,ref_key:t,ref_for:r})=>(typeof e=="number"&&(e=""+e),e!=null?ke(e)||xe(e)||Y(e)?{i:Pe,r:e,k:t,f:!!r}:e:null);function ge(e,t=null,r=null,o=0,n=null,i=e===Le?0:1,s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Hu(t),ref:t&&cn(t),scopeId:su,slotScopeIds:null,children:r,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:Pe};return a?(Is(l,r),i&128&&e.normalize(l)):r&&(l.shapeFlag|=ke(r)?8:16),go>0&&!s&&Ze&&(l.patchFlag>0||i&6)&&l.patchFlag!==32&&Ze.push(l),l}const we=fg;function fg(e,t=null,r=null,o=0,n=null,i=!1){if((!e||e===Su)&&(e=We),ho(e)){const a=er(e,t,!0);return r&&Is(a,r),go>0&&!i&&Ze&&(a.shapeFlag&6?Ze[Ze.indexOf(e)]=a:Ze.push(a)),a.patchFlag=-2,a}if(_g(e)&&(e=e.__vccOpts),t){t=pg(t);let{class:a,style:l}=t;a&&!ke(a)&&(t.class=Ir(a)),Ce(l)&&(xs(l)&&!q(l)&&(l=Ee({},l)),t.style=Mn(l))}const s=ke(e)?1:Fu(e)?128:lu(e)?64:Ce(e)?4:Y(e)?2:0;return ge(e,t,r,o,n,s,i,!0)}function pg(e){return e?xs(e)||Pu(e)?Ee({},e):e:null}function er(e,t,r=!1,o=!1){const{props:n,ref:i,patchFlag:s,children:a,transition:l}=e,u=t?he(n||{},t):n,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Hu(u),ref:t&&t.ref?r&&i?q(i)?i.concat(cn(t)):[i,cn(t)]:cn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Le?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&er(e.ssContent),ssFallback:e.ssFallback&&er(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&o&&mr(c,l.clone(c)),c}function gg(e=" ",t=0){return we(Kn,null,e,t)}function kw(e,t){const r=we(ln,null,e);return r.staticCount=t,r}function mo(e="",t=!1){return t?(se(),it(We,null,e)):we(We,null,e)}function $t(e){return e==null||typeof e=="boolean"?we(We):q(e)?we(Le,null,e.slice()):ho(e)?qt(e):we(Kn,null,String(e))}function qt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:er(e)}function Is(e,t){let r=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(q(t))r=16;else if(typeof t=="object")if(o&65){const n=t.default;n&&(n._c&&(n._d=!1),Is(e,n()),n._c&&(n._d=!0));return}else{r=32;const n=t._;!n&&!Pu(t)?t._ctx=Pe:n===3&&Pe&&(Pe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else Y(t)?(t={default:t,_ctx:Pe},r=32):(t=String(t),o&64?(r=16,t=[gg(t)]):r=8);e.children=t,e.shapeFlag|=r}function he(...e){const t={};for(let r=0;r<e.length;r++){const o=e[r];for(const n in o)if(n==="class")t.class!==o.class&&(t.class=Ir([t.class,o.class]));else if(n==="style")t.style=Mn([t.style,o.style]);else if(An(n)){const i=t[n],s=o[n];s&&i!==s&&!(q(i)&&i.includes(s))&&(t[n]=i?[].concat(i,s):s)}else n!==""&&(t[n]=o[n])}return t}function Ct(e,t,r,o=null){gt(e,t,7,[r,o])}const hg=Ru();let mg=0;function bg(e,t,r){const o=e.type,n=(t?t.appContext:e.appContext)||hg,i={uid:mg++,vnode:e,type:o,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ic(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ou(o,n),emitsOptions:Nu(o,n),emit:null,emitted:null,propsDefaults:me,inheritAttrs:o.inheritAttrs,ctx:me,data:me,props:me,attrs:me,slots:me,refs:me,setupState:me,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=ig.bind(null,i),e.ce&&e.ce(i),i}let Be=null;const bo=()=>Be||Pe;let xn,Ui;{const e=jn(),t=(r,o)=>{let n;return(n=e[r])||(n=e[r]=[]),n.push(o),i=>{n.length>1?n.forEach(s=>s(i)):n[0](i)}};xn=t("__VUE_INSTANCE_SETTERS__",r=>Be=r),Ui=t("__VUE_SSR_SETTERS__",r=>vo=r)}const No=e=>{const t=Be;return xn(e),e.scope.on(),()=>{e.scope.off(),xn(t)}},ma=()=>{Be&&Be.scope.off(),xn(null)};function Uu(e){return e.vnode.shapeFlag&4}let vo=!1;function vg(e,t=!1,r=!1){t&&Ui(t);const{props:o,children:n}=e.vnode,i=Uu(e);Wp(e,o,i,t),Jp(e,n,r);const s=i?yg(e,t):void 0;return t&&Ui(!1),s}function yg(e,t){const r=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Dp);const{setup:o}=r;if(o){rr();const n=e.setupContext=o.length>1?Sg(e):null,i=No(e),s=Mo(o,e,0,[e.props,n]),a=Tc(s);if(or(),i(),(a||e.sp)&&!Or(e)&&mu(e),a){if(s.then(ma,ma),t)return s.then(l=>{ba(e,l)}).catch(l=>{zn(l,e,0)});e.asyncDep=s}else ba(e,s)}else Vu(e)}function ba(e,t,r){Y(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Ce(t)&&(e.setupState=eu(t)),Vu(e)}function Vu(e,t,r){const o=e.type;e.render||(e.render=o.render||wt);{const n=No(e);rr();try{jp(e)}finally{or(),n()}}}const Cg={get(e,t){return je(e,"get",""),e[t]}};function Sg(e){const t=r=>{e.exposed=r||{}};return{attrs:new Proxy(e.attrs,Cg),slots:e.slots,emit:e.emit,expose:t}}function qn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(eu(Rs(e.exposed)),{get(t,r){if(r in t)return t[r];if(r in eo)return eo[r](e)},has(t,r){return r in t||r in eo}})):e.proxy}function kg(e,t=!0){return Y(e)?e.displayName||e.name:e.name||t&&e.__name}function _g(e){return Y(e)&&"__vccOpts"in e}const nt=(e,t)=>pp(e,t,vo);function Ds(e,t,r){const o=arguments.length;return o===2?Ce(t)&&!q(t)?ho(t)?we(e,null,[t]):we(e,t):we(e,null,t):(o>3?r=Array.prototype.slice.call(arguments,2):o===3&&ho(r)&&(r=[r]),we(e,t,r))}const $g="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Vi;const va=typeof window<"u"&&window.trustedTypes;if(va)try{Vi=va.createPolicy("vue",{createHTML:e=>e})}catch{}const Wu=Vi?e=>Vi.createHTML(e):e=>e,xg="http://www.w3.org/2000/svg",Rg="http://www.w3.org/1998/Math/MathML",At=typeof document<"u"?document:null,ya=At&&At.createElement("template"),wg={insert:(e,t,r)=>{t.insertBefore(e,r||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,r,o)=>{const n=t==="svg"?At.createElementNS(xg,e):t==="mathml"?At.createElementNS(Rg,e):r?At.createElement(e,{is:r}):At.createElement(e);return e==="select"&&o&&o.multiple!=null&&n.setAttribute("multiple",o.multiple),n},createText:e=>At.createTextNode(e),createComment:e=>At.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>At.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,r,o,n,i){const s=r?r.previousSibling:t.lastChild;if(n&&(n===i||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),r),!(n===i||!(n=n.nextSibling)););else{ya.innerHTML=Wu(o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e);const a=ya.content;if(o==="svg"||o==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,r)}return[s?s.nextSibling:t.firstChild,r?r.previousSibling:t.lastChild]}},Nt="transition",Hr="animation",Br=Symbol("_vtc"),Ku={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},qu=Ee({},fu,Ku),Tg=e=>(e.displayName="Transition",e.props=qu,e),_w=Tg((e,{slots:t})=>Ds(_p,Gu(e),t)),ar=(e,t=[])=>{q(e)?e.forEach(r=>r(...t)):e&&e(...t)},Ca=e=>e?q(e)?e.some(t=>t.length>1):e.length>1:!1;function Gu(e){const t={};for(const L in e)L in Ku||(t[L]=e[L]);if(e.css===!1)return t;const{name:r="v",type:o,duration:n,enterFromClass:i=`${r}-enter-from`,enterActiveClass:s=`${r}-enter-active`,enterToClass:a=`${r}-enter-to`,appearFromClass:l=i,appearActiveClass:u=s,appearToClass:c=a,leaveFromClass:d=`${r}-leave-from`,leaveActiveClass:f=`${r}-leave-active`,leaveToClass:p=`${r}-leave-to`}=e,h=Pg(n),b=h&&h[0],C=h&&h[1],{onBeforeEnter:k,onEnter:_,onEnterCancelled:w,onLeave:y,onLeaveCancelled:P,onBeforeAppear:F=k,onAppear:z=_,onAppearCancelled:N=w}=t,O=(L,X,ce,_e)=>{L._enterCancelled=_e,Ht(L,X?c:a),Ht(L,X?u:s),ce&&ce()},U=(L,X)=>{L._isLeaving=!1,Ht(L,d),Ht(L,p),Ht(L,f),X&&X()},G=L=>(X,ce)=>{const _e=L?z:_,re=()=>O(X,L,ce);ar(_e,[X,re]),Sa(()=>{Ht(X,L?l:i),kt(X,L?c:a),Ca(_e)||ka(X,o,b,re)})};return Ee(t,{onBeforeEnter(L){ar(k,[L]),kt(L,i),kt(L,s)},onBeforeAppear(L){ar(F,[L]),kt(L,l),kt(L,u)},onEnter:G(!1),onAppear:G(!0),onLeave(L,X){L._isLeaving=!0;const ce=()=>U(L,X);kt(L,d),L._enterCancelled?(kt(L,f),Wi()):(Wi(),kt(L,f)),Sa(()=>{L._isLeaving&&(Ht(L,d),kt(L,p),Ca(y)||ka(L,o,C,ce))}),ar(y,[L,ce])},onEnterCancelled(L){O(L,!1,void 0,!0),ar(w,[L])},onAppearCancelled(L){O(L,!0,void 0,!0),ar(N,[L])},onLeaveCancelled(L){U(L),ar(P,[L])}})}function Pg(e){if(e==null)return null;if(Ce(e))return[gi(e.enter),gi(e.leave)];{const t=gi(e);return[t,t]}}function gi(e){return Of(e)}function kt(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.add(r)),(e[Br]||(e[Br]=new Set)).add(t)}function Ht(e,t){t.split(/\s+/).forEach(o=>o&&e.classList.remove(o));const r=e[Br];r&&(r.delete(t),r.size||(e[Br]=void 0))}function Sa(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Eg=0;function ka(e,t,r,o){const n=e._endId=++Eg,i=()=>{n===e._endId&&o()};if(r!=null)return setTimeout(i,r);const{type:s,timeout:a,propCount:l}=Ju(e,t);if(!s)return o();const u=s+"end";let c=0;const d=()=>{e.removeEventListener(u,f),i()},f=p=>{p.target===e&&++c>=l&&d()};setTimeout(()=>{c<l&&d()},a+1),e.addEventListener(u,f)}function Ju(e,t){const r=window.getComputedStyle(e),o=h=>(r[h]||"").split(", "),n=o(`${Nt}Delay`),i=o(`${Nt}Duration`),s=_a(n,i),a=o(`${Hr}Delay`),l=o(`${Hr}Duration`),u=_a(a,l);let c=null,d=0,f=0;t===Nt?s>0&&(c=Nt,d=s,f=i.length):t===Hr?u>0&&(c=Hr,d=u,f=l.length):(d=Math.max(s,u),c=d>0?s>u?Nt:Hr:null,f=c?c===Nt?i.length:l.length:0);const p=c===Nt&&/\b(transform|all)(,|$)/.test(o(`${Nt}Property`).toString());return{type:c,timeout:d,propCount:f,hasTransform:p}}function _a(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((r,o)=>$a(r)+$a(e[o])))}function $a(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Wi(){return document.body.offsetHeight}function Og(e,t,r){const o=e[Br];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):r?e.setAttribute("class",t):e.className=t}const xa=Symbol("_vod"),Bg=Symbol("_vsh"),Ag=Symbol(""),Lg=/(^|;)\s*display\s*:/;function Ig(e,t,r){const o=e.style,n=ke(r);let i=!1;if(r&&!n){if(t)if(ke(t))for(const s of t.split(";")){const a=s.slice(0,s.indexOf(":")).trim();r[a]==null&&un(o,a,"")}else for(const s in t)r[s]==null&&un(o,s,"");for(const s in r)s==="display"&&(i=!0),un(o,s,r[s])}else if(n){if(t!==r){const s=o[Ag];s&&(r+=";"+s),o.cssText=r,i=Lg.test(r)}}else t&&e.removeAttribute("style");xa in e&&(e[xa]=i?o.display:"",e[Bg]&&(o.display="none"))}const Ra=/\s*!important$/;function un(e,t,r){if(q(r))r.forEach(o=>un(e,t,o));else if(r==null&&(r=""),t.startsWith("--"))e.setProperty(t,r);else{const o=Dg(e,t);Ra.test(r)?e.setProperty(yr(o),r.replace(Ra,""),"important"):e[o]=r}}const wa=["Webkit","Moz","ms"],hi={};function Dg(e,t){const r=hi[t];if(r)return r;let o=lt(t);if(o!=="filter"&&o in e)return hi[t]=o;o=Dn(o);for(let n=0;n<wa.length;n++){const i=wa[n]+o;if(i in e)return hi[t]=i}return t}const Ta="http://www.w3.org/1999/xlink";function Pa(e,t,r,o,n,i=Mf(t)){o&&t.startsWith("xlink:")?r==null?e.removeAttributeNS(Ta,t.slice(6,t.length)):e.setAttributeNS(Ta,t,r):r==null||i&&!Bc(r)?e.removeAttribute(t):e.setAttribute(t,i?"":jt(r)?String(r):r)}function Ea(e,t,r,o,n){if(t==="innerHTML"||t==="textContent"){r!=null&&(e[t]=t==="innerHTML"?Wu(r):r);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const a=i==="OPTION"?e.getAttribute("value")||"":e.value,l=r==null?e.type==="checkbox"?"on":"":String(r);(a!==l||!("_value"in e))&&(e.value=l),r==null&&e.removeAttribute(t),e._value=r;return}let s=!1;if(r===""||r==null){const a=typeof e[t];a==="boolean"?r=Bc(r):r==null&&a==="string"?(r="",s=!0):a==="number"&&(r=0,s=!0)}try{e[t]=r}catch{}s&&e.removeAttribute(n||t)}function jg(e,t,r,o){e.addEventListener(t,r,o)}function Mg(e,t,r,o){e.removeEventListener(t,r,o)}const Oa=Symbol("_vei");function Ng(e,t,r,o,n=null){const i=e[Oa]||(e[Oa]={}),s=i[t];if(o&&s)s.value=o;else{const[a,l]=Fg(t);if(o){const u=i[t]=Ug(o,n);jg(e,a,u,l)}else s&&(Mg(e,a,s,l),i[t]=void 0)}}const Ba=/(?:Once|Passive|Capture)$/;function Fg(e){let t;if(Ba.test(e)){t={};let o;for(;o=e.match(Ba);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):yr(e.slice(2)),t]}let mi=0;const zg=Promise.resolve(),Hg=()=>mi||(zg.then(()=>mi=0),mi=Date.now());function Ug(e,t){const r=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=r.attached)return;gt(Vg(o,r.value),t,5,[o])};return r.value=e,r.attached=Hg(),r}function Vg(e,t){if(q(t)){const r=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{r.call(e),e._stopped=!0},t.map(o=>n=>!n._stopped&&o&&o(n))}else return t}const Aa=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Wg=(e,t,r,o,n,i)=>{const s=n==="svg";t==="class"?Og(e,o,s):t==="style"?Ig(e,r,o):An(t)?ms(t)||Ng(e,t,r,o,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Kg(e,t,o,s))?(Ea(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Pa(e,t,o,s,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ke(o))?Ea(e,lt(t),o,i,t):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),Pa(e,t,o,s))};function Kg(e,t,r,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&Aa(t)&&Y(r));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Aa(t)&&ke(r)?!1:t in e}const Yu=new WeakMap,Xu=new WeakMap,Rn=Symbol("_moveCb"),La=Symbol("_enterCb"),qg=e=>(delete e.props.mode,e),Gg=qg({name:"TransitionGroup",props:Ee({},qu,{tag:String,moveClass:String}),setup(e,{slots:t}){const r=bo(),o=du();let n,i;return vu(()=>{if(!n.length)return;const s=e.moveClass||`${e.name||"v"}-move`;if(!Qg(n[0].el,r.vnode.el,s))return;n.forEach(Yg),n.forEach(Xg);const a=n.filter(Zg);Wi(),a.forEach(l=>{const u=l.el,c=u.style;kt(u,s),c.transform=c.webkitTransform=c.transitionDuration="";const d=u[Rn]=f=>{f&&f.target!==u||(!f||/transform$/.test(f.propertyName))&&(u.removeEventListener("transitionend",d),u[Rn]=null,Ht(u,s))};u.addEventListener("transitionend",d)})}),()=>{const s=ne(e),a=Gu(s);let l=s.tag||Le;if(n=[],i)for(let u=0;u<i.length;u++){const c=i[u];c.el&&c.el instanceof Element&&(n.push(c),mr(c,po(c,a,o,r)),Yu.set(c,c.el.getBoundingClientRect()))}i=t.default?Ts(t.default()):[];for(let u=0;u<i.length;u++){const c=i[u];c.key!=null&&mr(c,po(c,a,o,r))}return we(l,null,i)}}}),Jg=Gg;function Yg(e){const t=e.el;t[Rn]&&t[Rn](),t[La]&&t[La]()}function Xg(e){Xu.set(e,e.el.getBoundingClientRect())}function Zg(e){const t=Yu.get(e),r=Xu.get(e),o=t.left-r.left,n=t.top-r.top;if(o||n){const i=e.el.style;return i.transform=i.webkitTransform=`translate(${o}px,${n}px)`,i.transitionDuration="0s",e}}function Qg(e,t,r){const o=e.cloneNode(),n=e[Br];n&&n.forEach(a=>{a.split(/\s+/).forEach(l=>l&&o.classList.remove(l))}),r.split(/\s+/).forEach(a=>a&&o.classList.add(a)),o.style.display="none";const i=t.nodeType===1?t:t.parentNode;i.appendChild(o);const{hasTransform:s}=Ju(o);return i.removeChild(o),s}const eh=Ee({patchProp:Wg},wg);let Ia;function th(){return Ia||(Ia=Xp(eh))}const rh=(...e)=>{const t=th().createApp(...e),{mount:r}=t;return t.mount=o=>{const n=nh(o);if(!n)return;const i=t._component;!Y(i)&&!i.render&&!i.template&&(i.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const s=r(n,!1,oh(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),s},t};function oh(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function nh(e){return ke(e)?document.querySelector(e):e}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Zu;const Gn=e=>Zu=e,Qu=Symbol();function Ki(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var ro;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(ro||(ro={}));function ih(){const e=Dc(!0),t=e.run(()=>Tt({}));let r=[],o=[];const n=Rs({install(i){Gn(n),n._a=i,i.provide(Qu,n),i.config.globalProperties.$pinia=n,o.forEach(s=>r.push(s)),o=[]},use(i){return this._a?r.push(i):o.push(i),this},_p:r,_a:null,_e:e,_s:new Map,state:t});return n}const ed=()=>{};function Da(e,t,r,o=ed){e.push(t);const n=()=>{const i=e.indexOf(t);i>-1&&(e.splice(i,1),o())};return!r&&jc()&&Nf(n),n}function kr(e,...t){e.slice().forEach(r=>{r(...t)})}const sh=e=>e(),ja=Symbol(),bi=Symbol();function qi(e,t){e instanceof Map&&t instanceof Map?t.forEach((r,o)=>e.set(o,r)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const r in t){if(!t.hasOwnProperty(r))continue;const o=t[r],n=e[r];Ki(n)&&Ki(o)&&e.hasOwnProperty(r)&&!xe(o)&&!Zt(o)?e[r]=qi(n,o):e[r]=o}return e}const ah=Symbol();function lh(e){return!Ki(e)||!e.hasOwnProperty(ah)}const{assign:Ut}=Object;function ch(e){return!!(xe(e)&&e.effect)}function uh(e,t,r,o){const{state:n,actions:i,getters:s}=t,a=r.state.value[e];let l;function u(){a||(r.state.value[e]=n?n():{});const c=cp(r.state.value[e]);return Ut(c,i,Object.keys(s||{}).reduce((d,f)=>(d[f]=Rs(nt(()=>{Gn(r);const p=r._s.get(e);return s[f].call(p,p)})),d),{}))}return l=td(e,u,t,r,o,!0),l}function td(e,t,r={},o,n,i){let s;const a=Ut({actions:{}},r),l={deep:!0};let u,c,d=[],f=[],p;const h=o.state.value[e];!i&&!h&&(o.state.value[e]={}),Tt({});let b;function C(N){let O;u=c=!1,typeof N=="function"?(N(o.state.value[e]),O={type:ro.patchFunction,storeId:e,events:p}):(qi(o.state.value[e],N),O={type:ro.patchObject,payload:N,storeId:e,events:p});const U=b=Symbol();Hn().then(()=>{b===U&&(u=!0)}),c=!0,kr(d,O,o.state.value[e])}const k=i?function(){const{state:O}=r,U=O?O():{};this.$patch(G=>{Ut(G,U)})}:ed;function _(){s.stop(),d=[],f=[],o._s.delete(e)}const w=(N,O="")=>{if(ja in N)return N[bi]=O,N;const U=function(){Gn(o);const G=Array.from(arguments),L=[],X=[];function ce(K){L.push(K)}function _e(K){X.push(K)}kr(f,{args:G,name:U[bi],store:P,after:ce,onError:_e});let re;try{re=N.apply(this&&this.$id===e?this:P,G)}catch(K){throw kr(X,K),K}return re instanceof Promise?re.then(K=>(kr(L,K),K)).catch(K=>(kr(X,K),Promise.reject(K))):(kr(L,re),re)};return U[ja]=!0,U[bi]=O,U},y={_p:o,$id:e,$onAction:Da.bind(null,f),$patch:C,$reset:k,$subscribe(N,O={}){const U=Da(d,N,O.detached,()=>G()),G=s.run(()=>Rt(()=>o.state.value[e],L=>{(O.flush==="sync"?c:u)&&N({storeId:e,type:ro.direct,events:p},L)},Ut({},l,O)));return U},$dispose:_},P=Dr(y);o._s.set(e,P);const z=(o._a&&o._a.runWithContext||sh)(()=>o._e.run(()=>(s=Dc()).run(()=>t({action:w}))));for(const N in z){const O=z[N];if(xe(O)&&!ch(O)||Zt(O))i||(h&&lh(O)&&(xe(O)?O.value=h[N]:qi(O,h[N])),o.state.value[e][N]=O);else if(typeof O=="function"){const U=w(O,N);z[N]=U,a.actions[N]=O}}return Ut(P,z),Ut(ne(P),z),Object.defineProperty(P,"$state",{get:()=>o.state.value[e],set:N=>{C(O=>{Ut(O,N)})}}),o._p.forEach(N=>{Ut(P,s.run(()=>N({store:P,app:o._a,pinia:o,options:a})))}),h&&i&&r.hydrate&&r.hydrate(P.$state,h),u=!0,c=!0,P}/*! #__NO_SIDE_EFFECTS__ */function dh(e,t,r){let o,n;const i=typeof t=="function";o=e,n=i?r:t;function s(a,l){const u=Vp();return a=a||(u?Qe(Qu,null):null),a&&Gn(a),a=Zu,a._s.has(o)||(i?td(o,t,n,a):uh(o,n,a)),a._s.get(o)}return s.$id=o,s}/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const $r=typeof document<"u";function rd(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function fh(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&rd(e.default)}const ae=Object.assign;function vi(e,t){const r={};for(const o in t){const n=t[o];r[o]=ht(n)?n.map(e):e(n)}return r}const oo=()=>{},ht=Array.isArray,od=/#/g,ph=/&/g,gh=/\//g,hh=/=/g,mh=/\?/g,nd=/\+/g,bh=/%5B/g,vh=/%5D/g,id=/%5E/g,yh=/%60/g,sd=/%7B/g,Ch=/%7C/g,ad=/%7D/g,Sh=/%20/g;function js(e){return encodeURI(""+e).replace(Ch,"|").replace(bh,"[").replace(vh,"]")}function kh(e){return js(e).replace(sd,"{").replace(ad,"}").replace(id,"^")}function Gi(e){return js(e).replace(nd,"%2B").replace(Sh,"+").replace(od,"%23").replace(ph,"%26").replace(yh,"`").replace(sd,"{").replace(ad,"}").replace(id,"^")}function _h(e){return Gi(e).replace(hh,"%3D")}function $h(e){return js(e).replace(od,"%23").replace(mh,"%3F")}function xh(e){return e==null?"":$h(e).replace(gh,"%2F")}function yo(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Rh=/\/$/,wh=e=>e.replace(Rh,"");function yi(e,t,r="/"){let o,n={},i="",s="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),i=t.slice(l+1,a>-1?a:t.length),n=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=Oh(o??t,r),{fullPath:o+(i&&"?")+i+s,path:o,query:n,hash:yo(s)}}function Th(e,t){const r=t.query?e(t.query):"";return t.path+(r&&"?")+r+(t.hash||"")}function Ma(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ph(e,t,r){const o=t.matched.length-1,n=r.matched.length-1;return o>-1&&o===n&&Ar(t.matched[o],r.matched[n])&&ld(t.params,r.params)&&e(t.query)===e(r.query)&&t.hash===r.hash}function Ar(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ld(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(!Eh(e[r],t[r]))return!1;return!0}function Eh(e,t){return ht(e)?Na(e,t):ht(t)?Na(t,e):e===t}function Na(e,t){return ht(t)?e.length===t.length&&e.every((r,o)=>r===t[o]):e.length===1&&e[0]===t}function Oh(e,t){if(e.startsWith("/"))return e;if(!e)return t;const r=t.split("/"),o=e.split("/"),n=o[o.length-1];(n===".."||n===".")&&o.push("");let i=r.length-1,s,a;for(s=0;s<o.length;s++)if(a=o[s],a!==".")if(a==="..")i>1&&i--;else break;return r.slice(0,i).join("/")+"/"+o.slice(s).join("/")}const Ft={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Co;(function(e){e.pop="pop",e.push="push"})(Co||(Co={}));var no;(function(e){e.back="back",e.forward="forward",e.unknown=""})(no||(no={}));function Bh(e){if(!e)if($r){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),wh(e)}const Ah=/^[^#]+#/;function Lh(e,t){return e.replace(Ah,"#")+t}function Ih(e,t){const r=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-r.left-(t.left||0),top:o.top-r.top-(t.top||0)}}const Jn=()=>({left:window.scrollX,top:window.scrollY});function Dh(e){let t;if("el"in e){const r=e.el,o=typeof r=="string"&&r.startsWith("#"),n=typeof r=="string"?o?document.getElementById(r.slice(1)):document.querySelector(r):r;if(!n)return;t=Ih(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Fa(e,t){return(history.state?history.state.position-t:-1)+e}const Ji=new Map;function jh(e,t){Ji.set(e,t)}function Mh(e){const t=Ji.get(e);return Ji.delete(e),t}let Nh=()=>location.protocol+"//"+location.host;function cd(e,t){const{pathname:r,search:o,hash:n}=t,i=e.indexOf("#");if(i>-1){let a=n.includes(e.slice(i))?e.slice(i).length:1,l=n.slice(a);return l[0]!=="/"&&(l="/"+l),Ma(l,"")}return Ma(r,e)+o+n}function Fh(e,t,r,o){let n=[],i=[],s=null;const a=({state:f})=>{const p=cd(e,location),h=r.value,b=t.value;let C=0;if(f){if(r.value=p,t.value=f,s&&s===h){s=null;return}C=b?f.position-b.position:0}else o(p);n.forEach(k=>{k(r.value,h,{delta:C,type:Co.pop,direction:C?C>0?no.forward:no.back:no.unknown})})};function l(){s=r.value}function u(f){n.push(f);const p=()=>{const h=n.indexOf(f);h>-1&&n.splice(h,1)};return i.push(p),p}function c(){const{history:f}=window;f.state&&f.replaceState(ae({},f.state,{scroll:Jn()}),"")}function d(){for(const f of i)f();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:u,destroy:d}}function za(e,t,r,o=!1,n=!1){return{back:e,current:t,forward:r,replaced:o,position:window.history.length,scroll:n?Jn():null}}function zh(e){const{history:t,location:r}=window,o={value:cd(e,r)},n={value:t.state};n.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(l,u,c){const d=e.indexOf("#"),f=d>-1?(r.host&&document.querySelector("base")?e:e.slice(d))+l:Nh()+e+l;try{t[c?"replaceState":"pushState"](u,"",f),n.value=u}catch(p){console.error(p),r[c?"replace":"assign"](f)}}function s(l,u){const c=ae({},t.state,za(n.value.back,l,n.value.forward,!0),u,{position:n.value.position});i(l,c,!0),o.value=l}function a(l,u){const c=ae({},n.value,t.state,{forward:l,scroll:Jn()});i(c.current,c,!0);const d=ae({},za(o.value,l,null),{position:c.position+1},u);i(l,d,!1),o.value=l}return{location:o,state:n,push:a,replace:s}}function Hh(e){e=Bh(e);const t=zh(e),r=Fh(e,t.state,t.location,t.replace);function o(i,s=!0){s||r.pauseListeners(),history.go(i)}const n=ae({location:"",base:e,go:o,createHref:Lh.bind(null,e)},t,r);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function Uh(e){return typeof e=="string"||e&&typeof e=="object"}function ud(e){return typeof e=="string"||typeof e=="symbol"}const dd=Symbol("");var Ha;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Ha||(Ha={}));function Lr(e,t){return ae(new Error,{type:e,[dd]:!0},t)}function Ot(e,t){return e instanceof Error&&dd in e&&(t==null||!!(e.type&t))}const Ua="[^/]+?",Vh={sensitive:!1,strict:!1,start:!0,end:!0},Wh=/[.+*?^${}()[\]/\\]/g;function Kh(e,t){const r=ae({},Vh,t),o=[];let n=r.start?"^":"";const i=[];for(const u of e){const c=u.length?[]:[90];r.strict&&!u.length&&(n+="/");for(let d=0;d<u.length;d++){const f=u[d];let p=40+(r.sensitive?.25:0);if(f.type===0)d||(n+="/"),n+=f.value.replace(Wh,"\\$&"),p+=40;else if(f.type===1){const{value:h,repeatable:b,optional:C,regexp:k}=f;i.push({name:h,repeatable:b,optional:C});const _=k||Ua;if(_!==Ua){p+=10;try{new RegExp(`(${_})`)}catch(y){throw new Error(`Invalid custom RegExp for param "${h}" (${_}): `+y.message)}}let w=b?`((?:${_})(?:/(?:${_}))*)`:`(${_})`;d||(w=C&&u.length<2?`(?:/${w})`:"/"+w),C&&(w+="?"),n+=w,p+=20,C&&(p+=-8),b&&(p+=-20),_===".*"&&(p+=-50)}c.push(p)}o.push(c)}if(r.strict&&r.end){const u=o.length-1;o[u][o[u].length-1]+=.7000000000000001}r.strict||(n+="/?"),r.end?n+="$":r.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const s=new RegExp(n,r.sensitive?"":"i");function a(u){const c=u.match(s),d={};if(!c)return null;for(let f=1;f<c.length;f++){const p=c[f]||"",h=i[f-1];d[h.name]=p&&h.repeatable?p.split("/"):p}return d}function l(u){let c="",d=!1;for(const f of e){(!d||!c.endsWith("/"))&&(c+="/"),d=!1;for(const p of f)if(p.type===0)c+=p.value;else if(p.type===1){const{value:h,repeatable:b,optional:C}=p,k=h in u?u[h]:"";if(ht(k)&&!b)throw new Error(`Provided param "${h}" is an array but it is not repeatable (* or + modifiers)`);const _=ht(k)?k.join("/"):k;if(!_)if(C)f.length<2&&(c.endsWith("/")?c=c.slice(0,-1):d=!0);else throw new Error(`Missing required param "${h}"`);c+=_}}return c||"/"}return{re:s,score:o,keys:i,parse:a,stringify:l}}function qh(e,t){let r=0;for(;r<e.length&&r<t.length;){const o=t[r]-e[r];if(o)return o;r++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function fd(e,t){let r=0;const o=e.score,n=t.score;for(;r<o.length&&r<n.length;){const i=qh(o[r],n[r]);if(i)return i;r++}if(Math.abs(n.length-o.length)===1){if(Va(o))return 1;if(Va(n))return-1}return n.length-o.length}function Va(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Gh={type:0,value:""},Jh=/[a-zA-Z0-9_]/;function Yh(e){if(!e)return[[]];if(e==="/")return[[Gh]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${r})/"${u}": ${p}`)}let r=0,o=r;const n=[];let i;function s(){i&&n.push(i),i=[]}let a=0,l,u="",c="";function d(){u&&(r===0?i.push({type:0,value:u}):r===1||r===2||r===3?(i.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:u,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),u="")}function f(){u+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&r!==2){o=r,r=4;continue}switch(r){case 0:l==="/"?(u&&d(),s()):l===":"?(d(),r=1):f();break;case 4:f(),r=o;break;case 1:l==="("?r=2:Jh.test(l)?f():(d(),r=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:r=3:c+=l;break;case 3:d(),r=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return r===2&&t(`Unfinished custom RegExp for param "${u}"`),d(),s(),n}function Xh(e,t,r){const o=Kh(Yh(e.path),r),n=ae(o,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function Zh(e,t){const r=[],o=new Map;t=Ga({strict:!1,end:!0,sensitive:!1},t);function n(d){return o.get(d)}function i(d,f,p){const h=!p,b=Ka(d);b.aliasOf=p&&p.record;const C=Ga(t,d),k=[b];if("alias"in d){const y=typeof d.alias=="string"?[d.alias]:d.alias;for(const P of y)k.push(Ka(ae({},b,{components:p?p.record.components:b.components,path:P,aliasOf:p?p.record:b})))}let _,w;for(const y of k){const{path:P}=y;if(f&&P[0]!=="/"){const F=f.record.path,z=F[F.length-1]==="/"?"":"/";y.path=f.record.path+(P&&z+P)}if(_=Xh(y,f,C),p?p.alias.push(_):(w=w||_,w!==_&&w.alias.push(_),h&&d.name&&!qa(_)&&s(d.name)),pd(_)&&l(_),b.children){const F=b.children;for(let z=0;z<F.length;z++)i(F[z],_,p&&p.children[z])}p=p||_}return w?()=>{s(w)}:oo}function s(d){if(ud(d)){const f=o.get(d);f&&(o.delete(d),r.splice(r.indexOf(f),1),f.children.forEach(s),f.alias.forEach(s))}else{const f=r.indexOf(d);f>-1&&(r.splice(f,1),d.record.name&&o.delete(d.record.name),d.children.forEach(s),d.alias.forEach(s))}}function a(){return r}function l(d){const f=tm(d,r);r.splice(f,0,d),d.record.name&&!qa(d)&&o.set(d.record.name,d)}function u(d,f){let p,h={},b,C;if("name"in d&&d.name){if(p=o.get(d.name),!p)throw Lr(1,{location:d});C=p.record.name,h=ae(Wa(f.params,p.keys.filter(w=>!w.optional).concat(p.parent?p.parent.keys.filter(w=>w.optional):[]).map(w=>w.name)),d.params&&Wa(d.params,p.keys.map(w=>w.name))),b=p.stringify(h)}else if(d.path!=null)b=d.path,p=r.find(w=>w.re.test(b)),p&&(h=p.parse(b),C=p.record.name);else{if(p=f.name?o.get(f.name):r.find(w=>w.re.test(f.path)),!p)throw Lr(1,{location:d,currentLocation:f});C=p.record.name,h=ae({},f.params,d.params),b=p.stringify(h)}const k=[];let _=p;for(;_;)k.unshift(_.record),_=_.parent;return{name:C,path:b,params:h,matched:k,meta:em(k)}}e.forEach(d=>i(d));function c(){r.length=0,o.clear()}return{addRoute:i,resolve:u,removeRoute:s,clearRoutes:c,getRoutes:a,getRecordMatcher:n}}function Wa(e,t){const r={};for(const o of t)o in e&&(r[o]=e[o]);return r}function Ka(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Qh(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Qh(e){const t={},r=e.props||!1;if("component"in e)t.default=r;else for(const o in e.components)t[o]=typeof r=="object"?r[o]:r;return t}function qa(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function em(e){return e.reduce((t,r)=>ae(t,r.meta),{})}function Ga(e,t){const r={};for(const o in e)r[o]=o in t?t[o]:e[o];return r}function tm(e,t){let r=0,o=t.length;for(;r!==o;){const i=r+o>>1;fd(e,t[i])<0?o=i:r=i+1}const n=rm(e);return n&&(o=t.lastIndexOf(n,o-1)),o}function rm(e){let t=e;for(;t=t.parent;)if(pd(t)&&fd(e,t)===0)return t}function pd({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function om(e){const t={};if(e===""||e==="?")return t;const o=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<o.length;++n){const i=o[n].replace(nd," "),s=i.indexOf("="),a=yo(s<0?i:i.slice(0,s)),l=s<0?null:yo(i.slice(s+1));if(a in t){let u=t[a];ht(u)||(u=t[a]=[u]),u.push(l)}else t[a]=l}return t}function Ja(e){let t="";for(let r in e){const o=e[r];if(r=_h(r),o==null){o!==void 0&&(t+=(t.length?"&":"")+r);continue}(ht(o)?o.map(i=>i&&Gi(i)):[o&&Gi(o)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+r,i!=null&&(t+="="+i))})}return t}function nm(e){const t={};for(const r in e){const o=e[r];o!==void 0&&(t[r]=ht(o)?o.map(n=>n==null?null:""+n):o==null?o:""+o)}return t}const im=Symbol(""),Ya=Symbol(""),Yn=Symbol(""),Ms=Symbol(""),Yi=Symbol("");function Ur(){let e=[];function t(o){return e.push(o),()=>{const n=e.indexOf(o);n>-1&&e.splice(n,1)}}function r(){e=[]}return{add:t,list:()=>e.slice(),reset:r}}function Gt(e,t,r,o,n,i=s=>s()){const s=o&&(o.enterCallbacks[n]=o.enterCallbacks[n]||[]);return()=>new Promise((a,l)=>{const u=f=>{f===!1?l(Lr(4,{from:r,to:t})):f instanceof Error?l(f):Uh(f)?l(Lr(2,{from:t,to:f})):(s&&o.enterCallbacks[n]===s&&typeof f=="function"&&s.push(f),a())},c=i(()=>e.call(o&&o.instances[n],t,r,u));let d=Promise.resolve(c);e.length<3&&(d=d.then(u)),d.catch(f=>l(f))})}function Ci(e,t,r,o,n=i=>i()){const i=[];for(const s of e)for(const a in s.components){let l=s.components[a];if(!(t!=="beforeRouteEnter"&&!s.instances[a]))if(rd(l)){const c=(l.__vccOpts||l)[t];c&&i.push(Gt(c,r,o,s,a,n))}else{let u=l();i.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${s.path}"`);const d=fh(c)?c.default:c;s.mods[a]=c,s.components[a]=d;const p=(d.__vccOpts||d)[t];return p&&Gt(p,r,o,s,a,n)()}))}}return i}function Xa(e){const t=Qe(Yn),r=Qe(Ms),o=nt(()=>{const l=ft(e.to);return t.resolve(l)}),n=nt(()=>{const{matched:l}=o.value,{length:u}=l,c=l[u-1],d=r.matched;if(!c||!d.length)return-1;const f=d.findIndex(Ar.bind(null,c));if(f>-1)return f;const p=Za(l[u-2]);return u>1&&Za(c)===p&&d[d.length-1].path!==p?d.findIndex(Ar.bind(null,l[u-2])):f}),i=nt(()=>n.value>-1&&um(r.params,o.value.params)),s=nt(()=>n.value>-1&&n.value===r.matched.length-1&&ld(r.params,o.value.params));function a(l={}){if(cm(l)){const u=t[ft(e.replace)?"replace":"push"](ft(e.to)).catch(oo);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:o,href:nt(()=>o.value.href),isActive:i,isExactActive:s,navigate:a}}function sm(e){return e.length===1?e[0]:e}const am=Ps({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Xa,setup(e,{slots:t}){const r=Dr(Xa(e)),{options:o}=Qe(Yn),n=nt(()=>({[Qa(e.activeClass,o.linkActiveClass,"router-link-active")]:r.isActive,[Qa(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:r.isExactActive}));return()=>{const i=t.default&&sm(t.default(r));return e.custom?i:Ds("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:n.value},i)}}}),lm=am;function cm(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function um(e,t){for(const r in t){const o=t[r],n=e[r];if(typeof o=="string"){if(o!==n)return!1}else if(!ht(n)||n.length!==o.length||o.some((i,s)=>i!==n[s]))return!1}return!0}function Za(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Qa=(e,t,r)=>e??t??r,dm=Ps({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:r}){const o=Qe(Yi),n=nt(()=>e.route||o.value),i=Qe(Ya,0),s=nt(()=>{let u=ft(i);const{matched:c}=n.value;let d;for(;(d=c[u])&&!d.components;)u++;return u}),a=nt(()=>n.value.matched[s.value]);an(Ya,nt(()=>s.value+1)),an(im,a),an(Yi,n);const l=Tt();return Rt(()=>[l.value,a.value,e.name],([u,c,d],[f,p,h])=>{c&&(c.instances[d]=u,p&&p!==c&&u&&u===f&&(c.leaveGuards.size||(c.leaveGuards=p.leaveGuards),c.updateGuards.size||(c.updateGuards=p.updateGuards))),u&&c&&(!p||!Ar(c,p)||!f)&&(c.enterCallbacks[d]||[]).forEach(b=>b(u))},{flush:"post"}),()=>{const u=n.value,c=e.name,d=a.value,f=d&&d.components[c];if(!f)return el(r.default,{Component:f,route:u});const p=d.props[c],h=p?p===!0?u.params:typeof p=="function"?p(u):p:null,C=Ds(f,ae({},h,t,{onVnodeUnmounted:k=>{k.component.isUnmounted&&(d.instances[c]=null)},ref:l}));return el(r.default,{Component:C,route:u})||C}}});function el(e,t){if(!e)return null;const r=e(t);return r.length===1?r[0]:r}const gd=dm;function fm(e){const t=Zh(e.routes,e),r=e.parseQuery||om,o=e.stringifyQuery||Ja,n=e.history,i=Ur(),s=Ur(),a=Ur(),l=ip(Ft);let u=Ft;$r&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=vi.bind(null,x=>""+x),d=vi.bind(null,xh),f=vi.bind(null,yo);function p(x,M){let D,H;return ud(x)?(D=t.getRecordMatcher(x),H=M):H=x,t.addRoute(H,D)}function h(x){const M=t.getRecordMatcher(x);M&&t.removeRoute(M)}function b(){return t.getRoutes().map(x=>x.record)}function C(x){return!!t.getRecordMatcher(x)}function k(x,M){if(M=ae({},M||l.value),typeof x=="string"){const v=yi(r,x,M.path),$=t.resolve({path:v.path},M),T=n.createHref(v.fullPath);return ae(v,$,{params:f($.params),hash:yo(v.hash),redirectedFrom:void 0,href:T})}let D;if(x.path!=null)D=ae({},x,{path:yi(r,x.path,M.path).path});else{const v=ae({},x.params);for(const $ in v)v[$]==null&&delete v[$];D=ae({},x,{params:d(v)}),M.params=d(M.params)}const H=t.resolve(D,M),ue=x.hash||"";H.params=c(f(H.params));const g=Th(o,ae({},x,{hash:kh(ue),path:H.path})),m=n.createHref(g);return ae({fullPath:g,hash:ue,query:o===Ja?nm(x.query):x.query||{}},H,{redirectedFrom:void 0,href:m})}function _(x){return typeof x=="string"?yi(r,x,l.value.path):ae({},x)}function w(x,M){if(u!==x)return Lr(8,{from:M,to:x})}function y(x){return z(x)}function P(x){return y(ae(_(x),{replace:!0}))}function F(x){const M=x.matched[x.matched.length-1];if(M&&M.redirect){const{redirect:D}=M;let H=typeof D=="function"?D(x):D;return typeof H=="string"&&(H=H.includes("?")||H.includes("#")?H=_(H):{path:H},H.params={}),ae({query:x.query,hash:x.hash,params:H.path!=null?{}:x.params},H)}}function z(x,M){const D=u=k(x),H=l.value,ue=x.state,g=x.force,m=x.replace===!0,v=F(D);if(v)return z(ae(_(v),{state:typeof v=="object"?ae({},ue,v.state):ue,force:g,replace:m}),M||D);const $=D;$.redirectedFrom=M;let T;return!g&&Ph(o,H,D)&&(T=Lr(16,{to:$,from:H}),Re(H,H,!0,!1)),(T?Promise.resolve(T):U($,H)).catch(R=>Ot(R)?Ot(R,2)?R:Oe(R):ee(R,$,H)).then(R=>{if(R){if(Ot(R,2))return z(ae({replace:m},_(R.to),{state:typeof R.to=="object"?ae({},ue,R.to.state):ue,force:g}),M||$)}else R=L($,H,!0,m,ue);return G($,H,R),R})}function N(x,M){const D=w(x,M);return D?Promise.reject(D):Promise.resolve()}function O(x){const M=Je.values().next().value;return M&&typeof M.runWithContext=="function"?M.runWithContext(x):x()}function U(x,M){let D;const[H,ue,g]=pm(x,M);D=Ci(H.reverse(),"beforeRouteLeave",x,M);for(const v of H)v.leaveGuards.forEach($=>{D.push(Gt($,x,M))});const m=N.bind(null,x,M);return D.push(m),Fe(D).then(()=>{D=[];for(const v of i.list())D.push(Gt(v,x,M));return D.push(m),Fe(D)}).then(()=>{D=Ci(ue,"beforeRouteUpdate",x,M);for(const v of ue)v.updateGuards.forEach($=>{D.push(Gt($,x,M))});return D.push(m),Fe(D)}).then(()=>{D=[];for(const v of g)if(v.beforeEnter)if(ht(v.beforeEnter))for(const $ of v.beforeEnter)D.push(Gt($,x,M));else D.push(Gt(v.beforeEnter,x,M));return D.push(m),Fe(D)}).then(()=>(x.matched.forEach(v=>v.enterCallbacks={}),D=Ci(g,"beforeRouteEnter",x,M,O),D.push(m),Fe(D))).then(()=>{D=[];for(const v of s.list())D.push(Gt(v,x,M));return D.push(m),Fe(D)}).catch(v=>Ot(v,8)?v:Promise.reject(v))}function G(x,M,D){a.list().forEach(H=>O(()=>H(x,M,D)))}function L(x,M,D,H,ue){const g=w(x,M);if(g)return g;const m=M===Ft,v=$r?history.state:{};D&&(H||m?n.replace(x.fullPath,ae({scroll:m&&v&&v.scroll},ue)):n.push(x.fullPath,ue)),l.value=x,Re(x,M,D,m),Oe()}let X;function ce(){X||(X=n.listen((x,M,D)=>{if(!nr.listening)return;const H=k(x),ue=F(H);if(ue){z(ae(ue,{replace:!0,force:!0}),H).catch(oo);return}u=H;const g=l.value;$r&&jh(Fa(g.fullPath,D.delta),Jn()),U(H,g).catch(m=>Ot(m,12)?m:Ot(m,2)?(z(ae(_(m.to),{force:!0}),H).then(v=>{Ot(v,20)&&!D.delta&&D.type===Co.pop&&n.go(-1,!1)}).catch(oo),Promise.reject()):(D.delta&&n.go(-D.delta,!1),ee(m,H,g))).then(m=>{m=m||L(H,g,!1),m&&(D.delta&&!Ot(m,8)?n.go(-D.delta,!1):D.type===Co.pop&&Ot(m,20)&&n.go(-1,!1)),G(H,g,m)}).catch(oo)}))}let _e=Ur(),re=Ur(),K;function ee(x,M,D){Oe(x);const H=re.list();return H.length?H.forEach(ue=>ue(x,M,D)):console.error(x),Promise.reject(x)}function Ie(){return K&&l.value!==Ft?Promise.resolve():new Promise((x,M)=>{_e.add([x,M])})}function Oe(x){return K||(K=!x,ce(),_e.list().forEach(([M,D])=>x?D(x):M()),_e.reset()),x}function Re(x,M,D,H){const{scrollBehavior:ue}=e;if(!$r||!ue)return Promise.resolve();const g=!D&&Mh(Fa(x.fullPath,0))||(H||!D)&&history.state&&history.state.scroll||null;return Hn().then(()=>ue(x,M,g)).then(m=>m&&Dh(m)).catch(m=>ee(m,x,M))}const $e=x=>n.go(x);let ct;const Je=new Set,nr={currentRoute:l,listening:!0,addRoute:p,removeRoute:h,clearRoutes:t.clearRoutes,hasRoute:C,getRoutes:b,resolve:k,options:e,push:y,replace:P,go:$e,back:()=>$e(-1),forward:()=>$e(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:re.add,isReady:Ie,install(x){const M=this;x.component("RouterLink",lm),x.component("RouterView",gd),x.config.globalProperties.$router=M,Object.defineProperty(x.config.globalProperties,"$route",{enumerable:!0,get:()=>ft(l)}),$r&&!ct&&l.value===Ft&&(ct=!0,y(n.location).catch(ue=>{}));const D={};for(const ue in Ft)Object.defineProperty(D,ue,{get:()=>l.value[ue],enumerable:!0});x.provide(Yn,M),x.provide(Ms,Zc(D)),x.provide(Yi,l);const H=x.unmount;Je.add(x),x.unmount=function(){Je.delete(x),Je.size<1&&(u=Ft,X&&X(),X=null,l.value=Ft,ct=!1,K=!1),H()}}};function Fe(x){return x.reduce((M,D)=>M.then(()=>O(D)),Promise.resolve())}return nr}function pm(e,t){const r=[],o=[],n=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const a=t.matched[s];a&&(e.matched.find(u=>Ar(u,a))?o.push(a):r.push(a));const l=e.matched[s];l&&(t.matched.find(u=>Ar(u,l))||n.push(l))}return[r,o,n]}function $w(){return Qe(Yn)}function xw(e){return Qe(Ms)}const gm=dh("user",()=>{const e=Tt({});return{info:e,updateInfo:r=>{Object.assign(e.value,r)}}}),wn="https://meeting.codeemo.cn",hm="e7af3610-a6a8-441c-96d4-8b75d44acca2",Rw=[{urls:["turn:47.109.86.108:3478"],username:"new",credential:"new"}],hd="token";function md(){return localStorage.getItem(hd)}function tl(e){localStorage.setItem(hd,e)}function ww(e=""){window.location.href=`${wn}/login?redirect_uri=${encodeURIComponent(e||window.location.origin)}`}async function Tw(){const e=[],t=[],r=[],o=await navigator.mediaDevices.enumerateDevices();for(let n=0;n<o.length;n++){const i=o[n];if(!(i.deviceId==="default"||i.deviceId==="communications"))switch(i.kind){case"videoinput":e.push(i);break;case"audioinput":t.push(i);break;case"audiooutput":r.push(i);break}}return[e,t,r]}function bd(e,t){return function(){return e.apply(t,arguments)}}const{toString:mm}=Object.prototype,{getPrototypeOf:Ns}=Object,Xn=(e=>t=>{const r=mm.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),mt=e=>(e=e.toLowerCase(),t=>Xn(t)===e),Zn=e=>t=>typeof t===e,{isArray:jr}=Array,So=Zn("undefined");function bm(e){return e!==null&&!So(e)&&e.constructor!==null&&!So(e.constructor)&&et(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const vd=mt("ArrayBuffer");function vm(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&vd(e.buffer),t}const ym=Zn("string"),et=Zn("function"),yd=Zn("number"),Qn=e=>e!==null&&typeof e=="object",Cm=e=>e===!0||e===!1,dn=e=>{if(Xn(e)!=="object")return!1;const t=Ns(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},Sm=mt("Date"),km=mt("File"),_m=mt("Blob"),$m=mt("FileList"),xm=e=>Qn(e)&&et(e.pipe),Rm=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||et(e.append)&&((t=Xn(e))==="formdata"||t==="object"&&et(e.toString)&&e.toString()==="[object FormData]"))},wm=mt("URLSearchParams"),[Tm,Pm,Em,Om]=["ReadableStream","Request","Response","Headers"].map(mt),Bm=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Fo(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let o,n;if(typeof e!="object"&&(e=[e]),jr(e))for(o=0,n=e.length;o<n;o++)t.call(null,e[o],o,e);else{const i=r?Object.getOwnPropertyNames(e):Object.keys(e),s=i.length;let a;for(o=0;o<s;o++)a=i[o],t.call(null,e[a],a,e)}}function Cd(e,t){t=t.toLowerCase();const r=Object.keys(e);let o=r.length,n;for(;o-- >0;)if(n=r[o],t===n.toLowerCase())return n;return null}const dr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Sd=e=>!So(e)&&e!==dr;function Xi(){const{caseless:e}=Sd(this)&&this||{},t={},r=(o,n)=>{const i=e&&Cd(t,n)||n;dn(t[i])&&dn(o)?t[i]=Xi(t[i],o):dn(o)?t[i]=Xi({},o):jr(o)?t[i]=o.slice():t[i]=o};for(let o=0,n=arguments.length;o<n;o++)arguments[o]&&Fo(arguments[o],r);return t}const Am=(e,t,r,{allOwnKeys:o}={})=>(Fo(t,(n,i)=>{r&&et(n)?e[i]=bd(n,r):e[i]=n},{allOwnKeys:o}),e),Lm=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Im=(e,t,r,o)=>{e.prototype=Object.create(t.prototype,o),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Dm=(e,t,r,o)=>{let n,i,s;const a={};if(t=t||{},e==null)return t;do{for(n=Object.getOwnPropertyNames(e),i=n.length;i-- >0;)s=n[i],(!o||o(s,e,t))&&!a[s]&&(t[s]=e[s],a[s]=!0);e=r!==!1&&Ns(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},jm=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const o=e.indexOf(t,r);return o!==-1&&o===r},Mm=e=>{if(!e)return null;if(jr(e))return e;let t=e.length;if(!yd(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},Nm=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ns(Uint8Array)),Fm=(e,t)=>{const o=(e&&e[Symbol.iterator]).call(e);let n;for(;(n=o.next())&&!n.done;){const i=n.value;t.call(e,i[0],i[1])}},zm=(e,t)=>{let r;const o=[];for(;(r=e.exec(t))!==null;)o.push(r);return o},Hm=mt("HTMLFormElement"),Um=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,o,n){return o.toUpperCase()+n}),rl=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Vm=mt("RegExp"),kd=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),o={};Fo(r,(n,i)=>{let s;(s=t(n,i,e))!==!1&&(o[i]=s||n)}),Object.defineProperties(e,o)},Wm=e=>{kd(e,(t,r)=>{if(et(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const o=e[r];if(et(o)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Km=(e,t)=>{const r={},o=n=>{n.forEach(i=>{r[i]=!0})};return jr(e)?o(e):o(String(e).split(t)),r},qm=()=>{},Gm=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Jm(e){return!!(e&&et(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const Ym=e=>{const t=new Array(10),r=(o,n)=>{if(Qn(o)){if(t.indexOf(o)>=0)return;if(!("toJSON"in o)){t[n]=o;const i=jr(o)?[]:{};return Fo(o,(s,a)=>{const l=r(s,n+1);!So(l)&&(i[a]=l)}),t[n]=void 0,i}}return o};return r(e,0)},Xm=mt("AsyncFunction"),Zm=e=>e&&(Qn(e)||et(e))&&et(e.then)&&et(e.catch),_d=((e,t)=>e?setImmediate:t?((r,o)=>(dr.addEventListener("message",({source:n,data:i})=>{n===dr&&i===r&&o.length&&o.shift()()},!1),n=>{o.push(n),dr.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",et(dr.postMessage)),Qm=typeof queueMicrotask<"u"?queueMicrotask.bind(dr):typeof process<"u"&&process.nextTick||_d,S={isArray:jr,isArrayBuffer:vd,isBuffer:bm,isFormData:Rm,isArrayBufferView:vm,isString:ym,isNumber:yd,isBoolean:Cm,isObject:Qn,isPlainObject:dn,isReadableStream:Tm,isRequest:Pm,isResponse:Em,isHeaders:Om,isUndefined:So,isDate:Sm,isFile:km,isBlob:_m,isRegExp:Vm,isFunction:et,isStream:xm,isURLSearchParams:wm,isTypedArray:Nm,isFileList:$m,forEach:Fo,merge:Xi,extend:Am,trim:Bm,stripBOM:Lm,inherits:Im,toFlatObject:Dm,kindOf:Xn,kindOfTest:mt,endsWith:jm,toArray:Mm,forEachEntry:Fm,matchAll:zm,isHTMLForm:Hm,hasOwnProperty:rl,hasOwnProp:rl,reduceDescriptors:kd,freezeMethods:Wm,toObjectSet:Km,toCamelCase:Um,noop:qm,toFiniteNumber:Gm,findKey:Cd,global:dr,isContextDefined:Sd,isSpecCompliantForm:Jm,toJSONObject:Ym,isAsyncFn:Xm,isThenable:Zm,setImmediate:_d,asap:Qm};function Z(e,t,r,o,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),o&&(this.request=o),n&&(this.response=n,this.status=n.status?n.status:null)}S.inherits(Z,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:S.toJSONObject(this.config),code:this.code,status:this.status}}});const $d=Z.prototype,xd={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{xd[e]={value:e}});Object.defineProperties(Z,xd);Object.defineProperty($d,"isAxiosError",{value:!0});Z.from=(e,t,r,o,n,i)=>{const s=Object.create($d);return S.toFlatObject(e,s,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),Z.call(s,e.message,t,r,o,n),s.cause=e,s.name=e.name,i&&Object.assign(s,i),s};const e0=null;function Zi(e){return S.isPlainObject(e)||S.isArray(e)}function Rd(e){return S.endsWith(e,"[]")?e.slice(0,-2):e}function ol(e,t,r){return e?e.concat(t).map(function(n,i){return n=Rd(n),!r&&i?"["+n+"]":n}).join(r?".":""):t}function t0(e){return S.isArray(e)&&!e.some(Zi)}const r0=S.toFlatObject(S,{},null,function(t){return/^is[A-Z]/.test(t)});function ei(e,t,r){if(!S.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=S.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(b,C){return!S.isUndefined(C[b])});const o=r.metaTokens,n=r.visitor||c,i=r.dots,s=r.indexes,l=(r.Blob||typeof Blob<"u"&&Blob)&&S.isSpecCompliantForm(t);if(!S.isFunction(n))throw new TypeError("visitor must be a function");function u(h){if(h===null)return"";if(S.isDate(h))return h.toISOString();if(!l&&S.isBlob(h))throw new Z("Blob is not supported. Use a Buffer instead.");return S.isArrayBuffer(h)||S.isTypedArray(h)?l&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function c(h,b,C){let k=h;if(h&&!C&&typeof h=="object"){if(S.endsWith(b,"{}"))b=o?b:b.slice(0,-2),h=JSON.stringify(h);else if(S.isArray(h)&&t0(h)||(S.isFileList(h)||S.endsWith(b,"[]"))&&(k=S.toArray(h)))return b=Rd(b),k.forEach(function(w,y){!(S.isUndefined(w)||w===null)&&t.append(s===!0?ol([b],y,i):s===null?b:b+"[]",u(w))}),!1}return Zi(h)?!0:(t.append(ol(C,b,i),u(h)),!1)}const d=[],f=Object.assign(r0,{defaultVisitor:c,convertValue:u,isVisitable:Zi});function p(h,b){if(!S.isUndefined(h)){if(d.indexOf(h)!==-1)throw Error("Circular reference detected in "+b.join("."));d.push(h),S.forEach(h,function(k,_){(!(S.isUndefined(k)||k===null)&&n.call(t,k,S.isString(_)?_.trim():_,b,f))===!0&&p(k,b?b.concat(_):[_])}),d.pop()}}if(!S.isObject(e))throw new TypeError("data must be an object");return p(e),t}function nl(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(o){return t[o]})}function Fs(e,t){this._pairs=[],e&&ei(e,this,t)}const wd=Fs.prototype;wd.append=function(t,r){this._pairs.push([t,r])};wd.toString=function(t){const r=t?function(o){return t.call(this,o,nl)}:nl;return this._pairs.map(function(n){return r(n[0])+"="+r(n[1])},"").join("&")};function o0(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Td(e,t,r){if(!t)return e;const o=r&&r.encode||o0;S.isFunction(r)&&(r={serialize:r});const n=r&&r.serialize;let i;if(n?i=n(t,r):i=S.isURLSearchParams(t)?t.toString():new Fs(t,r).toString(o),i){const s=e.indexOf("#");s!==-1&&(e=e.slice(0,s)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class il{constructor(){this.handlers=[]}use(t,r,o){return this.handlers.push({fulfilled:t,rejected:r,synchronous:o?o.synchronous:!1,runWhen:o?o.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){S.forEach(this.handlers,function(o){o!==null&&t(o)})}}const Pd={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},n0=typeof URLSearchParams<"u"?URLSearchParams:Fs,i0=typeof FormData<"u"?FormData:null,s0=typeof Blob<"u"?Blob:null,a0={isBrowser:!0,classes:{URLSearchParams:n0,FormData:i0,Blob:s0},protocols:["http","https","file","blob","url","data"]},zs=typeof window<"u"&&typeof document<"u",Qi=typeof navigator=="object"&&navigator||void 0,l0=zs&&(!Qi||["ReactNative","NativeScript","NS"].indexOf(Qi.product)<0),c0=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",u0=zs&&window.location.href||"http://localhost",d0=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:zs,hasStandardBrowserEnv:l0,hasStandardBrowserWebWorkerEnv:c0,navigator:Qi,origin:u0},Symbol.toStringTag,{value:"Module"})),Ne={...d0,...a0};function f0(e,t){return ei(e,new Ne.classes.URLSearchParams,Object.assign({visitor:function(r,o,n,i){return Ne.isNode&&S.isBuffer(r)?(this.append(o,r.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function p0(e){return S.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function g0(e){const t={},r=Object.keys(e);let o;const n=r.length;let i;for(o=0;o<n;o++)i=r[o],t[i]=e[i];return t}function Ed(e){function t(r,o,n,i){let s=r[i++];if(s==="__proto__")return!0;const a=Number.isFinite(+s),l=i>=r.length;return s=!s&&S.isArray(n)?n.length:s,l?(S.hasOwnProp(n,s)?n[s]=[n[s],o]:n[s]=o,!a):((!n[s]||!S.isObject(n[s]))&&(n[s]=[]),t(r,o,n[s],i)&&S.isArray(n[s])&&(n[s]=g0(n[s])),!a)}if(S.isFormData(e)&&S.isFunction(e.entries)){const r={};return S.forEachEntry(e,(o,n)=>{t(p0(o),n,r,0)}),r}return null}function h0(e,t,r){if(S.isString(e))try{return(t||JSON.parse)(e),S.trim(e)}catch(o){if(o.name!=="SyntaxError")throw o}return(r||JSON.stringify)(e)}const zo={transitional:Pd,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const o=r.getContentType()||"",n=o.indexOf("application/json")>-1,i=S.isObject(t);if(i&&S.isHTMLForm(t)&&(t=new FormData(t)),S.isFormData(t))return n?JSON.stringify(Ed(t)):t;if(S.isArrayBuffer(t)||S.isBuffer(t)||S.isStream(t)||S.isFile(t)||S.isBlob(t)||S.isReadableStream(t))return t;if(S.isArrayBufferView(t))return t.buffer;if(S.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(i){if(o.indexOf("application/x-www-form-urlencoded")>-1)return f0(t,this.formSerializer).toString();if((a=S.isFileList(t))||o.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return ei(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return i||n?(r.setContentType("application/json",!1),h0(t)):t}],transformResponse:[function(t){const r=this.transitional||zo.transitional,o=r&&r.forcedJSONParsing,n=this.responseType==="json";if(S.isResponse(t)||S.isReadableStream(t))return t;if(t&&S.isString(t)&&(o&&!this.responseType||n)){const s=!(r&&r.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(a){if(s)throw a.name==="SyntaxError"?Z.from(a,Z.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ne.classes.FormData,Blob:Ne.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};S.forEach(["delete","get","head","post","put","patch"],e=>{zo.headers[e]={}});const m0=S.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),b0=e=>{const t={};let r,o,n;return e&&e.split(`
`).forEach(function(s){n=s.indexOf(":"),r=s.substring(0,n).trim().toLowerCase(),o=s.substring(n+1).trim(),!(!r||t[r]&&m0[r])&&(r==="set-cookie"?t[r]?t[r].push(o):t[r]=[o]:t[r]=t[r]?t[r]+", "+o:o)}),t},sl=Symbol("internals");function Vr(e){return e&&String(e).trim().toLowerCase()}function fn(e){return e===!1||e==null?e:S.isArray(e)?e.map(fn):String(e)}function v0(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=r.exec(e);)t[o[1]]=o[2];return t}const y0=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Si(e,t,r,o,n){if(S.isFunction(o))return o.call(this,t,r);if(n&&(t=r),!!S.isString(t)){if(S.isString(o))return t.indexOf(o)!==-1;if(S.isRegExp(o))return o.test(t)}}function C0(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,o)=>r.toUpperCase()+o)}function S0(e,t){const r=S.toCamelCase(" "+t);["get","set","has"].forEach(o=>{Object.defineProperty(e,o+r,{value:function(n,i,s){return this[o].call(this,t,n,i,s)},configurable:!0})})}let qe=class{constructor(t){t&&this.set(t)}set(t,r,o){const n=this;function i(a,l,u){const c=Vr(l);if(!c)throw new Error("header name must be a non-empty string");const d=S.findKey(n,c);(!d||n[d]===void 0||u===!0||u===void 0&&n[d]!==!1)&&(n[d||l]=fn(a))}const s=(a,l)=>S.forEach(a,(u,c)=>i(u,c,l));if(S.isPlainObject(t)||t instanceof this.constructor)s(t,r);else if(S.isString(t)&&(t=t.trim())&&!y0(t))s(b0(t),r);else if(S.isHeaders(t))for(const[a,l]of t.entries())i(l,a,o);else t!=null&&i(r,t,o);return this}get(t,r){if(t=Vr(t),t){const o=S.findKey(this,t);if(o){const n=this[o];if(!r)return n;if(r===!0)return v0(n);if(S.isFunction(r))return r.call(this,n,o);if(S.isRegExp(r))return r.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=Vr(t),t){const o=S.findKey(this,t);return!!(o&&this[o]!==void 0&&(!r||Si(this,this[o],o,r)))}return!1}delete(t,r){const o=this;let n=!1;function i(s){if(s=Vr(s),s){const a=S.findKey(o,s);a&&(!r||Si(o,o[a],a,r))&&(delete o[a],n=!0)}}return S.isArray(t)?t.forEach(i):i(t),n}clear(t){const r=Object.keys(this);let o=r.length,n=!1;for(;o--;){const i=r[o];(!t||Si(this,this[i],i,t,!0))&&(delete this[i],n=!0)}return n}normalize(t){const r=this,o={};return S.forEach(this,(n,i)=>{const s=S.findKey(o,i);if(s){r[s]=fn(n),delete r[i];return}const a=t?C0(i):String(i).trim();a!==i&&delete r[i],r[a]=fn(n),o[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return S.forEach(this,(o,n)=>{o!=null&&o!==!1&&(r[n]=t&&S.isArray(o)?o.join(", "):o)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const o=new this(t);return r.forEach(n=>o.set(n)),o}static accessor(t){const o=(this[sl]=this[sl]={accessors:{}}).accessors,n=this.prototype;function i(s){const a=Vr(s);o[a]||(S0(n,s),o[a]=!0)}return S.isArray(t)?t.forEach(i):i(t),this}};qe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);S.reduceDescriptors(qe.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(o){this[r]=o}}});S.freezeMethods(qe);function ki(e,t){const r=this||zo,o=t||r,n=qe.from(o.headers);let i=o.data;return S.forEach(e,function(a){i=a.call(r,i,n.normalize(),t?t.status:void 0)}),n.normalize(),i}function Od(e){return!!(e&&e.__CANCEL__)}function Mr(e,t,r){Z.call(this,e??"canceled",Z.ERR_CANCELED,t,r),this.name="CanceledError"}S.inherits(Mr,Z,{__CANCEL__:!0});function Bd(e,t,r){const o=r.config.validateStatus;!r.status||!o||o(r.status)?e(r):t(new Z("Request failed with status code "+r.status,[Z.ERR_BAD_REQUEST,Z.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function k0(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function _0(e,t){e=e||10;const r=new Array(e),o=new Array(e);let n=0,i=0,s;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),c=o[i];s||(s=u),r[n]=l,o[n]=u;let d=i,f=0;for(;d!==n;)f+=r[d++],d=d%e;if(n=(n+1)%e,n===i&&(i=(i+1)%e),u-s<t)return;const p=c&&u-c;return p?Math.round(f*1e3/p):void 0}}function $0(e,t){let r=0,o=1e3/t,n,i;const s=(u,c=Date.now())=>{r=c,n=null,i&&(clearTimeout(i),i=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),d=c-r;d>=o?s(u,c):(n=u,i||(i=setTimeout(()=>{i=null,s(n)},o-d)))},()=>n&&s(n)]}const Tn=(e,t,r=3)=>{let o=0;const n=_0(50,250);return $0(i=>{const s=i.loaded,a=i.lengthComputable?i.total:void 0,l=s-o,u=n(l),c=s<=a;o=s;const d={loaded:s,total:a,progress:a?s/a:void 0,bytes:l,rate:u||void 0,estimated:u&&a&&c?(a-s)/u:void 0,event:i,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(d)},r)},al=(e,t)=>{const r=e!=null;return[o=>t[0]({lengthComputable:r,total:e,loaded:o}),t[1]]},ll=e=>(...t)=>S.asap(()=>e(...t)),x0=Ne.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,Ne.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(Ne.origin),Ne.navigator&&/(msie|trident)/i.test(Ne.navigator.userAgent)):()=>!0,R0=Ne.hasStandardBrowserEnv?{write(e,t,r,o,n,i){const s=[e+"="+encodeURIComponent(t)];S.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),S.isString(o)&&s.push("path="+o),S.isString(n)&&s.push("domain="+n),i===!0&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function w0(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function T0(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Ad(e,t,r){let o=!w0(t);return e&&(o||r==!1)?T0(e,t):t}const cl=e=>e instanceof qe?{...e}:e;function br(e,t){t=t||{};const r={};function o(u,c,d,f){return S.isPlainObject(u)&&S.isPlainObject(c)?S.merge.call({caseless:f},u,c):S.isPlainObject(c)?S.merge({},c):S.isArray(c)?c.slice():c}function n(u,c,d,f){if(S.isUndefined(c)){if(!S.isUndefined(u))return o(void 0,u,d,f)}else return o(u,c,d,f)}function i(u,c){if(!S.isUndefined(c))return o(void 0,c)}function s(u,c){if(S.isUndefined(c)){if(!S.isUndefined(u))return o(void 0,u)}else return o(void 0,c)}function a(u,c,d){if(d in t)return o(u,c);if(d in e)return o(void 0,u)}const l={url:i,method:i,data:i,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(u,c,d)=>n(cl(u),cl(c),d,!0)};return S.forEach(Object.keys(Object.assign({},e,t)),function(c){const d=l[c]||n,f=d(e[c],t[c],c);S.isUndefined(f)&&d!==a||(r[c]=f)}),r}const Ld=e=>{const t=br({},e);let{data:r,withXSRFToken:o,xsrfHeaderName:n,xsrfCookieName:i,headers:s,auth:a}=t;t.headers=s=qe.from(s),t.url=Td(Ad(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&s.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(S.isFormData(r)){if(Ne.hasStandardBrowserEnv||Ne.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if((l=s.getContentType())!==!1){const[u,...c]=l?l.split(";").map(d=>d.trim()).filter(Boolean):[];s.setContentType([u||"multipart/form-data",...c].join("; "))}}if(Ne.hasStandardBrowserEnv&&(o&&S.isFunction(o)&&(o=o(t)),o||o!==!1&&x0(t.url))){const u=n&&i&&R0.read(i);u&&s.set(n,u)}return t},P0=typeof XMLHttpRequest<"u",E0=P0&&function(e){return new Promise(function(r,o){const n=Ld(e);let i=n.data;const s=qe.from(n.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:u}=n,c,d,f,p,h;function b(){p&&p(),h&&h(),n.cancelToken&&n.cancelToken.unsubscribe(c),n.signal&&n.signal.removeEventListener("abort",c)}let C=new XMLHttpRequest;C.open(n.method.toUpperCase(),n.url,!0),C.timeout=n.timeout;function k(){if(!C)return;const w=qe.from("getAllResponseHeaders"in C&&C.getAllResponseHeaders()),P={data:!a||a==="text"||a==="json"?C.responseText:C.response,status:C.status,statusText:C.statusText,headers:w,config:e,request:C};Bd(function(z){r(z),b()},function(z){o(z),b()},P),C=null}"onloadend"in C?C.onloadend=k:C.onreadystatechange=function(){!C||C.readyState!==4||C.status===0&&!(C.responseURL&&C.responseURL.indexOf("file:")===0)||setTimeout(k)},C.onabort=function(){C&&(o(new Z("Request aborted",Z.ECONNABORTED,e,C)),C=null)},C.onerror=function(){o(new Z("Network Error",Z.ERR_NETWORK,e,C)),C=null},C.ontimeout=function(){let y=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const P=n.transitional||Pd;n.timeoutErrorMessage&&(y=n.timeoutErrorMessage),o(new Z(y,P.clarifyTimeoutError?Z.ETIMEDOUT:Z.ECONNABORTED,e,C)),C=null},i===void 0&&s.setContentType(null),"setRequestHeader"in C&&S.forEach(s.toJSON(),function(y,P){C.setRequestHeader(P,y)}),S.isUndefined(n.withCredentials)||(C.withCredentials=!!n.withCredentials),a&&a!=="json"&&(C.responseType=n.responseType),u&&([f,h]=Tn(u,!0),C.addEventListener("progress",f)),l&&C.upload&&([d,p]=Tn(l),C.upload.addEventListener("progress",d),C.upload.addEventListener("loadend",p)),(n.cancelToken||n.signal)&&(c=w=>{C&&(o(!w||w.type?new Mr(null,e,C):w),C.abort(),C=null)},n.cancelToken&&n.cancelToken.subscribe(c),n.signal&&(n.signal.aborted?c():n.signal.addEventListener("abort",c)));const _=k0(n.url);if(_&&Ne.protocols.indexOf(_)===-1){o(new Z("Unsupported protocol "+_+":",Z.ERR_BAD_REQUEST,e));return}C.send(i||null)})},O0=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let o=new AbortController,n;const i=function(u){if(!n){n=!0,a();const c=u instanceof Error?u:this.reason;o.abort(c instanceof Z?c:new Mr(c instanceof Error?c.message:c))}};let s=t&&setTimeout(()=>{s=null,i(new Z(`timeout ${t} of ms exceeded`,Z.ETIMEDOUT))},t);const a=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),e=null)};e.forEach(u=>u.addEventListener("abort",i));const{signal:l}=o;return l.unsubscribe=()=>S.asap(a),l}},B0=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let o=0,n;for(;o<r;)n=o+t,yield e.slice(o,n),o=n},A0=async function*(e,t){for await(const r of L0(e))yield*B0(r,t)},L0=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:o}=await t.read();if(r)break;yield o}}finally{await t.cancel()}},ul=(e,t,r,o)=>{const n=A0(e,t);let i=0,s,a=l=>{s||(s=!0,o&&o(l))};return new ReadableStream({async pull(l){try{const{done:u,value:c}=await n.next();if(u){a(),l.close();return}let d=c.byteLength;if(r){let f=i+=d;r(f)}l.enqueue(new Uint8Array(c))}catch(u){throw a(u),u}},cancel(l){return a(l),n.return()}},{highWaterMark:2})},ti=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Id=ti&&typeof ReadableStream=="function",I0=ti&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Dd=(e,...t)=>{try{return!!e(...t)}catch{return!1}},D0=Id&&Dd(()=>{let e=!1;const t=new Request(Ne.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),dl=64*1024,es=Id&&Dd(()=>S.isReadableStream(new Response("").body)),Pn={stream:es&&(e=>e.body)};ti&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Pn[t]&&(Pn[t]=S.isFunction(e[t])?r=>r[t]():(r,o)=>{throw new Z(`Response type '${t}' is not supported`,Z.ERR_NOT_SUPPORT,o)})})})(new Response);const j0=async e=>{if(e==null)return 0;if(S.isBlob(e))return e.size;if(S.isSpecCompliantForm(e))return(await new Request(Ne.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(S.isArrayBufferView(e)||S.isArrayBuffer(e))return e.byteLength;if(S.isURLSearchParams(e)&&(e=e+""),S.isString(e))return(await I0(e)).byteLength},M0=async(e,t)=>{const r=S.toFiniteNumber(e.getContentLength());return r??j0(t)},N0=ti&&(async e=>{let{url:t,method:r,data:o,signal:n,cancelToken:i,timeout:s,onDownloadProgress:a,onUploadProgress:l,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=Ld(e);u=u?(u+"").toLowerCase():"text";let p=O0([n,i&&i.toAbortSignal()],s),h;const b=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let C;try{if(l&&D0&&r!=="get"&&r!=="head"&&(C=await M0(c,o))!==0){let P=new Request(t,{method:"POST",body:o,duplex:"half"}),F;if(S.isFormData(o)&&(F=P.headers.get("content-type"))&&c.setContentType(F),P.body){const[z,N]=al(C,Tn(ll(l)));o=ul(P.body,dl,z,N)}}S.isString(d)||(d=d?"include":"omit");const k="credentials"in Request.prototype;h=new Request(t,{...f,signal:p,method:r.toUpperCase(),headers:c.normalize().toJSON(),body:o,duplex:"half",credentials:k?d:void 0});let _=await fetch(h);const w=es&&(u==="stream"||u==="response");if(es&&(a||w&&b)){const P={};["status","statusText","headers"].forEach(O=>{P[O]=_[O]});const F=S.toFiniteNumber(_.headers.get("content-length")),[z,N]=a&&al(F,Tn(ll(a),!0))||[];_=new Response(ul(_.body,dl,z,()=>{N&&N(),b&&b()}),P)}u=u||"text";let y=await Pn[S.findKey(Pn,u)||"text"](_,e);return!w&&b&&b(),await new Promise((P,F)=>{Bd(P,F,{data:y,headers:qe.from(_.headers),status:_.status,statusText:_.statusText,config:e,request:h})})}catch(k){throw b&&b(),k&&k.name==="TypeError"&&/fetch/i.test(k.message)?Object.assign(new Z("Network Error",Z.ERR_NETWORK,e,h),{cause:k.cause||k}):Z.from(k,k&&k.code,e,h)}}),ts={http:e0,xhr:E0,fetch:N0};S.forEach(ts,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const fl=e=>`- ${e}`,F0=e=>S.isFunction(e)||e===null||e===!1,jd={getAdapter:e=>{e=S.isArray(e)?e:[e];const{length:t}=e;let r,o;const n={};for(let i=0;i<t;i++){r=e[i];let s;if(o=r,!F0(r)&&(o=ts[(s=String(r)).toLowerCase()],o===void 0))throw new Z(`Unknown adapter '${s}'`);if(o)break;n[s||"#"+i]=o}if(!o){const i=Object.entries(n).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let s=t?i.length>1?`since :
`+i.map(fl).join(`
`):" "+fl(i[0]):"as no adapter specified";throw new Z("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return o},adapters:ts};function _i(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Mr(null,e)}function pl(e){return _i(e),e.headers=qe.from(e.headers),e.data=ki.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),jd.getAdapter(e.adapter||zo.adapter)(e).then(function(o){return _i(e),o.data=ki.call(e,e.transformResponse,o),o.headers=qe.from(o.headers),o},function(o){return Od(o)||(_i(e),o&&o.response&&(o.response.data=ki.call(e,e.transformResponse,o.response),o.response.headers=qe.from(o.response.headers))),Promise.reject(o)})}const Md="1.8.4",ri={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ri[e]=function(o){return typeof o===e||"a"+(t<1?"n ":" ")+e}});const gl={};ri.transitional=function(t,r,o){function n(i,s){return"[Axios v"+Md+"] Transitional option '"+i+"'"+s+(o?". "+o:"")}return(i,s,a)=>{if(t===!1)throw new Z(n(s," has been removed"+(r?" in "+r:"")),Z.ERR_DEPRECATED);return r&&!gl[s]&&(gl[s]=!0,console.warn(n(s," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(i,s,a):!0}};ri.spelling=function(t){return(r,o)=>(console.warn(`${o} is likely a misspelling of ${t}`),!0)};function z0(e,t,r){if(typeof e!="object")throw new Z("options must be an object",Z.ERR_BAD_OPTION_VALUE);const o=Object.keys(e);let n=o.length;for(;n-- >0;){const i=o[n],s=t[i];if(s){const a=e[i],l=a===void 0||s(a,i,e);if(l!==!0)throw new Z("option "+i+" must be "+l,Z.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new Z("Unknown option "+i,Z.ERR_BAD_OPTION)}}const pn={assertOptions:z0,validators:ri},St=pn.validators;let gr=class{constructor(t){this.defaults=t,this.interceptors={request:new il,response:new il}}async request(t,r){try{return await this._request(t,r)}catch(o){if(o instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const i=n.stack?n.stack.replace(/^.+\n/,""):"";try{o.stack?i&&!String(o.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(o.stack+=`
`+i):o.stack=i}catch{}}throw o}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=br(this.defaults,r);const{transitional:o,paramsSerializer:n,headers:i}=r;o!==void 0&&pn.assertOptions(o,{silentJSONParsing:St.transitional(St.boolean),forcedJSONParsing:St.transitional(St.boolean),clarifyTimeoutError:St.transitional(St.boolean)},!1),n!=null&&(S.isFunction(n)?r.paramsSerializer={serialize:n}:pn.assertOptions(n,{encode:St.function,serialize:St.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),pn.assertOptions(r,{baseUrl:St.spelling("baseURL"),withXsrfToken:St.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let s=i&&S.merge(i.common,i[r.method]);i&&S.forEach(["delete","get","head","post","put","patch","common"],h=>{delete i[h]}),r.headers=qe.concat(s,i);const a=[];let l=!0;this.interceptors.request.forEach(function(b){typeof b.runWhen=="function"&&b.runWhen(r)===!1||(l=l&&b.synchronous,a.unshift(b.fulfilled,b.rejected))});const u=[];this.interceptors.response.forEach(function(b){u.push(b.fulfilled,b.rejected)});let c,d=0,f;if(!l){const h=[pl.bind(this),void 0];for(h.unshift.apply(h,a),h.push.apply(h,u),f=h.length,c=Promise.resolve(r);d<f;)c=c.then(h[d++],h[d++]);return c}f=a.length;let p=r;for(d=0;d<f;){const h=a[d++],b=a[d++];try{p=h(p)}catch(C){b.call(this,C);break}}try{c=pl.call(this,p)}catch(h){return Promise.reject(h)}for(d=0,f=u.length;d<f;)c=c.then(u[d++],u[d++]);return c}getUri(t){t=br(this.defaults,t);const r=Ad(t.baseURL,t.url,t.allowAbsoluteUrls);return Td(r,t.params,t.paramsSerializer)}};S.forEach(["delete","get","head","options"],function(t){gr.prototype[t]=function(r,o){return this.request(br(o||{},{method:t,url:r,data:(o||{}).data}))}});S.forEach(["post","put","patch"],function(t){function r(o){return function(i,s,a){return this.request(br(a||{},{method:t,headers:o?{"Content-Type":"multipart/form-data"}:{},url:i,data:s}))}}gr.prototype[t]=r(),gr.prototype[t+"Form"]=r(!0)});let H0=class Nd{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(i){r=i});const o=this;this.promise.then(n=>{if(!o._listeners)return;let i=o._listeners.length;for(;i-- >0;)o._listeners[i](n);o._listeners=null}),this.promise.then=n=>{let i;const s=new Promise(a=>{o.subscribe(a),i=a}).then(n);return s.cancel=function(){o.unsubscribe(i)},s},t(function(i,s,a){o.reason||(o.reason=new Mr(i,s,a),r(o.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=o=>{t.abort(o)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new Nd(function(n){t=n}),cancel:t}}};function U0(e){return function(r){return e.apply(null,r)}}function V0(e){return S.isObject(e)&&e.isAxiosError===!0}const rs={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(rs).forEach(([e,t])=>{rs[t]=e});function Fd(e){const t=new gr(e),r=bd(gr.prototype.request,t);return S.extend(r,gr.prototype,t,{allOwnKeys:!0}),S.extend(r,t,null,{allOwnKeys:!0}),r.create=function(n){return Fd(br(e,n))},r}const pe=Fd(zo);pe.Axios=gr;pe.CanceledError=Mr;pe.CancelToken=H0;pe.isCancel=Od;pe.VERSION=Md;pe.toFormData=ei;pe.AxiosError=Z;pe.Cancel=pe.CanceledError;pe.all=function(t){return Promise.all(t)};pe.spread=U0;pe.isAxiosError=V0;pe.mergeConfig=br;pe.AxiosHeaders=qe;pe.formToJSON=e=>Ed(S.isHTMLForm(e)?new FormData(e):e);pe.getAdapter=jd.getAdapter;pe.HttpStatusCode=rs;pe.default=pe;const{Axios:Ow,AxiosError:Bw,CanceledError:Aw,isCancel:Lw,CancelToken:Iw,VERSION:Dw,all:jw,Cancel:Mw,isAxiosError:Nw,spread:Fw,toFormData:zw,AxiosHeaders:Hw,HttpStatusCode:Uw,formToJSON:Vw,getAdapter:Ww,mergeConfig:Kw}=pe;function qw(e){const t=new URLSearchParams(e);return`${wn}/api/websocket?${t}`}function W0(){return pe.get("/login/token")}function Gw(){return pe.get("/api/meetings")}function Jw(e){return pe.get(`/api/meetings/${e}`)}function Yw(){return pe.post(`/api/user/signature?AppID=${hm}`)}function Ho(...e){if(e){let t=[];for(let r=0;r<e.length;r++){const o=e[r];if(!o)continue;const n=typeof o;if(n==="string"||n==="number")t.push(o);else if(n==="object"){const i=Array.isArray(o)?[Ho(...o)]:Object.entries(o).map(([s,a])=>a?s:void 0);t=i.length?t.concat(i.filter(s=>!!s)):t}}return t.join(" ").trim()}}function zd(e,t){return e?e.classList?e.classList.contains(t):new RegExp("(^| )"+t+"( |$)","gi").test(e.className):!1}function Hd(e,t){if(e&&t){const r=o=>{zd(e,o)||(e.classList?e.classList.add(o):e.className+=" "+o)};[t].flat().filter(Boolean).forEach(o=>o.split(" ").forEach(r))}}function gn(e,t){if(e&&t){const r=o=>{e.classList?e.classList.remove(o):e.className=e.className.replace(new RegExp("(^|\\b)"+o.split(" ").join("|")+"(\\b|$)","gi")," ")};[t].flat().filter(Boolean).forEach(o=>o.split(" ").forEach(r))}}function hl(e){for(const t of document==null?void 0:document.styleSheets)try{for(const r of t==null?void 0:t.cssRules)for(const o of r==null?void 0:r.style)if(e.test(o))return{name:o,value:r.style.getPropertyValue(o).trim()}}catch{}return null}function K0(e){const t={width:0,height:0};return e&&(e.style.visibility="hidden",e.style.display="block",t.width=e.offsetWidth,t.height=e.offsetHeight,e.style.display="none",e.style.visibility="visible"),t}function hn(){const e=window,t=document,r=t.documentElement,o=t.getElementsByTagName("body")[0],n=e.innerWidth||r.clientWidth||o.clientWidth,i=e.innerHeight||r.clientHeight||o.clientHeight;return{width:n,height:i}}function os(e){return e?Math.abs(e.scrollLeft):0}function Ud(){const e=document.documentElement;return(window.pageXOffset||os(e))-(e.clientLeft||0)}function Vd(){const e=document.documentElement;return(window.pageYOffset||e.scrollTop)-(e.clientTop||0)}function q0(e){return e?getComputedStyle(e).direction==="rtl":!1}function Xw(e,t,r=!0){var o,n,i,s;if(e){const a=e.offsetParent?{width:e.offsetWidth,height:e.offsetHeight}:K0(e),l=a.height,u=a.width,c=t.offsetHeight,d=t.offsetWidth,f=t.getBoundingClientRect(),p=Vd(),h=Ud(),b=hn();let C,k,_="top";f.top+c+l>b.height?(C=f.top+p-l,_="bottom",C<0&&(C=p)):C=c+f.top+p,f.left+u>b.width?k=Math.max(0,f.left+h+d-u):k=f.left+h,q0(e)?e.style.insetInlineEnd=k+"px":e.style.insetInlineStart=k+"px",e.style.top=C+"px",e.style.transformOrigin=_,r&&(e.style.marginTop=_==="bottom"?`calc(${(n=(o=hl(/-anchor-gutter$/))==null?void 0:o.value)!=null?n:"2px"} * -1)`:(s=(i=hl(/-anchor-gutter$/))==null?void 0:i.value)!=null?s:"")}}function Zw(e,t){e&&(typeof t=="string"?e.style.cssText=t:Object.entries(t||{}).forEach(([r,o])=>e.style[r]=o))}function rt(e,t){return e instanceof HTMLElement?e.offsetWidth:0}function Wd(e){if(e){let t=e.parentNode;return t&&t instanceof ShadowRoot&&t.host&&(t=t.host),t}return null}function Kd(e){return!!(e!==null&&typeof e<"u"&&e.nodeName&&Wd(e))}function Cr(e){return typeof Element<"u"?e instanceof Element:e!==null&&typeof e=="object"&&e.nodeType===1&&typeof e.nodeName=="string"}function En(e,t={}){if(Cr(e)){const r=(o,n)=>{var i,s;const a=(i=e==null?void 0:e.$attrs)!=null&&i[o]?[(s=e==null?void 0:e.$attrs)==null?void 0:s[o]]:[];return[n].flat().reduce((l,u)=>{if(u!=null){const c=typeof u;if(c==="string"||c==="number")l.push(u);else if(c==="object"){const d=Array.isArray(u)?r(o,u):Object.entries(u).map(([f,p])=>o==="style"&&(p||p===0)?`${f.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}:${p}`:p?f:void 0);l=d.length?l.concat(d.filter(f=>!!f)):l}}return l},a)};Object.entries(t).forEach(([o,n])=>{if(n!=null){const i=o.match(/^on(.+)/);i?e.addEventListener(i[1].toLowerCase(),n):o==="p-bind"||o==="pBind"?En(e,n):(n=o==="class"?[...new Set(r("class",n))].join(" ").trim():o==="style"?r("style",n).join(";").trim():n,(e.$attrs=e.$attrs||{})&&(e.$attrs[o]=n),e.setAttribute(o,n))}})}}function mn(e,t={},...r){if(e){const o=document.createElement(e);return En(o,t),o.append(...r),o}}function G0(e,t){if(e){e.style.opacity="0";let r=+new Date,o="0";const n=function(){o=`${+e.style.opacity+(new Date().getTime()-r)/t}`,e.style.opacity=o,r=+new Date,+o<1&&("requestAnimationFrame"in window?requestAnimationFrame(n):setTimeout(n,16))};n()}}function J0(e,t){return Cr(e)?Array.from(e.querySelectorAll(t)):[]}function On(e,t){return Cr(e)?e.matches(t)?e:e.querySelector(t):null}function Qw(e,t){e&&document.activeElement!==e&&e.focus(t)}function cr(e,t){if(Cr(e)){const r=e.getAttribute(t);return isNaN(r)?r==="true"||r==="false"?r==="true":r:+r}}function qd(e,t=""){const r=J0(e,`button:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [href][clientHeight][clientWidth]:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            input:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            select:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            textarea:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [tabIndex]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [contenteditable]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t}`),o=[];for(const n of r)getComputedStyle(n).display!="none"&&getComputedStyle(n).visibility!="hidden"&&o.push(n);return o}function e2(e,t){const r=qd(e,t);return r.length>0?r[0]:null}function ml(e){if(e){let t=e.offsetHeight;const r=getComputedStyle(e);return t-=parseFloat(r.paddingTop)+parseFloat(r.paddingBottom)+parseFloat(r.borderTopWidth)+parseFloat(r.borderBottomWidth),t}return 0}function t2(e,t){const r=qd(e,t);return r.length>0?r[r.length-1]:null}function Y0(e){if(e){const t=e.getBoundingClientRect();return{top:t.top+(window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0),left:t.left+(window.pageXOffset||os(document.documentElement)||os(document.body)||0)}}return{top:"auto",left:"auto"}}function Vt(e,t){return e?e.offsetHeight:0}function Gd(e,t=[]){const r=Wd(e);return r===null?t:Gd(r,t.concat([r]))}function X0(e){const t=[];if(e){const r=Gd(e),o=/(auto|scroll)/,n=i=>{try{const s=window.getComputedStyle(i,null);return o.test(s.getPropertyValue("overflow"))||o.test(s.getPropertyValue("overflowX"))||o.test(s.getPropertyValue("overflowY"))}catch{return!1}};for(const i of r){const s=i.nodeType===1&&i.dataset.scrollselectors;if(s){const a=s.split(",");for(const l of a){const u=On(i,l);u&&n(u)&&t.push(u)}}i.nodeType!==9&&n(i)&&t.push(i)}}return t}function bl(e){if(e){let t=e.offsetWidth;const r=getComputedStyle(e);return t-=parseFloat(r.paddingLeft)+parseFloat(r.paddingRight)+parseFloat(r.borderLeftWidth)+parseFloat(r.borderRightWidth),t}return 0}function Jd(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function r2(e,t=""){return Cr(e)?e.matches(`button:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [href][clientHeight][clientWidth]:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            input:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            select:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            textarea:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [tabIndex]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [contenteditable]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t}`):!1}function Z0(){return"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0}function Yd(e,t="",r){Cr(e)&&r!==null&&r!==void 0&&e.setAttribute(t,r)}function Hs(){const e=new Map;return{on(t,r){let o=e.get(t);return o?o.push(r):o=[r],e.set(t,o),this},off(t,r){const o=e.get(t);return o&&o.splice(o.indexOf(r)>>>0,1),this},emit(t,r){const o=e.get(t);o&&o.forEach(n=>{n(r)})},clear(){e.clear()}}}var Q0=Object.defineProperty,vl=Object.getOwnPropertySymbols,eb=Object.prototype.hasOwnProperty,tb=Object.prototype.propertyIsEnumerable,yl=(e,t,r)=>t in e?Q0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rb=(e,t)=>{for(var r in t||(t={}))eb.call(t,r)&&yl(e,r,t[r]);if(vl)for(var r of vl(t))tb.call(t,r)&&yl(e,r,t[r]);return e};function tr(e){return e==null||e===""||Array.isArray(e)&&e.length===0||!(e instanceof Date)&&typeof e=="object"&&Object.keys(e).length===0}function ns(e,t,r=new WeakSet){if(e===t)return!0;if(!e||!t||typeof e!="object"||typeof t!="object"||r.has(e)||r.has(t))return!1;r.add(e).add(t);const o=Array.isArray(e),n=Array.isArray(t);let i,s,a;if(o&&n){if(s=e.length,s!=t.length)return!1;for(i=s;i--!==0;)if(!ns(e[i],t[i],r))return!1;return!0}if(o!=n)return!1;const l=e instanceof Date,u=t instanceof Date;if(l!=u)return!1;if(l&&u)return e.getTime()==t.getTime();const c=e instanceof RegExp,d=t instanceof RegExp;if(c!=d)return!1;if(c&&d)return e.toString()==t.toString();const f=Object.keys(e);if(s=f.length,s!==Object.keys(t).length)return!1;for(i=s;i--!==0;)if(!Object.prototype.hasOwnProperty.call(t,f[i]))return!1;for(i=s;i--!==0;)if(a=f[i],!ns(e[a],t[a],r))return!1;return!0}function ob(e,t){return ns(e,t)}function oi(e){return typeof e=="function"&&"call"in e&&"apply"in e}function Se(e){return!tr(e)}function Cl(e,t){if(!e||!t)return null;try{const r=e[t];if(Se(r))return r}catch{}if(Object.keys(e).length){if(oi(t))return t(e);if(t.indexOf(".")===-1)return e[t];{const r=t.split(".");let o=e;for(let n=0,i=r.length;n<i;++n){if(o==null)return null;o=o[r[n]]}return o}}return null}function o2(e,t,r){return r?Cl(e,r)===Cl(t,r):ob(e,t)}function Pt(e,t=!0){return e instanceof Object&&e.constructor===Object&&(t||Object.keys(e).length!==0)}function Xd(e={},t={}){const r=rb({},e);return Object.keys(t).forEach(o=>{const n=o;Pt(t[n])&&n in e&&Pt(e[n])?r[n]=Xd(e[n],t[n]):r[n]=t[n]}),r}function nb(...e){return e.reduce((t,r,o)=>o===0?r:Xd(t,r),{})}function st(e,...t){return oi(e)?e(...t):e}function Ge(e,t=!0){return typeof e=="string"&&(t||e!=="")}function xt(e){return Ge(e)?e.replace(/(-|_)/g,"").toLowerCase():e}function Us(e,t="",r={}){const o=xt(t).split("."),n=o.shift();if(n){if(Pt(e)){const i=Object.keys(e).find(s=>xt(s)===n)||"";return Us(st(e[i],r),o.join("."),r)}return}return st(e,r)}function ni(e,t=!0){return Array.isArray(e)&&(t||e.length!==0)}function ib(e){return Se(e)&&!isNaN(e)}function Dt(e,t){if(t){const r=t.test(e);return t.lastIndex=0,r}return!1}function sb(...e){return nb(...e)}function io(e){return e&&e.replace(/\/\*(?:(?!\*\/)[\s\S])*\*\/|[\r\n\t]+/g,"").replace(/ {2,}/g," ").replace(/ ([{:}]) /g,"$1").replace(/([;,]) /g,"$1").replace(/ !/g,"!").replace(/: /g,":")}function ab(e){return Ge(e,!1)?e[0].toUpperCase()+e.slice(1):e}function Zd(e){return Ge(e)?e.replace(/(_)/g,"-").replace(/[A-Z]/g,(t,r)=>r===0?t:"-"+t.toLowerCase()).toLowerCase():e}function Sl(e){return Ge(e)?e.replace(/[A-Z]/g,(t,r)=>r===0?t:"."+t.toLowerCase()).toLowerCase():e}var Yo={};function Gr(e="pui_id_"){return Object.hasOwn(Yo,e)||(Yo[e]=0),Yo[e]++,`${e}${Yo[e]}`}function lb(){let e=[];const t=(s,a,l=999)=>{const u=n(s,a,l),c=u.value+(u.key===s?0:l)+1;return e.push({key:s,value:c}),c},r=s=>{e=e.filter(a=>a.value!==s)},o=(s,a)=>n(s).value,n=(s,a,l=0)=>[...e].reverse().find(u=>!0)||{key:s,value:l},i=s=>s&&parseInt(s.style.zIndex,10)||0;return{get:i,set:(s,a,l)=>{a&&(a.style.zIndex=String(t(s,!0,l)))},clear:s=>{s&&(r(i(s)),s.style.zIndex="")},getCurrent:s=>o(s)}}var so=lb(),cb=Object.defineProperty,ub=Object.defineProperties,db=Object.getOwnPropertyDescriptors,Bn=Object.getOwnPropertySymbols,Qd=Object.prototype.hasOwnProperty,ef=Object.prototype.propertyIsEnumerable,kl=(e,t,r)=>t in e?cb(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,dt=(e,t)=>{for(var r in t||(t={}))Qd.call(t,r)&&kl(e,r,t[r]);if(Bn)for(var r of Bn(t))ef.call(t,r)&&kl(e,r,t[r]);return e},$i=(e,t)=>ub(e,db(t)),Bt=(e,t)=>{var r={};for(var o in e)Qd.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&Bn)for(var o of Bn(e))t.indexOf(o)<0&&ef.call(e,o)&&(r[o]=e[o]);return r},fb=Hs(),Ae=fb;function _l(e,t){ni(e)?e.push(...t||[]):Pt(e)&&Object.assign(e,t)}function pb(e){return Pt(e)&&e.hasOwnProperty("$value")&&e.hasOwnProperty("$type")?e.$value:e}function gb(e){return e.replaceAll(/ /g,"").replace(/[^\w]/g,"-")}function is(e="",t=""){return gb(`${Ge(e,!1)&&Ge(t,!1)?`${e}-`:e}${t}`)}function tf(e="",t=""){return`--${is(e,t)}`}function hb(e=""){const t=(e.match(/{/g)||[]).length,r=(e.match(/}/g)||[]).length;return(t+r)%2!==0}function rf(e,t="",r="",o=[],n){if(Ge(e)){const i=/{([^}]*)}/g,s=e.trim();if(hb(s))return;if(Dt(s,i)){const a=s.replaceAll(i,c=>{const f=c.replace(/{|}/g,"").split(".").filter(p=>!o.some(h=>Dt(p,h)));return`var(${tf(r,Zd(f.join("-")))}${Se(n)?`, ${n}`:""})`}),l=/(\d+\s+[\+\-\*\/]\s+\d+)/g,u=/var\([^)]+\)/g;return Dt(a.replace(u,"0"),l)?`calc(${a})`:a}return s}else if(ib(e))return e}function mb(e,t,r){Ge(t,!1)&&e.push(`${t}:${r};`)}function xr(e,t){return e?`${e}{${t}}`:""}var n2=e=>{var t;const r=fe.getTheme(),o=ss(r,e,void 0,"variable"),n=(t=o==null?void 0:o.match(/--[\w-]+/g))==null?void 0:t[0],i=ss(r,e,void 0,"value");return{name:n,variable:o,value:i}},ao=(...e)=>ss(fe.getTheme(),...e),ss=(e={},t,r,o)=>{if(t){const{variable:n,options:i}=fe.defaults||{},{prefix:s,transform:a}=(e==null?void 0:e.options)||i||{},u=Dt(t,/{([^}]*)}/g)?t:`{${t}}`;return o==="value"||tr(o)&&a==="strict"?fe.getTokenValue(t):rf(u,void 0,s,[n.excludedKeyRegex],r)}return""};function bb(e,t={}){const r=fe.defaults.variable,{prefix:o=r.prefix,selector:n=r.selector,excludedKeyRegex:i=r.excludedKeyRegex}=t,s=(u,c="")=>Object.entries(u).reduce((d,[f,p])=>{const h=Dt(f,i)?is(c):is(c,Zd(f)),b=pb(p);if(Pt(b)){const{variables:C,tokens:k}=s(b,h);_l(d.tokens,k),_l(d.variables,C)}else d.tokens.push((o?h.replace(`${o}-`,""):h).replaceAll("-",".")),mb(d.variables,tf(h),rf(b,h,o,[i]));return d},{variables:[],tokens:[]}),{variables:a,tokens:l}=s(e,o);return{value:a,tokens:l,declarations:a.join(""),css:xr(n,a.join(""))}}var ut={regex:{rules:{class:{pattern:/^\.([a-zA-Z][\w-]*)$/,resolve(e){return{type:"class",selector:e,matched:this.pattern.test(e.trim())}}},attr:{pattern:/^\[(.*)\]$/,resolve(e){return{type:"attr",selector:`:root${e}`,matched:this.pattern.test(e.trim())}}},media:{pattern:/^@media (.*)$/,resolve(e){return{type:"media",selector:`${e}{:root{[CSS]}}`,matched:this.pattern.test(e.trim())}}},system:{pattern:/^system$/,resolve(e){return{type:"system",selector:"@media (prefers-color-scheme: dark){:root{[CSS]}}",matched:this.pattern.test(e.trim())}}},custom:{resolve(e){return{type:"custom",selector:e,matched:!0}}}},resolve(e){const t=Object.keys(this.rules).filter(r=>r!=="custom").map(r=>this.rules[r]);return[e].flat().map(r=>{var o;return(o=t.map(n=>n.resolve(r)).find(n=>n.matched))!=null?o:this.rules.custom.resolve(r)})}},_toVariables(e,t){return bb(e,{prefix:t==null?void 0:t.prefix})},getCommon({name:e="",theme:t={},params:r,set:o,defaults:n}){var i,s,a,l,u,c,d;const{preset:f,options:p}=t;let h,b,C,k,_,w,y;if(Se(f)&&p.transform!=="strict"){const{primitive:P,semantic:F,extend:z}=f,N=F||{},{colorScheme:O}=N,U=Bt(N,["colorScheme"]),G=z||{},{colorScheme:L}=G,X=Bt(G,["colorScheme"]),ce=O||{},{dark:_e}=ce,re=Bt(ce,["dark"]),K=L||{},{dark:ee}=K,Ie=Bt(K,["dark"]),Oe=Se(P)?this._toVariables({primitive:P},p):{},Re=Se(U)?this._toVariables({semantic:U},p):{},$e=Se(re)?this._toVariables({light:re},p):{},ct=Se(_e)?this._toVariables({dark:_e},p):{},Je=Se(X)?this._toVariables({semantic:X},p):{},nr=Se(Ie)?this._toVariables({light:Ie},p):{},Fe=Se(ee)?this._toVariables({dark:ee},p):{},[x,M]=[(i=Oe.declarations)!=null?i:"",Oe.tokens],[D,H]=[(s=Re.declarations)!=null?s:"",Re.tokens||[]],[ue,g]=[(a=$e.declarations)!=null?a:"",$e.tokens||[]],[m,v]=[(l=ct.declarations)!=null?l:"",ct.tokens||[]],[$,T]=[(u=Je.declarations)!=null?u:"",Je.tokens||[]],[R,I]=[(c=nr.declarations)!=null?c:"",nr.tokens||[]],[A,B]=[(d=Fe.declarations)!=null?d:"",Fe.tokens||[]];h=this.transformCSS(e,x,"light","variable",p,o,n),b=M;const E=this.transformCSS(e,`${D}${ue}`,"light","variable",p,o,n),W=this.transformCSS(e,`${m}`,"dark","variable",p,o,n);C=`${E}${W}`,k=[...new Set([...H,...g,...v])];const j=this.transformCSS(e,`${$}${R}color-scheme:light`,"light","variable",p,o,n),V=this.transformCSS(e,`${A}color-scheme:dark`,"dark","variable",p,o,n);_=`${j}${V}`,w=[...new Set([...T,...I,...B])],y=st(f.css,{dt:ao})}return{primitive:{css:h,tokens:b},semantic:{css:C,tokens:k},global:{css:_,tokens:w},style:y}},getPreset({name:e="",preset:t={},options:r,params:o,set:n,defaults:i,selector:s}){var a,l,u;let c,d,f;if(Se(t)&&r.transform!=="strict"){const p=e.replace("-directive",""),h=t,{colorScheme:b,extend:C,css:k}=h,_=Bt(h,["colorScheme","extend","css"]),w=C||{},{colorScheme:y}=w,P=Bt(w,["colorScheme"]),F=b||{},{dark:z}=F,N=Bt(F,["dark"]),O=y||{},{dark:U}=O,G=Bt(O,["dark"]),L=Se(_)?this._toVariables({[p]:dt(dt({},_),P)},r):{},X=Se(N)?this._toVariables({[p]:dt(dt({},N),G)},r):{},ce=Se(z)?this._toVariables({[p]:dt(dt({},z),U)},r):{},[_e,re]=[(a=L.declarations)!=null?a:"",L.tokens||[]],[K,ee]=[(l=X.declarations)!=null?l:"",X.tokens||[]],[Ie,Oe]=[(u=ce.declarations)!=null?u:"",ce.tokens||[]],Re=this.transformCSS(p,`${_e}${K}`,"light","variable",r,n,i,s),$e=this.transformCSS(p,Ie,"dark","variable",r,n,i,s);c=`${Re}${$e}`,d=[...new Set([...re,...ee,...Oe])],f=st(k,{dt:ao})}return{css:c,tokens:d,style:f}},getPresetC({name:e="",theme:t={},params:r,set:o,defaults:n}){var i;const{preset:s,options:a}=t,l=(i=s==null?void 0:s.components)==null?void 0:i[e];return this.getPreset({name:e,preset:l,options:a,params:r,set:o,defaults:n})},getPresetD({name:e="",theme:t={},params:r,set:o,defaults:n}){var i,s;const a=e.replace("-directive",""),{preset:l,options:u}=t,c=((i=l==null?void 0:l.components)==null?void 0:i[a])||((s=l==null?void 0:l.directives)==null?void 0:s[a]);return this.getPreset({name:a,preset:c,options:u,params:r,set:o,defaults:n})},applyDarkColorScheme(e){return!(e.darkModeSelector==="none"||e.darkModeSelector===!1)},getColorSchemeOption(e,t){var r;return this.applyDarkColorScheme(e)?this.regex.resolve(e.darkModeSelector===!0?t.options.darkModeSelector:(r=e.darkModeSelector)!=null?r:t.options.darkModeSelector):[]},getLayerOrder(e,t={},r,o){const{cssLayer:n}=t;return n?`@layer ${st(n.order||"primeui",r)}`:""},getCommonStyleSheet({name:e="",theme:t={},params:r,props:o={},set:n,defaults:i}){const s=this.getCommon({name:e,theme:t,params:r,set:n,defaults:i}),a=Object.entries(o).reduce((l,[u,c])=>l.push(`${u}="${c}"`)&&l,[]).join(" ");return Object.entries(s||{}).reduce((l,[u,c])=>{if(c!=null&&c.css){const d=io(c==null?void 0:c.css),f=`${u}-variables`;l.push(`<style type="text/css" data-primevue-style-id="${f}" ${a}>${d}</style>`)}return l},[]).join("")},getStyleSheet({name:e="",theme:t={},params:r,props:o={},set:n,defaults:i}){var s;const a={name:e,theme:t,params:r,set:n,defaults:i},l=(s=e.includes("-directive")?this.getPresetD(a):this.getPresetC(a))==null?void 0:s.css,u=Object.entries(o).reduce((c,[d,f])=>c.push(`${d}="${f}"`)&&c,[]).join(" ");return l?`<style type="text/css" data-primevue-style-id="${e}-variables" ${u}>${io(l)}</style>`:""},createTokens(e={},t,r="",o="",n={}){return Object.entries(e).forEach(([i,s])=>{const a=Dt(i,t.variable.excludedKeyRegex)?r:r?`${r}.${Sl(i)}`:Sl(i),l=o?`${o}.${i}`:i;Pt(s)?this.createTokens(s,t,a,l,n):(n[a]||(n[a]={paths:[],computed(u,c={}){var d,f;return this.paths.length===1?(d=this.paths[0])==null?void 0:d.computed(this.paths[0].scheme,c.binding):u&&u!=="none"?(f=this.paths.find(p=>p.scheme===u))==null?void 0:f.computed(u,c.binding):this.paths.map(p=>p.computed(p.scheme,c[p.scheme]))}}),n[a].paths.push({path:l,value:s,scheme:l.includes("colorScheme.light")?"light":l.includes("colorScheme.dark")?"dark":"none",computed(u,c={}){const d=/{([^}]*)}/g;let f=s;if(c.name=this.path,c.binding||(c.binding={}),Dt(s,d)){const h=s.trim().replaceAll(d,k=>{var _;const w=k.replace(/{|}/g,""),y=(_=n[w])==null?void 0:_.computed(u,c);return ni(y)&&y.length===2?`light-dark(${y[0].value},${y[1].value})`:y==null?void 0:y.value}),b=/(\d+\w*\s+[\+\-\*\/]\s+\d+\w*)/g,C=/var\([^)]+\)/g;f=Dt(h.replace(C,"0"),b)?`calc(${h})`:h}return tr(c.binding)&&delete c.binding,{colorScheme:u,path:this.path,paths:c,value:f.includes("undefined")?void 0:f}}}))}),n},getTokenValue(e,t,r){var o;const i=(l=>l.split(".").filter(c=>!Dt(c.toLowerCase(),r.variable.excludedKeyRegex)).join("."))(t),s=t.includes("colorScheme.light")?"light":t.includes("colorScheme.dark")?"dark":void 0,a=[(o=e[i])==null?void 0:o.computed(s)].flat().filter(l=>l);return a.length===1?a[0].value:a.reduce((l={},u)=>{const c=u,{colorScheme:d}=c,f=Bt(c,["colorScheme"]);return l[d]=f,l},void 0)},getSelectorRule(e,t,r,o){return r==="class"||r==="attr"?xr(Se(t)?`${e}${t},${e} ${t}`:e,o):xr(e,Se(t)?xr(t,o):o)},transformCSS(e,t,r,o,n={},i,s,a){if(Se(t)){const{cssLayer:l}=n;if(o!=="style"){const u=this.getColorSchemeOption(n,s);t=r==="dark"?u.reduce((c,{type:d,selector:f})=>(Se(f)&&(c+=f.includes("[CSS]")?f.replace("[CSS]",t):this.getSelectorRule(f,a,d,t)),c),""):xr(a??":root",t)}if(l){const u={name:"primeui"};Pt(l)&&(u.name=st(l.name,{name:e,type:o})),Se(u.name)&&(t=xr(`@layer ${u.name}`,t),i==null||i.layerNames(u.name))}return t}return""}},fe={defaults:{variable:{prefix:"p",selector:":root",excludedKeyRegex:/^(primitive|semantic|components|directives|variables|colorscheme|light|dark|common|root|states|extend|css)$/gi},options:{prefix:"p",darkModeSelector:"system",cssLayer:!1}},_theme:void 0,_layerNames:new Set,_loadedStyleNames:new Set,_loadingStyles:new Set,_tokens:{},update(e={}){const{theme:t}=e;t&&(this._theme=$i(dt({},t),{options:dt(dt({},this.defaults.options),t.options)}),this._tokens=ut.createTokens(this.preset,this.defaults),this.clearLoadedStyleNames())},get theme(){return this._theme},get preset(){var e;return((e=this.theme)==null?void 0:e.preset)||{}},get options(){var e;return((e=this.theme)==null?void 0:e.options)||{}},get tokens(){return this._tokens},getTheme(){return this.theme},setTheme(e){this.update({theme:e}),Ae.emit("theme:change",e)},getPreset(){return this.preset},setPreset(e){this._theme=$i(dt({},this.theme),{preset:e}),this._tokens=ut.createTokens(e,this.defaults),this.clearLoadedStyleNames(),Ae.emit("preset:change",e),Ae.emit("theme:change",this.theme)},getOptions(){return this.options},setOptions(e){this._theme=$i(dt({},this.theme),{options:e}),this.clearLoadedStyleNames(),Ae.emit("options:change",e),Ae.emit("theme:change",this.theme)},getLayerNames(){return[...this._layerNames]},setLayerNames(e){this._layerNames.add(e)},getLoadedStyleNames(){return this._loadedStyleNames},isStyleNameLoaded(e){return this._loadedStyleNames.has(e)},setLoadedStyleName(e){this._loadedStyleNames.add(e)},deleteLoadedStyleName(e){this._loadedStyleNames.delete(e)},clearLoadedStyleNames(){this._loadedStyleNames.clear()},getTokenValue(e){return ut.getTokenValue(this.tokens,e,this.defaults)},getCommon(e="",t){return ut.getCommon({name:e,theme:this.theme,params:t,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},getComponent(e="",t){const r={name:e,theme:this.theme,params:t,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return ut.getPresetC(r)},getDirective(e="",t){const r={name:e,theme:this.theme,params:t,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return ut.getPresetD(r)},getCustomPreset(e="",t,r,o){const n={name:e,preset:t,options:this.options,selector:r,params:o,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return ut.getPreset(n)},getLayerOrderCSS(e=""){return ut.getLayerOrder(e,this.options,{names:this.getLayerNames()},this.defaults)},transformCSS(e="",t,r="style",o){return ut.transformCSS(e,t,o,r,this.options,{layerNames:this.setLayerNames.bind(this)},this.defaults)},getCommonStyleSheet(e="",t,r={}){return ut.getCommonStyleSheet({name:e,theme:this.theme,params:t,props:r,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},getStyleSheet(e,t,r={}){return ut.getStyleSheet({name:e,theme:this.theme,params:t,props:r,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},onStyleMounted(e){this._loadingStyles.add(e)},onStyleUpdated(e){this._loadingStyles.add(e)},onStyleLoaded(e,{name:t}){this._loadingStyles.size&&(this._loadingStyles.delete(t),Ae.emit(`theme:${t}:load`,e),!this._loadingStyles.size&&Ae.emit("theme:load"))}},Jt={_loadedStyleNames:new Set,getLoadedStyleNames:function(){return this._loadedStyleNames},isStyleNameLoaded:function(t){return this._loadedStyleNames.has(t)},setLoadedStyleName:function(t){this._loadedStyleNames.add(t)},deleteLoadedStyleName:function(t){this._loadedStyleNames.delete(t)},clearLoadedStyleNames:function(){this._loadedStyleNames.clear()}},vb=({dt:e})=>`
*,
::before,
::after {
    box-sizing: border-box;
}

/* Non vue overlay animations */
.p-connected-overlay {
    opacity: 0;
    transform: scaleY(0.8);
    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1),
        opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.p-connected-overlay-visible {
    opacity: 1;
    transform: scaleY(1);
}

.p-connected-overlay-hidden {
    opacity: 0;
    transform: scaleY(1);
    transition: opacity 0.1s linear;
}

/* Vue based overlay animations */
.p-connected-overlay-enter-from {
    opacity: 0;
    transform: scaleY(0.8);
}

.p-connected-overlay-leave-to {
    opacity: 0;
}

.p-connected-overlay-enter-active {
    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1),
        opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.p-connected-overlay-leave-active {
    transition: opacity 0.1s linear;
}

/* Toggleable Content */
.p-toggleable-content-enter-from,
.p-toggleable-content-leave-to {
    max-height: 0;
}

.p-toggleable-content-enter-to,
.p-toggleable-content-leave-from {
    max-height: 1000px;
}

.p-toggleable-content-leave-active {
    overflow: hidden;
    transition: max-height 0.45s cubic-bezier(0, 1, 0, 1);
}

.p-toggleable-content-enter-active {
    overflow: hidden;
    transition: max-height 1s ease-in-out;
}

.p-disabled,
.p-disabled * {
    cursor: default;
    pointer-events: none;
    user-select: none;
}

.p-disabled,
.p-component:disabled {
    opacity: ${e("disabled.opacity")};
}

.pi {
    font-size: ${e("icon.size")};
}

.p-icon {
    width: ${e("icon.size")};
    height: ${e("icon.size")};
}

.p-overlay-mask {
    background: ${e("mask.background")};
    color: ${e("mask.color")};
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.p-overlay-mask-enter {
    animation: p-overlay-mask-enter-animation ${e("mask.transition.duration")} forwards;
}

.p-overlay-mask-leave {
    animation: p-overlay-mask-leave-animation ${e("mask.transition.duration")} forwards;
}

@keyframes p-overlay-mask-enter-animation {
    from {
        background: transparent;
    }
    to {
        background: ${e("mask.background")};
    }
}
@keyframes p-overlay-mask-leave-animation {
    from {
        background: ${e("mask.background")};
    }
    to {
        background: transparent;
    }
}
`;function ko(e){"@babel/helpers - typeof";return ko=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ko(e)}function $l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function xl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?$l(Object(r),!0).forEach(function(o){yb(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):$l(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function yb(e,t,r){return(t=Cb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Cb(e){var t=Sb(e,"string");return ko(t)=="symbol"?t:t+""}function Sb(e,t){if(ko(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(ko(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function kb(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;bo()&&bo().components?Es(e):t?e():Hn(e)}var _b=0;function $b(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=Tt(!1),o=Tt(e),n=Tt(null),i=Jd()?window.document:void 0,s=t.document,a=s===void 0?i:s,l=t.immediate,u=l===void 0?!0:l,c=t.manual,d=c===void 0?!1:c,f=t.name,p=f===void 0?"style_".concat(++_b):f,h=t.id,b=h===void 0?void 0:h,C=t.media,k=C===void 0?void 0:C,_=t.nonce,w=_===void 0?void 0:_,y=t.first,P=y===void 0?!1:y,F=t.onMounted,z=F===void 0?void 0:F,N=t.onUpdated,O=N===void 0?void 0:N,U=t.onLoad,G=U===void 0?void 0:U,L=t.props,X=L===void 0?{}:L,ce=function(){},_e=function(ee){var Ie=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(a){var Oe=xl(xl({},X),Ie),Re=Oe.name||p,$e=Oe.id||b,ct=Oe.nonce||w;n.value=a.querySelector('style[data-primevue-style-id="'.concat(Re,'"]'))||a.getElementById($e)||a.createElement("style"),n.value.isConnected||(o.value=ee||e,En(n.value,{type:"text/css",id:$e,media:k,nonce:ct}),P?a.head.prepend(n.value):a.head.appendChild(n.value),Yd(n.value,"data-primevue-style-id",Re),En(n.value,Oe),n.value.onload=function(Je){return G==null?void 0:G(Je,{name:Re})},z==null||z(Re)),!r.value&&(ce=Rt(o,function(Je){n.value.textContent=Je,O==null||O(Re)},{immediate:!0}),r.value=!0)}},re=function(){!a||!r.value||(ce(),Kd(n.value)&&a.head.removeChild(n.value),r.value=!1,n.value=null)};return u&&!d&&kb(_e),{id:b,name:p,el:n,css:o,unload:re,load:_e,isLoaded:_s(r)}}function _o(e){"@babel/helpers - typeof";return _o=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_o(e)}function Rl(e,t){return Tb(e)||wb(e,t)||Rb(e,t)||xb()}function xb(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Rb(e,t){if(e){if(typeof e=="string")return wl(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?wl(e,t):void 0}}function wl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function wb(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var o,n,i,s,a=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(l=(o=i.call(r)).done)&&(a.push(o.value),a.length!==t);l=!0);}catch(c){u=!0,n=c}finally{try{if(!l&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return a}}function Tb(e){if(Array.isArray(e))return e}function Tl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function xi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Tl(Object(r),!0).forEach(function(o){Pb(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Tl(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function Pb(e,t,r){return(t=Eb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Eb(e){var t=Ob(e,"string");return _o(t)=="symbol"?t:t+""}function Ob(e,t){if(_o(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(_o(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Bb=function(t){var r=t.dt;return`
.p-hidden-accessible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    opacity: 0;
    overflow: hidden;
    padding: 0;
    pointer-events: none;
    position: absolute;
    white-space: nowrap;
    width: 1px;
}

.p-overflow-hidden {
    overflow: hidden;
    padding-right: `.concat(r("scrollbar.width"),`;
}
`)},Ab={},Lb={},ye={name:"base",css:Bb,style:vb,classes:Ab,inlineStyles:Lb,load:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(i){return i},n=o(st(t,{dt:ao}));return Se(n)?$b(io(n),xi({name:this.name},r)):{}},loadCSS:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return this.load(this.css,t)},loadStyle:function(){var t=this,r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return this.load(this.style,r,function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";return fe.transformCSS(r.name||t.name,"".concat(n).concat(o))})},getCommonTheme:function(t){return fe.getCommon(this.name,t)},getComponentTheme:function(t){return fe.getComponent(this.name,t)},getDirectiveTheme:function(t){return fe.getDirective(this.name,t)},getPresetTheme:function(t,r,o){return fe.getCustomPreset(this.name,t,r,o)},getLayerOrderThemeCSS:function(){return fe.getLayerOrderCSS(this.name)},getStyleSheet:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.css){var o=st(this.css,{dt:ao})||"",n=io("".concat(o).concat(t)),i=Object.entries(r).reduce(function(s,a){var l=Rl(a,2),u=l[0],c=l[1];return s.push("".concat(u,'="').concat(c,'"'))&&s},[]).join(" ");return Se(n)?'<style type="text/css" data-primevue-style-id="'.concat(this.name,'" ').concat(i,">").concat(n,"</style>"):""}return""},getCommonThemeStyleSheet:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return fe.getCommonStyleSheet(this.name,t,r)},getThemeStyleSheet:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=[fe.getStyleSheet(this.name,t,r)];if(this.style){var n=this.name==="base"?"global-style":"".concat(this.name,"-style"),i=st(this.style,{dt:ao}),s=io(fe.transformCSS(n,i)),a=Object.entries(r).reduce(function(l,u){var c=Rl(u,2),d=c[0],f=c[1];return l.push("".concat(d,'="').concat(f,'"'))&&l},[]).join(" ");Se(s)&&o.push('<style type="text/css" data-primevue-style-id="'.concat(n,'" ').concat(a,">").concat(s,"</style>"))}return o.join("")},extend:function(t){return xi(xi({},this),{},{css:void 0,style:void 0},t)}};function Ib(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"pc",t=$p();return"".concat(e).concat(t.replace("v-","").replaceAll("-","_"))}var Pl=ye.extend({name:"common"});function $o(e){"@babel/helpers - typeof";return $o=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$o(e)}function Db(e){return sf(e)||jb(e)||nf(e)||of()}function jb(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Wr(e,t){return sf(e)||Mb(e,t)||nf(e,t)||of()}function of(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function nf(e,t){if(e){if(typeof e=="string")return El(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?El(e,t):void 0}}function El(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function Mb(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var o,n,i,s,a=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;l=!1}else for(;!(l=(o=i.call(r)).done)&&(a.push(o.value),a.length!==t);l=!0);}catch(c){u=!0,n=c}finally{try{if(!l&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return a}}function sf(e){if(Array.isArray(e))return e}function Ol(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function te(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ol(Object(r),!0).forEach(function(o){Jr(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ol(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function Jr(e,t,r){return(t=Nb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Nb(e){var t=Fb(e,"string");return $o(t)=="symbol"?t:t+""}function Fb(e,t){if($o(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if($o(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Uo={name:"BaseComponent",props:{pt:{type:Object,default:void 0},ptOptions:{type:Object,default:void 0},unstyled:{type:Boolean,default:void 0},dt:{type:Object,default:void 0}},inject:{$parentInstance:{default:void 0}},watch:{isUnstyled:{immediate:!0,handler:function(t){Ae.off("theme:change",this._loadCoreStyles),t||(this._loadCoreStyles(),this._themeChangeListener(this._loadCoreStyles))}},dt:{immediate:!0,handler:function(t,r){var o=this;Ae.off("theme:change",this._themeScopedListener),t?(this._loadScopedThemeStyles(t),this._themeScopedListener=function(){return o._loadScopedThemeStyles(t)},this._themeChangeListener(this._themeScopedListener)):this._unloadScopedThemeStyles()}}},scopedStyleEl:void 0,rootEl:void 0,uid:void 0,$attrSelector:void 0,beforeCreate:function(){var t,r,o,n,i,s,a,l,u,c,d,f=(t=this.pt)===null||t===void 0?void 0:t._usept,p=f?(r=this.pt)===null||r===void 0||(r=r.originalValue)===null||r===void 0?void 0:r[this.$.type.name]:void 0,h=f?(o=this.pt)===null||o===void 0||(o=o.value)===null||o===void 0?void 0:o[this.$.type.name]:this.pt;(n=h||p)===null||n===void 0||(n=n.hooks)===null||n===void 0||(i=n.onBeforeCreate)===null||i===void 0||i.call(n);var b=(s=this.$primevueConfig)===null||s===void 0||(s=s.pt)===null||s===void 0?void 0:s._usept,C=b?(a=this.$primevue)===null||a===void 0||(a=a.config)===null||a===void 0||(a=a.pt)===null||a===void 0?void 0:a.originalValue:void 0,k=b?(l=this.$primevue)===null||l===void 0||(l=l.config)===null||l===void 0||(l=l.pt)===null||l===void 0?void 0:l.value:(u=this.$primevue)===null||u===void 0||(u=u.config)===null||u===void 0?void 0:u.pt;(c=k||C)===null||c===void 0||(c=c[this.$.type.name])===null||c===void 0||(c=c.hooks)===null||c===void 0||(d=c.onBeforeCreate)===null||d===void 0||d.call(c),this.$attrSelector=Ib(),this.uid=this.$attrs.id||this.$attrSelector.replace("pc","pv_id_")},created:function(){this._hook("onCreated")},beforeMount:function(){var t;this.rootEl=On(Cr(this.$el)?this.$el:(t=this.$el)===null||t===void 0?void 0:t.parentElement,"[".concat(this.$attrSelector,"]")),this.rootEl&&(this.rootEl.$pc=te({name:this.$.type.name,attrSelector:this.$attrSelector},this.$params)),this._loadStyles(),this._hook("onBeforeMount")},mounted:function(){this._hook("onMounted")},beforeUpdate:function(){this._hook("onBeforeUpdate")},updated:function(){this._hook("onUpdated")},beforeUnmount:function(){this._hook("onBeforeUnmount")},unmounted:function(){this._removeThemeListeners(),this._unloadScopedThemeStyles(),this._hook("onUnmounted")},methods:{_hook:function(t){if(!this.$options.hostName){var r=this._usePT(this._getPT(this.pt,this.$.type.name),this._getOptionValue,"hooks.".concat(t)),o=this._useDefaultPT(this._getOptionValue,"hooks.".concat(t));r==null||r(),o==null||o()}},_mergeProps:function(t){for(var r=arguments.length,o=new Array(r>1?r-1:0),n=1;n<r;n++)o[n-1]=arguments[n];return oi(t)?t.apply(void 0,o):he.apply(void 0,o)},_load:function(){Jt.isStyleNameLoaded("base")||(ye.loadCSS(this.$styleOptions),this._loadGlobalStyles(),Jt.setLoadedStyleName("base")),this._loadThemeStyles()},_loadStyles:function(){this._load(),this._themeChangeListener(this._load)},_loadCoreStyles:function(){var t,r;!Jt.isStyleNameLoaded((t=this.$style)===null||t===void 0?void 0:t.name)&&(r=this.$style)!==null&&r!==void 0&&r.name&&(Pl.loadCSS(this.$styleOptions),this.$options.style&&this.$style.loadCSS(this.$styleOptions),Jt.setLoadedStyleName(this.$style.name))},_loadGlobalStyles:function(){var t=this._useGlobalPT(this._getOptionValue,"global.css",this.$params);Se(t)&&ye.load(t,te({name:"global"},this.$styleOptions))},_loadThemeStyles:function(){var t,r;if(!(this.isUnstyled||this.$theme==="none")){if(!fe.isStyleNameLoaded("common")){var o,n,i=((o=this.$style)===null||o===void 0||(n=o.getCommonTheme)===null||n===void 0?void 0:n.call(o))||{},s=i.primitive,a=i.semantic,l=i.global,u=i.style;ye.load(s==null?void 0:s.css,te({name:"primitive-variables"},this.$styleOptions)),ye.load(a==null?void 0:a.css,te({name:"semantic-variables"},this.$styleOptions)),ye.load(l==null?void 0:l.css,te({name:"global-variables"},this.$styleOptions)),ye.loadStyle(te({name:"global-style"},this.$styleOptions),u),fe.setLoadedStyleName("common")}if(!fe.isStyleNameLoaded((t=this.$style)===null||t===void 0?void 0:t.name)&&(r=this.$style)!==null&&r!==void 0&&r.name){var c,d,f,p,h=((c=this.$style)===null||c===void 0||(d=c.getComponentTheme)===null||d===void 0?void 0:d.call(c))||{},b=h.css,C=h.style;(f=this.$style)===null||f===void 0||f.load(b,te({name:"".concat(this.$style.name,"-variables")},this.$styleOptions)),(p=this.$style)===null||p===void 0||p.loadStyle(te({name:"".concat(this.$style.name,"-style")},this.$styleOptions),C),fe.setLoadedStyleName(this.$style.name)}if(!fe.isStyleNameLoaded("layer-order")){var k,_,w=(k=this.$style)===null||k===void 0||(_=k.getLayerOrderThemeCSS)===null||_===void 0?void 0:_.call(k);ye.load(w,te({name:"layer-order",first:!0},this.$styleOptions)),fe.setLoadedStyleName("layer-order")}}},_loadScopedThemeStyles:function(t){var r,o,n,i=((r=this.$style)===null||r===void 0||(o=r.getPresetTheme)===null||o===void 0?void 0:o.call(r,t,"[".concat(this.$attrSelector,"]")))||{},s=i.css,a=(n=this.$style)===null||n===void 0?void 0:n.load(s,te({name:"".concat(this.$attrSelector,"-").concat(this.$style.name)},this.$styleOptions));this.scopedStyleEl=a.el},_unloadScopedThemeStyles:function(){var t;(t=this.scopedStyleEl)===null||t===void 0||(t=t.value)===null||t===void 0||t.remove()},_themeChangeListener:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(){};Jt.clearLoadedStyleNames(),Ae.on("theme:change",t)},_removeThemeListeners:function(){Ae.off("theme:change",this._loadCoreStyles),Ae.off("theme:change",this._load),Ae.off("theme:change",this._themeScopedListener)},_getHostInstance:function(t){return t?this.$options.hostName?t.$.type.name===this.$options.hostName?t:this._getHostInstance(t.$parentInstance):t.$parentInstance:void 0},_getPropValue:function(t){var r;return this[t]||((r=this._getHostInstance(this))===null||r===void 0?void 0:r[t])},_getOptionValue:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Us(t,r,o)},_getPTValue:function(){var t,r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,s=/./g.test(o)&&!!n[o.split(".")[0]],a=this._getPropValue("ptOptions")||((t=this.$primevueConfig)===null||t===void 0?void 0:t.ptOptions)||{},l=a.mergeSections,u=l===void 0?!0:l,c=a.mergeProps,d=c===void 0?!1:c,f=i?s?this._useGlobalPT(this._getPTClassValue,o,n):this._useDefaultPT(this._getPTClassValue,o,n):void 0,p=s?void 0:this._getPTSelf(r,this._getPTClassValue,o,te(te({},n),{},{global:f||{}})),h=this._getPTDatasets(o);return u||!u&&p?d?this._mergeProps(d,f,p,h):te(te(te({},f),p),h):te(te({},p),h)},_getPTSelf:function(){for(var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length,o=new Array(r>1?r-1:0),n=1;n<r;n++)o[n-1]=arguments[n];return he(this._usePT.apply(this,[this._getPT(t,this.$name)].concat(o)),this._usePT.apply(this,[this.$_attrsPT].concat(o)))},_getPTDatasets:function(){var t,r,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n="data-pc-",i=o==="root"&&Se((t=this.pt)===null||t===void 0?void 0:t["data-pc-section"]);return o!=="transition"&&te(te({},o==="root"&&te(te(Jr({},"".concat(n,"name"),xt(i?(r=this.pt)===null||r===void 0?void 0:r["data-pc-section"]:this.$.type.name)),i&&Jr({},"".concat(n,"extend"),xt(this.$.type.name))),{},Jr({},"".concat(this.$attrSelector),""))),{},Jr({},"".concat(n,"section"),xt(o)))},_getPTClassValue:function(){var t=this._getOptionValue.apply(this,arguments);return Ge(t)||ni(t)?{class:t}:t},_getPT:function(t){var r=this,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,i=function(a){var l,u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,c=n?n(a):a,d=xt(o),f=xt(r.$name);return(l=u?d!==f?c==null?void 0:c[d]:void 0:c==null?void 0:c[d])!==null&&l!==void 0?l:c};return t!=null&&t.hasOwnProperty("_usept")?{_usept:t._usept,originalValue:i(t.originalValue),value:i(t.value)}:i(t,!0)},_usePT:function(t,r,o,n){var i=function(b){return r(b,o,n)};if(t!=null&&t.hasOwnProperty("_usept")){var s,a=t._usept||((s=this.$primevueConfig)===null||s===void 0?void 0:s.ptOptions)||{},l=a.mergeSections,u=l===void 0?!0:l,c=a.mergeProps,d=c===void 0?!1:c,f=i(t.originalValue),p=i(t.value);return f===void 0&&p===void 0?void 0:Ge(p)?p:Ge(f)?f:u||!u&&p?d?this._mergeProps(d,f,p):te(te({},f),p):p}return i(t)},_useGlobalPT:function(t,r,o){return this._usePT(this.globalPT,t,r,o)},_useDefaultPT:function(t,r,o){return this._usePT(this.defaultPT,t,r,o)},ptm:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this._getPTValue(this.pt,t,te(te({},this.$params),r))},ptmi:function(){var t,r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=he(this.$_attrsWithoutPT,this.ptm(r,o));return n!=null&&n.hasOwnProperty("id")&&((t=n.id)!==null&&t!==void 0||(n.id=this.$id)),n},ptmo:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this._getPTValue(t,r,te({instance:this},o),!1)},cx:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this.isUnstyled?void 0:this._getOptionValue(this.$style.classes,t,te(te({},this.$params),r))},sx:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(r){var n=this._getOptionValue(this.$style.inlineStyles,t,te(te({},this.$params),o)),i=this._getOptionValue(Pl.inlineStyles,t,te(te({},this.$params),o));return[i,n]}}},computed:{globalPT:function(){var t,r=this;return this._getPT((t=this.$primevueConfig)===null||t===void 0?void 0:t.pt,void 0,function(o){return st(o,{instance:r})})},defaultPT:function(){var t,r=this;return this._getPT((t=this.$primevueConfig)===null||t===void 0?void 0:t.pt,void 0,function(o){return r._getOptionValue(o,r.$name,te({},r.$params))||st(o,te({},r.$params))})},isUnstyled:function(){var t;return this.unstyled!==void 0?this.unstyled:(t=this.$primevueConfig)===null||t===void 0?void 0:t.unstyled},$id:function(){return this.$attrs.id||this.uid},$inProps:function(){var t,r=Object.keys(((t=this.$.vnode)===null||t===void 0?void 0:t.props)||{});return Object.fromEntries(Object.entries(this.$props).filter(function(o){var n=Wr(o,1),i=n[0];return r==null?void 0:r.includes(i)}))},$theme:function(){var t;return(t=this.$primevueConfig)===null||t===void 0?void 0:t.theme},$style:function(){return te(te({classes:void 0,inlineStyles:void 0,load:function(){},loadCSS:function(){},loadStyle:function(){}},(this._getHostInstance(this)||{}).$style),this.$options.style)},$styleOptions:function(){var t;return{nonce:(t=this.$primevueConfig)===null||t===void 0||(t=t.csp)===null||t===void 0?void 0:t.nonce}},$primevueConfig:function(){var t;return(t=this.$primevue)===null||t===void 0?void 0:t.config},$name:function(){return this.$options.hostName||this.$.type.name},$params:function(){var t=this._getHostInstance(this)||this.$parent;return{instance:this,props:this.$props,state:this.$data,attrs:this.$attrs,parent:{instance:t,props:t==null?void 0:t.$props,state:t==null?void 0:t.$data,attrs:t==null?void 0:t.$attrs}}},$_attrsPT:function(){return Object.entries(this.$attrs||{}).filter(function(t){var r=Wr(t,1),o=r[0];return o==null?void 0:o.startsWith("pt:")}).reduce(function(t,r){var o=Wr(r,2),n=o[0],i=o[1],s=n.split(":"),a=Db(s),l=a.slice(1);return l==null||l.reduce(function(u,c,d,f){return!u[c]&&(u[c]=d===f.length-1?i:{}),u[c]},t),t},{})},$_attrsWithoutPT:function(){return Object.entries(this.$attrs||{}).filter(function(t){var r=Wr(t,1),o=r[0];return!(o!=null&&o.startsWith("pt:"))}).reduce(function(t,r){var o=Wr(r,2),n=o[0],i=o[1];return t[n]=i,t},{})}}},zb=({dt:e})=>`
.p-divider-horizontal {
    display: flex;
    width: 100%;
    position: relative;
    align-items: center;
    margin: ${e("divider.horizontal.margin")};
    padding: ${e("divider.horizontal.padding")};
}

.p-divider-horizontal:before {
    position: absolute;
    display: block;
    inset-block-start: 50%;
    inset-inline-start: 0;
    width: 100%;
    content: "";
    border-block-start: 1px solid ${e("divider.border.color")};
}

.p-divider-horizontal .p-divider-content {
    padding: ${e("divider.horizontal.content.padding")};
}

.p-divider-vertical {
    min-height: 100%;
    display: flex;
    position: relative;
    justify-content: center;
    margin: ${e("divider.vertical.margin")};
    padding: ${e("divider.vertical.padding")};
}

.p-divider-vertical:before {
    position: absolute;
    display: block;
    inset-block-start: 0;
    inset-inline-start: 50%;
    height: 100%;
    content: "";
    border-inline-start: 1px solid ${e("divider.border.color")};
}

.p-divider.p-divider-vertical .p-divider-content {
    padding: ${e("divider.vertical.content.padding")};
}

.p-divider-content {
    z-index: 1;
    background: ${e("divider.content.background")};
    color: ${e("divider.content.color")};
}

.p-divider-solid.p-divider-horizontal:before {
    border-block-start-style: solid;
}

.p-divider-solid.p-divider-vertical:before {
    border-inline-start-style: solid;
}

.p-divider-dashed.p-divider-horizontal:before {
    border-block-start-style: dashed;
}

.p-divider-dashed.p-divider-vertical:before {
    border-inline-start-style: dashed;
}

.p-divider-dotted.p-divider-horizontal:before {
    border-block-start-style: dotted;
}

.p-divider-dotted.p-divider-vertical:before {
    border-inline-start-style: dotted;
}

.p-divider-left:dir(rtl),
.p-divider-right:dir(rtl) {
    flex-direction: row-reverse;
}
`,Hb={root:function(t){var r=t.props;return{justifyContent:r.layout==="horizontal"?r.align==="center"||r.align===null?"center":r.align==="left"?"flex-start":r.align==="right"?"flex-end":null:null,alignItems:r.layout==="vertical"?r.align==="center"||r.align===null?"center":r.align==="top"?"flex-start":r.align==="bottom"?"flex-end":null:null}}},Ub={root:function(t){var r=t.props;return["p-divider p-component","p-divider-"+r.layout,"p-divider-"+r.type,{"p-divider-left":r.layout==="horizontal"&&(!r.align||r.align==="left")},{"p-divider-center":r.layout==="horizontal"&&r.align==="center"},{"p-divider-right":r.layout==="horizontal"&&r.align==="right"},{"p-divider-top":r.layout==="vertical"&&r.align==="top"},{"p-divider-center":r.layout==="vertical"&&(!r.align||r.align==="center")},{"p-divider-bottom":r.layout==="vertical"&&r.align==="bottom"}]},content:"p-divider-content"},Vb=ye.extend({name:"divider",style:zb,classes:Ub,inlineStyles:Hb}),Wb={name:"BaseDivider",extends:Uo,props:{align:{type:String,default:null},layout:{type:String,default:"horizontal"},type:{type:String,default:"solid"}},style:Vb,provide:function(){return{$pcDivider:this,$parentInstance:this}}};function xo(e){"@babel/helpers - typeof";return xo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xo(e)}function Ri(e,t,r){return(t=Kb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Kb(e){var t=qb(e,"string");return xo(t)=="symbol"?t:t+""}function qb(e,t){if(xo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(xo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var af={name:"Divider",extends:Wb,inheritAttrs:!1,computed:{dataP:function(){return Ho(Ri(Ri(Ri({},this.align,this.align),this.layout,this.layout),this.type,this.type))}}},Gb=["aria-orientation","data-p"],Jb=["data-p"];function Yb(e,t,r,o,n,i){return se(),Te("div",he({class:e.cx("root"),style:e.sx("root"),role:"separator","aria-orientation":e.layout,"data-p":i.dataP},e.ptmi("root")),[e.$slots.default?(se(),Te("div",he({key:0,class:e.cx("content"),"data-p":i.dataP},e.ptm("content")),[_n(e.$slots,"default")],16,Jb)):mo("",!0)],16,Gb)}af.render=Yb;var Xb=({dt:e})=>`
.p-avatar {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: ${e("avatar.width")};
    height: ${e("avatar.height")};
    font-size: ${e("avatar.font.size")};
    background: ${e("avatar.background")};
    color: ${e("avatar.color")};
    border-radius: ${e("avatar.border.radius")};
}

.p-avatar-image {
    background: transparent;
}

.p-avatar-circle {
    border-radius: 50%;
}

.p-avatar-circle img {
    border-radius: 50%;
}

.p-avatar-icon {
    font-size: ${e("avatar.icon.size")};
    width: ${e("avatar.icon.size")};
    height: ${e("avatar.icon.size")};
}

.p-avatar img {
    width: 100%;
    height: 100%;
}

.p-avatar-lg {
    width: ${e("avatar.lg.width")};
    height: ${e("avatar.lg.width")};
    font-size: ${e("avatar.lg.font.size")};
}

.p-avatar-lg .p-avatar-icon {
    font-size: ${e("avatar.lg.icon.size")};
    width: ${e("avatar.lg.icon.size")};
    height: ${e("avatar.lg.icon.size")};
}

.p-avatar-xl {
    width: ${e("avatar.xl.width")};
    height: ${e("avatar.xl.width")};
    font-size: ${e("avatar.xl.font.size")};
}

.p-avatar-xl .p-avatar-icon {
    font-size: ${e("avatar.xl.icon.size")};
    width: ${e("avatar.xl.icon.size")};
    height: ${e("avatar.xl.icon.size")};
}

.p-avatar-group {
    display: flex;
    align-items: center;
}

.p-avatar-group .p-avatar + .p-avatar {
    margin-inline-start: ${e("avatar.group.offset")};
}

.p-avatar-group .p-avatar {
    border: 2px solid ${e("avatar.group.border.color")};
}

.p-avatar-group .p-avatar-lg + .p-avatar-lg {
    margin-inline-start: ${e("avatar.lg.group.offset")};
}

.p-avatar-group .p-avatar-xl + .p-avatar-xl {
    margin-inline-start: ${e("avatar.xl.group.offset")};
}
`,Zb={root:function(t){var r=t.props;return["p-avatar p-component",{"p-avatar-image":r.image!=null,"p-avatar-circle":r.shape==="circle","p-avatar-lg":r.size==="large","p-avatar-xl":r.size==="xlarge"}]},label:"p-avatar-label",icon:"p-avatar-icon"},Qb=ye.extend({name:"avatar",style:Xb,classes:Zb}),ev={name:"BaseAvatar",extends:Uo,props:{label:{type:String,default:null},icon:{type:String,default:null},image:{type:String,default:null},size:{type:String,default:"normal"},shape:{type:String,default:"square"},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:Qb,provide:function(){return{$pcAvatar:this,$parentInstance:this}}};function Ro(e){"@babel/helpers - typeof";return Ro=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ro(e)}function Bl(e,t,r){return(t=tv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tv(e){var t=rv(e,"string");return Ro(t)=="symbol"?t:t+""}function rv(e,t){if(Ro(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(Ro(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var lf={name:"Avatar",extends:ev,inheritAttrs:!1,emits:["error"],methods:{onError:function(t){this.$emit("error",t)}},computed:{dataP:function(){return Ho(Bl(Bl({},this.shape,this.shape),this.size,this.size))}}},ov=["aria-labelledby","aria-label","data-p"],nv=["data-p"],iv=["data-p"],sv=["src","alt","data-p"];function av(e,t,r,o,n,i){return se(),Te("div",he({class:e.cx("root"),"aria-labelledby":e.ariaLabelledby,"aria-label":e.ariaLabel},e.ptmi("root"),{"data-p":i.dataP}),[_n(e.$slots,"default",{},function(){return[e.label?(se(),Te("span",he({key:0,class:e.cx("label")},e.ptm("label"),{"data-p":i.dataP}),lo(e.label),17,nv)):e.$slots.icon?(se(),it(Kr(e.$slots.icon),{key:1,class:Ir(e.cx("icon"))},null,8,["class"])):e.icon?(se(),Te("span",he({key:2,class:[e.cx("icon"),e.icon]},e.ptm("icon"),{"data-p":i.dataP}),null,16,iv)):e.image?(se(),Te("img",he({key:3,src:e.image,alt:e.ariaLabel,onError:t[0]||(t[0]=function(){return i.onError&&i.onError.apply(i,arguments)})},e.ptm("image"),{"data-p":i.dataP}),null,16,sv)):mo("",!0)]})],16,ov)}lf.render=av;var cf={name:"Portal",props:{appendTo:{type:[String,Object],default:"body"},disabled:{type:Boolean,default:!1}},data:function(){return{mounted:!1}},mounted:function(){this.mounted=Jd()},computed:{inline:function(){return this.disabled||this.appendTo==="self"}}};function lv(e,t,r,o,n,i){return i.inline?_n(e.$slots,"default",{key:0}):n.mounted?(se(),it(Sp,{key:1,to:r.appendTo},[_n(e.$slots,"default")],8,["to"])):mo("",!0)}cf.render=lv;var ot=Hs(),cv=({dt:e})=>`
.p-toast {
    width: ${e("toast.width")};
    white-space: pre-line;
    word-break: break-word;
}

.p-toast-message {
    margin: 0 0 1rem 0;
}

.p-toast-message-icon {
    flex-shrink: 0;
    font-size: ${e("toast.icon.size")};
    width: ${e("toast.icon.size")};
    height: ${e("toast.icon.size")};
}

.p-toast-message-content {
    display: flex;
    align-items: flex-start;
    padding: ${e("toast.content.padding")};
    gap: ${e("toast.content.gap")};
}

.p-toast-message-text {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    gap: ${e("toast.text.gap")};
}

.p-toast-summary {
    font-weight: ${e("toast.summary.font.weight")};
    font-size: ${e("toast.summary.font.size")};
}

.p-toast-detail {
    font-weight: ${e("toast.detail.font.weight")};
    font-size: ${e("toast.detail.font.size")};
}

.p-toast-close-button {
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    background: transparent;
    transition: background ${e("toast.transition.duration")}, color ${e("toast.transition.duration")}, outline-color ${e("toast.transition.duration")}, box-shadow ${e("toast.transition.duration")};
    outline-color: transparent;
    color: inherit;
    width: ${e("toast.close.button.width")};
    height: ${e("toast.close.button.height")};
    border-radius: ${e("toast.close.button.border.radius")};
    margin: -25% 0 0 0;
    right: -25%;
    padding: 0;
    border: none;
    user-select: none;
}

.p-toast-close-button:dir(rtl) {
    margin: -25% 0 0 auto;
    left: -25%;
    right: auto;
}

.p-toast-message-info,
.p-toast-message-success,
.p-toast-message-warn,
.p-toast-message-error,
.p-toast-message-secondary,
.p-toast-message-contrast {
    border-width: ${e("toast.border.width")};
    border-style: solid;
    backdrop-filter: blur(${e("toast.blur")});
    border-radius: ${e("toast.border.radius")};
}

.p-toast-close-icon {
    font-size: ${e("toast.close.icon.size")};
    width: ${e("toast.close.icon.size")};
    height: ${e("toast.close.icon.size")};
}

.p-toast-close-button:focus-visible {
    outline-width: ${e("focus.ring.width")};
    outline-style: ${e("focus.ring.style")};
    outline-offset: ${e("focus.ring.offset")};
}

.p-toast-message-info {
    background: ${e("toast.info.background")};
    border-color: ${e("toast.info.border.color")};
    color: ${e("toast.info.color")};
    box-shadow: ${e("toast.info.shadow")};
}

.p-toast-message-info .p-toast-detail {
    color: ${e("toast.info.detail.color")};
}

.p-toast-message-info .p-toast-close-button:focus-visible {
    outline-color: ${e("toast.info.close.button.focus.ring.color")};
    box-shadow: ${e("toast.info.close.button.focus.ring.shadow")};
}

.p-toast-message-info .p-toast-close-button:hover {
    background: ${e("toast.info.close.button.hover.background")};
}

.p-toast-message-success {
    background: ${e("toast.success.background")};
    border-color: ${e("toast.success.border.color")};
    color: ${e("toast.success.color")};
    box-shadow: ${e("toast.success.shadow")};
}

.p-toast-message-success .p-toast-detail {
    color: ${e("toast.success.detail.color")};
}

.p-toast-message-success .p-toast-close-button:focus-visible {
    outline-color: ${e("toast.success.close.button.focus.ring.color")};
    box-shadow: ${e("toast.success.close.button.focus.ring.shadow")};
}

.p-toast-message-success .p-toast-close-button:hover {
    background: ${e("toast.success.close.button.hover.background")};
}

.p-toast-message-warn {
    background: ${e("toast.warn.background")};
    border-color: ${e("toast.warn.border.color")};
    color: ${e("toast.warn.color")};
    box-shadow: ${e("toast.warn.shadow")};
}

.p-toast-message-warn .p-toast-detail {
    color: ${e("toast.warn.detail.color")};
}

.p-toast-message-warn .p-toast-close-button:focus-visible {
    outline-color: ${e("toast.warn.close.button.focus.ring.color")};
    box-shadow: ${e("toast.warn.close.button.focus.ring.shadow")};
}

.p-toast-message-warn .p-toast-close-button:hover {
    background: ${e("toast.warn.close.button.hover.background")};
}

.p-toast-message-error {
    background: ${e("toast.error.background")};
    border-color: ${e("toast.error.border.color")};
    color: ${e("toast.error.color")};
    box-shadow: ${e("toast.error.shadow")};
}

.p-toast-message-error .p-toast-detail {
    color: ${e("toast.error.detail.color")};
}

.p-toast-message-error .p-toast-close-button:focus-visible {
    outline-color: ${e("toast.error.close.button.focus.ring.color")};
    box-shadow: ${e("toast.error.close.button.focus.ring.shadow")};
}

.p-toast-message-error .p-toast-close-button:hover {
    background: ${e("toast.error.close.button.hover.background")};
}

.p-toast-message-secondary {
    background: ${e("toast.secondary.background")};
    border-color: ${e("toast.secondary.border.color")};
    color: ${e("toast.secondary.color")};
    box-shadow: ${e("toast.secondary.shadow")};
}

.p-toast-message-secondary .p-toast-detail {
    color: ${e("toast.secondary.detail.color")};
}

.p-toast-message-secondary .p-toast-close-button:focus-visible {
    outline-color: ${e("toast.secondary.close.button.focus.ring.color")};
    box-shadow: ${e("toast.secondary.close.button.focus.ring.shadow")};
}

.p-toast-message-secondary .p-toast-close-button:hover {
    background: ${e("toast.secondary.close.button.hover.background")};
}

.p-toast-message-contrast {
    background: ${e("toast.contrast.background")};
    border-color: ${e("toast.contrast.border.color")};
    color: ${e("toast.contrast.color")};
    box-shadow: ${e("toast.contrast.shadow")};
}

.p-toast-message-contrast .p-toast-detail {
    color: ${e("toast.contrast.detail.color")};
}

.p-toast-message-contrast .p-toast-close-button:focus-visible {
    outline-color: ${e("toast.contrast.close.button.focus.ring.color")};
    box-shadow: ${e("toast.contrast.close.button.focus.ring.shadow")};
}

.p-toast-message-contrast .p-toast-close-button:hover {
    background: ${e("toast.contrast.close.button.hover.background")};
}

.p-toast-top-center {
    transform: translateX(-50%);
}

.p-toast-bottom-center {
    transform: translateX(-50%);
}

.p-toast-center {
    min-width: 20vw;
    transform: translate(-50%, -50%);
}

.p-toast-message-enter-from {
    opacity: 0;
    transform: translateY(50%);
}

.p-toast-message-leave-from {
    max-height: 1000px;
}

.p-toast .p-toast-message.p-toast-message-leave-to {
    max-height: 0;
    opacity: 0;
    margin-bottom: 0;
    overflow: hidden;
}

.p-toast-message-enter-active {
    transition: transform 0.3s, opacity 0.3s;
}

.p-toast-message-leave-active {
    transition: max-height 0.45s cubic-bezier(0, 1, 0, 1), opacity 0.3s, margin-bottom 0.3s;
}
`;function wo(e){"@babel/helpers - typeof";return wo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},wo(e)}function Xo(e,t,r){return(t=uv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function uv(e){var t=dv(e,"string");return wo(t)=="symbol"?t:t+""}function dv(e,t){if(wo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(wo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var fv={root:function(t){var r=t.position;return{position:"fixed",top:r==="top-right"||r==="top-left"||r==="top-center"?"20px":r==="center"?"50%":null,right:(r==="top-right"||r==="bottom-right")&&"20px",bottom:(r==="bottom-left"||r==="bottom-right"||r==="bottom-center")&&"20px",left:r==="top-left"||r==="bottom-left"?"20px":r==="center"||r==="top-center"||r==="bottom-center"?"50%":null}}},pv={root:function(t){var r=t.props;return["p-toast p-component p-toast-"+r.position]},message:function(t){var r=t.props;return["p-toast-message",{"p-toast-message-info":r.message.severity==="info"||r.message.severity===void 0,"p-toast-message-warn":r.message.severity==="warn","p-toast-message-error":r.message.severity==="error","p-toast-message-success":r.message.severity==="success","p-toast-message-secondary":r.message.severity==="secondary","p-toast-message-contrast":r.message.severity==="contrast"}]},messageContent:"p-toast-message-content",messageIcon:function(t){var r=t.props;return["p-toast-message-icon",Xo(Xo(Xo(Xo({},r.infoIcon,r.message.severity==="info"),r.warnIcon,r.message.severity==="warn"),r.errorIcon,r.message.severity==="error"),r.successIcon,r.message.severity==="success")]},messageText:"p-toast-message-text",summary:"p-toast-summary",detail:"p-toast-detail",closeButton:"p-toast-close-button",closeIcon:"p-toast-close-icon"},gv=ye.extend({name:"toast",style:cv,classes:pv,inlineStyles:fv}),hv=`
.p-icon {
    display: inline-block;
    vertical-align: baseline;
}

.p-icon-spin {
    -webkit-animation: p-icon-spin 2s infinite linear;
    animation: p-icon-spin 2s infinite linear;
}

@-webkit-keyframes p-icon-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}

@keyframes p-icon-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}
`,mv=ye.extend({name:"baseicon",css:hv});function To(e){"@babel/helpers - typeof";return To=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},To(e)}function Al(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function Ll(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Al(Object(r),!0).forEach(function(o){bv(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Al(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function bv(e,t,r){return(t=vv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vv(e){var t=yv(e,"string");return To(t)=="symbol"?t:t+""}function yv(e,t){if(To(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(To(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Vo={name:"BaseIcon",extends:Uo,props:{label:{type:String,default:void 0},spin:{type:Boolean,default:!1}},style:mv,provide:function(){return{$pcIcon:this,$parentInstance:this}},methods:{pti:function(){var t=tr(this.label);return Ll(Ll({},!this.isUnstyled&&{class:["p-icon",{"p-icon-spin":this.spin}]}),{},{role:t?void 0:"img","aria-label":t?void 0:this.label,"aria-hidden":t})}}},as={name:"CheckIcon",extends:Vo};function Cv(e,t,r,o,n,i){return se(),Te("svg",he({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.pti()),t[0]||(t[0]=[ge("path",{d:"M4.86199 11.5948C4.78717 11.5923 4.71366 11.5745 4.64596 11.5426C4.57826 11.5107 4.51779 11.4652 4.46827 11.4091L0.753985 7.69483C0.683167 7.64891 0.623706 7.58751 0.580092 7.51525C0.536478 7.44299 0.509851 7.36177 0.502221 7.27771C0.49459 7.19366 0.506156 7.10897 0.536046 7.03004C0.565935 6.95111 0.613367 6.88 0.674759 6.82208C0.736151 6.76416 0.8099 6.72095 0.890436 6.69571C0.970973 6.67046 1.05619 6.66385 1.13966 6.67635C1.22313 6.68886 1.30266 6.72017 1.37226 6.76792C1.44186 6.81567 1.4997 6.8786 1.54141 6.95197L4.86199 10.2503L12.6397 2.49483C12.7444 2.42694 12.8689 2.39617 12.9932 2.40745C13.1174 2.41873 13.2343 2.47141 13.3251 2.55705C13.4159 2.64268 13.4753 2.75632 13.4938 2.87973C13.5123 3.00315 13.4888 3.1292 13.4271 3.23768L5.2557 11.4091C5.20618 11.4652 5.14571 11.5107 5.07801 11.5426C5.01031 11.5745 4.9368 11.5923 4.86199 11.5948Z",fill:"currentColor"},null,-1)]),16)}as.render=Cv;var ls={name:"ExclamationTriangleIcon",extends:Vo};function Sv(e,t,r,o,n,i){return se(),Te("svg",he({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.pti()),t[0]||(t[0]=[ge("path",{d:"M13.4018 13.1893H0.598161C0.49329 13.189 0.390283 13.1615 0.299143 13.1097C0.208003 13.0578 0.131826 12.9832 0.0780112 12.8932C0.0268539 12.8015 0 12.6982 0 12.5931C0 12.4881 0.0268539 12.3848 0.0780112 12.293L6.47985 1.08982C6.53679 1.00399 6.61408 0.933574 6.70484 0.884867C6.7956 0.836159 6.897 0.810669 7 0.810669C7.103 0.810669 7.2044 0.836159 7.29516 0.884867C7.38592 0.933574 7.46321 1.00399 7.52015 1.08982L13.922 12.293C13.9731 12.3848 14 12.4881 14 12.5931C14 12.6982 13.9731 12.8015 13.922 12.8932C13.8682 12.9832 13.792 13.0578 13.7009 13.1097C13.6097 13.1615 13.5067 13.189 13.4018 13.1893ZM1.63046 11.989H12.3695L7 2.59425L1.63046 11.989Z",fill:"currentColor"},null,-1),ge("path",{d:"M6.99996 8.78801C6.84143 8.78594 6.68997 8.72204 6.57787 8.60993C6.46576 8.49782 6.40186 8.34637 6.39979 8.18784V5.38703C6.39979 5.22786 6.46302 5.0752 6.57557 4.96265C6.68813 4.85009 6.84078 4.78686 6.99996 4.78686C7.15914 4.78686 7.31179 4.85009 7.42435 4.96265C7.5369 5.0752 7.60013 5.22786 7.60013 5.38703V8.18784C7.59806 8.34637 7.53416 8.49782 7.42205 8.60993C7.30995 8.72204 7.15849 8.78594 6.99996 8.78801Z",fill:"currentColor"},null,-1),ge("path",{d:"M6.99996 11.1887C6.84143 11.1866 6.68997 11.1227 6.57787 11.0106C6.46576 10.8985 6.40186 10.7471 6.39979 10.5885V10.1884C6.39979 10.0292 6.46302 9.87658 6.57557 9.76403C6.68813 9.65147 6.84078 9.58824 6.99996 9.58824C7.15914 9.58824 7.31179 9.65147 7.42435 9.76403C7.5369 9.87658 7.60013 10.0292 7.60013 10.1884V10.5885C7.59806 10.7471 7.53416 10.8985 7.42205 11.0106C7.30995 11.1227 7.15849 11.1866 6.99996 11.1887Z",fill:"currentColor"},null,-1)]),16)}ls.render=Sv;var cs={name:"InfoCircleIcon",extends:Vo};function kv(e,t,r,o,n,i){return se(),Te("svg",he({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.pti()),t[0]||(t[0]=[ge("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M3.11101 12.8203C4.26215 13.5895 5.61553 14 7 14C8.85652 14 10.637 13.2625 11.9497 11.9497C13.2625 10.637 14 8.85652 14 7C14 5.61553 13.5895 4.26215 12.8203 3.11101C12.0511 1.95987 10.9579 1.06266 9.67879 0.532846C8.3997 0.00303296 6.99224 -0.13559 5.63437 0.134506C4.2765 0.404603 3.02922 1.07129 2.05026 2.05026C1.07129 3.02922 0.404603 4.2765 0.134506 5.63437C-0.13559 6.99224 0.00303296 8.3997 0.532846 9.67879C1.06266 10.9579 1.95987 12.0511 3.11101 12.8203ZM3.75918 2.14976C4.71846 1.50879 5.84628 1.16667 7 1.16667C8.5471 1.16667 10.0308 1.78125 11.1248 2.87521C12.2188 3.96918 12.8333 5.45291 12.8333 7C12.8333 8.15373 12.4912 9.28154 11.8502 10.2408C11.2093 11.2001 10.2982 11.9478 9.23232 12.3893C8.16642 12.8308 6.99353 12.9463 5.86198 12.7212C4.73042 12.4962 3.69102 11.9406 2.87521 11.1248C2.05941 10.309 1.50384 9.26958 1.27876 8.13803C1.05367 7.00647 1.16919 5.83358 1.61071 4.76768C2.05222 3.70178 2.79989 2.79074 3.75918 2.14976ZM7.00002 4.8611C6.84594 4.85908 6.69873 4.79698 6.58977 4.68801C6.48081 4.57905 6.4187 4.43185 6.41669 4.27776V3.88888C6.41669 3.73417 6.47815 3.58579 6.58754 3.4764C6.69694 3.367 6.84531 3.30554 7.00002 3.30554C7.15473 3.30554 7.3031 3.367 7.4125 3.4764C7.52189 3.58579 7.58335 3.73417 7.58335 3.88888V4.27776C7.58134 4.43185 7.51923 4.57905 7.41027 4.68801C7.30131 4.79698 7.1541 4.85908 7.00002 4.8611ZM7.00002 10.6945C6.84594 10.6925 6.69873 10.6304 6.58977 10.5214C6.48081 10.4124 6.4187 10.2652 6.41669 10.1111V6.22225C6.41669 6.06754 6.47815 5.91917 6.58754 5.80977C6.69694 5.70037 6.84531 5.63892 7.00002 5.63892C7.15473 5.63892 7.3031 5.70037 7.4125 5.80977C7.52189 5.91917 7.58335 6.06754 7.58335 6.22225V10.1111C7.58134 10.2652 7.51923 10.4124 7.41027 10.5214C7.30131 10.6304 7.1541 10.6925 7.00002 10.6945Z",fill:"currentColor"},null,-1)]),16)}cs.render=kv;var uf={name:"TimesIcon",extends:Vo};function _v(e,t,r,o,n,i){return se(),Te("svg",he({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.pti()),t[0]||(t[0]=[ge("path",{d:"M8.01186 7.00933L12.27 2.75116C12.341 2.68501 12.398 2.60524 12.4375 2.51661C12.4769 2.42798 12.4982 2.3323 12.4999 2.23529C12.5016 2.13827 12.4838 2.0419 12.4474 1.95194C12.4111 1.86197 12.357 1.78024 12.2884 1.71163C12.2198 1.64302 12.138 1.58893 12.0481 1.55259C11.9581 1.51625 11.8617 1.4984 11.7647 1.50011C11.6677 1.50182 11.572 1.52306 11.4834 1.56255C11.3948 1.60204 11.315 1.65898 11.2488 1.72997L6.99067 5.98814L2.7325 1.72997C2.59553 1.60234 2.41437 1.53286 2.22718 1.53616C2.03999 1.53946 1.8614 1.61529 1.72901 1.74767C1.59663 1.88006 1.5208 2.05865 1.5175 2.24584C1.5142 2.43303 1.58368 2.61419 1.71131 2.75116L5.96948 7.00933L1.71131 11.2675C1.576 11.403 1.5 11.5866 1.5 11.7781C1.5 11.9696 1.576 12.1532 1.71131 12.2887C1.84679 12.424 2.03043 12.5 2.2219 12.5C2.41338 12.5 2.59702 12.424 2.7325 12.2887L6.99067 8.03052L11.2488 12.2887C11.3843 12.424 11.568 12.5 11.7594 12.5C11.9509 12.5 12.1346 12.424 12.27 12.2887C12.4053 12.1532 12.4813 11.9696 12.4813 11.7781C12.4813 11.5866 12.4053 11.403 12.27 11.2675L8.01186 7.00933Z",fill:"currentColor"},null,-1)]),16)}uf.render=_v;var us={name:"TimesCircleIcon",extends:Vo};function $v(e,t,r,o,n,i){return se(),Te("svg",he({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.pti()),t[0]||(t[0]=[ge("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7 14C5.61553 14 4.26215 13.5895 3.11101 12.8203C1.95987 12.0511 1.06266 10.9579 0.532846 9.67879C0.00303296 8.3997 -0.13559 6.99224 0.134506 5.63437C0.404603 4.2765 1.07129 3.02922 2.05026 2.05026C3.02922 1.07129 4.2765 0.404603 5.63437 0.134506C6.99224 -0.13559 8.3997 0.00303296 9.67879 0.532846C10.9579 1.06266 12.0511 1.95987 12.8203 3.11101C13.5895 4.26215 14 5.61553 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM7 1.16667C5.84628 1.16667 4.71846 1.50879 3.75918 2.14976C2.79989 2.79074 2.05222 3.70178 1.61071 4.76768C1.16919 5.83358 1.05367 7.00647 1.27876 8.13803C1.50384 9.26958 2.05941 10.309 2.87521 11.1248C3.69102 11.9406 4.73042 12.4962 5.86198 12.7212C6.99353 12.9463 8.16642 12.8308 9.23232 12.3893C10.2982 11.9478 11.2093 11.2001 11.8502 10.2408C12.4912 9.28154 12.8333 8.15373 12.8333 7C12.8333 5.45291 12.2188 3.96918 11.1248 2.87521C10.0308 1.78125 8.5471 1.16667 7 1.16667ZM4.66662 9.91668C4.58998 9.91704 4.51404 9.90209 4.44325 9.87271C4.37246 9.84333 4.30826 9.8001 4.2544 9.74557C4.14516 9.6362 4.0838 9.48793 4.0838 9.33335C4.0838 9.17876 4.14516 9.0305 4.2544 8.92113L6.17553 7L4.25443 5.07891C4.15139 4.96832 4.09529 4.82207 4.09796 4.67094C4.10063 4.51982 4.16185 4.37563 4.26872 4.26876C4.3756 4.16188 4.51979 4.10066 4.67091 4.09799C4.82204 4.09532 4.96829 4.15142 5.07887 4.25446L6.99997 6.17556L8.92106 4.25446C9.03164 4.15142 9.1779 4.09532 9.32903 4.09799C9.48015 4.10066 9.62434 4.16188 9.73121 4.26876C9.83809 4.37563 9.89931 4.51982 9.90198 4.67094C9.90464 4.82207 9.84855 4.96832 9.74551 5.07891L7.82441 7L9.74554 8.92113C9.85478 9.0305 9.91614 9.17876 9.91614 9.33335C9.91614 9.48793 9.85478 9.6362 9.74554 9.74557C9.69168 9.8001 9.62748 9.84333 9.55669 9.87271C9.4859 9.90209 9.40996 9.91704 9.33332 9.91668C9.25668 9.91704 9.18073 9.90209 9.10995 9.87271C9.03916 9.84333 8.97495 9.8001 8.9211 9.74557L6.99997 7.82444L5.07884 9.74557C5.02499 9.8001 4.96078 9.84333 4.88999 9.87271C4.81921 9.90209 4.74326 9.91704 4.66662 9.91668Z",fill:"currentColor"},null,-1)]),16)}us.render=$v;var Yt=Hs();function Po(e){"@babel/helpers - typeof";return Po=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Po(e)}function Il(e,t){return Tv(e)||wv(e,t)||Rv(e,t)||xv()}function xv(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Rv(e,t){if(e){if(typeof e=="string")return Dl(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Dl(e,t):void 0}}function Dl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function wv(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var o,n,i,s,a=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(l=(o=i.call(r)).done)&&(a.push(o.value),a.length!==t);l=!0);}catch(c){u=!0,n=c}finally{try{if(!l&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return a}}function Tv(e){if(Array.isArray(e))return e}function jl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function ie(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?jl(Object(r),!0).forEach(function(o){ds(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):jl(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function ds(e,t,r){return(t=Pv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Pv(e){var t=Ev(e,"string");return Po(t)=="symbol"?t:t+""}function Ev(e,t){if(Po(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(Po(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Q={_getMeta:function(){return[Pt(arguments.length<=0?void 0:arguments[0])||arguments.length<=0?void 0:arguments[0],st(Pt(arguments.length<=0?void 0:arguments[0])?arguments.length<=0?void 0:arguments[0]:arguments.length<=1?void 0:arguments[1])]},_getConfig:function(t,r){var o,n,i;return(o=(t==null||(n=t.instance)===null||n===void 0?void 0:n.$primevue)||(r==null||(i=r.ctx)===null||i===void 0||(i=i.appContext)===null||i===void 0||(i=i.config)===null||i===void 0||(i=i.globalProperties)===null||i===void 0?void 0:i.$primevue))===null||o===void 0?void 0:o.config},_getOptionValue:Us,_getPTValue:function(){var t,r,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"",s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,l=function(){var _=Q._getOptionValue.apply(Q,arguments);return Ge(_)||ni(_)?{class:_}:_},u=((t=o.binding)===null||t===void 0||(t=t.value)===null||t===void 0?void 0:t.ptOptions)||((r=o.$primevueConfig)===null||r===void 0?void 0:r.ptOptions)||{},c=u.mergeSections,d=c===void 0?!0:c,f=u.mergeProps,p=f===void 0?!1:f,h=a?Q._useDefaultPT(o,o.defaultPT(),l,i,s):void 0,b=Q._usePT(o,Q._getPT(n,o.$name),l,i,ie(ie({},s),{},{global:h||{}})),C=Q._getPTDatasets(o,i);return d||!d&&b?p?Q._mergeProps(o,p,h,b,C):ie(ie(ie({},h),b),C):ie(ie({},b),C)},_getPTDatasets:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o="data-pc-";return ie(ie({},r==="root"&&ds({},"".concat(o,"name"),xt(t.$name))),{},ds({},"".concat(o,"section"),xt(r)))},_getPT:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o=arguments.length>2?arguments[2]:void 0,n=function(s){var a,l=o?o(s):s,u=xt(r);return(a=l==null?void 0:l[u])!==null&&a!==void 0?a:l};return t&&Object.hasOwn(t,"_usept")?{_usept:t._usept,originalValue:n(t.originalValue),value:n(t.value)}:n(t)},_usePT:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0,o=arguments.length>2?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0,s=function(C){return o(C,n,i)};if(r&&Object.hasOwn(r,"_usept")){var a,l=r._usept||((a=t.$primevueConfig)===null||a===void 0?void 0:a.ptOptions)||{},u=l.mergeSections,c=u===void 0?!0:u,d=l.mergeProps,f=d===void 0?!1:d,p=s(r.originalValue),h=s(r.value);return p===void 0&&h===void 0?void 0:Ge(h)?h:Ge(p)?p:c||!c&&h?f?Q._mergeProps(t,f,p,h):ie(ie({},p),h):h}return s(r)},_useDefaultPT:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=arguments.length>2?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;return Q._usePT(t,r,o,n,i)},_loadStyles:function(){var t,r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},o=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0,i=Q._getConfig(o,n),s={nonce:i==null||(t=i.csp)===null||t===void 0?void 0:t.nonce};Q._loadCoreStyles(r,s),Q._loadThemeStyles(r,s),Q._loadScopedThemeStyles(r,s),Q._removeThemeListeners(r),r.$loadStyles=function(){return Q._loadThemeStyles(r,s)},Q._themeChangeListener(r.$loadStyles)},_loadCoreStyles:function(){var t,r,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;if(!Jt.isStyleNameLoaded((t=o.$style)===null||t===void 0?void 0:t.name)&&(r=o.$style)!==null&&r!==void 0&&r.name){var i;ye.loadCSS(n),(i=o.$style)===null||i===void 0||i.loadCSS(n),Jt.setLoadedStyleName(o.$style.name)}},_loadThemeStyles:function(){var t,r,o,n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=arguments.length>1?arguments[1]:void 0;if(!(n!=null&&n.isUnstyled()||(n==null||(t=n.theme)===null||t===void 0?void 0:t.call(n))==="none")){if(!fe.isStyleNameLoaded("common")){var s,a,l=((s=n.$style)===null||s===void 0||(a=s.getCommonTheme)===null||a===void 0?void 0:a.call(s))||{},u=l.primitive,c=l.semantic,d=l.global,f=l.style;ye.load(u==null?void 0:u.css,ie({name:"primitive-variables"},i)),ye.load(c==null?void 0:c.css,ie({name:"semantic-variables"},i)),ye.load(d==null?void 0:d.css,ie({name:"global-variables"},i)),ye.loadStyle(ie({name:"global-style"},i),f),fe.setLoadedStyleName("common")}if(!fe.isStyleNameLoaded((r=n.$style)===null||r===void 0?void 0:r.name)&&(o=n.$style)!==null&&o!==void 0&&o.name){var p,h,b,C,k=((p=n.$style)===null||p===void 0||(h=p.getDirectiveTheme)===null||h===void 0?void 0:h.call(p))||{},_=k.css,w=k.style;(b=n.$style)===null||b===void 0||b.load(_,ie({name:"".concat(n.$style.name,"-variables")},i)),(C=n.$style)===null||C===void 0||C.loadStyle(ie({name:"".concat(n.$style.name,"-style")},i),w),fe.setLoadedStyleName(n.$style.name)}if(!fe.isStyleNameLoaded("layer-order")){var y,P,F=(y=n.$style)===null||y===void 0||(P=y.getLayerOrderThemeCSS)===null||P===void 0?void 0:P.call(y);ye.load(F,ie({name:"layer-order",first:!0},i)),fe.setLoadedStyleName("layer-order")}}},_loadScopedThemeStyles:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0,o=t.preset();if(o&&t.$attrSelector){var n,i,s,a=((n=t.$style)===null||n===void 0||(i=n.getPresetTheme)===null||i===void 0?void 0:i.call(n,o,"[".concat(t.$attrSelector,"]")))||{},l=a.css,u=(s=t.$style)===null||s===void 0?void 0:s.load(l,ie({name:"".concat(t.$attrSelector,"-").concat(t.$style.name)},r));t.scopedStyleEl=u.el}},_themeChangeListener:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(){};Jt.clearLoadedStyleNames(),Ae.on("theme:change",t)},_removeThemeListeners:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Ae.off("theme:change",t.$loadStyles),t.$loadStyles=void 0},_hook:function(t,r,o,n,i,s){var a,l,u="on".concat(ab(r)),c=Q._getConfig(n,i),d=o==null?void 0:o.$instance,f=Q._usePT(d,Q._getPT(n==null||(a=n.value)===null||a===void 0?void 0:a.pt,t),Q._getOptionValue,"hooks.".concat(u)),p=Q._useDefaultPT(d,c==null||(l=c.pt)===null||l===void 0||(l=l.directives)===null||l===void 0?void 0:l[t],Q._getOptionValue,"hooks.".concat(u)),h={el:o,binding:n,vnode:i,prevVnode:s};f==null||f(d,h),p==null||p(d,h)},_mergeProps:function(){for(var t=arguments.length>1?arguments[1]:void 0,r=arguments.length,o=new Array(r>2?r-2:0),n=2;n<r;n++)o[n-2]=arguments[n];return oi(t)?t.apply(void 0,o):he.apply(void 0,o)},_extend:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=function(a,l,u,c,d){var f,p,h,b;l._$instances=l._$instances||{};var C=Q._getConfig(u,c),k=l._$instances[t]||{},_=tr(k)?ie(ie({},r),r==null?void 0:r.methods):{};l._$instances[t]=ie(ie({},k),{},{$name:t,$host:l,$binding:u,$modifiers:u==null?void 0:u.modifiers,$value:u==null?void 0:u.value,$el:k.$el||l||void 0,$style:ie({classes:void 0,inlineStyles:void 0,load:function(){},loadCSS:function(){},loadStyle:function(){}},r==null?void 0:r.style),$primevueConfig:C,$attrSelector:(f=l.$pd)===null||f===void 0||(f=f[t])===null||f===void 0?void 0:f.attrSelector,defaultPT:function(){return Q._getPT(C==null?void 0:C.pt,void 0,function(y){var P;return y==null||(P=y.directives)===null||P===void 0?void 0:P[t]})},isUnstyled:function(){var y,P;return((y=l._$instances[t])===null||y===void 0||(y=y.$binding)===null||y===void 0||(y=y.value)===null||y===void 0?void 0:y.unstyled)!==void 0?(P=l._$instances[t])===null||P===void 0||(P=P.$binding)===null||P===void 0||(P=P.value)===null||P===void 0?void 0:P.unstyled:C==null?void 0:C.unstyled},theme:function(){var y;return(y=l._$instances[t])===null||y===void 0||(y=y.$primevueConfig)===null||y===void 0?void 0:y.theme},preset:function(){var y;return(y=l._$instances[t])===null||y===void 0||(y=y.$binding)===null||y===void 0||(y=y.value)===null||y===void 0?void 0:y.dt},ptm:function(){var y,P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",F=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return Q._getPTValue(l._$instances[t],(y=l._$instances[t])===null||y===void 0||(y=y.$binding)===null||y===void 0||(y=y.value)===null||y===void 0?void 0:y.pt,P,ie({},F))},ptmo:function(){var y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},P=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",F=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Q._getPTValue(l._$instances[t],y,P,F,!1)},cx:function(){var y,P,F=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return(y=l._$instances[t])!==null&&y!==void 0&&y.isUnstyled()?void 0:Q._getOptionValue((P=l._$instances[t])===null||P===void 0||(P=P.$style)===null||P===void 0?void 0:P.classes,F,ie({},z))},sx:function(){var y,P=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",F=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,z=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return F?Q._getOptionValue((y=l._$instances[t])===null||y===void 0||(y=y.$style)===null||y===void 0?void 0:y.inlineStyles,P,ie({},z)):void 0}},_),l.$instance=l._$instances[t],(p=(h=l.$instance)[a])===null||p===void 0||p.call(h,l,u,c,d),l["$".concat(t)]=l.$instance,Q._hook(t,a,l,u,c,d),l.$pd||(l.$pd={}),l.$pd[t]=ie(ie({},(b=l.$pd)===null||b===void 0?void 0:b[t]),{},{name:t,instance:l._$instances[t]})},n=function(a){var l,u,c,d=a._$instances[t],f=d==null?void 0:d.watch,p=function(C){var k,_=C.newValue,w=C.oldValue;return f==null||(k=f.config)===null||k===void 0?void 0:k.call(d,_,w)},h=function(C){var k,_=C.newValue,w=C.oldValue;return f==null||(k=f["config.ripple"])===null||k===void 0?void 0:k.call(d,_,w)};d.$watchersCallback={config:p,"config.ripple":h},f==null||(l=f.config)===null||l===void 0||l.call(d,d==null?void 0:d.$primevueConfig),Yt.on("config:change",p),f==null||(u=f["config.ripple"])===null||u===void 0||u.call(d,d==null||(c=d.$primevueConfig)===null||c===void 0?void 0:c.ripple),Yt.on("config:ripple:change",h)},i=function(a){var l=a._$instances[t].$watchersCallback;l&&(Yt.off("config:change",l.config),Yt.off("config:ripple:change",l["config.ripple"]),a._$instances[t].$watchersCallback=void 0)};return{created:function(a,l,u,c){a.$pd||(a.$pd={}),a.$pd[t]={name:t,attrSelector:Gr("pd")},o("created",a,l,u,c)},beforeMount:function(a,l,u,c){var d;Q._loadStyles((d=a.$pd[t])===null||d===void 0?void 0:d.instance,l,u),o("beforeMount",a,l,u,c),n(a)},mounted:function(a,l,u,c){var d;Q._loadStyles((d=a.$pd[t])===null||d===void 0?void 0:d.instance,l,u),o("mounted",a,l,u,c)},beforeUpdate:function(a,l,u,c){o("beforeUpdate",a,l,u,c)},updated:function(a,l,u,c){var d;Q._loadStyles((d=a.$pd[t])===null||d===void 0?void 0:d.instance,l,u),o("updated",a,l,u,c)},beforeUnmount:function(a,l,u,c){var d;i(a),Q._removeThemeListeners((d=a.$pd[t])===null||d===void 0?void 0:d.instance),o("beforeUnmount",a,l,u,c)},unmounted:function(a,l,u,c){var d;(d=a.$pd[t])===null||d===void 0||(d=d.instance)===null||d===void 0||(d=d.scopedStyleEl)===null||d===void 0||(d=d.value)===null||d===void 0||d.remove(),o("unmounted",a,l,u,c)}}},extend:function(){var t=Q._getMeta.apply(Q,arguments),r=Il(t,2),o=r[0],n=r[1];return ie({extend:function(){var s=Q._getMeta.apply(Q,arguments),a=Il(s,2),l=a[0],u=a[1];return Q.extend(l,ie(ie(ie({},n),n==null?void 0:n.methods),u))}},Q._extend(o,n))}},Ov=({dt:e})=>`
.p-ink {
    display: block;
    position: absolute;
    background: ${e("ripple.background")};
    border-radius: 100%;
    transform: scale(0);
    pointer-events: none;
}

.p-ink-active {
    animation: ripple 0.4s linear;
}

@keyframes ripple {
    100% {
        opacity: 0;
        transform: scale(2.5);
    }
}
`,Bv={root:"p-ink"},Av=ye.extend({name:"ripple-directive",style:Ov,classes:Bv}),Lv=Q.extend({style:Av});function Eo(e){"@babel/helpers - typeof";return Eo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Eo(e)}function Iv(e){return Nv(e)||Mv(e)||jv(e)||Dv()}function Dv(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function jv(e,t){if(e){if(typeof e=="string")return fs(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?fs(e,t):void 0}}function Mv(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Nv(e){if(Array.isArray(e))return fs(e)}function fs(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function Ml(e,t,r){return(t=Fv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fv(e){var t=zv(e,"string");return Eo(t)=="symbol"?t:t+""}function zv(e,t){if(Eo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(Eo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Hv=Lv.extend("ripple",{watch:{"config.ripple":function(t){t?(this.createRipple(this.$host),this.bindEvents(this.$host),this.$host.setAttribute("data-pd-ripple",!0),this.$host.style.overflow="hidden",this.$host.style.position="relative"):(this.remove(this.$host),this.$host.removeAttribute("data-pd-ripple"))}},unmounted:function(t){this.remove(t)},timeout:void 0,methods:{bindEvents:function(t){t.addEventListener("mousedown",this.onMouseDown.bind(this))},unbindEvents:function(t){t.removeEventListener("mousedown",this.onMouseDown.bind(this))},createRipple:function(t){var r=this.getInk(t);r||(r=mn("span",Ml(Ml({role:"presentation","aria-hidden":!0,"data-p-ink":!0,"data-p-ink-active":!1,class:!this.isUnstyled()&&this.cx("root"),onAnimationEnd:this.onAnimationEnd.bind(this)},this.$attrSelector,""),"p-bind",this.ptm("root"))),t.appendChild(r),this.$el=r)},remove:function(t){var r=this.getInk(t);r&&(this.$host.style.overflow="",this.$host.style.position="",this.unbindEvents(t),r.removeEventListener("animationend",this.onAnimationEnd),r.remove())},onMouseDown:function(t){var r=this,o=t.currentTarget,n=this.getInk(o);if(!(!n||getComputedStyle(n,null).display==="none")){if(!this.isUnstyled()&&gn(n,"p-ink-active"),n.setAttribute("data-p-ink-active","false"),!ml(n)&&!bl(n)){var i=Math.max(rt(o),Vt(o));n.style.height=i+"px",n.style.width=i+"px"}var s=Y0(o),a=t.pageX-s.left+document.body.scrollTop-bl(n)/2,l=t.pageY-s.top+document.body.scrollLeft-ml(n)/2;n.style.top=l+"px",n.style.left=a+"px",!this.isUnstyled()&&Hd(n,"p-ink-active"),n.setAttribute("data-p-ink-active","true"),this.timeout=setTimeout(function(){n&&(!r.isUnstyled()&&gn(n,"p-ink-active"),n.setAttribute("data-p-ink-active","false"))},401)}},onAnimationEnd:function(t){this.timeout&&clearTimeout(this.timeout),!this.isUnstyled()&&gn(t.currentTarget,"p-ink-active"),t.currentTarget.setAttribute("data-p-ink-active","false")},getInk:function(t){return t&&t.children?Iv(t.children).find(function(r){return cr(r,"data-pc-name")==="ripple"}):void 0}}}),Uv={name:"BaseToast",extends:Uo,props:{group:{type:String,default:null},position:{type:String,default:"top-right"},autoZIndex:{type:Boolean,default:!0},baseZIndex:{type:Number,default:0},breakpoints:{type:Object,default:null},closeIcon:{type:String,default:void 0},infoIcon:{type:String,default:void 0},warnIcon:{type:String,default:void 0},errorIcon:{type:String,default:void 0},successIcon:{type:String,default:void 0},closeButtonProps:{type:null,default:null},onMouseEnter:{type:Function,default:void 0},onMouseLeave:{type:Function,default:void 0},onClick:{type:Function,default:void 0}},style:gv,provide:function(){return{$pcToast:this,$parentInstance:this}}};function Oo(e){"@babel/helpers - typeof";return Oo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Oo(e)}function Vv(e,t,r){return(t=Wv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Wv(e){var t=Kv(e,"string");return Oo(t)=="symbol"?t:t+""}function Kv(e,t){if(Oo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(Oo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var df={name:"ToastMessage",hostName:"Toast",extends:Uo,emits:["close"],closeTimeout:null,createdAt:null,lifeRemaining:null,props:{message:{type:null,default:null},templates:{type:Object,default:null},closeIcon:{type:String,default:null},infoIcon:{type:String,default:null},warnIcon:{type:String,default:null},errorIcon:{type:String,default:null},successIcon:{type:String,default:null},closeButtonProps:{type:null,default:null}},mounted:function(){this.message.life&&(this.lifeRemaining=this.message.life,this.startTimeout())},beforeUnmount:function(){this.clearCloseTimeout()},methods:{startTimeout:function(){var t=this;this.createdAt=new Date().valueOf(),this.closeTimeout=setTimeout(function(){t.close({message:t.message,type:"life-end"})},this.lifeRemaining)},close:function(t){this.$emit("close",t)},onCloseClick:function(){this.clearCloseTimeout(),this.close({message:this.message,type:"close"})},clearCloseTimeout:function(){this.closeTimeout&&(clearTimeout(this.closeTimeout),this.closeTimeout=null)},onMessageClick:function(t){var r;!((r=this.props)===null||r===void 0)&&r.onClick&&this.props.onClick({originalEvent:t,message:this.message})},onMouseEnter:function(t){var r;if((r=this.props)!==null&&r!==void 0&&r.onMouseEnter){if(this.props.onMouseEnter({originalEvent:t,message:this.message}),t.defaultPrevented)return;this.message.life&&(this.lifeRemaining=this.createdAt+this.lifeRemaining-Date().valueOf(),this.createdAt=null,this.clearCloseTimeout())}},onMouseLeave:function(t){var r;if((r=this.props)!==null&&r!==void 0&&r.onMouseLeave){if(this.props.onMouseLeave({originalEvent:t,message:this.message}),t.defaultPrevented)return;this.message.life&&this.startTimeout()}}},computed:{iconComponent:function(){return{info:!this.infoIcon&&cs,success:!this.successIcon&&as,warn:!this.warnIcon&&ls,error:!this.errorIcon&&us}[this.message.severity]},closeAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.close:void 0},dataP:function(){return Ho(Vv({},this.message.severity,this.message.severity))}},components:{TimesIcon:uf,InfoCircleIcon:cs,CheckIcon:as,ExclamationTriangleIcon:ls,TimesCircleIcon:us},directives:{ripple:Hv}};function Bo(e){"@babel/helpers - typeof";return Bo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bo(e)}function Nl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function Fl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Nl(Object(r),!0).forEach(function(o){qv(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nl(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function qv(e,t,r){return(t=Gv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Gv(e){var t=Jv(e,"string");return Bo(t)=="symbol"?t:t+""}function Jv(e,t){if(Bo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(Bo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Yv=["data-p"],Xv=["data-p"],Zv=["data-p"],Qv=["data-p"],ey=["aria-label","data-p"];function ty(e,t,r,o,n,i){var s=Ip("ripple");return se(),Te("div",he({class:[e.cx("message"),r.message.styleClass],role:"alert","aria-live":"assertive","aria-atomic":"true","data-p":i.dataP},e.ptm("message"),{onClick:t[1]||(t[1]=function(){return i.onMessageClick&&i.onMessageClick.apply(i,arguments)}),onMouseenter:t[2]||(t[2]=function(){return i.onMouseEnter&&i.onMouseEnter.apply(i,arguments)}),onMouseleave:t[3]||(t[3]=function(){return i.onMouseLeave&&i.onMouseLeave.apply(i,arguments)})}),[r.templates.container?(se(),it(Kr(r.templates.container),{key:0,message:r.message,closeCallback:i.onCloseClick},null,8,["message","closeCallback"])):(se(),Te("div",he({key:1,class:[e.cx("messageContent"),r.message.contentStyleClass]},e.ptm("messageContent")),[r.templates.message?(se(),it(Kr(r.templates.message),{key:1,message:r.message},null,8,["message"])):(se(),Te(Le,{key:0},[(se(),it(Kr(r.templates.messageicon?r.templates.messageicon:r.templates.icon?r.templates.icon:i.iconComponent&&i.iconComponent.name?i.iconComponent:"span"),he({class:e.cx("messageIcon")},e.ptm("messageIcon")),null,16,["class"])),ge("div",he({class:e.cx("messageText"),"data-p":i.dataP},e.ptm("messageText")),[ge("span",he({class:e.cx("summary"),"data-p":i.dataP},e.ptm("summary")),lo(r.message.summary),17,Zv),r.message.detail?(se(),Te("div",he({key:0,class:e.cx("detail"),"data-p":i.dataP},e.ptm("detail")),lo(r.message.detail),17,Qv)):mo("",!0)],16,Xv)],64)),r.message.closable!==!1?(se(),Te("div",Df(he({key:2},e.ptm("buttonContainer"))),[yp((se(),Te("button",he({class:e.cx("closeButton"),type:"button","aria-label":i.closeAriaLabel,onClick:t[0]||(t[0]=function(){return i.onCloseClick&&i.onCloseClick.apply(i,arguments)}),autofocus:"","data-p":i.dataP},Fl(Fl({},r.closeButtonProps),e.ptm("closeButton"))),[(se(),it(Kr(r.templates.closeicon||"TimesIcon"),he({class:[e.cx("closeIcon"),r.closeIcon]},e.ptm("closeIcon")),null,16,["class"]))],16,ey)),[[s]])],16)):mo("",!0)],16))],16,Yv)}df.render=ty;function Ao(e){"@babel/helpers - typeof";return Ao=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ao(e)}function ry(e,t,r){return(t=oy(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function oy(e){var t=ny(e,"string");return Ao(t)=="symbol"?t:t+""}function ny(e,t){if(Ao(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(Ao(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function iy(e){return cy(e)||ly(e)||ay(e)||sy()}function sy(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ay(e,t){if(e){if(typeof e=="string")return ps(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ps(e,t):void 0}}function ly(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function cy(e){if(Array.isArray(e))return ps(e)}function ps(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}var uy=0,ff={name:"Toast",extends:Uv,inheritAttrs:!1,emits:["close","life-end"],data:function(){return{messages:[]}},styleElement:null,mounted:function(){ot.on("add",this.onAdd),ot.on("remove",this.onRemove),ot.on("remove-group",this.onRemoveGroup),ot.on("remove-all-groups",this.onRemoveAllGroups),this.breakpoints&&this.createStyle()},beforeUnmount:function(){this.destroyStyle(),this.$refs.container&&this.autoZIndex&&so.clear(this.$refs.container),ot.off("add",this.onAdd),ot.off("remove",this.onRemove),ot.off("remove-group",this.onRemoveGroup),ot.off("remove-all-groups",this.onRemoveAllGroups)},methods:{add:function(t){t.id==null&&(t.id=uy++),this.messages=[].concat(iy(this.messages),[t])},remove:function(t){var r=this.messages.findIndex(function(o){return o.id===t.message.id});r!==-1&&(this.messages.splice(r,1),this.$emit(t.type,{message:t.message}))},onAdd:function(t){this.group==t.group&&this.add(t)},onRemove:function(t){this.remove({message:t,type:"close"})},onRemoveGroup:function(t){this.group===t&&(this.messages=[])},onRemoveAllGroups:function(){var t=this;this.messages.forEach(function(r){return t.$emit("close",{message:r})}),this.messages=[]},onEnter:function(){this.autoZIndex&&so.set("modal",this.$refs.container,this.baseZIndex||this.$primevue.config.zIndex.modal)},onLeave:function(){var t=this;this.$refs.container&&this.autoZIndex&&tr(this.messages)&&setTimeout(function(){so.clear(t.$refs.container)},200)},createStyle:function(){if(!this.styleElement&&!this.isUnstyled){var t;this.styleElement=document.createElement("style"),this.styleElement.type="text/css",Yd(this.styleElement,"nonce",(t=this.$primevue)===null||t===void 0||(t=t.config)===null||t===void 0||(t=t.csp)===null||t===void 0?void 0:t.nonce),document.head.appendChild(this.styleElement);var r="";for(var o in this.breakpoints){var n="";for(var i in this.breakpoints[o])n+=i+":"+this.breakpoints[o][i]+"!important;";r+=`
                        @media screen and (max-width: `.concat(o,`) {
                            .p-toast[`).concat(this.$attrSelector,`] {
                                `).concat(n,`
                            }
                        }
                    `)}this.styleElement.innerHTML=r}},destroyStyle:function(){this.styleElement&&(document.head.removeChild(this.styleElement),this.styleElement=null)}},computed:{dataP:function(){return Ho(ry({},this.position,this.position))}},components:{ToastMessage:df,Portal:cf}};function Lo(e){"@babel/helpers - typeof";return Lo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lo(e)}function zl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function dy(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?zl(Object(r),!0).forEach(function(o){fy(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):zl(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function fy(e,t,r){return(t=py(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function py(e){var t=gy(e,"string");return Lo(t)=="symbol"?t:t+""}function gy(e,t){if(Lo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(Lo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var hy=["data-p"];function my(e,t,r,o,n,i){var s=Mi("ToastMessage"),a=Mi("Portal");return se(),it(a,null,{default:Sn(function(){return[ge("div",he({ref:"container",class:e.cx("root"),style:e.sx("root",!0,{position:e.position}),"data-p":i.dataP},e.ptmi("root")),[we(Jg,he({name:"p-toast-message",tag:"div",onEnter:i.onEnter,onLeave:i.onLeave},dy({},e.ptm("transition"))),{default:Sn(function(){return[(se(!0),Te(Le,null,ku(n.messages,function(l){return se(),it(s,{key:l.id,message:l,templates:e.$slots,closeIcon:e.closeIcon,infoIcon:e.infoIcon,warnIcon:e.warnIcon,errorIcon:e.errorIcon,successIcon:e.successIcon,closeButtonProps:e.closeButtonProps,unstyled:e.unstyled,onClose:t[0]||(t[0]=function(u){return i.remove(u)}),pt:e.pt},null,8,["message","templates","closeIcon","infoIcon","warnIcon","errorIcon","successIcon","closeButtonProps","unstyled","pt"])}),128))]}),_:1},16,["onEnter","onLeave"])],16,hy)]}),_:1})}ff.render=my;var pf=Symbol();function by(){var e=Qe(pf);if(!e)throw new Error("No PrimeVue Toast provided!");return e}const vy={class:"bg-surface-0 border md:h-screen w-screen border-black/10 dark:border-white/20 dark:bg-surface-950 p-6 flex items-start gap-6 overflow-hidden flex-col md:flex-row h-screen sm:h-full"},yy={class:"hidden sm:flex w-auto rounded-2xl p-5 bg-surface-50 dark:bg-surface-900 h-full flex-col justify-between"},Cy={class:"w-12 flex flex-col items-center"},Sy={class:"mt-10 flex flex-col gap-2"},ky={class:"hidden"},_y={class:"w-12 flex flex-col items-center"},$y={class:"justify-center flex items-center"},xy=Ps({__name:"App",setup(e){const t=by(),r=gm(),o=Tt([]),n=["/login/token"];return pe.defaults.withCredentials=!0,pe.defaults.baseURL=wn,pe.interceptors.request.use(i=>{i.headers["Content-Type"]="application/json",i.headers.Accept="application/json";const s=md();return s&&(i.headers.Authorization=`Bearer ${s}`),i},i=>Promise.reject(i)),pe.interceptors.response.use(i=>{const s=i.data;return s.code!==0?(t.add({severity:"error",summary:"出错了",detail:s.message,group:"br",life:3e3}),Promise.reject(new Error(s.message))):i.data.data},i=>{var s,a,l;if(!n.includes(i.config.url)&&((s=i.response)==null?void 0:s.status)===401){window.location.href=`${wn}/login?redirect_uri=${window.location.href}`;return}return t.add({severity:"error",summary:"出错了",detail:((l=(a=i.response)==null?void 0:a.data)==null?void 0:l.message)||i.message,group:"br",life:3e3}),Promise.reject(i)}),W0().then(i=>{const{user:s,token:a}=i;tl(a),r.updateInfo(s)}).catch(i=>{tl(""),r.updateInfo({})}),(i,s)=>{const a=Mi("router-link");return se(),Te("div",vy,[ge("div",yy,[ge("div",Cy,[s[1]||(s[1]=ge("div",{class:"flex items-center gap-3"},[ge("div",{class:"w-11 h-11 border border-primary rounded-xl flex items-center justify-center"},[ge("img",{src:"https://www.chengyao.xyz/images/logo.png",alt:""})]),ge("div",{class:"hidden text-surface-950 dark:text-surface-0 font-medium text-3xl"},"Prime")],-1)),ge("div",Sy,[(se(!0),Te(Le,null,ku(o.value,l=>(se(),it(a,{to:l.path,key:l.name,class:"px-4 py-1 flex items-center gap-1 cursor-pointer text-base rounded-lg transition-all select-none w-12 justify-center py-4 hover:bg-emphasis text-muted-color bg-transparent","data-pd-tooltip":"true"},{default:Sn(()=>[ge("i",{class:Ir(l.icon)},null,2),s[0]||(s[0]=ge("span",{class:"hidden"},"・",-1)),ge("span",ky,lo(l.name),1)]),_:2},1032,["to"]))),128))])]),ge("div",_y,[we(ft(af)),ge("div",$y,[we(ft(lf),{class:"shrink-0",size:"large",shape:"circle",image:ft(r).info.avatar},null,8,["image"]),s[2]||(s[2]=ge("div",null,[ge("div",{class:"hidden"},"Robin Jonas"),ge("div",{class:"hidden"},"<EMAIL>")],-1))])])]),we(ft(gd)),we(ft(ff))])}}}),Ry="modulepreload",wy=function(e){return"/"+e},Hl={},zt=function(t,r,o){let n=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),a=(s==null?void 0:s.nonce)||(s==null?void 0:s.getAttribute("nonce"));n=Promise.allSettled(r.map(l=>{if(l=wy(l),l in Hl)return;Hl[l]=!0;const u=l.endsWith(".css"),c=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${c}`))return;const d=document.createElement("link");if(d.rel=u?"stylesheet":Ry,u||(d.as="script"),d.crossOrigin="",d.href=l,a&&d.setAttribute("nonce",a),document.head.appendChild(d),u)return new Promise((f,p)=>{d.addEventListener("load",f),d.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${l}`)))})}))}function i(s){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=s,window.dispatchEvent(a),!a.defaultPrevented)throw s}return n.then(s=>{for(const a of s||[])a.status==="rejected"&&i(a.reason);return t().catch(i)})},gf=fm({history:Hh("/"),routes:[{path:"/meeting/:id",component:()=>zt(()=>import("./MeetingView-2VMLXPJN.js"),__vite__mapDeps([0,1,2,3])),meta:{requireAuth:!0}},{path:"/login",component:()=>zt(()=>import("./LoginView-CLzqqMm5.js"),__vite__mapDeps([4,1]))},{path:"/",component:()=>zt(()=>import("./LoginView-CLzqqMm5.js"),__vite__mapDeps([4,1]))},{path:"/chats",component:()=>zt(()=>import("./ChatView-Eab6ittC.js"),__vite__mapDeps([5,2,1])),meta:{requireAuth:!0}},{path:"/inbox",component:()=>zt(()=>import("./InboxView-BY1tYbrT.js"),__vite__mapDeps([6,7]))},{path:"/customers",component:()=>zt(()=>import("./CustomersView-P3IxWO33.js"),__vite__mapDeps([8,7]))},{path:"/cards",component:()=>zt(()=>import("./CardsView-BwqfqlAb.js"),__vite__mapDeps([9,7]))},{path:"/movies",component:()=>zt(()=>import("./MoviesView-DUABXTdB.js"),__vite__mapDeps([10,7]))}]});gf.beforeEach((e,t,r)=>{var o;if((o=e.meta)!=null&&o.requireAuth&&!md()){window.location.href=`/login?redirect_uri=${encodeURIComponent(window.location.href)}`;return}r()});var Ty={install:function(t){var r={add:function(n){ot.emit("add",n)},remove:function(n){ot.emit("remove",n)},removeGroup:function(n){ot.emit("remove-group",n)},removeAllGroups:function(){ot.emit("remove-all-groups")}};t.config.globalProperties.$toast=r,t.provide(pf,r)}},De={STARTS_WITH:"startsWith",CONTAINS:"contains",NOT_CONTAINS:"notContains",ENDS_WITH:"endsWith",EQUALS:"equals",NOT_EQUALS:"notEquals",LESS_THAN:"lt",LESS_THAN_OR_EQUAL_TO:"lte",GREATER_THAN:"gt",GREATER_THAN_OR_EQUAL_TO:"gte",DATE_IS:"dateIs",DATE_IS_NOT:"dateIsNot",DATE_BEFORE:"dateBefore",DATE_AFTER:"dateAfter"};function Io(e){"@babel/helpers - typeof";return Io=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Io(e)}function Ul(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function Zo(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ul(Object(r),!0).forEach(function(o){Py(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ul(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function Py(e,t,r){return(t=Ey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Ey(e){var t=Oy(e,"string");return Io(t)=="symbol"?t:t+""}function Oy(e,t){if(Io(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(Io(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var By={ripple:!1,inputStyle:null,inputVariant:null,locale:{startsWith:"Starts with",contains:"Contains",notContains:"Not contains",endsWith:"Ends with",equals:"Equals",notEquals:"Not equals",noFilter:"No Filter",lt:"Less than",lte:"Less than or equal to",gt:"Greater than",gte:"Greater than or equal to",dateIs:"Date is",dateIsNot:"Date is not",dateBefore:"Date is before",dateAfter:"Date is after",clear:"Clear",apply:"Apply",matchAll:"Match All",matchAny:"Match Any",addRule:"Add Rule",removeRule:"Remove Rule",accept:"Yes",reject:"No",choose:"Choose",upload:"Upload",cancel:"Cancel",completed:"Completed",pending:"Pending",fileSizeTypes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],chooseYear:"Choose Year",chooseMonth:"Choose Month",chooseDate:"Choose Date",prevDecade:"Previous Decade",nextDecade:"Next Decade",prevYear:"Previous Year",nextYear:"Next Year",prevMonth:"Previous Month",nextMonth:"Next Month",prevHour:"Previous Hour",nextHour:"Next Hour",prevMinute:"Previous Minute",nextMinute:"Next Minute",prevSecond:"Previous Second",nextSecond:"Next Second",am:"am",pm:"pm",today:"Today",weekHeader:"Wk",firstDayOfWeek:0,showMonthAfterYear:!1,dateFormat:"mm/dd/yy",weak:"Weak",medium:"Medium",strong:"Strong",passwordPrompt:"Enter a password",emptyFilterMessage:"No results found",searchMessage:"{0} results are available",selectionMessage:"{0} items selected",emptySelectionMessage:"No selected item",emptySearchMessage:"No results found",fileChosenMessage:"{0} files",noFileChosenMessage:"No file chosen",emptyMessage:"No available options",aria:{trueLabel:"True",falseLabel:"False",nullLabel:"Not Selected",star:"1 star",stars:"{star} stars",selectAll:"All items selected",unselectAll:"All items unselected",close:"Close",previous:"Previous",next:"Next",navigation:"Navigation",scrollTop:"Scroll Top",moveTop:"Move Top",moveUp:"Move Up",moveDown:"Move Down",moveBottom:"Move Bottom",moveToTarget:"Move to Target",moveToSource:"Move to Source",moveAllToTarget:"Move All to Target",moveAllToSource:"Move All to Source",pageLabel:"Page {page}",firstPageLabel:"First Page",lastPageLabel:"Last Page",nextPageLabel:"Next Page",prevPageLabel:"Previous Page",rowsPerPageLabel:"Rows per page",jumpToPageDropdownLabel:"Jump to Page Dropdown",jumpToPageInputLabel:"Jump to Page Input",selectRow:"Row Selected",unselectRow:"Row Unselected",expandRow:"Row Expanded",collapseRow:"Row Collapsed",showFilterMenu:"Show Filter Menu",hideFilterMenu:"Hide Filter Menu",filterOperator:"Filter Operator",filterConstraint:"Filter Constraint",editRow:"Row Edit",saveEdit:"Save Edit",cancelEdit:"Cancel Edit",listView:"List View",gridView:"Grid View",slide:"Slide",slideNumber:"{slideNumber}",zoomImage:"Zoom Image",zoomIn:"Zoom In",zoomOut:"Zoom Out",rotateRight:"Rotate Right",rotateLeft:"Rotate Left",listLabel:"Option List"}},filterMatchModeOptions:{text:[De.STARTS_WITH,De.CONTAINS,De.NOT_CONTAINS,De.ENDS_WITH,De.EQUALS,De.NOT_EQUALS],numeric:[De.EQUALS,De.NOT_EQUALS,De.LESS_THAN,De.LESS_THAN_OR_EQUAL_TO,De.GREATER_THAN,De.GREATER_THAN_OR_EQUAL_TO],date:[De.DATE_IS,De.DATE_IS_NOT,De.DATE_BEFORE,De.DATE_AFTER]},zIndex:{modal:1100,overlay:1e3,menu:1e3,tooltip:1100},theme:void 0,unstyled:!1,pt:void 0,ptOptions:{mergeSections:!0,mergeProps:!1},csp:{nonce:void 0}},Ay=Symbol();function Ly(e,t){var r={config:Dr(t)};return e.config.globalProperties.$primevue=r,e.provide(Ay,r),Iy(),Dy(e,r),r}var Rr=[];function Iy(){Ae.clear(),Rr.forEach(function(e){return e==null?void 0:e()}),Rr=[]}function Dy(e,t){var r=Tt(!1),o=function(){var u;if(((u=t.config)===null||u===void 0?void 0:u.theme)!=="none"&&!fe.isStyleNameLoaded("common")){var c,d,f=((c=ye.getCommonTheme)===null||c===void 0?void 0:c.call(ye))||{},p=f.primitive,h=f.semantic,b=f.global,C=f.style,k={nonce:(d=t.config)===null||d===void 0||(d=d.csp)===null||d===void 0?void 0:d.nonce};ye.load(p==null?void 0:p.css,Zo({name:"primitive-variables"},k)),ye.load(h==null?void 0:h.css,Zo({name:"semantic-variables"},k)),ye.load(b==null?void 0:b.css,Zo({name:"global-variables"},k)),ye.loadStyle(Zo({name:"global-style"},k),C),fe.setLoadedStyleName("common")}};Ae.on("theme:change",function(l){r.value||(e.config.globalProperties.$primevue.config.theme=l,r.value=!0)});var n=Rt(t.config,function(l,u){Yt.emit("config:change",{newValue:l,oldValue:u})},{immediate:!0,deep:!0}),i=Rt(function(){return t.config.ripple},function(l,u){Yt.emit("config:ripple:change",{newValue:l,oldValue:u})},{immediate:!0,deep:!0}),s=Rt(function(){return t.config.theme},function(l,u){r.value||fe.setTheme(l),t.config.unstyled||o(),r.value=!1,Yt.emit("config:theme:change",{newValue:l,oldValue:u})},{immediate:!0,deep:!1}),a=Rt(function(){return t.config.unstyled},function(l,u){!l&&t.config.theme&&o(),Yt.emit("config:unstyled:change",{newValue:l,oldValue:u})},{immediate:!0,deep:!0});Rr.push(n),Rr.push(i),Rr.push(s),Rr.push(a)}var jy={install:function(t,r){var o=sb(By,r);Ly(t,o)}},My={transitionDuration:"{transition.duration}"},Ny={borderWidth:"0 0 1px 0",borderColor:"{content.border.color}"},Fy={color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{text.color}",padding:"1.125rem",fontWeight:"600",borderRadius:"0",borderWidth:"0",borderColor:"{content.border.color}",background:"{content.background}",hoverBackground:"{content.background}",activeBackground:"{content.background}",activeHoverBackground:"{content.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},toggleIcon:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{text.color}",activeHoverColor:"{text.color}"},first:{topBorderRadius:"{content.border.radius}",borderWidth:"0"},last:{bottomBorderRadius:"{content.border.radius}",activeBottomBorderRadius:"0"}},zy={borderWidth:"0",borderColor:"{content.border.color}",background:"{content.background}",color:"{text.color}",padding:"0 1.125rem 1.125rem 1.125rem"},Hy={root:My,panel:Ny,header:Fy,content:zy},Uy={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},Vy={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},Wy={padding:"{list.padding}",gap:"{list.gap}"},Ky={focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},qy={background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},Gy={width:"2.5rem",sm:{width:"2rem"},lg:{width:"3rem"},borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Jy={borderRadius:"{border.radius.sm}"},Yy={padding:"{list.option.padding}"},Xy={light:{chip:{focusBackground:"{surface.200}",focusColor:"{surface.800}"},dropdown:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"}},dark:{chip:{focusBackground:"{surface.700}",focusColor:"{surface.0}"},dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"}}},Zy={root:Uy,overlay:Vy,list:Wy,option:Ky,optionGroup:qy,dropdown:Gy,chip:Jy,emptyMessage:Yy,colorScheme:Xy},Qy={width:"2rem",height:"2rem",fontSize:"1rem",background:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},e1={size:"1rem"},t1={borderColor:"{content.background}",offset:"-0.75rem"},r1={width:"3rem",height:"3rem",fontSize:"1.5rem",icon:{size:"1.5rem"},group:{offset:"-1rem"}},o1={width:"4rem",height:"4rem",fontSize:"2rem",icon:{size:"2rem"},group:{offset:"-1.5rem"}},n1={root:Qy,icon:e1,group:t1,lg:r1,xl:o1},i1={borderRadius:"{border.radius.md}",padding:"0 0.5rem",fontSize:"0.75rem",fontWeight:"700",minWidth:"1.5rem",height:"1.5rem"},s1={size:"0.5rem"},a1={fontSize:"0.625rem",minWidth:"1.25rem",height:"1.25rem"},l1={fontSize:"0.875rem",minWidth:"1.75rem",height:"1.75rem"},c1={fontSize:"1rem",minWidth:"2rem",height:"2rem"},u1={light:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.100}",color:"{surface.600}"},success:{background:"{green.500}",color:"{surface.0}"},info:{background:"{sky.500}",color:"{surface.0}"},warn:{background:"{orange.500}",color:"{surface.0}"},danger:{background:"{red.500}",color:"{surface.0}"},contrast:{background:"{surface.950}",color:"{surface.0}"}},dark:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.800}",color:"{surface.300}"},success:{background:"{green.400}",color:"{green.950}"},info:{background:"{sky.400}",color:"{sky.950}"},warn:{background:"{orange.400}",color:"{orange.950}"},danger:{background:"{red.400}",color:"{red.950}"},contrast:{background:"{surface.0}",color:"{surface.950}"}}},d1={root:i1,dot:s1,sm:a1,lg:l1,xl:c1,colorScheme:u1},f1={borderRadius:{none:"0",xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"},slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"}},p1={transitionDuration:"0.2s",focusRing:{width:"1px",style:"solid",color:"{primary.color}",offset:"2px",shadow:"none"},disabledOpacity:"0.6",iconSize:"1rem",anchorGutter:"2px",primary:{50:"{emerald.50}",100:"{emerald.100}",200:"{emerald.200}",300:"{emerald.300}",400:"{emerald.400}",500:"{emerald.500}",600:"{emerald.600}",700:"{emerald.700}",800:"{emerald.800}",900:"{emerald.900}",950:"{emerald.950}"},formField:{paddingX:"0.75rem",paddingY:"0.5rem",sm:{fontSize:"0.875rem",paddingX:"0.625rem",paddingY:"0.375rem"},lg:{fontSize:"1.125rem",paddingX:"0.875rem",paddingY:"0.625rem"},borderRadius:"{border.radius.md}",focusRing:{width:"0",style:"none",color:"transparent",offset:"0",shadow:"none"},transitionDuration:"{transition.duration}"},list:{padding:"0.25rem 0.25rem",gap:"2px",header:{padding:"0.5rem 1rem 0.25rem 1rem"},option:{padding:"0.5rem 0.75rem",borderRadius:"{border.radius.sm}"},optionGroup:{padding:"0.5rem 0.75rem",fontWeight:"600"}},content:{borderRadius:"{border.radius.md}"},mask:{transitionDuration:"0.15s"},navigation:{list:{padding:"0.25rem 0.25rem",gap:"2px"},item:{padding:"0.5rem 0.75rem",borderRadius:"{border.radius.sm}",gap:"0.5rem"},submenuLabel:{padding:"0.5rem 0.75rem",fontWeight:"600"},submenuIcon:{size:"0.875rem"}},overlay:{select:{borderRadius:"{border.radius.md}",shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"},popover:{borderRadius:"{border.radius.md}",padding:"0.75rem",shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"},modal:{borderRadius:"{border.radius.xl}",padding:"1.25rem",shadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)"},navigation:{shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"}},colorScheme:{light:{surface:{0:"#ffffff",50:"{slate.50}",100:"{slate.100}",200:"{slate.200}",300:"{slate.300}",400:"{slate.400}",500:"{slate.500}",600:"{slate.600}",700:"{slate.700}",800:"{slate.800}",900:"{slate.900}",950:"{slate.950}"},primary:{color:"{primary.500}",contrastColor:"#ffffff",hoverColor:"{primary.600}",activeColor:"{primary.700}"},highlight:{background:"{primary.50}",focusBackground:"{primary.100}",color:"{primary.700}",focusColor:"{primary.800}"},mask:{background:"rgba(0,0,0,0.4)",color:"{surface.200}"},formField:{background:"{surface.0}",disabledBackground:"{surface.200}",filledBackground:"{surface.50}",filledHoverBackground:"{surface.50}",filledFocusBackground:"{surface.50}",borderColor:"{surface.300}",hoverBorderColor:"{surface.400}",focusBorderColor:"{primary.color}",invalidBorderColor:"{red.400}",color:"{surface.700}",disabledColor:"{surface.500}",placeholderColor:"{surface.500}",invalidPlaceholderColor:"{red.600}",floatLabelColor:"{surface.500}",floatLabelFocusColor:"{primary.600}",floatLabelActiveColor:"{surface.500}",floatLabelInvalidColor:"{form.field.invalid.placeholder.color}",iconColor:"{surface.400}",shadow:"0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05)"},text:{color:"{surface.700}",hoverColor:"{surface.800}",mutedColor:"{surface.500}",hoverMutedColor:"{surface.600}"},content:{background:"{surface.0}",hoverBackground:"{surface.100}",borderColor:"{surface.200}",color:"{text.color}",hoverColor:"{text.hover.color}"},overlay:{select:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"},popover:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"},modal:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"}},list:{option:{focusBackground:"{surface.100}",selectedBackground:"{highlight.background}",selectedFocusBackground:"{highlight.focus.background}",color:"{text.color}",focusColor:"{text.hover.color}",selectedColor:"{highlight.color}",selectedFocusColor:"{highlight.focus.color}",icon:{color:"{surface.400}",focusColor:"{surface.500}"}},optionGroup:{background:"transparent",color:"{text.muted.color}"}},navigation:{item:{focusBackground:"{surface.100}",activeBackground:"{surface.100}",color:"{text.color}",focusColor:"{text.hover.color}",activeColor:"{text.hover.color}",icon:{color:"{surface.400}",focusColor:"{surface.500}",activeColor:"{surface.500}"}},submenuLabel:{background:"transparent",color:"{text.muted.color}"},submenuIcon:{color:"{surface.400}",focusColor:"{surface.500}",activeColor:"{surface.500}"}}},dark:{surface:{0:"#ffffff",50:"{zinc.50}",100:"{zinc.100}",200:"{zinc.200}",300:"{zinc.300}",400:"{zinc.400}",500:"{zinc.500}",600:"{zinc.600}",700:"{zinc.700}",800:"{zinc.800}",900:"{zinc.900}",950:"{zinc.950}"},primary:{color:"{primary.400}",contrastColor:"{surface.900}",hoverColor:"{primary.300}",activeColor:"{primary.200}"},highlight:{background:"color-mix(in srgb, {primary.400}, transparent 84%)",focusBackground:"color-mix(in srgb, {primary.400}, transparent 76%)",color:"rgba(255,255,255,.87)",focusColor:"rgba(255,255,255,.87)"},mask:{background:"rgba(0,0,0,0.6)",color:"{surface.200}"},formField:{background:"{surface.950}",disabledBackground:"{surface.700}",filledBackground:"{surface.800}",filledHoverBackground:"{surface.800}",filledFocusBackground:"{surface.800}",borderColor:"{surface.600}",hoverBorderColor:"{surface.500}",focusBorderColor:"{primary.color}",invalidBorderColor:"{red.300}",color:"{surface.0}",disabledColor:"{surface.400}",placeholderColor:"{surface.400}",invalidPlaceholderColor:"{red.400}",floatLabelColor:"{surface.400}",floatLabelFocusColor:"{primary.color}",floatLabelActiveColor:"{surface.400}",floatLabelInvalidColor:"{form.field.invalid.placeholder.color}",iconColor:"{surface.400}",shadow:"0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05)"},text:{color:"{surface.0}",hoverColor:"{surface.0}",mutedColor:"{surface.400}",hoverMutedColor:"{surface.300}"},content:{background:"{surface.900}",hoverBackground:"{surface.800}",borderColor:"{surface.700}",color:"{text.color}",hoverColor:"{text.hover.color}"},overlay:{select:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"},popover:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"},modal:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"}},list:{option:{focusBackground:"{surface.800}",selectedBackground:"{highlight.background}",selectedFocusBackground:"{highlight.focus.background}",color:"{text.color}",focusColor:"{text.hover.color}",selectedColor:"{highlight.color}",selectedFocusColor:"{highlight.focus.color}",icon:{color:"{surface.500}",focusColor:"{surface.400}"}},optionGroup:{background:"transparent",color:"{text.muted.color}"}},navigation:{item:{focusBackground:"{surface.800}",activeBackground:"{surface.800}",color:"{text.color}",focusColor:"{text.hover.color}",activeColor:"{text.hover.color}",icon:{color:"{surface.500}",focusColor:"{surface.400}",activeColor:"{surface.400}"}},submenuLabel:{background:"transparent",color:"{text.muted.color}"},submenuIcon:{color:"{surface.500}",focusColor:"{surface.400}",activeColor:"{surface.400}"}}}}},g1={primitive:f1,semantic:p1},h1={borderRadius:"{content.border.radius}"},m1={root:h1},b1={padding:"1rem",background:"{content.background}",gap:"0.5rem",transitionDuration:"{transition.duration}"},v1={color:"{text.muted.color}",hoverColor:"{text.color}",borderRadius:"{content.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",hoverColor:"{navigation.item.icon.focus.color}"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},y1={color:"{navigation.item.icon.color}"},C1={root:b1,item:v1,separator:y1},S1={borderRadius:"{form.field.border.radius}",roundedBorderRadius:"2rem",gap:"0.5rem",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",iconOnlyWidth:"2.5rem",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}",iconOnlyWidth:"2rem"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}",iconOnlyWidth:"3rem"},label:{fontWeight:"500"},raisedShadow:"0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"},badgeSize:"1rem",transitionDuration:"{form.field.transition.duration}"},k1={light:{root:{primary:{background:"{primary.color}",hoverBackground:"{primary.hover.color}",activeBackground:"{primary.active.color}",borderColor:"{primary.color}",hoverBorderColor:"{primary.hover.color}",activeBorderColor:"{primary.active.color}",color:"{primary.contrast.color}",hoverColor:"{primary.contrast.color}",activeColor:"{primary.contrast.color}",focusRing:{color:"{primary.color}",shadow:"none"}},secondary:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",borderColor:"{surface.100}",hoverBorderColor:"{surface.200}",activeBorderColor:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}",focusRing:{color:"{surface.600}",shadow:"none"}},info:{background:"{sky.500}",hoverBackground:"{sky.600}",activeBackground:"{sky.700}",borderColor:"{sky.500}",hoverBorderColor:"{sky.600}",activeBorderColor:"{sky.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{sky.500}",shadow:"none"}},success:{background:"{green.500}",hoverBackground:"{green.600}",activeBackground:"{green.700}",borderColor:"{green.500}",hoverBorderColor:"{green.600}",activeBorderColor:"{green.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{green.500}",shadow:"none"}},warn:{background:"{orange.500}",hoverBackground:"{orange.600}",activeBackground:"{orange.700}",borderColor:"{orange.500}",hoverBorderColor:"{orange.600}",activeBorderColor:"{orange.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{orange.500}",shadow:"none"}},help:{background:"{purple.500}",hoverBackground:"{purple.600}",activeBackground:"{purple.700}",borderColor:"{purple.500}",hoverBorderColor:"{purple.600}",activeBorderColor:"{purple.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{purple.500}",shadow:"none"}},danger:{background:"{red.500}",hoverBackground:"{red.600}",activeBackground:"{red.700}",borderColor:"{red.500}",hoverBorderColor:"{red.600}",activeBorderColor:"{red.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{red.500}",shadow:"none"}},contrast:{background:"{surface.950}",hoverBackground:"{surface.900}",activeBackground:"{surface.800}",borderColor:"{surface.950}",hoverBorderColor:"{surface.900}",activeBorderColor:"{surface.800}",color:"{surface.0}",hoverColor:"{surface.0}",activeColor:"{surface.0}",focusRing:{color:"{surface.950}",shadow:"none"}}},outlined:{primary:{hoverBackground:"{primary.50}",activeBackground:"{primary.100}",borderColor:"{primary.200}",color:"{primary.color}"},secondary:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.200}",color:"{surface.500}"},success:{hoverBackground:"{green.50}",activeBackground:"{green.100}",borderColor:"{green.200}",color:"{green.500}"},info:{hoverBackground:"{sky.50}",activeBackground:"{sky.100}",borderColor:"{sky.200}",color:"{sky.500}"},warn:{hoverBackground:"{orange.50}",activeBackground:"{orange.100}",borderColor:"{orange.200}",color:"{orange.500}"},help:{hoverBackground:"{purple.50}",activeBackground:"{purple.100}",borderColor:"{purple.200}",color:"{purple.500}"},danger:{hoverBackground:"{red.50}",activeBackground:"{red.100}",borderColor:"{red.200}",color:"{red.500}"},contrast:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.700}",color:"{surface.950}"},plain:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.200}",color:"{surface.700}"}},text:{primary:{hoverBackground:"{primary.50}",activeBackground:"{primary.100}",color:"{primary.color}"},secondary:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.500}"},success:{hoverBackground:"{green.50}",activeBackground:"{green.100}",color:"{green.500}"},info:{hoverBackground:"{sky.50}",activeBackground:"{sky.100}",color:"{sky.500}"},warn:{hoverBackground:"{orange.50}",activeBackground:"{orange.100}",color:"{orange.500}"},help:{hoverBackground:"{purple.50}",activeBackground:"{purple.100}",color:"{purple.500}"},danger:{hoverBackground:"{red.50}",activeBackground:"{red.100}",color:"{red.500}"},contrast:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.950}"},plain:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.700}"}},link:{color:"{primary.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}},dark:{root:{primary:{background:"{primary.color}",hoverBackground:"{primary.hover.color}",activeBackground:"{primary.active.color}",borderColor:"{primary.color}",hoverBorderColor:"{primary.hover.color}",activeBorderColor:"{primary.active.color}",color:"{primary.contrast.color}",hoverColor:"{primary.contrast.color}",activeColor:"{primary.contrast.color}",focusRing:{color:"{primary.color}",shadow:"none"}},secondary:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",borderColor:"{surface.800}",hoverBorderColor:"{surface.700}",activeBorderColor:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}",focusRing:{color:"{surface.300}",shadow:"none"}},info:{background:"{sky.400}",hoverBackground:"{sky.300}",activeBackground:"{sky.200}",borderColor:"{sky.400}",hoverBorderColor:"{sky.300}",activeBorderColor:"{sky.200}",color:"{sky.950}",hoverColor:"{sky.950}",activeColor:"{sky.950}",focusRing:{color:"{sky.400}",shadow:"none"}},success:{background:"{green.400}",hoverBackground:"{green.300}",activeBackground:"{green.200}",borderColor:"{green.400}",hoverBorderColor:"{green.300}",activeBorderColor:"{green.200}",color:"{green.950}",hoverColor:"{green.950}",activeColor:"{green.950}",focusRing:{color:"{green.400}",shadow:"none"}},warn:{background:"{orange.400}",hoverBackground:"{orange.300}",activeBackground:"{orange.200}",borderColor:"{orange.400}",hoverBorderColor:"{orange.300}",activeBorderColor:"{orange.200}",color:"{orange.950}",hoverColor:"{orange.950}",activeColor:"{orange.950}",focusRing:{color:"{orange.400}",shadow:"none"}},help:{background:"{purple.400}",hoverBackground:"{purple.300}",activeBackground:"{purple.200}",borderColor:"{purple.400}",hoverBorderColor:"{purple.300}",activeBorderColor:"{purple.200}",color:"{purple.950}",hoverColor:"{purple.950}",activeColor:"{purple.950}",focusRing:{color:"{purple.400}",shadow:"none"}},danger:{background:"{red.400}",hoverBackground:"{red.300}",activeBackground:"{red.200}",borderColor:"{red.400}",hoverBorderColor:"{red.300}",activeBorderColor:"{red.200}",color:"{red.950}",hoverColor:"{red.950}",activeColor:"{red.950}",focusRing:{color:"{red.400}",shadow:"none"}},contrast:{background:"{surface.0}",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",borderColor:"{surface.0}",hoverBorderColor:"{surface.100}",activeBorderColor:"{surface.200}",color:"{surface.950}",hoverColor:"{surface.950}",activeColor:"{surface.950}",focusRing:{color:"{surface.0}",shadow:"none"}}},outlined:{primary:{hoverBackground:"color-mix(in srgb, {primary.color}, transparent 96%)",activeBackground:"color-mix(in srgb, {primary.color}, transparent 84%)",borderColor:"{primary.700}",color:"{primary.color}"},secondary:{hoverBackground:"rgba(255,255,255,0.04)",activeBackground:"rgba(255,255,255,0.16)",borderColor:"{surface.700}",color:"{surface.400}"},success:{hoverBackground:"color-mix(in srgb, {green.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {green.400}, transparent 84%)",borderColor:"{green.700}",color:"{green.400}"},info:{hoverBackground:"color-mix(in srgb, {sky.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {sky.400}, transparent 84%)",borderColor:"{sky.700}",color:"{sky.400}"},warn:{hoverBackground:"color-mix(in srgb, {orange.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {orange.400}, transparent 84%)",borderColor:"{orange.700}",color:"{orange.400}"},help:{hoverBackground:"color-mix(in srgb, {purple.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {purple.400}, transparent 84%)",borderColor:"{purple.700}",color:"{purple.400}"},danger:{hoverBackground:"color-mix(in srgb, {red.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {red.400}, transparent 84%)",borderColor:"{red.700}",color:"{red.400}"},contrast:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.500}",color:"{surface.0}"},plain:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.600}",color:"{surface.0}"}},text:{primary:{hoverBackground:"color-mix(in srgb, {primary.color}, transparent 96%)",activeBackground:"color-mix(in srgb, {primary.color}, transparent 84%)",color:"{primary.color}"},secondary:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.400}"},success:{hoverBackground:"color-mix(in srgb, {green.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {green.400}, transparent 84%)",color:"{green.400}"},info:{hoverBackground:"color-mix(in srgb, {sky.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {sky.400}, transparent 84%)",color:"{sky.400}"},warn:{hoverBackground:"color-mix(in srgb, {orange.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {orange.400}, transparent 84%)",color:"{orange.400}"},help:{hoverBackground:"color-mix(in srgb, {purple.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {purple.400}, transparent 84%)",color:"{purple.400}"},danger:{hoverBackground:"color-mix(in srgb, {red.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {red.400}, transparent 84%)",color:"{red.400}"},contrast:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.0}"},plain:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.0}"}},link:{color:"{primary.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}}},_1={root:S1,colorScheme:k1},$1={background:"{content.background}",borderRadius:"{border.radius.xl}",color:"{content.color}",shadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)"},x1={padding:"1.25rem",gap:"0.5rem"},R1={gap:"0.5rem"},w1={fontSize:"1.25rem",fontWeight:"500"},T1={color:"{text.muted.color}"},P1={root:$1,body:x1,caption:R1,title:w1,subtitle:T1},E1={transitionDuration:"{transition.duration}"},O1={gap:"0.25rem"},B1={padding:"1rem",gap:"0.5rem"},A1={width:"2rem",height:"0.5rem",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},L1={light:{indicator:{background:"{surface.200}",hoverBackground:"{surface.300}",activeBackground:"{primary.color}"}},dark:{indicator:{background:"{surface.700}",hoverBackground:"{surface.600}",activeBackground:"{primary.color}"}}},I1={root:E1,content:O1,indicatorList:B1,indicator:A1,colorScheme:L1},D1={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},j1={width:"2.5rem",color:"{form.field.icon.color}"},M1={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},N1={padding:"{list.padding}",gap:"{list.gap}",mobileIndent:"1rem"},F1={focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}",icon:{color:"{list.option.icon.color}",focusColor:"{list.option.icon.focus.color}",size:"0.875rem"}},z1={color:"{form.field.icon.color}"},H1={root:D1,dropdown:j1,overlay:M1,list:N1,option:F1,clearIcon:z1},U1={borderRadius:"{border.radius.sm}",width:"1.25rem",height:"1.25rem",background:"{form.field.background}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.border.color}",checkedBorderColor:"{primary.color}",checkedHoverBorderColor:"{primary.hover.color}",checkedFocusBorderColor:"{primary.color}",checkedDisabledBorderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{width:"1rem",height:"1rem"},lg:{width:"1.5rem",height:"1.5rem"}},V1={size:"0.875rem",color:"{form.field.color}",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}",disabledColor:"{form.field.disabled.color}",sm:{size:"0.75rem"},lg:{size:"1rem"}},W1={root:U1,icon:V1},K1={borderRadius:"16px",paddingX:"0.75rem",paddingY:"0.5rem",gap:"0.5rem",transitionDuration:"{transition.duration}"},q1={width:"2rem",height:"2rem"},G1={size:"1rem"},J1={size:"1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"}},Y1={light:{root:{background:"{surface.100}",color:"{surface.800}"},icon:{color:"{surface.800}"},removeIcon:{color:"{surface.800}"}},dark:{root:{background:"{surface.800}",color:"{surface.0}"},icon:{color:"{surface.0}"},removeIcon:{color:"{surface.0}"}}},X1={root:K1,image:q1,icon:G1,removeIcon:J1,colorScheme:Y1},Z1={transitionDuration:"{transition.duration}"},Q1={width:"1.5rem",height:"1.5rem",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},eC={shadow:"{overlay.popover.shadow}",borderRadius:"{overlay.popover.borderRadius}"},tC={light:{panel:{background:"{surface.800}",borderColor:"{surface.900}"},handle:{color:"{surface.0}"}},dark:{panel:{background:"{surface.900}",borderColor:"{surface.700}"},handle:{color:"{surface.0}"}}},rC={root:Z1,preview:Q1,panel:eC,colorScheme:tC},oC={size:"2rem",color:"{overlay.modal.color}"},nC={gap:"1rem"},iC={icon:oC,content:nC},sC={background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",color:"{overlay.popover.color}",borderRadius:"{overlay.popover.border.radius}",shadow:"{overlay.popover.shadow}",gutter:"10px",arrowOffset:"1.25rem"},aC={padding:"{overlay.popover.padding}",gap:"1rem"},lC={size:"1.5rem",color:"{overlay.popover.color}"},cC={gap:"0.5rem",padding:"0 {overlay.popover.padding} {overlay.popover.padding} {overlay.popover.padding}"},uC={root:sC,content:aC,icon:lC,footer:cC},dC={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},fC={padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},pC={focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},gC={mobileIndent:"1rem"},hC={size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},mC={borderColor:"{content.border.color}"},bC={root:dC,list:fC,item:pC,submenu:gC,submenuIcon:hC,separator:mC},vC={transitionDuration:"{transition.duration}"},yC={background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},CC={background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{datatable.border.color}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",gap:"0.5rem",padding:"0.75rem 1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},SC={fontWeight:"600"},kC={background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},_C={borderColor:"{datatable.border.color}",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},$C={background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},xC={fontWeight:"600"},RC={background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",sm:{padding:"0.375rem 0.5rem"},lg:{padding:"1rem 1.25rem"}},wC={color:"{primary.color}"},TC={width:"0.5rem"},PC={width:"1px",color:"{primary.color}"},EC={color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",size:"0.875rem"},OC={size:"2rem"},BC={hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",selectedHoverColor:"{primary.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},AC={inlineGap:"0.5rem",overlaySelect:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},overlayPopover:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",borderRadius:"{overlay.popover.border.radius}",color:"{overlay.popover.color}",shadow:"{overlay.popover.shadow}",padding:"{overlay.popover.padding}",gap:"0.5rem"},rule:{borderColor:"{content.border.color}"},constraintList:{padding:"{list.padding}",gap:"{list.gap}"},constraint:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",separator:{borderColor:"{content.border.color}"},padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"}},LC={borderColor:"{datatable.border.color}",borderWidth:"0 0 1px 0"},IC={borderColor:"{datatable.border.color}",borderWidth:"0 0 1px 0"},DC={light:{root:{borderColor:"{content.border.color}"},row:{stripedBackground:"{surface.50}"},bodyCell:{selectedBorderColor:"{primary.100}"}},dark:{root:{borderColor:"{surface.800}"},row:{stripedBackground:"{surface.950}"},bodyCell:{selectedBorderColor:"{primary.900}"}}},jC={root:vC,header:yC,headerCell:CC,columnTitle:SC,row:kC,bodyCell:_C,footerCell:$C,columnFooter:xC,footer:RC,dropPoint:wC,columnResizer:TC,resizeIndicator:PC,sortIcon:EC,loadingIcon:OC,rowToggleButton:BC,filter:AC,paginatorTop:LC,paginatorBottom:IC,colorScheme:DC},MC={borderColor:"transparent",borderWidth:"0",borderRadius:"0",padding:"0"},NC={background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",borderRadius:"0"},FC={background:"{content.background}",color:"{content.color}",borderColor:"transparent",borderWidth:"0",padding:"0",borderRadius:"0"},zC={background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"1px 0 0 0",padding:"0.75rem 1rem",borderRadius:"0"},HC={borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},UC={borderColor:"{content.border.color}",borderWidth:"1px 0 0 0"},VC={root:MC,header:NC,content:FC,footer:zC,paginatorTop:HC,paginatorBottom:UC},WC={transitionDuration:"{transition.duration}"},KC={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.popover.shadow}",padding:"{overlay.popover.padding}"},qC={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",padding:"0 0 0.5rem 0"},GC={gap:"0.5rem",fontWeight:"500"},JC={width:"2.5rem",sm:{width:"2rem"},lg:{width:"3rem"},borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},YC={color:"{form.field.icon.color}"},XC={hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}"},ZC={hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}"},QC={borderColor:"{content.border.color}",gap:"{overlay.popover.padding}"},eS={margin:"0.5rem 0 0 0"},tS={padding:"0.25rem",fontWeight:"500",color:"{content.color}"},rS={hoverBackground:"{content.hover.background}",selectedBackground:"{primary.color}",rangeSelectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{primary.contrast.color}",rangeSelectedColor:"{highlight.color}",width:"2rem",height:"2rem",borderRadius:"50%",padding:"0.25rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},oS={margin:"0.5rem 0 0 0"},nS={padding:"0.375rem",borderRadius:"{content.border.radius}"},iS={margin:"0.5rem 0 0 0"},sS={padding:"0.375rem",borderRadius:"{content.border.radius}"},aS={padding:"0.5rem 0 0 0",borderColor:"{content.border.color}"},lS={padding:"0.5rem 0 0 0",borderColor:"{content.border.color}",gap:"0.5rem",buttonGap:"0.25rem"},cS={light:{dropdown:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"},today:{background:"{surface.200}",color:"{surface.900}"}},dark:{dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"},today:{background:"{surface.700}",color:"{surface.0}"}}},uS={root:WC,panel:KC,header:qC,title:GC,dropdown:JC,inputIcon:YC,selectMonth:XC,selectYear:ZC,group:QC,dayView:eS,weekDay:tS,date:rS,monthView:oS,month:nS,yearView:iS,year:sS,buttonbar:aS,timePicker:lS,colorScheme:cS},dS={background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",borderRadius:"{overlay.modal.border.radius}",shadow:"{overlay.modal.shadow}"},fS={padding:"{overlay.modal.padding}",gap:"0.5rem"},pS={fontSize:"1.25rem",fontWeight:"600"},gS={padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"},hS={padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}",gap:"0.5rem"},mS={root:dS,header:fS,title:pS,content:gS,footer:hS},bS={borderColor:"{content.border.color}"},vS={background:"{content.background}",color:"{text.color}"},yS={margin:"1rem 0",padding:"0 1rem",content:{padding:"0 0.5rem"}},CS={margin:"0 1rem",padding:"0.5rem 0",content:{padding:"0.5rem 0"}},SS={root:bS,content:vS,horizontal:yS,vertical:CS},kS={background:"rgba(255, 255, 255, 0.1)",borderColor:"rgba(255, 255, 255, 0.2)",padding:"0.5rem",borderRadius:"{border.radius.xl}"},_S={borderRadius:"{content.border.radius}",padding:"0.5rem",size:"3rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},$S={root:kS,item:_S},xS={background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",shadow:"{overlay.modal.shadow}"},RS={padding:"{overlay.modal.padding}"},wS={fontSize:"1.5rem",fontWeight:"600"},TS={padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"},PS={padding:"{overlay.modal.padding}"},ES={root:xS,header:RS,title:wS,content:TS,footer:PS},OS={background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}"},BS={color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},AS={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}",padding:"{list.padding}"},LS={focusBackground:"{list.option.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},IS={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},DS={toolbar:OS,toolbarItem:BS,overlay:AS,overlayOption:LS,content:IS},jS={background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",padding:"0 1.125rem 1.125rem 1.125rem",transitionDuration:"{transition.duration}"},MS={background:"{content.background}",hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",borderRadius:"{content.border.radius}",borderWidth:"1px",borderColor:"transparent",padding:"0.5rem 0.75rem",gap:"0.5rem",fontWeight:"600",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},NS={color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}"},FS={padding:"0"},zS={root:jS,legend:MS,toggleIcon:NS,content:FS},HS={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},US={background:"transparent",color:"{text.color}",padding:"1.125rem",borderColor:"unset",borderWidth:"0",borderRadius:"0",gap:"0.5rem"},VS={highlightBorderColor:"{primary.color}",padding:"0 1.125rem 1.125rem 1.125rem",gap:"1rem"},WS={padding:"1rem",gap:"1rem",borderColor:"{content.border.color}",info:{gap:"0.5rem"}},KS={gap:"0.5rem"},qS={height:"0.25rem"},GS={gap:"0.5rem"},JS={root:HS,header:US,content:VS,file:WS,fileList:KS,progressbar:qS,basic:GS},YS={color:"{form.field.float.label.color}",focusColor:"{form.field.float.label.focus.color}",activeColor:"{form.field.float.label.active.color}",invalidColor:"{form.field.float.label.invalid.color}",transitionDuration:"0.2s",positionX:"{form.field.padding.x}",positionY:"{form.field.padding.y}",fontWeight:"500",active:{fontSize:"0.75rem",fontWeight:"400"}},XS={active:{top:"-1.25rem"}},ZS={input:{paddingTop:"1.5rem",paddingBottom:"{form.field.padding.y}"},active:{top:"{form.field.padding.y}"}},QS={borderRadius:"{border.radius.xs}",active:{background:"{form.field.background}",padding:"0 0.125rem"}},e5={root:YS,over:XS,in:ZS,on:QS},t5={borderWidth:"1px",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},r5={background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.100}",hoverColor:"{surface.0}",size:"3rem",gutter:"0.5rem",prev:{borderRadius:"50%"},next:{borderRadius:"50%"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},o5={size:"1.5rem"},n5={background:"{content.background}",padding:"1rem 0.25rem"},i5={size:"2rem",borderRadius:"{content.border.radius}",gutter:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},s5={size:"1rem"},a5={background:"rgba(0, 0, 0, 0.5)",color:"{surface.100}",padding:"1rem"},l5={gap:"0.5rem",padding:"1rem"},c5={width:"1rem",height:"1rem",activeBackground:"{primary.color}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},u5={background:"rgba(0, 0, 0, 0.5)"},d5={background:"rgba(255, 255, 255, 0.4)",hoverBackground:"rgba(255, 255, 255, 0.6)",activeBackground:"rgba(255, 255, 255, 0.9)"},f5={size:"3rem",gutter:"0.5rem",background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.50}",hoverColor:"{surface.0}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},p5={size:"1.5rem"},g5={light:{thumbnailNavButton:{hoverBackground:"{surface.100}",color:"{surface.600}",hoverColor:"{surface.700}"},indicatorButton:{background:"{surface.200}",hoverBackground:"{surface.300}"}},dark:{thumbnailNavButton:{hoverBackground:"{surface.700}",color:"{surface.400}",hoverColor:"{surface.0}"},indicatorButton:{background:"{surface.700}",hoverBackground:"{surface.600}"}}},h5={root:t5,navButton:r5,navIcon:o5,thumbnailsContent:n5,thumbnailNavButton:i5,thumbnailNavButtonIcon:s5,caption:a5,indicatorList:l5,indicatorButton:c5,insetIndicatorList:u5,insetIndicatorButton:d5,closeButton:f5,closeButtonIcon:p5,colorScheme:g5},m5={color:"{form.field.icon.color}"},b5={icon:m5},v5={color:"{form.field.float.label.color}",focusColor:"{form.field.float.label.focus.color}",invalidColor:"{form.field.float.label.invalid.color}",transitionDuration:"0.2s",positionX:"{form.field.padding.x}",top:"{form.field.padding.y}",fontSize:"0.75rem",fontWeight:"400"},y5={paddingTop:"1.5rem",paddingBottom:"{form.field.padding.y}"},C5={root:v5,input:y5},S5={transitionDuration:"{transition.duration}"},k5={icon:{size:"1.5rem"},mask:{background:"{mask.background}",color:"{mask.color}"}},_5={position:{left:"auto",right:"1rem",top:"1rem",bottom:"auto"},blur:"8px",background:"rgba(255,255,255,0.1)",borderColor:"rgba(255,255,255,0.2)",borderWidth:"1px",borderRadius:"30px",padding:".5rem",gap:"0.5rem"},$5={hoverBackground:"rgba(255,255,255,0.1)",color:"{surface.50}",hoverColor:"{surface.0}",size:"3rem",iconSize:"1.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},x5={root:S5,preview:k5,toolbar:_5,action:$5},R5={size:"15px",hoverSize:"30px",background:"rgba(255,255,255,0.3)",hoverBackground:"rgba(255,255,255,0.3)",borderColor:"unset",hoverBorderColor:"unset",borderWidth:"0",borderRadius:"50%",transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"rgba(255,255,255,0.3)",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},w5={handle:R5},T5={padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{content.border.radius}",gap:"0.5rem"},P5={fontWeight:"500"},E5={size:"1rem"},O5={light:{info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)"},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)"},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)"},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)"},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)"},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)"}},dark:{info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)"},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)"},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)"},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)"},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)"},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)"}}},B5={root:T5,text:P5,icon:E5,colorScheme:O5},A5={padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{transition.duration}"},L5={hoverBackground:"{content.hover.background}",hoverColor:"{content.hover.color}"},I5={root:A5,display:L5},D5={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},j5={borderRadius:"{border.radius.sm}"},M5={light:{chip:{focusBackground:"{surface.200}",color:"{surface.800}"}},dark:{chip:{focusBackground:"{surface.700}",color:"{surface.0}"}}},N5={root:D5,chip:j5,colorScheme:M5},F5={background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.icon.color}",borderRadius:"{form.field.border.radius}",padding:"0.5rem",minWidth:"2.5rem"},z5={addon:F5},H5={transitionDuration:"{transition.duration}"},U5={width:"2.5rem",borderRadius:"{form.field.border.radius}",verticalPadding:"{form.field.padding.y}"},V5={light:{button:{background:"transparent",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",color:"{surface.400}",hoverColor:"{surface.500}",activeColor:"{surface.600}"}},dark:{button:{background:"transparent",hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",color:"{surface.400}",hoverColor:"{surface.300}",activeColor:"{surface.200}"}}},W5={root:H5,button:U5,colorScheme:V5},K5={gap:"0.5rem"},q5={width:"2.5rem",sm:{width:"2rem"},lg:{width:"3rem"}},G5={root:K5,input:q5},J5={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},Y5={root:J5},X5={transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Z5={background:"{primary.color}"},Q5={background:"{content.border.color}"},ek={color:"{text.muted.color}"},tk={root:X5,value:Z5,range:Q5,text:ek},rk={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",borderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",shadow:"{form.field.shadow}",borderRadius:"{form.field.border.radius}",transitionDuration:"{form.field.transition.duration}"},ok={padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},nk={focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},ik={background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},sk={color:"{list.option.color}",gutterStart:"-0.375rem",gutterEnd:"0.375rem"},ak={padding:"{list.option.padding}"},lk={light:{option:{stripedBackground:"{surface.50}"}},dark:{option:{stripedBackground:"{surface.900}"}}},ck={root:rk,list:ok,option:nk,optionGroup:ik,checkmark:sk,emptyMessage:ak,colorScheme:lk},uk={background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",verticalOrientation:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},horizontalOrientation:{padding:"0.5rem 0.75rem",gap:"0.5rem"},transitionDuration:"{transition.duration}"},dk={borderRadius:"{content.border.radius}",padding:"{navigation.item.padding}"},fk={focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},pk={padding:"0",background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",shadow:"{overlay.navigation.shadow}",gap:"0.5rem"},gk={padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},hk={padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background.}",color:"{navigation.submenu.label.color}"},mk={size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},bk={borderColor:"{content.border.color}"},vk={borderRadius:"50%",size:"1.75rem",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",hoverBackground:"{content.hover.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},yk={root:uk,baseItem:dk,item:fk,overlay:pk,submenu:gk,submenuLabel:hk,submenuIcon:mk,separator:bk,mobileButton:vk},Ck={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},Sk={padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},kk={focusBackground:"{navigation.item.focus.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}"}},_k={padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background}",color:"{navigation.submenu.label.color}"},$k={borderColor:"{content.border.color}"},xk={root:Ck,list:Sk,item:kk,submenuLabel:_k,separator:$k},Rk={background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",padding:"0.5rem 0.75rem",transitionDuration:"{transition.duration}"},wk={borderRadius:"{content.border.radius}",padding:"{navigation.item.padding}"},Tk={focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},Pk={padding:"{navigation.list.padding}",gap:"{navigation.list.gap}",background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",mobileIndent:"1rem",icon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"}},Ek={borderColor:"{content.border.color}"},Ok={borderRadius:"50%",size:"1.75rem",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",hoverBackground:"{content.hover.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},Bk={root:Rk,baseItem:wk,item:Tk,submenu:Pk,separator:Ek,mobileButton:Ok},Ak={borderRadius:"{content.border.radius}",borderWidth:"1px",transitionDuration:"{transition.duration}"},Lk={padding:"0.5rem 0.75rem",gap:"0.5rem",sm:{padding:"0.375rem 0.625rem"},lg:{padding:"0.625rem 0.875rem"}},Ik={fontSize:"1rem",fontWeight:"500",sm:{fontSize:"0.875rem"},lg:{fontSize:"1.125rem"}},Dk={size:"1.125rem",sm:{size:"1rem"},lg:{size:"1.25rem"}},jk={width:"1.75rem",height:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"}},Mk={size:"1rem",sm:{size:"0.875rem"},lg:{size:"1.125rem"}},Nk={root:{borderWidth:"1px"}},Fk={content:{padding:"0"}},zk={light:{info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"{blue.100}",focusRing:{color:"{blue.600}",shadow:"none"}},outlined:{color:"{blue.600}",borderColor:"{blue.600}"},simple:{color:"{blue.600}"}},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"{green.100}",focusRing:{color:"{green.600}",shadow:"none"}},outlined:{color:"{green.600}",borderColor:"{green.600}"},simple:{color:"{green.600}"}},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"{yellow.100}",focusRing:{color:"{yellow.600}",shadow:"none"}},outlined:{color:"{yellow.600}",borderColor:"{yellow.600}"},simple:{color:"{yellow.600}"}},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"{red.100}",focusRing:{color:"{red.600}",shadow:"none"}},outlined:{color:"{red.600}",borderColor:"{red.600}"},simple:{color:"{red.600}"}},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.200}",focusRing:{color:"{surface.600}",shadow:"none"}},outlined:{color:"{surface.500}",borderColor:"{surface.500}"},simple:{color:"{surface.500}"}},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.800}",focusRing:{color:"{surface.50}",shadow:"none"}},outlined:{color:"{surface.950}",borderColor:"{surface.950}"},simple:{color:"{surface.950}"}}},dark:{info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{blue.500}",shadow:"none"}},outlined:{color:"{blue.500}",borderColor:"{blue.500}"},simple:{color:"{blue.500}"}},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{green.500}",shadow:"none"}},outlined:{color:"{green.500}",borderColor:"{green.500}"},simple:{color:"{green.500}"}},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{yellow.500}",shadow:"none"}},outlined:{color:"{yellow.500}",borderColor:"{yellow.500}"},simple:{color:"{yellow.500}"}},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{red.500}",shadow:"none"}},outlined:{color:"{red.500}",borderColor:"{red.500}"},simple:{color:"{red.500}"}},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.700}",focusRing:{color:"{surface.300}",shadow:"none"}},outlined:{color:"{surface.400}",borderColor:"{surface.400}"},simple:{color:"{surface.400}"}},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.100}",focusRing:{color:"{surface.950}",shadow:"none"}},outlined:{color:"{surface.0}",borderColor:"{surface.0}"},simple:{color:"{surface.0}"}}}},Hk={root:Ak,content:Lk,text:Ik,icon:Dk,closeButton:jk,closeIcon:Mk,outlined:Nk,simple:Fk,colorScheme:zk},Uk={borderRadius:"{content.border.radius}",gap:"1rem"},Vk={background:"{content.border.color}",size:"0.5rem"},Wk={gap:"0.5rem"},Kk={size:"0.5rem"},qk={size:"1rem"},Gk={verticalGap:"0.5rem",horizontalGap:"1rem"},Jk={root:Uk,meters:Vk,label:Wk,labelMarker:Kk,labelIcon:qk,labelList:Gk},Yk={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},Xk={width:"2.5rem",color:"{form.field.icon.color}"},Zk={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},Qk={padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},e_={focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}",gap:"0.5rem"},t_={background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},r_={color:"{form.field.icon.color}"},o_={borderRadius:"{border.radius.sm}"},n_={padding:"{list.option.padding}"},i_={root:Yk,dropdown:Xk,overlay:Zk,list:Qk,option:e_,optionGroup:t_,chip:o_,clearIcon:r_,emptyMessage:n_},s_={gap:"1.125rem"},a_={gap:"0.5rem"},l_={root:s_,controls:a_},c_={gutter:"0.75rem",transitionDuration:"{transition.duration}"},u_={background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{content.border.color}",color:"{content.color}",selectedColor:"{highlight.color}",hoverColor:"{content.hover.color}",padding:"0.75rem 1rem",toggleablePadding:"0.75rem 1rem 1.25rem 1rem",borderRadius:"{content.border.radius}"},d_={background:"{content.background}",hoverBackground:"{content.hover.background}",borderColor:"{content.border.color}",color:"{text.muted.color}",hoverColor:"{text.color}",size:"1.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},f_={color:"{content.border.color}",borderRadius:"{content.border.radius}",height:"24px"},p_={root:c_,node:u_,nodeToggleButton:d_,connector:f_},g_={outline:{width:"2px",color:"{content.background}"}},h_={root:g_},m_={padding:"0.5rem 1rem",gap:"0.25rem",borderRadius:"{content.border.radius}",background:"{content.background}",color:"{content.color}",transitionDuration:"{transition.duration}"},b_={background:"transparent",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedColor:"{highlight.color}",width:"2.5rem",height:"2.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},v_={color:"{text.muted.color}"},y_={maxWidth:"2.5rem"},C_={root:m_,navButton:b_,currentPageReport:v_,jumpToPageInput:y_},S_={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},k_={background:"transparent",color:"{text.color}",padding:"1.125rem",borderColor:"{content.border.color}",borderWidth:"0",borderRadius:"0"},__={padding:"0.375rem 1.125rem"},$_={fontWeight:"600"},x_={padding:"0 1.125rem 1.125rem 1.125rem"},R_={padding:"0 1.125rem 1.125rem 1.125rem"},w_={root:S_,header:k_,toggleableHeader:__,title:$_,content:x_,footer:R_},T_={gap:"0.5rem",transitionDuration:"{transition.duration}"},P_={background:"{content.background}",borderColor:"{content.border.color}",borderWidth:"1px",color:"{content.color}",padding:"0.25rem 0.25rem",borderRadius:"{content.border.radius}",first:{borderWidth:"1px",topBorderRadius:"{content.border.radius}"},last:{borderWidth:"1px",bottomBorderRadius:"{content.border.radius}"}},E_={focusBackground:"{navigation.item.focus.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",gap:"0.5rem",padding:"{navigation.item.padding}",borderRadius:"{content.border.radius}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}"}},O_={indent:"1rem"},B_={color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}"},A_={root:T_,panel:P_,item:E_,submenu:O_,submenuIcon:B_},L_={background:"{content.border.color}",borderRadius:"{content.border.radius}",height:".75rem"},I_={color:"{form.field.icon.color}"},D_={background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",borderRadius:"{overlay.popover.border.radius}",color:"{overlay.popover.color}",padding:"{overlay.popover.padding}",shadow:"{overlay.popover.shadow}"},j_={gap:"0.5rem"},M_={light:{strength:{weakBackground:"{red.500}",mediumBackground:"{amber.500}",strongBackground:"{green.500}"}},dark:{strength:{weakBackground:"{red.400}",mediumBackground:"{amber.400}",strongBackground:"{green.400}"}}},N_={meter:L_,icon:I_,overlay:D_,content:j_,colorScheme:M_},F_={gap:"1.125rem"},z_={gap:"0.5rem"},H_={root:F_,controls:z_},U_={background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",color:"{overlay.popover.color}",borderRadius:"{overlay.popover.border.radius}",shadow:"{overlay.popover.shadow}",gutter:"10px",arrowOffset:"1.25rem"},V_={padding:"{overlay.popover.padding}"},W_={root:U_,content:V_},K_={background:"{content.border.color}",borderRadius:"{content.border.radius}",height:"1.25rem"},q_={background:"{primary.color}"},G_={color:"{primary.contrast.color}",fontSize:"0.75rem",fontWeight:"600"},J_={root:K_,value:q_,label:G_},Y_={light:{root:{colorOne:"{red.500}",colorTwo:"{blue.500}",colorThree:"{green.500}",colorFour:"{yellow.500}"}},dark:{root:{colorOne:"{red.400}",colorTwo:"{blue.400}",colorThree:"{green.400}",colorFour:"{yellow.400}"}}},X_={colorScheme:Y_},Z_={width:"1.25rem",height:"1.25rem",background:"{form.field.background}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.border.color}",checkedBorderColor:"{primary.color}",checkedHoverBorderColor:"{primary.hover.color}",checkedFocusBorderColor:"{primary.color}",checkedDisabledBorderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{width:"1rem",height:"1rem"},lg:{width:"1.5rem",height:"1.5rem"}},Q_={size:"0.75rem",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}",disabledColor:"{form.field.disabled.color}",sm:{size:"0.5rem"},lg:{size:"1rem"}},e$={root:Z_,icon:Q_},t$={gap:"0.25rem",transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},r$={size:"1rem",color:"{text.muted.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"},o$={root:t$,icon:r$},n$={light:{root:{background:"rgba(0,0,0,0.1)"}},dark:{root:{background:"rgba(255,255,255,0.3)"}}},i$={colorScheme:n$},s$={transitionDuration:"{transition.duration}"},a$={size:"9px",borderRadius:"{border.radius.sm}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},l$={light:{bar:{background:"{surface.100}"}},dark:{bar:{background:"{surface.800}"}}},c$={root:s$,bar:a$,colorScheme:l$},u$={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},d$={width:"2.5rem",color:"{form.field.icon.color}"},f$={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},p$={padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},g$={focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},h$={background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},m$={color:"{form.field.icon.color}"},b$={color:"{list.option.color}",gutterStart:"-0.375rem",gutterEnd:"0.375rem"},v$={padding:"{list.option.padding}"},y$={root:u$,dropdown:d$,overlay:f$,list:p$,option:g$,optionGroup:h$,clearIcon:m$,checkmark:b$,emptyMessage:v$},C$={borderRadius:"{form.field.border.radius}"},S$={light:{root:{invalidBorderColor:"{form.field.invalid.border.color}"}},dark:{root:{invalidBorderColor:"{form.field.invalid.border.color}"}}},k$={root:C$,colorScheme:S$},_$={borderRadius:"{content.border.radius}"},$$={light:{root:{background:"{surface.200}",animationBackground:"rgba(255,255,255,0.4)"}},dark:{root:{background:"rgba(255, 255, 255, 0.06)",animationBackground:"rgba(255, 255, 255, 0.04)"}}},x$={root:_$,colorScheme:$$},R$={transitionDuration:"{transition.duration}"},w$={background:"{content.border.color}",borderRadius:"{content.border.radius}",size:"3px"},T$={background:"{primary.color}"},P$={width:"20px",height:"20px",borderRadius:"50%",background:"{content.border.color}",hoverBackground:"{content.border.color}",content:{borderRadius:"50%",hoverBackground:"{content.background}",width:"16px",height:"16px",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.08), 0px 1px 1px 0px rgba(0, 0, 0, 0.14)"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},E$={light:{handle:{content:{background:"{surface.0}"}}},dark:{handle:{content:{background:"{surface.950}"}}}},O$={root:R$,track:w$,range:T$,handle:P$,colorScheme:E$},B$={gap:"0.5rem",transitionDuration:"{transition.duration}"},A$={root:B$},L$={borderRadius:"{form.field.border.radius}",roundedBorderRadius:"2rem",raisedShadow:"0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)"},I$={root:L$},D$={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",transitionDuration:"{transition.duration}"},j$={background:"{content.border.color}"},M$={size:"24px",background:"transparent",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},N$={root:D$,gutter:j$,handle:M$},F$={transitionDuration:"{transition.duration}"},z$={background:"{content.border.color}",activeBackground:"{primary.color}",margin:"0 0 0 1.625rem",size:"2px"},H$={padding:"0.5rem",gap:"1rem"},U$={padding:"0",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},gap:"0.5rem"},V$={color:"{text.muted.color}",activeColor:"{primary.color}",fontWeight:"500"},W$={background:"{content.background}",activeBackground:"{content.background}",borderColor:"{content.border.color}",activeBorderColor:"{content.border.color}",color:"{text.muted.color}",activeColor:"{primary.color}",size:"2rem",fontSize:"1.143rem",fontWeight:"500",borderRadius:"50%",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"},K$={padding:"0.875rem 0.5rem 1.125rem 0.5rem"},q$={background:"{content.background}",color:"{content.color}",padding:"0",indent:"1rem"},G$={root:F$,separator:z$,step:H$,stepHeader:U$,stepTitle:V$,stepNumber:W$,steppanels:K$,steppanel:q$},J$={transitionDuration:"{transition.duration}"},Y$={background:"{content.border.color}"},X$={borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},gap:"0.5rem"},Z$={color:"{text.muted.color}",activeColor:"{primary.color}",fontWeight:"500"},Q$={background:"{content.background}",activeBackground:"{content.background}",borderColor:"{content.border.color}",activeBorderColor:"{content.border.color}",color:"{text.muted.color}",activeColor:"{primary.color}",size:"2rem",fontSize:"1.143rem",fontWeight:"500",borderRadius:"50%",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"},ex={root:J$,separator:Y$,itemLink:X$,itemLabel:Z$,itemNumber:Q$},tx={transitionDuration:"{transition.duration}"},rx={borderWidth:"0 0 1px 0",background:"{content.background}",borderColor:"{content.border.color}"},ox={background:"transparent",hoverBackground:"transparent",activeBackground:"transparent",borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",hoverBorderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}",padding:"1rem 1.125rem",fontWeight:"600",margin:"0 0 -1px 0",gap:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},nx={color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},ix={height:"1px",bottom:"-1px",background:"{primary.color}"},sx={root:tx,tablist:rx,item:ox,itemIcon:nx,activeBar:ix},ax={transitionDuration:"{transition.duration}"},lx={borderWidth:"0 0 1px 0",background:"{content.background}",borderColor:"{content.border.color}"},cx={background:"transparent",hoverBackground:"transparent",activeBackground:"transparent",borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",hoverBorderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}",padding:"1rem 1.125rem",fontWeight:"600",margin:"0 0 -1px 0",gap:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},ux={background:"{content.background}",color:"{content.color}",padding:"0.875rem 1.125rem 1.125rem 1.125rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"inset {focus.ring.shadow}"}},dx={background:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",width:"2.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},fx={height:"1px",bottom:"-1px",background:"{primary.color}"},px={light:{navButton:{shadow:"0px 0px 10px 50px rgba(255, 255, 255, 0.6)"}},dark:{navButton:{shadow:"0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"}}},gx={root:ax,tablist:lx,tab:cx,tabpanel:ux,navButton:dx,activeBar:fx,colorScheme:px},hx={transitionDuration:"{transition.duration}"},mx={background:"{content.background}",borderColor:"{content.border.color}"},bx={borderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},vx={background:"{content.background}",color:"{content.color}"},yx={background:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}"},Cx={light:{navButton:{shadow:"0px 0px 10px 50px rgba(255, 255, 255, 0.6)"}},dark:{navButton:{shadow:"0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"}}},Sx={root:hx,tabList:mx,tab:bx,tabPanel:vx,navButton:yx,colorScheme:Cx},kx={fontSize:"0.875rem",fontWeight:"700",padding:"0.25rem 0.5rem",gap:"0.25rem",borderRadius:"{content.border.radius}",roundedBorderRadius:"{border.radius.xl}"},_x={size:"0.75rem"},$x={light:{primary:{background:"{primary.100}",color:"{primary.700}"},secondary:{background:"{surface.100}",color:"{surface.600}"},success:{background:"{green.100}",color:"{green.700}"},info:{background:"{sky.100}",color:"{sky.700}"},warn:{background:"{orange.100}",color:"{orange.700}"},danger:{background:"{red.100}",color:"{red.700}"},contrast:{background:"{surface.950}",color:"{surface.0}"}},dark:{primary:{background:"color-mix(in srgb, {primary.500}, transparent 84%)",color:"{primary.300}"},secondary:{background:"{surface.800}",color:"{surface.300}"},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",color:"{green.300}"},info:{background:"color-mix(in srgb, {sky.500}, transparent 84%)",color:"{sky.300}"},warn:{background:"color-mix(in srgb, {orange.500}, transparent 84%)",color:"{orange.300}"},danger:{background:"color-mix(in srgb, {red.500}, transparent 84%)",color:"{red.300}"},contrast:{background:"{surface.0}",color:"{surface.950}"}}},xx={root:kx,icon:_x,colorScheme:$x},Rx={background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.color}",height:"18rem",padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{form.field.border.radius}"},wx={gap:"0.25rem"},Tx={margin:"2px 0"},Px={root:Rx,prompt:wx,commandResponse:Tx},Ex={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},Ox={root:Ex},Bx={background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},Ax={padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},Lx={focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},Ix={mobileIndent:"1rem"},Dx={size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},jx={borderColor:"{content.border.color}"},Mx={root:Bx,list:Ax,item:Lx,submenu:Ix,submenuIcon:Dx,separator:jx},Nx={minHeight:"5rem"},Fx={eventContent:{padding:"1rem 0"}},zx={eventContent:{padding:"0 1rem"}},Hx={size:"1.125rem",borderRadius:"50%",borderWidth:"2px",background:"{content.background}",borderColor:"{content.border.color}",content:{borderRadius:"50%",size:"0.375rem",background:"{primary.color}",insetShadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"}},Ux={color:"{content.border.color}",size:"2px"},Vx={event:Nx,horizontal:Fx,vertical:zx,eventMarker:Hx,eventConnector:Ux},Wx={width:"25rem",borderRadius:"{content.border.radius}",borderWidth:"1px",transitionDuration:"{transition.duration}"},Kx={size:"1.125rem"},qx={padding:"{overlay.popover.padding}",gap:"0.5rem"},Gx={gap:"0.5rem"},Jx={fontWeight:"500",fontSize:"1rem"},Yx={fontWeight:"500",fontSize:"0.875rem"},Xx={width:"1.75rem",height:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"}},Zx={size:"1rem"},Qx={light:{blur:"1.5px",info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"{blue.100}",focusRing:{color:"{blue.600}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"{green.100}",focusRing:{color:"{green.600}",shadow:"none"}}},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"{yellow.100}",focusRing:{color:"{yellow.600}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"{red.100}",focusRing:{color:"{red.600}",shadow:"none"}}},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.200}",focusRing:{color:"{surface.600}",shadow:"none"}}},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.800}",focusRing:{color:"{surface.50}",shadow:"none"}}}},dark:{blur:"10px",info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{blue.500}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{green.500}",shadow:"none"}}},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{yellow.500}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{red.500}",shadow:"none"}}},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.700}",focusRing:{color:"{surface.300}",shadow:"none"}}},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",detailColor:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.100}",focusRing:{color:"{surface.950}",shadow:"none"}}}}},eR={root:Wx,icon:Kx,content:qx,text:Gx,summary:Jx,detail:Yx,closeButton:Xx,closeIcon:Zx,colorScheme:Qx},tR={padding:"0.25rem",borderRadius:"{content.border.radius}",gap:"0.5rem",fontWeight:"500",disabledBackground:"{form.field.disabled.background}",disabledBorderColor:"{form.field.disabled.background}",disabledColor:"{form.field.disabled.color}",invalidBorderColor:"{form.field.invalid.border.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",padding:"0.25rem"},lg:{fontSize:"{form.field.lg.font.size}",padding:"0.25rem"}},rR={disabledColor:"{form.field.disabled.color}"},oR={padding:"0.25rem 0.75rem",borderRadius:"{content.border.radius}",checkedShadow:"0px 1px 2px 0px rgba(0, 0, 0, 0.02), 0px 1px 2px 0px rgba(0, 0, 0, 0.04)",sm:{padding:"0.25rem 0.75rem"},lg:{padding:"0.25rem 0.75rem"}},nR={light:{root:{background:"{surface.100}",checkedBackground:"{surface.100}",hoverBackground:"{surface.100}",borderColor:"{surface.100}",color:"{surface.500}",hoverColor:"{surface.700}",checkedColor:"{surface.900}",checkedBorderColor:"{surface.100}"},content:{checkedBackground:"{surface.0}"},icon:{color:"{surface.500}",hoverColor:"{surface.700}",checkedColor:"{surface.900}"}},dark:{root:{background:"{surface.950}",checkedBackground:"{surface.950}",hoverBackground:"{surface.950}",borderColor:"{surface.950}",color:"{surface.400}",hoverColor:"{surface.300}",checkedColor:"{surface.0}",checkedBorderColor:"{surface.950}"},content:{checkedBackground:"{surface.800}"},icon:{color:"{surface.400}",hoverColor:"{surface.300}",checkedColor:"{surface.0}"}}},iR={root:tR,icon:rR,content:oR,colorScheme:nR},sR={width:"2.5rem",height:"1.5rem",borderRadius:"30px",gap:"0.25rem",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},borderWidth:"1px",borderColor:"transparent",hoverBorderColor:"transparent",checkedBorderColor:"transparent",checkedHoverBorderColor:"transparent",invalidBorderColor:"{form.field.invalid.border.color}",transitionDuration:"{form.field.transition.duration}",slideDuration:"0.2s"},aR={borderRadius:"50%",size:"1rem"},lR={light:{root:{background:"{surface.300}",disabledBackground:"{form.field.disabled.background}",hoverBackground:"{surface.400}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}"},handle:{background:"{surface.0}",disabledBackground:"{form.field.disabled.color}",hoverBackground:"{surface.0}",checkedBackground:"{surface.0}",checkedHoverBackground:"{surface.0}",color:"{text.muted.color}",hoverColor:"{text.color}",checkedColor:"{primary.color}",checkedHoverColor:"{primary.hover.color}"}},dark:{root:{background:"{surface.700}",disabledBackground:"{surface.600}",hoverBackground:"{surface.600}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}"},handle:{background:"{surface.400}",disabledBackground:"{surface.900}",hoverBackground:"{surface.300}",checkedBackground:"{surface.900}",checkedHoverBackground:"{surface.900}",color:"{surface.900}",hoverColor:"{surface.800}",checkedColor:"{primary.color}",checkedHoverColor:"{primary.hover.color}"}}},cR={root:sR,handle:aR,colorScheme:lR},uR={background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",padding:"0.75rem"},dR={root:uR},fR={maxWidth:"12.5rem",gutter:"0.25rem",shadow:"{overlay.popover.shadow}",padding:"0.5rem 0.75rem",borderRadius:"{overlay.popover.border.radius}"},pR={light:{root:{background:"{surface.700}",color:"{surface.0}"}},dark:{root:{background:"{surface.700}",color:"{surface.0}"}}},gR={root:fR,colorScheme:pR},hR={background:"{content.background}",color:"{content.color}",padding:"1rem",gap:"2px",indent:"1rem",transitionDuration:"{transition.duration}"},mR={padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{text.color}",hoverColor:"{text.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},gap:"0.25rem"},bR={color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedColor:"{highlight.color}"},vR={borderRadius:"50%",size:"1.75rem",hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedHoverColor:"{primary.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},yR={size:"2rem"},CR={margin:"0 0 0.5rem 0"},SR={root:hR,node:mR,nodeIcon:bR,nodeToggleButton:vR,loadingIcon:yR,filter:CR},kR={background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledHoverBackground:"{form.field.filled.hover.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",invalidPlaceholderColor:"{form.field.invalid.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"{form.field.sm.font.size}",paddingX:"{form.field.sm.padding.x}",paddingY:"{form.field.sm.padding.y}"},lg:{fontSize:"{form.field.lg.font.size}",paddingX:"{form.field.lg.padding.x}",paddingY:"{form.field.lg.padding.y}"}},_R={width:"2.5rem",color:"{form.field.icon.color}"},$R={background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},xR={padding:"{list.padding}"},RR={padding:"{list.option.padding}"},wR={borderRadius:"{border.radius.sm}"},TR={color:"{form.field.icon.color}"},PR={root:kR,dropdown:_R,overlay:$R,tree:xR,emptyMessage:RR,chip:wR,clearIcon:TR},ER={transitionDuration:"{transition.duration}"},OR={background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},BR={background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{treetable.border.color}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",gap:"0.5rem",padding:"0.75rem 1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},AR={fontWeight:"600"},LR={background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},IR={borderColor:"{treetable.border.color}",padding:"0.75rem 1rem",gap:"0.5rem"},DR={background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",padding:"0.75rem 1rem"},jR={fontWeight:"600"},MR={background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},NR={width:"0.5rem"},FR={width:"1px",color:"{primary.color}"},zR={color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",size:"0.875rem"},HR={size:"2rem"},UR={hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",selectedHoverColor:"{primary.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},VR={borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},WR={borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},KR={light:{root:{borderColor:"{content.border.color}"},bodyCell:{selectedBorderColor:"{primary.100}"}},dark:{root:{borderColor:"{surface.800}"},bodyCell:{selectedBorderColor:"{primary.900}"}}},qR={root:ER,header:OR,headerCell:BR,columnTitle:AR,row:LR,bodyCell:IR,footerCell:DR,columnFooter:jR,footer:MR,columnResizer:NR,resizeIndicator:FR,sortIcon:zR,loadingIcon:HR,nodeToggleButton:UR,paginatorTop:VR,paginatorBottom:WR,colorScheme:KR},GR={mask:{background:"{content.background}",color:"{text.muted.color}"},icon:{size:"2rem"}},JR={loader:GR};function Do(e){"@babel/helpers - typeof";return Do=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Do(e)}function Vl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function Wl(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Vl(Object(r),!0).forEach(function(o){YR(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vl(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function YR(e,t,r){return(t=XR(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function XR(e){var t=ZR(e,"string");return Do(t)=="symbol"?t:t+""}function ZR(e,t){if(Do(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(Do(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var QR=Wl(Wl({},g1),{},{components:{accordion:Hy,autocomplete:Zy,avatar:n1,badge:d1,blockui:m1,breadcrumb:C1,button:_1,datepicker:uS,card:P1,carousel:I1,cascadeselect:H1,checkbox:W1,chip:X1,colorpicker:rC,confirmdialog:iC,confirmpopup:uC,contextmenu:bC,dataview:VC,datatable:jC,dialog:mS,divider:SS,dock:$S,drawer:ES,editor:DS,fieldset:zS,fileupload:JS,iftalabel:C5,floatlabel:e5,galleria:h5,iconfield:b5,image:x5,imagecompare:w5,inlinemessage:B5,inplace:I5,inputchips:N5,inputgroup:z5,inputnumber:W5,inputotp:G5,inputtext:Y5,knob:tk,listbox:ck,megamenu:yk,menu:xk,menubar:Bk,message:Hk,metergroup:Jk,multiselect:i_,orderlist:l_,organizationchart:p_,overlaybadge:h_,popover:W_,paginator:C_,password:N_,panel:w_,panelmenu:A_,picklist:H_,progressbar:J_,progressspinner:X_,radiobutton:e$,rating:o$,ripple:i$,scrollpanel:c$,select:y$,selectbutton:k$,skeleton:x$,slider:O$,speeddial:A$,splitter:N$,splitbutton:I$,stepper:G$,steps:ex,tabmenu:sx,tabs:gx,tabview:Sx,textarea:Ox,tieredmenu:Mx,tag:xx,terminal:Px,timeline:Vx,togglebutton:iR,toggleswitch:cR,tree:SR,treeselect:PR,treetable:qR,toast:eR,toolbar:dR,tooltip:gR,virtualscroller:JR}});function jo(e){"@babel/helpers - typeof";return jo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},jo(e)}function ew(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function tw(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,ow(o.key),o)}}function rw(e,t,r){return t&&tw(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ow(e){var t=nw(e,"string");return jo(t)=="symbol"?t:t+""}function nw(e,t){if(jo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(jo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var iw=function(){function e(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:function(){};ew(this,e),this.element=t,this.listener=r}return rw(e,[{key:"bindScrollListener",value:function(){this.scrollableParents=X0(this.element);for(var r=0;r<this.scrollableParents.length;r++)this.scrollableParents[r].addEventListener("scroll",this.listener)}},{key:"unbindScrollListener",value:function(){if(this.scrollableParents)for(var r=0;r<this.scrollableParents.length;r++)this.scrollableParents[r].removeEventListener("scroll",this.listener)}},{key:"destroy",value:function(){this.unbindScrollListener(),this.element=null,this.listener=null,this.scrollableParents=null}}])}(),sw=({dt:e})=>`
.p-tooltip {
    position: absolute;
    display: none;
    max-width: ${e("tooltip.max.width")};
}

.p-tooltip-right,
.p-tooltip-left {
    padding: 0 ${e("tooltip.gutter")};
}

.p-tooltip-top,
.p-tooltip-bottom {
    padding: ${e("tooltip.gutter")} 0;
}

.p-tooltip-text {
    white-space: pre-line;
    word-break: break-word;
    background: ${e("tooltip.background")};
    color: ${e("tooltip.color")};
    padding: ${e("tooltip.padding")};
    box-shadow: ${e("tooltip.shadow")};
    border-radius: ${e("tooltip.border.radius")};
}

.p-tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}

.p-tooltip-right .p-tooltip-arrow {
    margin-top: calc(-1 * ${e("tooltip.gutter")});
    border-width: ${e("tooltip.gutter")} ${e("tooltip.gutter")} ${e("tooltip.gutter")} 0;
    border-right-color: ${e("tooltip.background")};
}

.p-tooltip-left .p-tooltip-arrow {
    margin-top: calc(-1 * ${e("tooltip.gutter")});
    border-width: ${e("tooltip.gutter")} 0 ${e("tooltip.gutter")} ${e("tooltip.gutter")};
    border-left-color: ${e("tooltip.background")};
}

.p-tooltip-top .p-tooltip-arrow {
    margin-left: calc(-1 * ${e("tooltip.gutter")});
    border-width: ${e("tooltip.gutter")} ${e("tooltip.gutter")} 0 ${e("tooltip.gutter")};
    border-top-color: ${e("tooltip.background")};
    border-bottom-color: ${e("tooltip.background")};
}

.p-tooltip-bottom .p-tooltip-arrow {
    margin-left: calc(-1 * ${e("tooltip.gutter")});
    border-width: 0 ${e("tooltip.gutter")} ${e("tooltip.gutter")} ${e("tooltip.gutter")};
    border-top-color: ${e("tooltip.background")};
    border-bottom-color: ${e("tooltip.background")};
}
`,aw={root:"p-tooltip p-component",arrow:"p-tooltip-arrow",text:"p-tooltip-text"},lw=ye.extend({name:"tooltip-directive",style:sw,classes:aw}),cw=Q.extend({style:lw});function uw(e,t){return gw(e)||pw(e,t)||fw(e,t)||dw()}function dw(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fw(e,t){if(e){if(typeof e=="string")return Kl(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Kl(e,t):void 0}}function Kl(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function pw(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var o,n,i,s,a=[],l=!0,u=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(l=(o=i.call(r)).done)&&(a.push(o.value),a.length!==t);l=!0);}catch(c){u=!0,n=c}finally{try{if(!l&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return a}}function gw(e){if(Array.isArray(e))return e}function ql(e,t,r){return(t=hw(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hw(e){var t=mw(e,"string");return Qt(t)=="symbol"?t:t+""}function mw(e,t){if(Qt(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(Qt(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Qt(e){"@babel/helpers - typeof";return Qt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qt(e)}var bw=cw.extend("tooltip",{beforeMount:function(t,r){var o,n=this.getTarget(t);if(n.$_ptooltipModifiers=this.getModifiers(r),r.value){if(typeof r.value=="string")n.$_ptooltipValue=r.value,n.$_ptooltipDisabled=!1,n.$_ptooltipEscape=!0,n.$_ptooltipClass=null,n.$_ptooltipFitContent=!0,n.$_ptooltipIdAttr=Gr("pv_id")+"_tooltip",n.$_ptooltipShowDelay=0,n.$_ptooltipHideDelay=0,n.$_ptooltipAutoHide=!0;else if(Qt(r.value)==="object"&&r.value){if(tr(r.value.value)||r.value.value.trim()==="")return;n.$_ptooltipValue=r.value.value,n.$_ptooltipDisabled=!!r.value.disabled===r.value.disabled?r.value.disabled:!1,n.$_ptooltipEscape=!!r.value.escape===r.value.escape?r.value.escape:!0,n.$_ptooltipClass=r.value.class||"",n.$_ptooltipFitContent=!!r.value.fitContent===r.value.fitContent?r.value.fitContent:!0,n.$_ptooltipIdAttr=r.value.id||Gr("pv_id")+"_tooltip",n.$_ptooltipShowDelay=r.value.showDelay||0,n.$_ptooltipHideDelay=r.value.hideDelay||0,n.$_ptooltipAutoHide=!!r.value.autoHide===r.value.autoHide?r.value.autoHide:!0}}else return;n.$_ptooltipZIndex=(o=r.instance.$primevue)===null||o===void 0||(o=o.config)===null||o===void 0||(o=o.zIndex)===null||o===void 0?void 0:o.tooltip,this.bindEvents(n,r),t.setAttribute("data-pd-tooltip",!0)},updated:function(t,r){var o=this.getTarget(t);if(o.$_ptooltipModifiers=this.getModifiers(r),this.unbindEvents(o),!!r.value){if(typeof r.value=="string")o.$_ptooltipValue=r.value,o.$_ptooltipDisabled=!1,o.$_ptooltipEscape=!0,o.$_ptooltipClass=null,o.$_ptooltipIdAttr=o.$_ptooltipIdAttr||Gr("pv_id")+"_tooltip",o.$_ptooltipShowDelay=0,o.$_ptooltipHideDelay=0,o.$_ptooltipAutoHide=!0,this.bindEvents(o,r);else if(Qt(r.value)==="object"&&r.value)if(tr(r.value.value)||r.value.value.trim()===""){this.unbindEvents(o,r);return}else o.$_ptooltipValue=r.value.value,o.$_ptooltipDisabled=!!r.value.disabled===r.value.disabled?r.value.disabled:!1,o.$_ptooltipEscape=!!r.value.escape===r.value.escape?r.value.escape:!0,o.$_ptooltipClass=r.value.class||"",o.$_ptooltipFitContent=!!r.value.fitContent===r.value.fitContent?r.value.fitContent:!0,o.$_ptooltipIdAttr=r.value.id||o.$_ptooltipIdAttr||Gr("pv_id")+"_tooltip",o.$_ptooltipShowDelay=r.value.showDelay||0,o.$_ptooltipHideDelay=r.value.hideDelay||0,o.$_ptooltipAutoHide=!!r.value.autoHide===r.value.autoHide?r.value.autoHide:!0,this.bindEvents(o,r)}},unmounted:function(t,r){var o=this.getTarget(t);this.remove(o),this.unbindEvents(o,r),o.$_ptooltipScrollHandler&&(o.$_ptooltipScrollHandler.destroy(),o.$_ptooltipScrollHandler=null)},timer:void 0,methods:{bindEvents:function(t,r){var o=this,n=t.$_ptooltipModifiers;n.focus?(t.$_ptooltipFocusEvent=function(i){return o.onFocus(i,r)},t.$_ptooltipBlurEvent=this.onBlur.bind(this),t.addEventListener("focus",t.$_ptooltipFocusEvent),t.addEventListener("blur",t.$_ptooltipBlurEvent)):(t.$_ptooltipMouseEnterEvent=function(i){return o.onMouseEnter(i,r)},t.$_ptooltipMouseLeaveEvent=this.onMouseLeave.bind(this),t.$_ptooltipClickEvent=this.onClick.bind(this),t.addEventListener("mouseenter",t.$_ptooltipMouseEnterEvent),t.addEventListener("mouseleave",t.$_ptooltipMouseLeaveEvent),t.addEventListener("click",t.$_ptooltipClickEvent)),t.addEventListener("keydown",this.onKeydown.bind(this))},unbindEvents:function(t){var r=t.$_ptooltipModifiers;r.focus?(t.removeEventListener("focus",t.$_ptooltipFocusEvent),t.$_ptooltipFocusEvent=null,t.removeEventListener("blur",t.$_ptooltipBlurEvent),t.$_ptooltipBlurEvent=null):(t.removeEventListener("mouseenter",t.$_ptooltipMouseEnterEvent),t.$_ptooltipMouseEnterEvent=null,t.removeEventListener("mouseleave",t.$_ptooltipMouseLeaveEvent),t.$_ptooltipMouseLeaveEvent=null,t.removeEventListener("click",t.$_ptooltipClickEvent),t.$_ptooltipClickEvent=null),t.removeEventListener("keydown",this.onKeydown.bind(this))},bindScrollListener:function(t){var r=this;t.$_ptooltipScrollHandler||(t.$_ptooltipScrollHandler=new iw(t,function(){r.hide(t)})),t.$_ptooltipScrollHandler.bindScrollListener()},unbindScrollListener:function(t){t.$_ptooltipScrollHandler&&t.$_ptooltipScrollHandler.unbindScrollListener()},onMouseEnter:function(t,r){var o=t.currentTarget,n=o.$_ptooltipShowDelay;this.show(o,r,n)},onMouseLeave:function(t){var r=t.currentTarget,o=r.$_ptooltipHideDelay,n=r.$_ptooltipAutoHide;if(n)this.hide(r,o);else{var i=cr(t.target,"data-pc-name")==="tooltip"||cr(t.target,"data-pc-section")==="arrow"||cr(t.target,"data-pc-section")==="text"||cr(t.relatedTarget,"data-pc-name")==="tooltip"||cr(t.relatedTarget,"data-pc-section")==="arrow"||cr(t.relatedTarget,"data-pc-section")==="text";!i&&this.hide(r,o)}},onFocus:function(t,r){var o=t.currentTarget,n=o.$_ptooltipShowDelay;this.show(o,r,n)},onBlur:function(t){var r=t.currentTarget,o=r.$_ptooltipHideDelay;this.hide(r,o)},onClick:function(t){var r=t.currentTarget,o=r.$_ptooltipHideDelay;this.hide(r,o)},onKeydown:function(t){var r=t.currentTarget,o=r.$_ptooltipHideDelay;t.code==="Escape"&&this.hide(t.currentTarget,o)},tooltipActions:function(t,r){if(!(t.$_ptooltipDisabled||!Kd(t))){var o=this.create(t,r);this.align(t),!this.isUnstyled()&&G0(o,250);var n=this;window.addEventListener("resize",function i(){Z0()||n.hide(t),window.removeEventListener("resize",i)}),o.addEventListener("mouseleave",function i(){n.hide(t),o.removeEventListener("mouseleave",i),t.removeEventListener("mouseenter",t.$_ptooltipMouseEnterEvent),setTimeout(function(){return t.addEventListener("mouseenter",t.$_ptooltipMouseEnterEvent)},50)}),this.bindScrollListener(t),so.set("tooltip",o,t.$_ptooltipZIndex)}},show:function(t,r,o){var n=this;o!==void 0?this.timer=setTimeout(function(){return n.tooltipActions(t,r)},o):this.tooltipActions(t,r)},tooltipRemoval:function(t){this.remove(t),this.unbindScrollListener(t)},hide:function(t,r){var o=this;clearTimeout(this.timer),r!==void 0?setTimeout(function(){return o.tooltipRemoval(t)},r):this.tooltipRemoval(t)},getTooltipElement:function(t){return document.getElementById(t.$_ptooltipId)},getArrowElement:function(t){var r=this.getTooltipElement(t);return On(r,'[data-pc-section="arrow"]')},create:function(t){var r=t.$_ptooltipModifiers,o=mn("div",{class:!this.isUnstyled()&&this.cx("arrow"),"p-bind":this.ptm("arrow",{context:r})}),n=mn("div",{class:!this.isUnstyled()&&this.cx("text"),"p-bind":this.ptm("text",{context:r})});t.$_ptooltipEscape?(n.innerHTML="",n.appendChild(document.createTextNode(t.$_ptooltipValue))):n.innerHTML=t.$_ptooltipValue;var i=mn("div",ql(ql({id:t.$_ptooltipIdAttr,role:"tooltip",style:{display:"inline-block",width:t.$_ptooltipFitContent?"fit-content":void 0,pointerEvents:!this.isUnstyled()&&t.$_ptooltipAutoHide&&"none"},class:[!this.isUnstyled()&&this.cx("root"),t.$_ptooltipClass]},this.$attrSelector,""),"p-bind",this.ptm("root",{context:r})),o,n);return document.body.appendChild(i),t.$_ptooltipId=i.id,this.$el=i,i},remove:function(t){if(t){var r=this.getTooltipElement(t);r&&r.parentElement&&(so.clear(r),document.body.removeChild(r)),t.$_ptooltipId=null}},align:function(t){var r=t.$_ptooltipModifiers;r.top?(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignTop(t))):r.left?(this.alignLeft(t),this.isOutOfBounds(t)&&(this.alignRight(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignLeft(t))))):r.bottom?(this.alignBottom(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&this.alignBottom(t))):(this.alignRight(t),this.isOutOfBounds(t)&&(this.alignLeft(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignRight(t)))))},getHostOffset:function(t){var r=t.getBoundingClientRect(),o=r.left+Ud(),n=r.top+Vd();return{left:o,top:n}},alignRight:function(t){this.preAlign(t,"right");var r=this.getTooltipElement(t),o=this.getArrowElement(t),n=this.getHostOffset(t),i=n.left+rt(t),s=n.top+(Vt(t)-Vt(r))/2;r.style.left=i+"px",r.style.top=s+"px",o.style.top="50%",o.style.right=null,o.style.bottom=null,o.style.left="0"},alignLeft:function(t){this.preAlign(t,"left");var r=this.getTooltipElement(t),o=this.getArrowElement(t),n=this.getHostOffset(t),i=n.left-rt(r),s=n.top+(Vt(t)-Vt(r))/2;r.style.left=i+"px",r.style.top=s+"px",o.style.top="50%",o.style.right="0",o.style.bottom=null,o.style.left=null},alignTop:function(t){this.preAlign(t,"top");var r=this.getTooltipElement(t),o=this.getArrowElement(t),n=rt(r),i=rt(t),s=hn(),a=s.width,l=this.getHostOffset(t),u=l.left+(rt(t)-rt(r))/2,c=l.top-Vt(r);l.left+n>a&&(u=Math.floor(l.left+i-n)),r.style.left=u+"px",r.style.top=c+"px";var d=l.left-this.getHostOffset(r).left+i/2;o.style.top=null,o.style.right=null,o.style.bottom="0",o.style.left=d+"px"},alignBottom:function(t){this.preAlign(t,"bottom");var r=this.getTooltipElement(t),o=this.getArrowElement(t),n=rt(r),i=rt(t),s=hn(),a=s.width,l=this.getHostOffset(t),u=l.left+(rt(t)-rt(r))/2,c=l.top+Vt(t);l.left+n>a&&(u=Math.floor(l.left+i-n)),r.style.left=u+"px",r.style.top=c+"px";var d=l.left-this.getHostOffset(r).left+i/2;o.style.top="0",o.style.right=null,o.style.bottom=null,o.style.left=d+"px"},preAlign:function(t,r){var o=this.getTooltipElement(t);o.style.left="-999px",o.style.top="-999px",gn(o,"p-tooltip-".concat(o.$_ptooltipPosition)),!this.isUnstyled()&&Hd(o,"p-tooltip-".concat(r)),o.$_ptooltipPosition=r,o.setAttribute("data-p-position",r)},isOutOfBounds:function(t){var r=this.getTooltipElement(t),o=r.getBoundingClientRect(),n=o.top,i=o.left,s=rt(r),a=Vt(r),l=hn();return i+s>l.width||i<0||n<0||n+a>l.height},getTarget:function(t){var r;return zd(t,"p-inputwrapper")&&(r=On(t,"input"))!==null&&r!==void 0?r:t},getModifiers:function(t){return t.modifiers&&Object.keys(t.modifiers).length?t.modifiers:t.arg&&Qt(t.arg)==="object"?Object.entries(t.arg).reduce(function(r,o){var n=uw(o,2),i=n[0],s=n[1];return(i==="event"||i==="position")&&(r[s]=!0),r},{}):{}}}});console.log($f.browserDetails.browser);const Nr=rh(xy);Nr.use(ih());Nr.use(jy,{theme:{preset:QR,options:{cssLayer:{name:"primevue",order:"tailwind-base, primevue, tailwind-utilities"}}}});Nr.use(Ty);Nr.use(gf);Nr.directive("tooltip",bw);Nr.mount("#app");export{n2 as $,Ip as A,ye as B,iw as C,it as D,Hs as E,se as F,Sn as G,we as H,he as I,yp as J,mo as K,Te as L,_n as M,Ho as N,o2 as O,ge as P,vw as Q,Hv as R,Cf as S,_w as T,Ps as U,by as V,gm as W,xw as X,Dr as Y,so as Z,Jw as _,Tt as a,Tw as a0,Es as a1,Yw as a2,qw as a3,lf as a4,Le as a5,ku as a6,Ir as a7,Mn as a8,lo as a9,Rw as aa,Gw as ab,gg as ac,$w as ad,ww as ae,Cl as af,Sw as ag,kw as ah,Vo as ai,tr as aj,Kr as ak,Q as b,yw as c,t2 as d,e2 as e,Qw as f,jc as g,mn as h,xe as i,r2 as j,Se as k,Uo as l,Yd as m,Z0 as n,Nf as o,Jd as p,Xw as q,_s as r,cf as s,Cw as t,ft as u,Y0 as v,Rt as w,Hd as x,Zw as y,Mi as z};
