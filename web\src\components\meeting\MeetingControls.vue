<template>
  <div class="bg-gray-800 p-4">
    <div class="flex items-center justify-center space-x-4">
      <!-- Microphone Control -->
      <button
        @click="$emit('toggleMicrophone')"
        :class="[
          'p-3 rounded-full transition-colors',
          mediaState.microphone ? 'bg-gray-600 hover:bg-gray-500' : 'bg-red-600 hover:bg-red-500',
        ]"
        :title="
          mediaState.microphone
            ? $t('tools.webRtcMeeting.controls.muteMic')
            : $t('tools.webRtcMeeting.controls.unmuteMic')
        "
      >
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path v-if="mediaState.microphone" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4z" />
          <path
            v-if="mediaState.microphone"
            d="M5.5 9.643a.75.75 0 00-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 000 1.5h4.5a.75.75 0 000-1.5H10.5v-1.546A6.001 6.001 0 0016 10v-.357a.75.75 0 00-1.5 0V10a4.5 4.5 0 01-9 0v-.357z"
          />
          <path
            v-else
            d="M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06L3.28 2.22z"
          />
        </svg>
      </button>

      <!-- Camera Control -->
      <button
        @click="$emit('toggleCamera')"
        :class="[
          'p-3 rounded-full transition-colors',
          mediaState.camera ? 'bg-gray-600 hover:bg-gray-500' : 'bg-red-600 hover:bg-red-500',
        ]"
        :title="
          mediaState.camera
            ? $t('tools.webRtcMeeting.controls.turnOffCamera')
            : $t('tools.webRtcMeeting.controls.turnOnCamera')
        "
      >
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path
            v-if="mediaState.camera"
            d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
          />
          <path
            v-else
            d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A2 2 0 0018 14V8a2 2 0 00-2-2h-3l-1-1H9.414l-3.707-3.707z"
          />
        </svg>
      </button>

      <!-- Screen Share Control -->
      <button
        @click="$emit('toggleScreenShare')"
        :class="[
          'p-3 rounded-full transition-colors',
          mediaState.screenSharing
            ? 'bg-purple-600 hover:bg-purple-500'
            : 'bg-gray-600 hover:bg-gray-500',
        ]"
        :title="
          mediaState.screenSharing
            ? $t('tools.webRtcMeeting.controls.stopScreenShare')
            : $t('tools.webRtcMeeting.controls.startScreenShare')
        "
      >
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path
            d="M2 3a1 1 0 011-1h14a1 1 0 011 1v11a1 1 0 01-1 1H3a1 1 0 01-1-1V3zm2 2v7h12V5H4z"
          />
        </svg>
      </button>

      <!-- Recording Control (Host only) -->
      <button
        v-if="isHost"
        @click="$emit('toggleRecording')"
        :class="[
          'p-3 rounded-full transition-colors',
          isRecording ? 'bg-red-600 hover:bg-red-500' : 'bg-gray-600 hover:bg-gray-500',
        ]"
        :title="
          isRecording
            ? $t('tools.webRtcMeeting.recording.stop')
            : $t('tools.webRtcMeeting.recording.start')
        "
      >
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path
            v-if="isRecording"
            d="M5.25 3A2.25 2.25 0 003 5.25v9.5A2.25 2.25 0 005.25 17h9.5A2.25 2.25 0 0017 14.75v-9.5A2.25 2.25 0 0014.75 3h-9.5z"
          />
          <circle v-else cx="10" cy="10" r="3" />
        </svg>
      </button>

      <!-- Chat Toggle -->
      <button
        @click="$emit('toggleChat')"
        class="p-3 rounded-full bg-gray-600 hover:bg-gray-500 transition-colors relative"
        :title="$t('tools.webRtcMeeting.chat.toggleChat')"
      >
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path
            d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"
          />
          <path
            d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"
          />
        </svg>
        <div
          v-if="unreadMessages > 0"
          class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs flex items-center justify-center text-white"
        >
          {{ unreadMessages > 9 ? '9+' : unreadMessages }}
        </div>
      </button>

      <!-- Participants Panel Toggle -->
      <button
        @click="$emit('toggleParticipants')"
        class="p-3 rounded-full bg-gray-600 hover:bg-gray-500 transition-colors"
        :title="$t('tools.webRtcMeeting.participants.togglePanel')"
      >
        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path
            d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
          />
        </svg>
      </button>

      <!-- More Options -->
      <div class="relative">
        <button
          @click="showMoreOptions = !showMoreOptions"
          class="p-3 rounded-full bg-gray-600 hover:bg-gray-500 transition-colors"
          :title="$t('tools.webRtcMeeting.controls.moreOptions')"
        >
          <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path
              d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"
            />
          </svg>
        </button>

        <!-- More Options Menu -->
        <div
          v-if="showMoreOptions"
          v-click-outside="() => showMoreOptions = false"
          class="absolute bottom-full right-0 mb-2 bg-white dark:bg-gray-700 rounded-lg shadow-lg py-2 min-w-48 text-gray-900 dark:text-white z-10"
        >
          <button
            @click="handleCopyMeetingLink"
            class="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-2"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
              <path
                d="M3 5a2 2 0 012-2 3 3 0 003 3h6a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L12.586 15H17v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"
              />
            </svg>
            <span>{{ $t('tools.webRtcMeeting.meeting.copyLink') }}</span>
          </button>

          <button
            v-if="isHost"
            @click="handleMuteAll"
            class="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-2"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793z"
              />
              <path
                d="M15.293 7.293a1 1 0 011.414 0L18 8.586l1.293-1.293a1 1 0 111.414 1.414L19.414 10l1.293 1.293a1 1 0 01-1.414 1.414L18 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L16.586 10l-1.293-1.293a1 1 0 010-1.414z"
              />
            </svg>
            <span>{{ $t('tools.webRtcMeeting.controls.muteAll') }}</span>
          </button>

          <div class="border-t border-gray-200 dark:border-gray-600 my-1"></div>

          <button
            @click="handleSettings"
            class="w-full px-4 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-2"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fill-rule="evenodd"
                d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                clip-rule="evenodd"
              />
            </svg>
            <span>{{ $t('tools.webRtcMeeting.controls.settings') }}</span>
          </button>
        </div>
      </div>

      <!-- Leave Meeting Button -->
      <button
        @click="$emit('leaveMeeting')"
        class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm font-medium text-white transition-colors"
      >
        {{ $t('tools.webRtcMeeting.meeting.leave') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import type { MediaState } from '@/types/meeting'

interface Props {
  mediaState: MediaState
  isHost: boolean
  isRecording: boolean
  unreadMessages: number
}

interface Emits {
  (e: 'toggleMicrophone'): void
  (e: 'toggleCamera'): void
  (e: 'toggleScreenShare'): void
  (e: 'toggleRecording'): void
  (e: 'toggleChat'): void
  (e: 'toggleParticipants'): void
  (e: 'copyMeetingLink'): void
  (e: 'muteAll'): void
  (e: 'leaveMeeting'): void
  (e: 'openSettings'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

// Local state
const showMoreOptions = ref(false)

// Methods
const handleCopyMeetingLink = () => {
  emit('copyMeetingLink')
  showMoreOptions.value = false
}

const handleMuteAll = () => {
  emit('muteAll')
  showMoreOptions.value = false
}

const handleSettings = () => {
  emit('openSettings')
  showMoreOptions.value = false
}

// Click outside directive (you might want to implement this as a global directive)
const vClickOutside = {
  mounted(el: HTMLElement, binding: any) {
    el.clickOutsideEvent = (event: Event) => {
      if (!(el === event.target || el.contains(event.target as Node))) {
        binding.value()
      }
    }
    document.addEventListener('click', el.clickOutsideEvent)
  },
  unmounted(el: HTMLElement) {
    document.removeEventListener('click', el.clickOutsideEvent)
  }
}
</script>
