package service

import (
	"context"
	"errors"
	"meeting/internal/entity"
	"meeting/internal/repository"
	"meeting/pkg/core"
	"meeting/pkg/jwt"
	"time"

	"github.com/gin-contrib/sessions"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AuthService struct {
	userRepo repository.UserRepository
	logger   *zap.SugaredLogger
}

func NewAuthService(userRepo repository.UserRepository, logger *zap.SugaredLogger) *AuthService {
	return &AuthService{
		userRepo: userRepo,
		logger:   logger,
	}
}

// LoginWithJWT 使用JWT方式登录用户
func (s *AuthService) LoginWithJWT(user *entity.User) (string, int64, error) {
	return jwt.FromUser(user)
}

// LoginWithSession 使用Session方式登录用户
func (s *AuthService) LoginWithSession(ctx *gin.Context, user *entity.User) error {
	session := sessions.Default(ctx)
	session.Set("user", user.ID)
	session.Set("login_time", time.Now().Unix())
	return session.Save()
}

// Logout 登出用户（清除Session）
func (s *AuthService) Logout(ctx *gin.Context) error {
	session := sessions.Default(ctx)
	session.Clear()
	return session.Save()
}

// GetUserByID 根据ID获取用户
func (s *AuthService) GetUserByID(userID string) (*entity.User, error) {
	if userID == "" {
		return nil, errors.New("用户ID不能为空")
	}

	var user entity.User
	err := core.DB(context.Background()).Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserByUUID 根据UUID获取用户
func (s *AuthService) GetUserByUUID(uuid string) (*entity.User, error) {
	if uuid == "" {
		return nil, errors.New("用户UUID不能为空")
	}

	var user entity.User
	err := core.DB(context.Background()).Where("uuid = ?", uuid).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// CreateOrUpdateUser 创建或更新用户信息
func (s *AuthService) CreateOrUpdateUser(userInfo *entity.User) (*entity.User, error) {
	if userInfo.UUID == "" {
		return nil, errors.New("用户UUID不能为空")
	}

	database := core.DB(context.Background())
	var user entity.User

	// 查找现有用户
	err := database.Where("uuid = ?", userInfo.UUID).First(&user).Error
	if err != nil {
		// 用户不存在，创建新用户
		user = *userInfo
	} else {
		// 用户存在，更新信息
		user.Avatar = userInfo.Avatar
		user.Email = userInfo.Email
		user.Nickname = userInfo.Nickname
		user.Name = userInfo.Name
	}

	err = database.Save(&user).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

// ValidateToken 验证JWT Token
func (s *AuthService) ValidateToken(tokenString string) (*entity.User, error) {
	return jwt.Authenticate(tokenString)
}

// RefreshToken 刷新JWT Token
func (s *AuthService) RefreshToken(oldToken string) (string, int64, error) {
	user, err := jwt.Authenticate(oldToken)
	if err != nil {
		return "", 0, err
	}

	return jwt.FromUser(user)
}
