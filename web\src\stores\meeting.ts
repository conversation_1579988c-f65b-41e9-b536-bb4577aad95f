import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { Meeting, Participant, MediaState, RemoteStream } from '@/types/meeting'
import type { ChatMessage } from '@/types/message'

export const useMeetingStore = defineStore('meeting', () => {
  // Meeting state
  const currentMeeting = ref<Meeting | null>(null)
  const inMeeting = ref(false)
  const isHost = ref(false)
  const localDeviceId = ref('')
  const displayName = ref('')

  // Connection state
  const signalServerConnected = ref(false)
  const connectionState = ref<'disconnected' | 'connecting' | 'connected'>('disconnected')

  // Participants
  const participants = ref<Participant[]>([])
  const localParticipant = computed(() => 
    participants.value.find(p => p.id === localDeviceId.value)
  )

  // Media state
  const localMediaState = ref<MediaState>({
    microphone: false,
    camera: false,
    screenSharing: false
  })
  const remoteStreams = ref<RemoteStream[]>([])

  // Chat
  const chatMessages = ref<ChatMessage[]>([])
  const unreadMessages = ref(0)

  // UI state
  const showChatPanel = ref(false)
  const showParticipantsPanel = ref(false)
  const showMoreOptions = ref(false)

  // Recording state
  const isRecording = ref(false)

  // Actions
  const setMeeting = (meeting: Meeting) => {
    currentMeeting.value = meeting
    inMeeting.value = true
  }

  const addParticipant = (participant: Participant) => {
    const existingIndex = participants.value.findIndex(p => p.id === participant.id)
    if (existingIndex >= 0) {
      participants.value[existingIndex] = participant
    } else {
      participants.value.push(participant)
    }
  }

  const removeParticipant = (participantId: string) => {
    participants.value = participants.value.filter(p => p.id !== participantId)
  }

  const updateParticipantMediaState = (participantId: string, mediaState: Partial<MediaState>) => {
    const participant = participants.value.find(p => p.id === participantId)
    if (participant) {
      participant.mediaState = { ...participant.mediaState, ...mediaState }
    }
  }

  const addChatMessage = (message: ChatMessage) => {
    chatMessages.value.push(message)
    if (!showChatPanel.value && message.type !== 'system') {
      unreadMessages.value++
    }
  }

  const addRemoteStream = (stream: RemoteStream) => {
    const existingIndex = remoteStreams.value.findIndex(s => s.id === stream.id)
    if (existingIndex >= 0) {
      remoteStreams.value[existingIndex] = stream
    } else {
      remoteStreams.value.push(stream)
    }
  }

  const removeRemoteStream = (streamId: string) => {
    remoteStreams.value = remoteStreams.value.filter(s => s.id !== streamId)
  }

  const clearChatUnread = () => {
    unreadMessages.value = 0
  }

  const resetMeeting = () => {
    currentMeeting.value = null
    inMeeting.value = false
    isHost.value = false
    participants.value = []
    chatMessages.value = []
    remoteStreams.value = []
    unreadMessages.value = 0
    showChatPanel.value = false
    showParticipantsPanel.value = false
    showMoreOptions.value = false
    isRecording.value = false
    localMediaState.value = {
      microphone: false,
      camera: false,
      screenSharing: false
    }
  }

  const setConnectionState = (state: 'disconnected' | 'connecting' | 'connected') => {
    connectionState.value = state
    signalServerConnected.value = state === 'connected'
  }

  // Getters
  const participantCount = computed(() => participants.value.length)
  const isLocalParticipantHost = computed(() => 
    localParticipant.value?.role === 'host'
  )

  return {
    // State
    currentMeeting,
    inMeeting,
    isHost,
    localDeviceId,
    displayName,
    signalServerConnected,
    connectionState,
    participants,
    localParticipant,
    localMediaState,
    remoteStreams,
    chatMessages,
    unreadMessages,
    showChatPanel,
    showParticipantsPanel,
    showMoreOptions,
    isRecording,

    // Computed
    participantCount,
    isLocalParticipantHost,

    // Actions
    setMeeting,
    addParticipant,
    removeParticipant,
    updateParticipantMediaState,
    addChatMessage,
    addRemoteStream,
    removeRemoteStream,
    clearChatUnread,
    resetMeeting,
    setConnectionState
  }
})
