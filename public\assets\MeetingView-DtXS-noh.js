import{t as Re,r as Pe,a as j,i as Ae,w as ce,g as He,o as ze,u as F,c as Be,B as ge,b as Ve,f as ae,d as ie,e as Ue,h as we,j as Ke,k as Ne,E as We,Z as be,l as Ye,m as Ze,n as Se,$ as Ge,p as Je,q as qe,C as Qe,s as Xe,v as et,U as tt,R as nt,x as ot,y as De,z as it,A as Me,D as B,F as rt,G as me,H as R,T as at,I as ee,J as se,K,L as $e,M as st,N as ct,O as g,P as lt,Q as ut,S as dt,V as ft,W as pt,X as vt,Y as bt,_ as ht,a0 as mt,a1 as yt,a2 as gt,a3 as re,a4 as de,a5 as _e,a6 as kt,a7 as Ce,a8 as fe,a9 as Le}from"./index-kFS7FdWJ.js";import{s as X}from"./index-BQiBqhCI.js";import{s as xe,a as wt}from"./index-aCApzGP5.js";function ve(n){return He()?(ze(n),!0):!1}function ke(n){return typeof n=="function"?n():F(n)}const ne=typeof window<"u"&&typeof document<"u",St=typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope,$t=Object.prototype.toString,_t=n=>$t.call(n)==="[object Object]",Fe=()=>{};function Ct(...n){if(n.length!==1)return Re(...n);const e=n[0];return typeof e=="function"?Pe(Be(()=>({get:e,set:Fe}))):j(e)}function Lt(n,e=1e3,t={}){const{immediate:s=!0,immediateCallback:m=!1}=t;let u=null;const x=j(!1);function E(){u&&(clearInterval(u),u=null)}function k(){x.value=!1,E()}function C(){const b=ke(e);b<=0||(x.value=!0,m&&n(),E(),u=setInterval(n,b))}if(s&&ne&&C(),Ae(e)||typeof e=="function"){const b=ce(e,()=>{x.value&&ne&&C()});ve(b)}return ve(k),{isActive:x,pause:k,resume:C}}function xt(n){var e;const t=ke(n);return(e=t==null?void 0:t.$el)!=null?e:t}const Et=ne?window:void 0;function Ot(...n){let e,t,s,m;if(typeof n[0]=="string"||Array.isArray(n[0])?([t,s,m]=n,e=Et):[e,t,s,m]=n,!e)return Fe;Array.isArray(t)||(t=[t]),Array.isArray(s)||(s=[s]);const u=[],x=()=>{u.forEach(b=>b()),u.length=0},E=(b,v,M,O)=>(b.addEventListener(v,M,O),()=>b.removeEventListener(v,M,O)),k=ce(()=>[xt(e),ke(m)],([b,v])=>{if(x(),!b)return;const M=_t(v)?{...v}:v;u.push(...t.flatMap(O=>s.map(P=>E(b,O,P,M))))},{immediate:!0,flush:"post"}),C=()=>{k(),x()};return ve(C),C}const Ee="ping";function he(n){return n===!0?{}:n}function Tt(n,e={}){const{onConnected:t,onDisconnected:s,onError:m,onMessage:u,immediate:x=!0,autoClose:E=!0,protocols:k=[]}=e,C=j(null),b=j("CLOSED"),v=j(),M=Ct(n);let O,P,A=!1,W=0,J=[],q;const Y=()=>{if(J.length&&v.value&&b.value==="OPEN"){for(const T of J)v.value.send(T);J=[]}},te=()=>{clearTimeout(q),q=void 0},z=(T=1e3,w)=>{!ne||!v.value||(A=!0,te(),O==null||O(),v.value.close(T,w),v.value=void 0)},H=(T,w=!0)=>!v.value||b.value!=="OPEN"?(w&&J.push(T),!1):(Y(),v.value.send(T),!0),Q=()=>{if(A||typeof M.value>"u")return;const T=new WebSocket(M.value,k);v.value=T,b.value="CONNECTING",T.onopen=()=>{b.value="OPEN",t==null||t(T),P==null||P(),Y()},T.onclose=w=>{if(b.value="CLOSED",s==null||s(T,w),!A&&e.autoReconnect){const{retries:y=-1,delay:V=1e3,onFailed:Z}=he(e.autoReconnect);W+=1,typeof y=="number"&&(y<0||W<y)||typeof y=="function"&&y()?setTimeout(Q,V):Z==null||Z()}},T.onerror=w=>{m==null||m(T,w)},T.onmessage=w=>{if(e.heartbeat){te();const{message:y=Ee}=he(e.heartbeat);if(w.data===y)return}C.value=w.data,u==null||u(T,w)}};if(e.heartbeat){const{message:T=Ee,interval:w=1e3,pongTimeout:y=1e3}=he(e.heartbeat),{pause:V,resume:Z}=Lt(()=>{H(T,!1),q==null&&(q=setTimeout(()=>{z(),A=!1},y))},w,{immediate:!1});O=V,P=Z}E&&(ne&&Ot("beforeunload",()=>z()),ve(z));const G=()=>{!ne&&!St||(z(),A=!1,W=0,Q())};return x&&G(),ce(M,G),{data:C,status:b,close:z,send:H,open:G,ws:v}}var Dt=ge.extend({name:"focustrap-directive"}),Mt=Ve.extend({style:Dt});function le(n){"@babel/helpers - typeof";return le=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},le(n)}function Oe(n,e){var t=Object.keys(n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);e&&(s=s.filter(function(m){return Object.getOwnPropertyDescriptor(n,m).enumerable})),t.push.apply(t,s)}return t}function Te(n){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Oe(Object(t),!0).forEach(function(s){Ft(n,s,t[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):Oe(Object(t)).forEach(function(s){Object.defineProperty(n,s,Object.getOwnPropertyDescriptor(t,s))})}return n}function Ft(n,e,t){return(e=jt(e))in n?Object.defineProperty(n,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[e]=t,n}function jt(n){var e=It(n,"string");return le(e)=="symbol"?e:e+""}function It(n,e){if(le(n)!="object"||!n)return n;var t=n[Symbol.toPrimitive];if(t!==void 0){var s=t.call(n,e||"default");if(le(s)!="object")return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(n)}var Rt=Mt.extend("focustrap",{mounted:function(e,t){var s=t.value||{},m=s.disabled;m||(this.createHiddenFocusableElements(e,t),this.bind(e,t),this.autoElementFocus(e,t)),e.setAttribute("data-pd-focustrap",!0),this.$el=e},updated:function(e,t){var s=t.value||{},m=s.disabled;m&&this.unbind(e)},unmounted:function(e){this.unbind(e)},methods:{getComputedSelector:function(e){return':not(.p-hidden-focusable):not([data-p-hidden-focusable="true"])'.concat(e??"")},bind:function(e,t){var s=this,m=t.value||{},u=m.onFocusIn,x=m.onFocusOut;e.$_pfocustrap_mutationobserver=new MutationObserver(function(E){E.forEach(function(k){if(k.type==="childList"&&!e.contains(document.activeElement)){var C=function b(v){var M=we(v)?we(v,s.getComputedSelector(e.$_pfocustrap_focusableselector))?v:ie(e,s.getComputedSelector(e.$_pfocustrap_focusableselector)):ie(v);return Ke(M)?M:v.nextSibling&&b(v.nextSibling)};ae(C(k.nextSibling))}})}),e.$_pfocustrap_mutationobserver.disconnect(),e.$_pfocustrap_mutationobserver.observe(e,{childList:!0}),e.$_pfocustrap_focusinlistener=function(E){return u&&u(E)},e.$_pfocustrap_focusoutlistener=function(E){return x&&x(E)},e.addEventListener("focusin",e.$_pfocustrap_focusinlistener),e.addEventListener("focusout",e.$_pfocustrap_focusoutlistener)},unbind:function(e){e.$_pfocustrap_mutationobserver&&e.$_pfocustrap_mutationobserver.disconnect(),e.$_pfocustrap_focusinlistener&&e.removeEventListener("focusin",e.$_pfocustrap_focusinlistener)&&(e.$_pfocustrap_focusinlistener=null),e.$_pfocustrap_focusoutlistener&&e.removeEventListener("focusout",e.$_pfocustrap_focusoutlistener)&&(e.$_pfocustrap_focusoutlistener=null)},autoFocus:function(e){this.autoElementFocus(this.$el,{value:Te(Te({},e),{},{autoFocus:!0})})},autoElementFocus:function(e,t){var s=t.value||{},m=s.autoFocusSelector,u=m===void 0?"":m,x=s.firstFocusableSelector,E=x===void 0?"":x,k=s.autoFocus,C=k===void 0?!1:k,b=ie(e,"[autofocus]".concat(this.getComputedSelector(u)));C&&!b&&(b=ie(e,this.getComputedSelector(E))),ae(b)},onFirstHiddenElementFocus:function(e){var t,s=e.currentTarget,m=e.relatedTarget,u=m===s.$_pfocustrap_lasthiddenfocusableelement||!((t=this.$el)!==null&&t!==void 0&&t.contains(m))?ie(s.parentElement,this.getComputedSelector(s.$_pfocustrap_focusableselector)):s.$_pfocustrap_lasthiddenfocusableelement;ae(u)},onLastHiddenElementFocus:function(e){var t,s=e.currentTarget,m=e.relatedTarget,u=m===s.$_pfocustrap_firsthiddenfocusableelement||!((t=this.$el)!==null&&t!==void 0&&t.contains(m))?Ue(s.parentElement,this.getComputedSelector(s.$_pfocustrap_focusableselector)):s.$_pfocustrap_firsthiddenfocusableelement;ae(u)},createHiddenFocusableElements:function(e,t){var s=this,m=t.value||{},u=m.tabIndex,x=u===void 0?0:u,E=m.firstFocusableSelector,k=E===void 0?"":E,C=m.lastFocusableSelector,b=C===void 0?"":C,v=function(A){return Ne("span",{class:"p-hidden-accessible p-hidden-focusable",tabIndex:x,role:"presentation","aria-hidden":!0,"data-p-hidden-accessible":!0,"data-p-hidden-focusable":!0,onFocus:A==null?void 0:A.bind(s)})},M=v(this.onFirstHiddenElementFocus),O=v(this.onLastHiddenElementFocus);M.$_pfocustrap_lasthiddenfocusableelement=O,M.$_pfocustrap_focusableselector=k,M.setAttribute("data-pc-section","firstfocusableelement"),O.$_pfocustrap_firsthiddenfocusableelement=M,O.$_pfocustrap_focusableselector=b,O.setAttribute("data-pc-section","lastfocusableelement"),e.prepend(M),e.append(O)}}}),pe=We(),Pt=function(e){var t=e.dt;return`
.p-popover {
    margin-top: `.concat(t("popover.gutter"),`;
    background: `).concat(t("popover.background"),`;
    color: `).concat(t("popover.color"),`;
    border: 1px solid `).concat(t("popover.border.color"),`;
    border-radius: `).concat(t("popover.border.radius"),`;
    box-shadow: `).concat(t("popover.shadow"),`;
}

.p-popover-content {
    padding: `).concat(t("popover.content.padding"),`;
}

.p-popover-flipped {
    margin-top: calc(`).concat(t("popover.gutter"),` * -1);
    margin-bottom: `).concat(t("popover.gutter"),`;
}

.p-popover-enter-from {
    opacity: 0;
    transform: scaleY(0.8);
}

.p-popover-leave-to {
    opacity: 0;
}

.p-popover-enter-active {
    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1), opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.p-popover-leave-active {
    transition: opacity 0.1s linear;
}

.p-popover:after,
.p-popover:before {
    bottom: 100%;
    left: `).concat(t("popover.arrow.offset"),`;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.p-popover:after {
    border-width: calc(`).concat(t("popover.gutter"),` - 2px);
    margin-left: calc(-1 * (`).concat(t("popover.gutter"),` - 2px));
    border-style: solid;
    border-color: transparent;
    border-bottom-color: `).concat(t("popover.background"),`;
}

.p-popover:before {
    border-width: `).concat(t("popover.gutter"),`;
    margin-left: calc(-1 * `).concat(t("popover.gutter"),`);
    border-style: solid;
    border-color: transparent;
    border-bottom-color: `).concat(t("popover.border.color"),`;
}

.p-popover-flipped:after,
.p-popover-flipped:before {
    bottom: auto;
    top: 100%;
}

.p-popover.p-popover-flipped:after {
    border-bottom-color: transparent;
    border-top-color: `).concat(t("popover.background"),`;
}

.p-popover.p-popover-flipped:before {
    border-bottom-color: transparent;
    border-top-color: `).concat(t("popover.border.color"),`;
}
`)},At={root:"p-popover p-component",content:"p-popover-content"},Ht=ge.extend({name:"popover",theme:Pt,classes:At}),zt={name:"BasePopover",extends:De,props:{dismissable:{type:Boolean,default:!0},appendTo:{type:[String,Object],default:"body"},baseZIndex:{type:Number,default:0},autoZIndex:{type:Boolean,default:!0},breakpoints:{type:Object,default:null},closeOnEscape:{type:Boolean,default:!0}},style:Ht,provide:function(){return{$pcPopover:this,$parentInstance:this}}},je={name:"Popover",extends:zt,inheritAttrs:!1,emits:["show","hide"],data:function(){return{visible:!1}},watch:{dismissable:{immediate:!0,handler:function(e){e?this.bindOutsideClickListener():this.unbindOutsideClickListener()}}},selfClick:!1,target:null,eventTarget:null,outsideClickListener:null,scrollHandler:null,resizeListener:null,container:null,styleElement:null,overlayEventListener:null,documentKeydownListener:null,beforeUnmount:function(){this.dismissable&&this.unbindOutsideClickListener(),this.scrollHandler&&(this.scrollHandler.destroy(),this.scrollHandler=null),this.destroyStyle(),this.unbindResizeListener(),this.target=null,this.container&&this.autoZIndex&&be.clear(this.container),this.overlayEventListener&&(pe.off("overlay-click",this.overlayEventListener),this.overlayEventListener=null),this.container=null},mounted:function(){this.breakpoints&&this.createStyle()},methods:{toggle:function(e,t){this.visible?this.hide():this.show(e,t)},show:function(e,t){this.visible=!0,this.eventTarget=e.currentTarget,this.target=t||e.currentTarget},hide:function(){this.visible=!1},onContentClick:function(){this.selfClick=!0},onEnter:function(e){var t=this;this.container.setAttribute(this.attributeSelector,""),Ye(e,{position:"absolute",top:"0",left:"0"}),this.alignOverlay(),this.dismissable&&this.bindOutsideClickListener(),this.bindScrollListener(),this.bindResizeListener(),this.autoZIndex&&be.set("overlay",e,this.baseZIndex+this.$primevue.config.zIndex.overlay),this.overlayEventListener=function(s){t.container.contains(s.target)&&(t.selfClick=!0)},this.focus(),pe.on("overlay-click",this.overlayEventListener),this.$emit("show"),this.closeOnEscape&&this.bindDocumentKeyDownListener()},onLeave:function(){this.unbindOutsideClickListener(),this.unbindScrollListener(),this.unbindResizeListener(),this.unbindDocumentKeyDownListener(),pe.off("overlay-click",this.overlayEventListener),this.overlayEventListener=null,this.$emit("hide")},onAfterLeave:function(e){this.autoZIndex&&be.clear(e)},alignOverlay:function(){Ze(this.container,this.target,!1);var e=Se(this.container),t=Se(this.target),s=0;e.left<t.left&&(s=t.left-e.left),this.container.style.setProperty(Ge("popover.arrow.left").name,"".concat(s,"px")),e.top<t.top&&(this.container.setAttribute("data-p-popover-flipped","true"),!this.isUnstyled&&Je(this.container,"p-popover-flipped"))},onContentKeydown:function(e){e.code==="Escape"&&this.closeOnEscape&&(this.hide(),ae(this.target))},onButtonKeydown:function(e){switch(e.code){case"ArrowDown":case"ArrowUp":case"ArrowLeft":case"ArrowRight":e.preventDefault()}},focus:function(){var e=this.container.querySelector("[autofocus]");e&&e.focus()},onKeyDown:function(e){e.code==="Escape"&&this.closeOnEscape&&(this.visible=!1)},bindDocumentKeyDownListener:function(){this.documentKeydownListener||(this.documentKeydownListener=this.onKeyDown.bind(this),window.document.addEventListener("keydown",this.documentKeydownListener))},unbindDocumentKeyDownListener:function(){this.documentKeydownListener&&(window.document.removeEventListener("keydown",this.documentKeydownListener),this.documentKeydownListener=null)},bindOutsideClickListener:function(){var e=this;!this.outsideClickListener&&qe()&&(this.outsideClickListener=function(t){e.visible&&!e.selfClick&&!e.isTargetClicked(t)&&(e.visible=!1),e.selfClick=!1},document.addEventListener("click",this.outsideClickListener))},unbindOutsideClickListener:function(){this.outsideClickListener&&(document.removeEventListener("click",this.outsideClickListener),this.outsideClickListener=null,this.selfClick=!1)},bindScrollListener:function(){var e=this;this.scrollHandler||(this.scrollHandler=new Qe(this.target,function(){e.visible&&(e.visible=!1)})),this.scrollHandler.bindScrollListener()},unbindScrollListener:function(){this.scrollHandler&&this.scrollHandler.unbindScrollListener()},bindResizeListener:function(){var e=this;this.resizeListener||(this.resizeListener=function(){e.visible&&!Xe()&&(e.visible=!1)},window.addEventListener("resize",this.resizeListener))},unbindResizeListener:function(){this.resizeListener&&(window.removeEventListener("resize",this.resizeListener),this.resizeListener=null)},isTargetClicked:function(e){return this.eventTarget&&(this.eventTarget===e.target||this.eventTarget.contains(e.target))},containerRef:function(e){this.container=e},createStyle:function(){if(!this.styleElement&&!this.isUnstyled){var e;this.styleElement=document.createElement("style"),this.styleElement.type="text/css",et(this.styleElement,"nonce",(e=this.$primevue)===null||e===void 0||(e=e.config)===null||e===void 0||(e=e.csp)===null||e===void 0?void 0:e.nonce),document.head.appendChild(this.styleElement);var t="";for(var s in this.breakpoints)t+=`
                        @media screen and (max-width: `.concat(s,`) {
                            .p-popover[`).concat(this.attributeSelector,`] {
                                width: `).concat(this.breakpoints[s],` !important;
                            }
                        }
                    `);this.styleElement.innerHTML=t}},destroyStyle:function(){this.styleElement&&(document.head.removeChild(this.styleElement),this.styleElement=null)},onOverlayClick:function(e){pe.emit("overlay-click",{originalEvent:e,target:this.target})}},computed:{attributeSelector:function(){return tt()}},directives:{focustrap:Rt,ripple:nt},components:{Portal:ot}},Bt=["aria-modal"];function Vt(n,e,t,s,m,u){var x=it("Portal"),E=Me("focustrap");return B(),rt(x,{appendTo:n.appendTo},{default:me(function(){return[R(at,ee({name:"p-popover",onEnter:u.onEnter,onLeave:u.onLeave,onAfterLeave:u.onAfterLeave},n.ptm("transition")),{default:me(function(){return[m.visible?se((B(),K("div",ee({key:0,ref:u.containerRef,role:"dialog","aria-modal":m.visible,onClick:e[3]||(e[3]=function(){return u.onOverlayClick&&u.onOverlayClick.apply(u,arguments)}),class:n.cx("root")},n.ptmi("root")),[n.$slots.container?$e(n.$slots,"container",{key:0,closeCallback:u.hide,keydownCallback:function(C){return u.onButtonKeydown(C)}}):(B(),K("div",ee({key:1,class:n.cx("content"),onClick:e[0]||(e[0]=function(){return u.onContentClick&&u.onContentClick.apply(u,arguments)}),onMousedown:e[1]||(e[1]=function(){return u.onContentClick&&u.onContentClick.apply(u,arguments)}),onKeydown:e[2]||(e[2]=function(){return u.onContentKeydown&&u.onContentKeydown.apply(u,arguments)})},n.ptm("content")),[$e(n.$slots,"default")],16))],16,Bt)),[[E]]):st("",!0)]}),_:3},16,["onEnter","onLeave","onAfterLeave"])]}),_:3},8,["appendTo"])}je.render=Vt;var Ut=function(e){var t=e.dt;return`
.p-radiobutton {
    position: relative;
    display: inline-flex;
    user-select: none;
    vertical-align: bottom;
    width: `.concat(t("radiobutton.width"),`;
    height: `).concat(t("radiobutton.height"),`;
}

.p-radiobutton-input {
    cursor: pointer;
    appearance: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    opacity: 0;
    z-index: 1;
    outline: 0 none;
    border: 1px solid transparent;
    border-radius: 50%;
}

.p-radiobutton-box {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    border: 1px solid `).concat(t("radiobutton.border.color"),`;
    background: `).concat(t("radiobutton.background"),`;
    width: `).concat(t("radiobutton.width"),`;
    height: `).concat(t("radiobutton.height"),`;
    transition: background `).concat(t("radiobutton.transition.duration"),", color ").concat(t("radiobutton.transition.duration"),", border-color ").concat(t("radiobutton.transition.duration"),", box-shadow ").concat(t("radiobutton.transition.duration"),", outline-color ").concat(t("radiobutton.transition.duration"),`;
    outline-color: transparent;
    box-shadow: `).concat(t("radiobutton.shadow"),`;
}

.p-radiobutton-icon {
    transition-duration: `).concat(t("radiobutton.transition.duration"),`;
    background: transparent;
    font-size: `).concat(t("radiobutton.icon.size"),`;
    width: `).concat(t("radiobutton.icon.size"),`;
    height: `).concat(t("radiobutton.icon.size"),`;
    border-radius: 50%;
    backface-visibility: hidden;
    transform: translateZ(0) scale(0.1);
}

.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {
    border-color: `).concat(t("radiobutton.hover.border.color"),`;
}

.p-radiobutton-checked .p-radiobutton-box {
    border-color: `).concat(t("radiobutton.checked.border.color"),`;
    background: `).concat(t("radiobutton.checked.background"),`;
}

.p-radiobutton-checked .p-radiobutton-box .p-radiobutton-icon {
    background: `).concat(t("radiobutton.icon.checked.color"),`;
    transform: translateZ(0) scale(1, 1);
    visibility: visible;
}

.p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:hover) .p-radiobutton-box {
    border-color: `).concat(t("radiobutton.checked.hover.border.color"),`;
    background: `).concat(t("radiobutton.checked.hover.background"),`;
}

.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:hover).p-radiobutton-checked .p-radiobutton-box .p-radiobutton-icon {
    background: `).concat(t("radiobutton.icon.checked.hover.color"),`;
}

.p-radiobutton:not(.p-disabled):has(.p-radiobutton-input:focus-visible) .p-radiobutton-box {
    border-color: `).concat(t("radiobutton.focus.border.color"),`;
    box-shadow: `).concat(t("radiobutton.focus.ring.shadow"),`;
    outline: `).concat(t("radiobutton.focus.ring.width")," ").concat(t("radiobutton.focus.ring.style")," ").concat(t("radiobutton.focus.ring.color"),`;
    outline-offset: `).concat(t("radiobutton.focus.ring.offset"),`;
}

.p-radiobutton-checked:not(.p-disabled):has(.p-radiobutton-input:focus-visible) .p-radiobutton-box {
    border-color: `).concat(t("radiobutton.checked.focus.border.color"),`;
}

.p-radiobutton.p-invalid > .p-radiobutton-box {
    border-color: `).concat(t("radiobutton.invalid.border.color"),`;
}

.p-radiobutton.p-variant-filled .p-radiobutton-box {
    background: `).concat(t("radiobutton.filled.background"),`;
}

.p-radiobutton.p-variant-filled.p-radiobutton-checked .p-radiobutton-box {
    background: `).concat(t("radiobutton.checked.background"),`;
}

.p-radiobutton.p-variant-filled:not(.p-disabled):has(.p-radiobutton-input:hover).p-radiobutton-checked .p-radiobutton-box {
    background: `).concat(t("radiobutton.checked.hover.background"),`;
}

.p-radiobutton.p-disabled {
    opacity: 1;
}

.p-radiobutton.p-disabled .p-radiobutton-box {
    background: `).concat(t("radiobutton.disabled.background"),`;
    border-color: `).concat(t("radiobutton.checked.disabled.border.color"),`;
}

.p-radiobutton-checked.p-disabled .p-radiobutton-box .p-radiobutton-icon {
    background: `).concat(t("radiobutton.icon.disabled.color"),`;
}
`)},Kt={root:function(e){var t=e.instance,s=e.props;return["p-radiobutton p-component",{"p-radiobutton-checked":t.checked,"p-disabled":s.disabled,"p-invalid":s.invalid,"p-variant-filled":s.variant?s.variant==="filled":t.$primevue.config.inputStyle==="filled"||t.$primevue.config.inputVariant==="filled"}]},box:"p-radiobutton-box",input:"p-radiobutton-input",icon:"p-radiobutton-icon"},Nt=ge.extend({name:"radiobutton",theme:Ut,classes:Kt}),Wt={name:"BaseRadioButton",extends:De,props:{value:null,modelValue:null,binary:Boolean,name:{type:String,default:null},variant:{type:String,default:null},invalid:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},tabindex:{type:Number,default:null},inputId:{type:String,default:null},inputClass:{type:[String,Object],default:null},inputStyle:{type:Object,default:null},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:Nt,provide:function(){return{$pcRadioButton:this,$parentInstance:this}}},ye={name:"RadioButton",extends:Wt,inheritAttrs:!1,emits:["update:modelValue","change","focus","blur"],methods:{getPTOptions:function(e){var t=e==="root"?this.ptmi:this.ptm;return t(e,{context:{checked:this.checked,disabled:this.disabled}})},onChange:function(e){if(!this.disabled&&!this.readonly){var t=this.binary?!this.checked:this.value;this.$emit("update:modelValue",t),this.$emit("change",e)}},onFocus:function(e){this.$emit("focus",e)},onBlur:function(e){this.$emit("blur",e)}},computed:{checked:function(){return this.modelValue!=null&&(this.binary?!!this.modelValue:ct(this.modelValue,this.value))}}},Yt=["data-p-checked","data-p-disabled"],Zt=["id","value","name","checked","tabindex","disabled","readonly","aria-labelledby","aria-label","aria-invalid"];function Gt(n,e,t,s,m,u){return B(),K("div",ee({class:n.cx("root")},u.getPTOptions("root"),{"data-p-checked":u.checked,"data-p-disabled":n.disabled}),[g("input",ee({id:n.inputId,type:"radio",class:[n.cx("input"),n.inputClass],style:n.inputStyle,value:n.value,name:n.name,checked:u.checked,tabindex:n.tabindex,disabled:n.disabled,readonly:n.readonly,"aria-labelledby":n.ariaLabelledby,"aria-label":n.ariaLabel,"aria-invalid":n.invalid||void 0,onFocus:e[0]||(e[0]=function(){return u.onFocus&&u.onFocus.apply(u,arguments)}),onBlur:e[1]||(e[1]=function(){return u.onBlur&&u.onBlur.apply(u,arguments)}),onChange:e[2]||(e[2]=function(){return u.onChange&&u.onChange.apply(u,arguments)})},u.getPTOptions("input")),null,16,Zt),g("div",ee({class:n.cx("box")},u.getPTOptions("box")),[g("div",ee({class:n.cx("icon")},u.getPTOptions("icon")),null,16)],16)],16,Yt)}ye.render=Gt;var Ie={exports:{}};(function(n,e){(function(t,s){n.exports=s()})(lt,function(){var t=1e3,s=6e4,m=36e5,u="millisecond",x="second",E="minute",k="hour",C="day",b="week",v="month",M="quarter",O="year",P="date",A="Invalid Date",W=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,J=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,q={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(f){var a=["th","st","nd","rd"],r=f%100;return"["+f+(a[(r-20)%10]||a[r]||a[0])+"]"}},Y=function(f,a,r){var d=String(f);return!d||d.length>=a?f:""+Array(a+1-d.length).join(r)+f},te={s:Y,z:function(f){var a=-f.utcOffset(),r=Math.abs(a),d=Math.floor(r/60),l=r%60;return(a<=0?"+":"-")+Y(d,2,"0")+":"+Y(l,2,"0")},m:function f(a,r){if(a.date()<r.date())return-f(r,a);var d=12*(r.year()-a.year())+(r.month()-a.month()),l=a.clone().add(d,v),h=r-l<0,o=a.clone().add(d+(h?-1:1),v);return+(-(d+(r-l)/(h?l-o:o-l))||0)},a:function(f){return f<0?Math.ceil(f)||0:Math.floor(f)},p:function(f){return{M:v,y:O,w:b,d:C,D:P,h:k,m:E,s:x,ms:u,Q:M}[f]||String(f||"").toLowerCase().replace(/s$/,"")},u:function(f){return f===void 0}},z="en",H={};H[z]=q;var Q="$isDayjsObject",G=function(f){return f instanceof V||!(!f||!f[Q])},T=function f(a,r,d){var l;if(!a)return z;if(typeof a=="string"){var h=a.toLowerCase();H[h]&&(l=h),r&&(H[h]=r,l=h);var o=a.split("-");if(!l&&o.length>1)return f(o[0])}else{var i=a.name;H[i]=a,l=i}return!d&&l&&(z=l),l||!d&&z},w=function(f,a){if(G(f))return f.clone();var r=typeof a=="object"?a:{};return r.date=f,r.args=arguments,new V(r)},y=te;y.l=T,y.i=G,y.w=function(f,a){return w(f,{locale:a.$L,utc:a.$u,x:a.$x,$offset:a.$offset})};var V=function(){function f(r){this.$L=T(r.locale,null,!0),this.parse(r),this.$x=this.$x||r.x||{},this[Q]=!0}var a=f.prototype;return a.parse=function(r){this.$d=function(d){var l=d.date,h=d.utc;if(l===null)return new Date(NaN);if(y.u(l))return new Date;if(l instanceof Date)return new Date(l);if(typeof l=="string"&&!/Z$/i.test(l)){var o=l.match(W);if(o){var i=o[2]-1||0,p=(o[7]||"0").substring(0,3);return h?new Date(Date.UTC(o[1],i,o[3]||1,o[4]||0,o[5]||0,o[6]||0,p)):new Date(o[1],i,o[3]||1,o[4]||0,o[5]||0,o[6]||0,p)}}return new Date(l)}(r),this.init()},a.init=function(){var r=this.$d;this.$y=r.getFullYear(),this.$M=r.getMonth(),this.$D=r.getDate(),this.$W=r.getDay(),this.$H=r.getHours(),this.$m=r.getMinutes(),this.$s=r.getSeconds(),this.$ms=r.getMilliseconds()},a.$utils=function(){return y},a.isValid=function(){return this.$d.toString()!==A},a.isSame=function(r,d){var l=w(r);return this.startOf(d)<=l&&l<=this.endOf(d)},a.isAfter=function(r,d){return w(r)<this.startOf(d)},a.isBefore=function(r,d){return this.endOf(d)<w(r)},a.$g=function(r,d,l){return y.u(r)?this[d]:this.set(l,r)},a.unix=function(){return Math.floor(this.valueOf()/1e3)},a.valueOf=function(){return this.$d.getTime()},a.startOf=function(r,d){var l=this,h=!!y.u(d)||d,o=y.p(r),i=function(_,L){var U=y.w(l.$u?Date.UTC(l.$y,L,_):new Date(l.$y,L,_),l);return h?U:U.endOf(C)},p=function(_,L){return y.w(l.toDate()[_].apply(l.toDate("s"),(h?[0,0,0,0]:[23,59,59,999]).slice(L)),l)},c=this.$W,S=this.$M,D=this.$D,$="set"+(this.$u?"UTC":"");switch(o){case O:return h?i(1,0):i(31,11);case v:return h?i(1,S):i(0,S+1);case b:var I=this.$locale().weekStart||0,N=(c<I?c+7:c)-I;return i(h?D-N:D+(6-N),S);case C:case P:return p($+"Hours",0);case k:return p($+"Minutes",1);case E:return p($+"Seconds",2);case x:return p($+"Milliseconds",3);default:return this.clone()}},a.endOf=function(r){return this.startOf(r,!1)},a.$set=function(r,d){var l,h=y.p(r),o="set"+(this.$u?"UTC":""),i=(l={},l[C]=o+"Date",l[P]=o+"Date",l[v]=o+"Month",l[O]=o+"FullYear",l[k]=o+"Hours",l[E]=o+"Minutes",l[x]=o+"Seconds",l[u]=o+"Milliseconds",l)[h],p=h===C?this.$D+(d-this.$W):d;if(h===v||h===O){var c=this.clone().set(P,1);c.$d[i](p),c.init(),this.$d=c.set(P,Math.min(this.$D,c.daysInMonth())).$d}else i&&this.$d[i](p);return this.init(),this},a.set=function(r,d){return this.clone().$set(r,d)},a.get=function(r){return this[y.p(r)]()},a.add=function(r,d){var l,h=this;r=Number(r);var o=y.p(d),i=function(S){var D=w(h);return y.w(D.date(D.date()+Math.round(S*r)),h)};if(o===v)return this.set(v,this.$M+r);if(o===O)return this.set(O,this.$y+r);if(o===C)return i(1);if(o===b)return i(7);var p=(l={},l[E]=s,l[k]=m,l[x]=t,l)[o]||1,c=this.$d.getTime()+r*p;return y.w(c,this)},a.subtract=function(r,d){return this.add(-1*r,d)},a.format=function(r){var d=this,l=this.$locale();if(!this.isValid())return l.invalidDate||A;var h=r||"YYYY-MM-DDTHH:mm:ssZ",o=y.z(this),i=this.$H,p=this.$m,c=this.$M,S=l.weekdays,D=l.months,$=l.meridiem,I=function(L,U,oe,ue){return L&&(L[U]||L(d,h))||oe[U].slice(0,ue)},N=function(L){return y.s(i%12||12,L,"0")},_=$||function(L,U,oe){var ue=L<12?"AM":"PM";return oe?ue.toLowerCase():ue};return h.replace(J,function(L,U){return U||function(oe){switch(oe){case"YY":return String(d.$y).slice(-2);case"YYYY":return y.s(d.$y,4,"0");case"M":return c+1;case"MM":return y.s(c+1,2,"0");case"MMM":return I(l.monthsShort,c,D,3);case"MMMM":return I(D,c);case"D":return d.$D;case"DD":return y.s(d.$D,2,"0");case"d":return String(d.$W);case"dd":return I(l.weekdaysMin,d.$W,S,2);case"ddd":return I(l.weekdaysShort,d.$W,S,3);case"dddd":return S[d.$W];case"H":return String(i);case"HH":return y.s(i,2,"0");case"h":return N(1);case"hh":return N(2);case"a":return _(i,p,!0);case"A":return _(i,p,!1);case"m":return String(p);case"mm":return y.s(p,2,"0");case"s":return String(d.$s);case"ss":return y.s(d.$s,2,"0");case"SSS":return y.s(d.$ms,3,"0");case"Z":return o}return null}(L)||o.replace(":","")})},a.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},a.diff=function(r,d,l){var h,o=this,i=y.p(d),p=w(r),c=(p.utcOffset()-this.utcOffset())*s,S=this-p,D=function(){return y.m(o,p)};switch(i){case O:h=D()/12;break;case v:h=D();break;case M:h=D()/3;break;case b:h=(S-c)/6048e5;break;case C:h=(S-c)/864e5;break;case k:h=S/m;break;case E:h=S/s;break;case x:h=S/t;break;default:h=S}return l?h:y.a(h)},a.daysInMonth=function(){return this.endOf(v).$D},a.$locale=function(){return H[this.$L]},a.locale=function(r,d){if(!r)return this.$L;var l=this.clone(),h=T(r,d,!0);return h&&(l.$L=h),l},a.clone=function(){return y.w(this.$d,this)},a.toDate=function(){return new Date(this.valueOf())},a.toJSON=function(){return this.isValid()?this.toISOString():null},a.toISOString=function(){return this.$d.toISOString()},a.toString=function(){return this.$d.toUTCString()},f}(),Z=V.prototype;return w.prototype=Z,[["$ms",u],["$s",x],["$m",E],["$H",k],["$W",C],["$M",v],["$y",O],["$D",P]].forEach(function(f){Z[f[1]]=function(a){return this.$g(a,f[0],f[1])}}),w.extend=function(f,a){return f.$i||(f(a,V,w),f.$i=!0),w},w.locale=T,w.isDayjs=G,w.unix=function(f){return w(1e3*f)},w.en=H[z],w.Ls=H,w.p={},w})})(Ie);var Jt=Ie.exports;const qt=ut(Jt),Qt={class:"hidden flex justify-center items-center w-full h-full"},Xt={class:"rounded-md flex flex-col gap-2 justify-between bg-gray-100 w-1/2 h-96"},en=[".srcObject"],tn={class:"flex justify-between items-center w-full bg-gray-50 p-2"},nn={class:"flex justify-between items-center gap-2"},on=g("i",{class:"text-lg pi pi-video"},null,-1),rn=g("i",{class:"text-lg pi pi-microphone"},null,-1),an={class:"w-full h-full relative border border-surface rounded-2xl overflow-hidden flex"},sn={class:"w-full sm:w-9/12 border-r border-surface flex flex-col relative p-4"},cn={class:"flex h-full w-full user-list flex-wrap justify-center items-center content-center"},ln={class:"box bg-gray-100 rounded-md overflow-hidden relative"},un=[".srcObject"],dn={class:"absolute w-full bottom-0 flex gap-1 items-center justify-between py-2 px-4"},fn=g("div",{class:"flex gap-2"},[g("i",{class:"pi pi-video"}),g("i",{class:"pi pi-microphone"})],-1),pn=["onResize",".srcObject","id"],vn={class:"absolute w-full bottom-0 flex gap-1 items-center justify-between py-2 px-4"},bn=g("div",{class:"flex gap-2"},[g("i",{class:"pi pi-video"}),g("i",{class:"pi pi-microphone"})],-1),hn={class:"absolute top-8 left-0 right-0 flex items-center justify-center"},mn={class:"bg-gray-200 flex gap-2 rounded-md h-10 p-1 opacity-90"},yn={class:"flex flex-col gap-4 w-fit"},gn=g("span",{class:"font-medium block mb-2"},"麦克风",-1),kn=["for"],wn=g("span",{class:"font-medium block mb-2"},"摄像头",-1),Sn=["for"],$n={class:"hidden sm:flex w-3/12 min-w-40 flex-col gap-6 p-4"},_n={class:"flex-1 overflow-auto"},Cn={target:"_blank",class:"block my-0 mx-2",href:""},Ln=["src"],xn={class:"text-sm w-fit mb-2"},En={class:"flex justify-between items-end gap-2"},Mn=dt({__name:"MeetingView",setup(n){const e=ft(),t=pt(),s=j([]),m=j([]),u=j([]),x=kt(),E=x.params.id,k=j(),C=j(!0),b=j([]),v=vt({video:!1,audio:{echoCancellation:!0}}),M=j([]),O=j(""),P=j(),A=j();let W;bt(x.params.id).catch(o=>{window.location.href="/"});const J=o=>{const i=document.getElementById(o);i&&console.log(`Remote video ${o} size changed to ${i.videoWidth}x${i.videoHeight}`)},q={offerToReceiveAudio:!0,offerToReceiveVideo:!0},Y=j({video:"",audio:""}),te={iceServers:[{urls:["turn:47.109.86.108:3478"],username:"new",credential:"new"}]};ht().then(o=>{const[i,p,c]=o;m.value.push(...i),s.value.push(...p),u.value.push(...c)}),mt(()=>{yt().then(o=>{W=Tt(gt({MeetingId:E,Signature:o.signature,...o.parameters}),{autoReconnect:!0,autoClose:!0,heartbeat:{message:"ping",interval:15e3,pongTimeout:1e3},onMessage(p,c){c.data.split(`
`).forEach(async S=>{var N;if(S==="pong")return;const{cmd:D,payload:$}=JSON.parse(S);let I;switch(D){case 10001:M.value.push($);break;case 10002:$.id===t.info.id?W(JSON.stringify({cmd:10004})):b.value.push(z($));break;case 10003:b.value=b.value.filter(_=>{const L=_.id===$.id;return L&&(_.stream&&a(_.stream),_.pc&&_.pc.close()),!L});break;case 10005:if(I=b.value.find(_=>_.id===$.FromUser),I){const _=I.pc;switch($.Event){case"icecandidate":await _.addIceCandidate($.Payload);break;case"offer":await _.setRemoteDescription(new RTCSessionDescription($.Payload)),_.createAnswer().then(L=>{_.setLocalDescription(L).then(()=>H(I.id,"answer",L))});break;case"answer":await _.setRemoteDescription(new RTCSessionDescription($.Payload));break}}break;case 10008:if($.id===t.info.id)(N=k.value)!=null&&N.getAudioTracks()&&(C.value=k.value.getAudioTracks()[0].enabled=$.muted),C.value=$.muted;else{let _=b.value.find(L=>L.id===$.id);_&&Object.assign(_,$)}break;case 10004:for(let _=0;_<$.length;_++){let L=$[_];if(L.id!==t.info.id)try{L=z(L);const U=await L.pc.createOffer(q);await L.pc.setLocalDescription(U),H(L.id,"offer",U),b.value.push(L)}catch(U){alert(`getUserMedia() error: ${U}`)}}break}})}}).send})});function z(o){return o.pc=new RTCPeerConnection(te),o.pc.addEventListener("icecandidate",i=>{i.candidate&&H(o.id,"icecandidate",i.candidate)}),o.pc.addEventListener("track",i=>{console.log("track",i),o.stream!==i.streams[0]&&(b.value=b.value.map(p=>(p.id===o.id&&(p.stream=i.streams[0]),p)))}),k.value&&k.value.getTracks().forEach(i=>{o.pc.addTrack(i,k.value)}),o}function H(o,i,p){W(JSON.stringify({cmd:10005,payload:{Event:i,ToUser:o,Payload:p,FromUser:t.info.id}}))}function Q(){W(JSON.stringify({cmd:10001,payload:{user:t.info,message:O.value}})),O.value=""}async function G(o){const i=await o.pc.createOffer();await o.pc.setLocalDescription(i),H(o.id,"offer",i)}ce(v,async(o,i,p)=>{try{if(!o.video&&!o.audio){a(k.value),k.value=void 0;return}const c=await navigator.mediaDevices.getUserMedia({video:o.video,audio:o.audio?{echoCancellation:!0}:!1});a(k.value),k.value=c;const[S]=c.getVideoTracks(),[D]=c.getAudioTracks();b.value.forEach($=>{if(S){console.log("Got video track ",S);const I=$.pc.getSenders().find(N=>{var _;return((_=N.track)==null?void 0:_.kind)===S.kind});I?(console.log("Got video sender"),I.replaceTrack(S)):($.pc.addTrack(S,c),G($))}if(D){console.log("Got audio track ",D);const I=$.pc.getSenders().find(N=>{var _;return((_=N.track)==null?void 0:_.kind)===D.kind});I&&(console.log("Got audio sender ",D),I.replaceTrack(D))}})}catch(c){e.add({severity:"error",summary:"出错了",detail:c.message,group:"br",life:3e3})}}),j({Camera:1,Screen:2}.Camera);const w=j(!1),y=()=>{w.value?(a(k.value),k.value=void 0,w.value=!1):(console.log("share screen"),navigator.mediaDevices.getDisplayMedia({video:!0,audio:v.audio}).then(o=>{a(k.value),k.value=o,o.addEventListener("inactive",i=>{a(o),k.value=void 0}),b.value.forEach(i=>{const[p]=o.getVideoTracks();if(p){const S=i.pc.getSenders().find(D=>{var $;return(($=D.track)==null?void 0:$.kind)===p.kind});S?S.replaceTrack(p):i.pc.addTrack(p,o)}const[c]=o.getAudioTracks();if(c){const S=i.pc.getSenders().find(D=>{var $;return(($=D.track)==null?void 0:$.kind)===c.kind});S&&S.replaceTrack(c)}G(i)}),w.value=!0}))},V=j([]),Z=j(!1),f=async()=>{if(Z.value){const o=new Blob(V.value,{type:"video/webm"}),i=URL.createObjectURL(o),p=document.createElement("a");p.href=i,p.style.display="none",p.download=`屏幕录制${qt().format("YYYYMMDDHHmm")}.webm`,p.click()}else{V.value=[],A.value=await navigator.mediaDevices.getDisplayMedia({video:!0,audio:{echoCancellation:!0}});const o=new MediaRecorder(A.value,{mimeType:"video/webm"});o.ondataavailable=i=>V.value.push(i.data),o.start(100),e.add({severity:"success",summary:"录制中...",detail:"再次点击录制按钮结束录制",life:3e3})}Z.value=!Z.value},a=o=>{o&&o.getTracks().forEach(function(i){i.readyState==="live"&&i.stop()})},r=j(),d=j({video:!1,audio:!1});ce(d.value,async o=>{if(!o.audio&&!o.video)a(r.value),r.value=void 0;else{const i=r.value;r.value=await navigator.mediaDevices.getUserMedia(o),a(i)}});const l=j(!1),h=()=>{k.value=r.value,r.value=void 0,l.value=!0};return(o,i)=>{const p=Me("tooltip");return B(),K(re,null,[g("div",Qt,[g("div",Xt,[g("video",{class:"flex-1 h-80",".srcObject":r.value,playsinline:"",autoplay:""},null,40,en),g("div",tn,[g("div",nn,[on,R(F(xe),{modelValue:d.value.video,"onUpdate:modelValue":i[0]||(i[0]=c=>d.value.video=c)},null,8,["modelValue"]),rn,R(F(xe),{modelValue:d.value.audio,"onUpdate:modelValue":i[1]||(i[1]=c=>d.value.audio=c)},null,8,["modelValue"])]),g("div",null,[R(F(X),{onClick:h,class:"px-3 py-1 text-sm",label:"进入会议"})])])])]),g("div",an,[g("div",sn,[g("div",cn,[g("div",ln,[g("video",{".srcObject":k.value,playsinline:"",muted:"",autoplay:""},null,40,un),g("div",dn,[R(F(Ce),{image:F(t).info.avatar,shape:"circle"},null,8,["image"]),fn])]),(B(!0),K(re,null,de(b.value,c=>(B(),K("div",{class:"box bg-gray-100 rounded-md overflow-hidden relative",key:c.id},[(B(),K("video",{onResize:()=>J(c.id),".srcObject":c.stream,id:c.id,key:c.id,playsinline:"",autoplay:""},null,40,pn)),g("div",vn,[R(F(Ce),{image:c.avatar,shape:"circle"},null,8,["image"]),bn])]))),128))]),g("div",hn,[g("div",mn,[se(R(F(X),{onClick:f,icon:"pi pi-stop-circle",text:"",class:"py-0 hidden sm:block"},null,512),[[p,{value:"屏幕录制"},void 0,{bottom:!0}]]),se(R(F(X),{icon:"pi pi-share-alt",class:"py-0 hidden sm:block",text:"",onClick:y},null,512),[[p,{value:"分享屏幕"},void 0,{bottom:!0}]]),se(R(F(X),{class:"py-0",icon:"pi pi-video",text:"",onClick:i[2]||(i[2]=()=>v.video=!v.video)},null,512),[[p,{value:"摄像头"},void 0,{bottom:!0}]]),se(R(F(X),{icon:"pi pi-microphone",class:_e(["py-0",C.value?"":"bg-green-100"]),text:"",onClick:i[3]||(i[3]=()=>F(W)(JSON.stringify({cmd:10006,payload:{id:F(t).info.id,muted:!C.value}})))},null,8,["class"]),[[p,{value:"麦克风"},void 0,{bottom:!0}]]),R(F(X),{type:"button",icon:"pi pi-cog",text:"",onClick:i[4]||(i[4]=c=>P.value.toggle(c))}),R(F(je),{ref_key:"op",ref:P},{default:me(()=>[g("div",yn,[g("div",null,[gn,(B(!0),K(re,null,de(s.value,c=>(B(),K("div",{key:c.deviceId,class:"flex items-center"},[R(F(ye),{onClick:()=>v.audio={deviceId:c.deviceId},modelValue:Y.value.audio,"onUpdate:modelValue":i[5]||(i[5]=S=>Y.value.audio=S),inputId:c.deviceId,name:"audio",value:c.deviceId},null,8,["onClick","modelValue","inputId","value"]),g("label",{for:c.deviceId,class:"ml-2"},fe(c.label),9,kn)]))),128)),wn,(B(!0),K(re,null,de(m.value,c=>(B(),K("div",{key:c.deviceId,class:"flex items-center"},[R(F(ye),{onClick:()=>v.video={deviceId:c.deviceId},modelValue:Y.value.video,"onUpdate:modelValue":i[6]||(i[6]=S=>Y.value.video=S),inputId:c.deviceId,name:"video",value:c.deviceId},null,8,["onClick","modelValue","inputId","value"]),g("label",{for:c.deviceId,class:"ml-2"},fe(c.label),9,Sn)]))),128))])])]),_:1},512)])])]),g("div",$n,[g("div",_n,[(B(!0),K(re,null,de(M.value,c=>(B(),K("div",{key:c.id,class:"flex py-1.5 px-0",style:Le({flexDirection:c.user.id===F(t).info.id?"row-reverse":"row"})},[g("a",Cn,[g("img",{src:c.user.avatar,alt:"",class:"h-10 w-10 border-2 border-white rounded-full"},null,8,Ln)]),g("div",{class:_e(["flex flex-col w-fit",c.user.id===F(t).info.id?"items-end":"items-start"])},[g("div",xn,fe(c.user.nickname),1),g("div",{class:"p-3 w-fit text-sm bg-gray-100",style:Le({borderRadius:c.user.id===F(t).info.id?"1.8em 0.1em 1.8em 1.8em":"0.1em 1.8em 1.8em"})},fe(c.message),5)],2)],4))),128))]),g("div",En,[R(F(wt),{modelValue:O.value,"onUpdate:modelValue":i[7]||(i[7]=c=>O.value=c),autoResize:"",rows:"1",class:"w-full max-h-20 min-h-8"},null,8,["modelValue"]),R(F(X),{icon:"pi pi-face-smile",text:""}),R(F(X),{icon:"pi pi-send",text:"",class:"",onClick:Q})])])])],64)}}});export{Mn as default};
