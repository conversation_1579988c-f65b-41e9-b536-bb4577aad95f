package service

import (
	"context"
	"meeting/internal/entity"
	"meeting/internal/repository"

	"go.uber.org/zap"
)

// StickerService 贴纸服务接口
type StickerService interface {
	GetAllStickers(ctx context.Context) ([]*entity.Sticker, error)
	GetSticker(ctx context.Context, id uint) (*entity.Sticker, error)
	CreateSticker(ctx context.Context, sticker *entity.Sticker) error
	UpdateSticker(ctx context.Context, sticker *entity.Sticker) error
	DeleteSticker(ctx context.Context, id uint) error
	GetStickerDetail(ctx context.Context, uuid string, currentUser *entity.User) (*StickerDetail, error)
	FavoriteSticker(ctx context.Context, uuid string, user *entity.User, isDelete bool) error
	GetStickerList(ctx context.Context, page, pageSize int, keyword, categoryID string) (*StickerList, error)
	GetRandomSticker(ctx context.Context, categoryID string) (*StickerDetail, error)
}

// stickerService 贴纸服务实现
type stickerService struct {
	stickerRepo repository.StickerRepository
	logger      *zap.SugaredLogger
}

// NewStickerService 创建贴纸服务
func NewStickerService(stickerRepo repository.StickerRepository, logger *zap.SugaredLogger) StickerService {
	return &stickerService{
		stickerRepo: stickerRepo,
		logger:      logger,
	}
}

type StickerDetail struct {
	ID        uint
	UUID      string
	Name      string
	Image     string
	Views     uint
	Pack      *StickerPackDetail
	Favorited bool
}

type StickerPackDetail struct {
	ID    uint
	UUID  string
	Name  string
	Image string
}

type StickerList struct {
	Data        []StickerDetail
	Total       int64
	CurrentPage int
	PerPage     int
	CurrentSize int
}

func (s *stickerService) GetAllStickers(ctx context.Context) ([]*entity.Sticker, error) {
	s.logger.Info("Getting all stickers")
	return s.stickerRepo.GetAll(ctx)
}

func (s *stickerService) GetSticker(ctx context.Context, id uint) (*entity.Sticker, error) {
	s.logger.Infow("Getting sticker", "id", id)
	return s.stickerRepo.GetByID(ctx, id)
}

func (s *stickerService) CreateSticker(ctx context.Context, sticker *entity.Sticker) error {
	s.logger.Infow("Creating sticker", "sticker", sticker)
	return s.stickerRepo.Create(ctx, sticker)
}

func (s *stickerService) UpdateSticker(ctx context.Context, sticker *entity.Sticker) error {
	s.logger.Infow("Updating sticker", "sticker", sticker)
	return s.stickerRepo.Update(ctx, sticker)
}

func (s *stickerService) DeleteSticker(ctx context.Context, id uint) error {
	s.logger.Infow("Deleting sticker", "id", id)
	return s.stickerRepo.Delete(ctx, id)
}

// GetRandomSticker 获取随机贴纸
func (s *stickerService) GetRandomSticker(ctx context.Context, categoryID string) (*StickerDetail, error) {
	// 获取随机贴纸
	stickers, err := s.stickerRepo.FindStickers(ctx, 0, 1, "", categoryID)
	if err != nil {
		return nil, err
	}

	if len(stickers) == 0 {
		return nil, nil
	}

	sticker := stickers[0]

	// 增加浏览次数
	err = s.stickerRepo.IncrementViews(ctx, sticker.ID)
	if err != nil {
		s.logger.Warnw("Failed to increment views", "error", err)
	}

	// 构造返回数据
	detail := &StickerDetail{
		ID:    sticker.ID,
		UUID:  sticker.UUID,
		Name:  sticker.Name,
		Image: sticker.Image,
		Views: sticker.Views + 1, // 返回更新后的浏览量
	}

	return detail, nil
}

// GetStickerDetail 获取贴纸详情
func (s *stickerService) GetStickerDetail(ctx context.Context, uuid string, currentUser *entity.User) (*StickerDetail, error) {
	// 查找贴纸
	sticker, err := s.stickerRepo.FindByUUID(ctx, uuid)
	if err != nil {
		return nil, err
	}

	// 增加浏览次数
	err = s.stickerRepo.IncrementViews(ctx, sticker.ID)
	if err != nil {
		s.logger.Warnw("Failed to increment views", "error", err)
	}

	// 构造返回数据
	detail := &StickerDetail{
		ID:    sticker.ID,
		UUID:  sticker.UUID,
		Name:  sticker.Name,
		Image: sticker.Image,
		Views: sticker.Views + 1, // 返回更新后的浏览量
	}

	// 如果有当前用户，检查是否已收藏
	if currentUser != nil {
		_, err := s.stickerRepo.FindFavoriteByUserAndModel(ctx, currentUser.ID, "sticker", sticker.ID)
		detail.Favorited = err == nil // 如果没有错误说明找到了收藏记录
	}

	return detail, nil
}

// FavoriteSticker 贴纸收藏/取消收藏
func (s *stickerService) FavoriteSticker(ctx context.Context, uuid string, user *entity.User, isDelete bool) error {
	// 查找贴纸
	sticker, err := s.stickerRepo.FindByUUID(ctx, uuid)
	if err != nil {
		return err
	}

	// 根据是否删除操作决定行为
	if isDelete {
		// 取消收藏
		err = s.stickerRepo.DeleteFavorite(ctx, user.ID, "sticker", sticker.ID)
	} else {
		// 收藏贴纸
		// 先检查是否已经收藏
		_, err := s.stickerRepo.FindFavoriteByUserAndModel(ctx, user.ID, "sticker", sticker.ID)
		if err != nil {
			// 未找到收藏记录，创建新的收藏
			favorite := &entity.Favorite{
				UserID:    user.ID,
				ModelType: "sticker",
				ModelID:   sticker.ID,
			}
			err = s.stickerRepo.CreateFavorite(ctx, favorite)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// GetStickerList 获取贴纸列表
func (s *stickerService) GetStickerList(ctx context.Context, page, pageSize int, keyword, categoryID string) (*StickerList, error) {
	offset := (page - 1) * pageSize

	// 获取贴纸列表
	stickers, err := s.stickerRepo.FindStickers(ctx, offset, pageSize, keyword, categoryID)
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	stickerDetails := make([]StickerDetail, len(stickers))
	for i, sticker := range stickers {
		detail := StickerDetail{
			ID:    sticker.ID,
			UUID:  sticker.UUID,
			Name:  sticker.Name,
			Image: sticker.Image,
			Views: sticker.Views,
		}

		stickerDetails[i] = detail
	}

	// 返回分页结果
	response := &StickerList{
		Data:        stickerDetails,
		CurrentPage: page,
		PerPage:     pageSize,
		CurrentSize: len(stickerDetails),
	}

	return response, nil
}
