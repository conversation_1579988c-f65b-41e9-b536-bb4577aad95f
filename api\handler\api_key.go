package handler

import (
	"meeting/pkg/api"
	"meeting/pkg/auth"
	"meeting/pkg/services"
	"net/http"

	"github.com/gin-gonic/gin"
)

type apiKeyHandler struct{}

var APIKey = &apiKeyHandler{}

// CreateAPIKey 创建新的API密钥
func (h *apiKeyHandler) CreateAPIKey(ctx *gin.Context) {
	// 获取当前用户
	currentUser := auth.User(ctx)
	if currentUser == nil {
		ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("用户未登录")))
		return
	}

	// 解析请求参数
	var req struct {
		Name  string `json:"name" binding:"required"`
		Quota uint   `json:"quota" binding:"required,min=1"`
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, api.Fail(api.WithMessage("参数错误: "+err.Error())))
		return
	}

	// 获取API密钥服务
	apiKeyService := services.GetServiceManager().GetAPIKeyService()

	// 生成新的API密钥
	apiKey, err := apiKeyService.GenerateAPIKey(currentUser.ID, req.Name, req.Quota)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, api.Fail(api.WithMessage("创建API密钥失败: "+err.Error())))
		return
	}

	// 返回成功响应
	ctx.JSON(http.StatusOK, api.Okay(api.WithData(gin.H{
		"id":        apiKey.ID,
		"name":      apiKey.Name,
		"key":       apiKey.Key,
		"quota":     apiKey.Quota,
		"used":      apiKey.UsedQuota,
		"available": apiKey.Quota - apiKey.UsedQuota,
	})))
}

// ListAPIKeys 获取用户的所有API密钥
func (h *apiKeyHandler) ListAPIKeys(ctx *gin.Context) {
	// 获取当前用户
	currentUser := auth.User(ctx)
	if currentUser == nil {
		ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("用户未登录")))
		return
	}

	// 获取API密钥服务
	apiKeyService := services.GetServiceManager().GetAPIKeyService()

	// 获取用户的所有API密钥
	apiKeys, err := apiKeyService.GetAPIKeysByUserID(currentUser.UUID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, api.Fail(api.WithMessage("获取API密钥列表失败: "+err.Error())))
		return
	}

	// 构造响应数据
	keys := make([]gin.H, len(apiKeys))
	for i, key := range apiKeys {
		keys[i] = gin.H{
			"id":         key.ID,
			"name":       key.Name,
			"created_at": key.CreatedAt,
			"quota":      key.Quota,
			"used":       key.UsedQuota,
			"available":  key.Quota - key.UsedQuota,
			"is_active":  key.IsActive,
		}
	}

	// 返回成功响应
	ctx.JSON(http.StatusOK, api.Okay(api.WithData(gin.H{
		"keys": keys,
	})))
}

// DeleteAPIKey 删除API密钥
func (h *apiKeyHandler) DeleteAPIKey(ctx *gin.Context) {
	// 获取当前用户
	currentUser := auth.User(ctx)
	if currentUser == nil {
		ctx.JSON(http.StatusUnauthorized, api.Fail(api.WithMessage("用户未登录")))
		return
	}

	// 获取API密钥ID
	keyID := ctx.Param("id")
	if keyID == "" {
		ctx.JSON(http.StatusBadRequest, api.Fail(api.WithMessage("API密钥ID不能为空")))
		return
	}

	// 获取API密钥服务
	apiKeyService := services.GetServiceManager().GetAPIKeyService()

	// 删除API密钥
	err := apiKeyService.DeleteAPIKey(keyID, currentUser.UUID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, api.Fail(api.WithMessage("删除API密钥失败: "+err.Error())))
		return
	}

	// 返回成功响应
	ctx.JSON(http.StatusOK, api.Okay(api.WithMessage("API密钥删除成功")))
}
