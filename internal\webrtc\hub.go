package webrtc

import (
	"context"
	"log"
	"sync"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
)

// Client represents a connected client
type Client struct {
	ID     string          `json:"id"`
	Name   string          `json:"name"`
	Conn   *websocket.Conn `json:"-"`
	RoomID string          `json:"roomId"`
}

// Participant represents a meeting participant
type Participant struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Role string `json:"role"`
}

// Room represents a meeting room
type Room struct {
	ID           string        `json:"id"`
	Host         string        `json:"host"`
	Participants []Participant `json:"participants"`
}

// Hub maintains the set of active clients and rooms
type Hub struct {
	// Registered clients
	clients map[string]*Client

	// Registered rooms
	rooms map[string]*Room

	// Register requests from the clients
	register chan *Client

	// Unregister requests from clients
	unregister chan *Client

	// Messages from clients
	broadcast chan Message

	// Context for cancellation
	ctx    context.Context
	cancel context.CancelFunc

	// Mutex for thread safety
	mutex sync.RWMutex
}

// Message represents a WebSocket message
type Message struct {
	Type     string      `json:"type"`
	ClientID string      `json:"clientId,omitempty"`
	TargetID string      `json:"target,omitempty"`
	Data     interface{} `json:"data,omitempty"`
	RoomID   string      `json:"roomId,omitempty"`
}

// NewHub creates a new Hub
func NewHub() *Hub {
	ctx, cancel := context.WithCancel(context.Background())
	return &Hub{
		clients:    make(map[string]*Client),
		rooms:      make(map[string]*Room),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		broadcast:  make(chan Message),
		ctx:        ctx,
		cancel:     cancel,
	}
}

// Run starts the hub's main loop
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.registerClient(client)
		case client := <-h.unregister:
			h.unregisterClient(client)
		case message := <-h.broadcast:
			h.handleMessage(message)
		case <-h.ctx.Done():
			// Context cancelled, exit the loop
			log.Println("WebRTC hub shutting down...")
			return
		}
	}
}

// Stop stops the hub
func (h *Hub) Stop() {
	h.cancel()
}

// registerClient registers a new client
func (h *Hub) registerClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client.ID] = client
	log.Printf("Client %s connected", client.ID)

	// Send client their ID
	response := Message{
		Type: "id",
		Data: map[string]interface{}{
			"id": client.ID,
		},
	}
	h.sendToClient(client, response)

	// Notify all clients about the new device (only those not in rooms)
	h.broadcastToLobby(Message{
		Type: "device-discovered",
		Data: map[string]interface{}{
			"id":   client.ID,
			"name": client.Name,
		},
	}, client.ID)
}

// unregisterClient unregisters a client
func (h *Hub) unregisterClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, ok := h.clients[client.ID]; !ok {
		return
	}

	log.Printf("Client %s disconnected", client.ID)
	delete(h.clients, client.ID)

	// Handle room cleanup if client was in a room
	if client.RoomID != "" {
		if room, ok := h.rooms[client.RoomID]; ok {
			// Remove participant from room
			participants := make([]Participant, 0)
			for _, p := range room.Participants {
				if p.ID != client.ID {
					participants = append(participants, p)
				}
			}
			room.Participants = participants

			// Notify remaining participants
			for _, p := range room.Participants {
				if c, ok := h.clients[p.ID]; ok {
					response := Message{
						Type: "participant-left",
						Data: map[string]interface{}{
							"participantId": client.ID,
						},
					}
					h.sendToClient(c, response)
				}
			}

			// If room is empty, delete it
			if len(room.Participants) == 0 {
				delete(h.rooms, client.RoomID)
			}
		}
	}

	// Notify all clients about the disconnected device (only those not in rooms)
	h.broadcastToLobby(Message{
		Type: "device-disconnected",
		Data: map[string]interface{}{
			"id": client.ID,
		},
	}, client.ID)
}

// handleMessage processes messages from clients
func (h *Hub) handleMessage(message Message) {
	h.mutex.RLock()
	client, ok := h.clients[message.ClientID]
	h.mutex.RUnlock()

	if !ok {
		return
	}

	switch message.Type {
	case "discover":
		h.handleDiscover(client)
	case "room-offer":
		h.handleRoomOffer(client, message)
	case "room-answer":
		h.handleRoomAnswer(client, message)
	case "room-ice-candidate":
		h.handleRoomIceCandidate(client, message)
	case "connection-request":
		h.handleConnectionRequest(client, message)
	case "offer":
		h.handleOffer(client, message)
	case "answer":
		h.handleAnswer(client, message)
	case "ice-candidate":
		h.handleIceCandidate(client, message)
	case "create-meeting":
		h.handleCreateMeeting(client, message)
	case "create-room":
		h.handleCreateRoom(client, message)
	case "join-meeting":
		h.handleJoinMeeting(client, message)
	case "join-room":
		h.handleJoinRoom(client, message)
	case "leave-room":
		h.handleLeaveRoom(client)
	case "meeting-message":
		h.handleMeetingMessage(client, message)
	case "meeting-action":
		h.handleMeetingAction(client, message)
	case "room-message":
		h.handleRoomMessage(client, message)
	case "room-action":
		h.handleRoomAction(client, message)
	}
}

// sendToClient sends a message to a specific client
func (h *Hub) sendToClient(client *Client, message Message) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Error sending message to client %s: %v", client.ID, r)
		}
	}()

	err := client.Conn.WriteJSON(message)
	if err != nil {
		log.Printf("Error sending message to client %s: %v", client.ID, err)
		// Unregister the client if we can't send a message
		h.unregister <- client
	}
}

// broadcastToRoom sends a message to all clients in a room except the sender
func (h *Hub) broadcastToRoom(roomID, senderID string, message Message) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	if room, ok := h.rooms[roomID]; ok {
		for _, participant := range room.Participants {
			if participant.ID != senderID {
				if client, ok := h.clients[participant.ID]; ok {
					h.sendToClient(client, message)
				}
			}
		}
	}
}

// broadcastToLobby sends a message to all clients not in rooms except the sender
func (h *Hub) broadcastToLobby(message Message, senderID string) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for _, client := range h.clients {
		if client.ID != senderID && client.RoomID == "" {
			h.sendToClient(client, message)
		}
	}
}

// generateRoomId generates a random room ID
func (h *Hub) generateRoomId() string {
	return "room_" + uuid.New().String()[:8]
}

// handleDiscover sends list of all connected devices not in rooms
func (h *Hub) handleDiscover(client *Client) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	devices := make([]map[string]interface{}, 0)
	for id, c := range h.clients {
		if id != client.ID && c.RoomID == "" {
			devices = append(devices, map[string]interface{}{
				"id":   id,
				"name": c.Name,
			})
		}
	}

	response := Message{
		Type: "devices",
		Data: map[string]interface{}{
			"devices": devices,
		},
	}
	h.sendToClient(client, response)
}

// handleRoomOffer forwards WebRTC offer to other participants in the room
func (h *Hub) handleRoomOffer(client *Client, message Message) {
	if client.RoomID == "" {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	sdp, ok := data["sdp"].(string)
	if !ok {
		return
	}

	response := Message{
		Type: "offer",
		Data: map[string]interface{}{
			"source": client.ID,
			"sdp":    sdp,
		},
	}

	h.broadcastToRoom(client.RoomID, client.ID, response)
}

// handleRoomAnswer forwards WebRTC answer to other participants in the room
func (h *Hub) handleRoomAnswer(client *Client, message Message) {
	if client.RoomID == "" {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	sdp, ok := data["sdp"].(string)
	if !ok {
		return
	}

	response := Message{
		Type: "answer",
		Data: map[string]interface{}{
			"source": client.ID,
			"sdp":    sdp,
		},
	}

	h.broadcastToRoom(client.RoomID, client.ID, response)
}

// handleRoomIceCandidate forwards ICE candidate to other participants in the room
func (h *Hub) handleRoomIceCandidate(client *Client, message Message) {
	if client.RoomID == "" {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	candidate, ok := data["candidate"].(map[string]interface{})
	if !ok {
		return
	}

	response := Message{
		Type: "ice-candidate",
		Data: map[string]interface{}{
			"source":    client.ID,
			"candidate": candidate,
		},
	}

	h.broadcastToRoom(client.RoomID, client.ID, response)
}

// handleConnectionRequest forwards connection request to target device
func (h *Hub) handleConnectionRequest(client *Client, message Message) {
	h.mutex.RLock()
	targetClient, ok := h.clients[message.TargetID]
	h.mutex.RUnlock()

	if !ok {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		data = make(map[string]interface{})
	}

	name, _ := data["name"].(string)
	if name == "" {
		name = client.Name
	}

	response := Message{
		Type: "connection-request",
		Data: map[string]interface{}{
			"source": client.ID,
			"name":   name,
		},
	}

	h.sendToClient(targetClient, response)
}

// handleOffer forwards WebRTC offer to target device
func (h *Hub) handleOffer(client *Client, message Message) {
	h.mutex.RLock()
	targetClient, ok := h.clients[message.TargetID]
	h.mutex.RUnlock()

	if !ok {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	sdp, ok := data["sdp"].(string)
	if !ok {
		return
	}

	response := Message{
		Type: "offer",
		Data: map[string]interface{}{
			"source": client.ID,
			"sdp":    sdp,
		},
	}

	h.sendToClient(targetClient, response)
}

// handleAnswer forwards WebRTC answer to target device
func (h *Hub) handleAnswer(client *Client, message Message) {
	h.mutex.RLock()
	targetClient, ok := h.clients[message.TargetID]
	h.mutex.RUnlock()

	if !ok {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	sdp, ok := data["sdp"].(string)
	if !ok {
		return
	}

	response := Message{
		Type: "answer",
		Data: map[string]interface{}{
			"source": client.ID,
			"sdp":    sdp,
		},
	}

	h.sendToClient(targetClient, response)
}

// handleIceCandidate forwards ICE candidate to target device
func (h *Hub) handleIceCandidate(client *Client, message Message) {
	h.mutex.RLock()
	targetClient, ok := h.clients[message.TargetID]
	h.mutex.RUnlock()

	if !ok {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	candidate, ok := data["candidate"].(map[string]interface{})
	if !ok {
		return
	}

	response := Message{
		Type: "ice-candidate",
		Data: map[string]interface{}{
			"source":    client.ID,
			"candidate": candidate,
		},
	}

	h.sendToClient(targetClient, response)
}

// handleCreateMeeting creates a new meeting
func (h *Hub) handleCreateMeeting(client *Client, message Message) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		data = make(map[string]interface{})
	}

	displayName, _ := data["displayName"].(string)
	if displayName == "" {
		displayName = client.Name
	} else {
		client.Name = displayName
	}

	meetingID := h.generateRoomId()
	meeting := &Room{
		ID:   meetingID,
		Host: client.ID,
		Participants: []Participant{
			{
				ID:   client.ID,
				Name: displayName,
				Role: "host",
			},
		},
	}

	h.rooms[meetingID] = meeting
	client.RoomID = meetingID

	response := Message{
		Type: "meeting-created",
		Data: map[string]interface{}{
			"meetingId": meetingID,
		},
	}

	h.sendToClient(client, response)
	log.Printf("Meeting %s created by %s (%s)", meetingID, displayName, client.ID)
}

// handleCreateRoom creates a new room (legacy support)
func (h *Hub) handleCreateRoom(client *Client, message Message) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		data = make(map[string]interface{})
	}

	userName, _ := data["userName"].(string)
	if userName == "" {
		userName = client.Name
	} else {
		client.Name = userName
	}

	roomID := h.generateRoomId()
	room := &Room{
		ID:   roomID,
		Host: client.ID,
		Participants: []Participant{
			{
				ID:   client.ID,
				Name: userName,
				Role: "host",
			},
		},
	}

	h.rooms[roomID] = room
	client.RoomID = roomID

	response := Message{
		Type: "room-created",
		Data: map[string]interface{}{
			"roomId": roomID,
		},
	}

	h.sendToClient(client, response)
	log.Printf("Room %s created by %s (%s)", roomID, userName, client.ID)
}

// handleJoinMeeting handles a client joining a meeting
func (h *Hub) handleJoinMeeting(client *Client, message Message) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	meetingID, ok := data["meetingId"].(string)
	if !ok {
		return
	}

	meeting, ok := h.rooms[meetingID]
	if !ok {
		response := Message{
			Type: "meeting-error",
			Data: map[string]interface{}{
				"error": "Meeting not found",
			},
		}
		h.sendToClient(client, response)
		return
	}

	displayName, _ := data["displayName"].(string)
	if displayName == "" {
		displayName = client.Name
	} else {
		client.Name = displayName
	}

	// Add participant to meeting
	participant := Participant{
		ID:   client.ID,
		Name: displayName,
		Role: "member",
	}
	meeting.Participants = append(meeting.Participants, participant)
	client.RoomID = meetingID

	// Notify all meeting participants about the new participant
	participantJoined := Message{
		Type: "participant-joined",
		Data: map[string]interface{}{
			"participantId": client.ID,
			"name":          displayName,
			"role":          "member",
		},
	}
	h.broadcastToRoom(meetingID, client.ID, participantJoined)

	// Send meeting joined message with participant list
	response := Message{
		Type: "meeting-joined",
		Data: map[string]interface{}{
			"meetingId":    meetingID,
			"participants": meeting.Participants,
			"isHost":       false,
		},
	}

	h.sendToClient(client, response)
	log.Printf("%s (%s) joined meeting %s", displayName, client.ID, meetingID)
}

// handleJoinRoom handles a client joining a room (legacy support)
func (h *Hub) handleJoinRoom(client *Client, message Message) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	roomID, ok := data["roomId"].(string)
	if !ok {
		return
	}

	room, ok := h.rooms[roomID]
	if !ok {
		response := Message{
			Type: "room-error",
			Data: map[string]interface{}{
				"error": "Room not found",
			},
		}
		h.sendToClient(client, response)
		return
	}

	userName, _ := data["userName"].(string)
	if userName == "" {
		userName = client.Name
	} else {
		client.Name = userName
	}

	// Add participant to room
	participant := Participant{
		ID:   client.ID,
		Name: userName,
		Role: "member",
	}
	room.Participants = append(room.Participants, participant)
	client.RoomID = roomID

	// Notify all room participants about the new participant
	participantJoined := Message{
		Type: "participant-joined",
		Data: map[string]interface{}{
			"participantId": client.ID,
			"name":          userName,
			"role":          "member",
		},
	}
	h.broadcastToRoom(roomID, client.ID, participantJoined)

	// Send room joined message with participant list
	response := Message{
		Type: "room-joined",
		Data: map[string]interface{}{
			"roomId":       roomID,
			"participants": room.Participants,
			"isHost":       false,
		},
	}

	h.sendToClient(client, response)
	log.Printf("%s (%s) joined room %s", userName, client.ID, roomID)
}

// handleLeaveRoom handles a client leaving a room
func (h *Hub) handleLeaveRoom(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if client.RoomID == "" {
		return
	}

	if room, ok := h.rooms[client.RoomID]; ok {
		// Remove participant from room
		participants := make([]Participant, 0)
		for _, p := range room.Participants {
			if p.ID != client.ID {
				participants = append(participants, p)
			}
		}
		room.Participants = participants

		// Notify remaining participants
		for _, p := range room.Participants {
			if c, ok := h.clients[p.ID]; ok {
				response := Message{
					Type: "participant-left",
					Data: map[string]interface{}{
						"participantId": client.ID,
					},
				}
				h.sendToClient(c, response)
			}
		}

		// If room is empty, delete it
		if len(room.Participants) == 0 {
			delete(h.rooms, client.RoomID)
		}
	}

	// Update client info
	client.RoomID = ""
}

// handleMeetingMessage broadcasts a message to all meeting participants
func (h *Hub) handleMeetingMessage(client *Client, message Message) {
	if client.RoomID == "" {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		data = make(map[string]interface{})
	}

	messageType, _ := data["messageType"].(string)
	msg, _ := data["message"].(string)
	content, _ := data["content"].(interface{})
	senderName := client.Name

	response := Message{
		Type: "meeting-message",
		Data: map[string]interface{}{
			"messageType": messageType,
			"message":     msg,
			"content":     content,
			"sender":      client.ID,
			"senderName":  senderName,
		},
	}

	h.broadcastToRoom(client.RoomID, client.ID, response)
}

// handleMeetingAction handles meeting actions (only host can perform)
func (h *Hub) handleMeetingAction(client *Client, message Message) {
	if client.RoomID == "" {
		return
	}

	h.mutex.RLock()
	meeting, ok := h.rooms[client.RoomID]
	h.mutex.RUnlock()

	if !ok {
		return
	}

	// Only host can perform actions
	if meeting.Host != client.ID {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	action, ok := data["action"].(string)
	if !ok {
		return
	}

	switch action {
	case "kick":
		targetID, ok := data["targetId"].(string)
		if !ok {
			return
		}

		h.mutex.RLock()
		targetClient, ok := h.clients[targetID]
		h.mutex.RUnlock()

		if !ok {
			return
		}

		response := Message{
			Type: "kicked",
			Data: map[string]interface{}{
				"reason": "Removed from meeting by host",
			},
		}

		h.sendToClient(targetClient, response)

		// Force disconnect the target client
		targetClient.Conn.Close()

	case "mute":
		targetID, ok := data["targetId"].(string)
		if !ok {
			return
		}

		h.mutex.RLock()
		targetClient, ok := h.clients[targetID]
		h.mutex.RUnlock()

		if !ok {
			return
		}

		response := Message{
			Type: "force-mute",
			Data: map[string]interface{}{
				"reason": "Muted by host",
			},
		}

		h.sendToClient(targetClient, response)

	case "mute-all":
		response := Message{
			Type: "force-mute",
			Data: map[string]interface{}{
				"reason": "Muted by host (mute all)",
			},
		}

		h.broadcastToRoom(client.RoomID, client.ID, response)
	}
}

// handleRoomMessage broadcasts a message to all room participants
func (h *Hub) handleRoomMessage(client *Client, message Message) {
	if client.RoomID == "" {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		data = make(map[string]interface{})
	}

	messageType, _ := data["messageType"].(string)
	msg, _ := data["message"].(string)
	content, _ := data["content"].(interface{})
	senderName := client.Name

	response := Message{
		Type: "room-message",
		Data: map[string]interface{}{
			"messageType": messageType,
			"message":     msg,
			"content":     content,
			"sender":      client.ID,
			"senderName":  senderName,
		},
	}

	h.broadcastToRoom(client.RoomID, client.ID, response)
}

// handleRoomAction handles room actions (only host can perform)
func (h *Hub) handleRoomAction(client *Client, message Message) {
	if client.RoomID == "" {
		return
	}

	h.mutex.RLock()
	room, ok := h.rooms[client.RoomID]
	h.mutex.RUnlock()

	if !ok {
		return
	}

	// Only host can perform actions
	if room.Host != client.ID {
		return
	}

	data, ok := message.Data.(map[string]interface{})
	if !ok {
		return
	}

	action, ok := data["action"].(string)
	if !ok {
		return
	}

	switch action {
	case "kick":
		targetID, ok := data["targetId"].(string)
		if !ok {
			return
		}

		h.mutex.RLock()
		targetClient, ok := h.clients[targetID]
		h.mutex.RUnlock()

		if !ok {
			return
		}

		response := Message{
			Type: "kicked",
			Data: map[string]interface{}{
				"reason": "Kicked by host",
			},
		}

		h.sendToClient(targetClient, response)

		// Force disconnect the target client
		targetClient.Conn.Close()
	}
}
