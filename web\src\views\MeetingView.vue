<script setup lang="ts">
import { getMeetingInfo, getWebsocketUrl, userSignature } from '@/api'
import { useUserStore } from '@/stores/user'
import type { User as ApiUser } from '@/types/user'
import { getMediaDevices } from '@/utils/helper'
import { useWebSocket } from '@vueuse/core'
import Avatar from 'primevue/avatar'
import Button from 'primevue/button'
import Popover from 'primevue/popover'
import RadioButton from 'primevue/radiobutton'
import Textarea from 'primevue/textarea'
import { useToast } from 'primevue/usetoast'
import { onMounted, reactive, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import ToggleSwitch from 'primevue/toggleswitch'
import type { Message } from '@/types/message'
import dayjs from 'dayjs'
import { iceServers } from '@/config'

interface User extends ApiUser {
  pc: RTCPeerConnection
  stream: MediaStream
}

enum OpCode {
  UpBroadCast = 10001,
  UserJoin = 10002,
  UserLeave = 10003,
  GetRoomUsers = 10004,
  RTCEvent = 10005,
  UserState = 10006
}

enum MediaSource {
  UserMedia = 1,
  DeviceMedia = 2
}

const toast = useToast()
const userStore = useUserStore()
const audioDevices = ref<MediaDeviceInfo[]>([])
const videoDevices = ref<MediaDeviceInfo[]>([])
const audioOutputDevices = ref<MediaDeviceInfo[]>([])
const route = useRoute()
const meetingId = route.params.id as string
const stream = ref<MediaStream>()
const muted = ref(true)
const users = ref<User[]>([])
const constraints = reactive<MediaStreamConstraints>({
  video: false,
  audio: { echoCancellation: true }
})
const messages = ref<Message[]>([])
const message = ref<string>('')
const op = ref()
const recordStream = ref<MediaStream>()
let send

getMeetingInfo(route.params.id).catch((e) => {
  window.location.href = `/`
})

const onRemoteVideoResize = (id: string) => {
  const video = document.getElementById(id) as HTMLVideoElement
  if (video) {
    console.log(`Remote video ${id} size changed to ${video.videoWidth}x${video.videoHeight}`)
  }
}
const offerOptions = {
  offerToReceiveAudio: true,
  offerToReceiveVideo: true
}

const devices = ref({ video: '', audio: '' })
const configuration = { iceServers }
// navigator.mediaDevices.getUserMedia(constraints).then((newStream) => (stream.value = newStream))
getMediaDevices().then((devices) => {
  const [vd, ad, aod] = devices
  videoDevices.value.push(...vd)
  audioDevices.value.push(...ad)
  audioOutputDevices.value.push(...aod)
})

onMounted(() => {
  userSignature().then((result: any) => {
    const ws = useWebSocket(
      getWebsocketUrl({
        MeetingId: meetingId,
        Signature: result.signature,
        ...result.parameters
      }),
      {
        autoReconnect: true,
        autoClose: true,
        heartbeat: {
          message: 'ping',
          interval: 15000,
          pongTimeout: 1000
        },
        onMessage(ws: WebSocket, event: MessageEvent) {
          event.data.split('\n').forEach(async (line: string) => {
            if (line === 'pong') {
              return
            }
            const { cmd, payload } = JSON.parse(line)
            let user: User
            switch (cmd) {
              case OpCode.UpBroadCast:
                messages.value.push(payload)
                break
              case OpCode.UserJoin: // 加入
                if (payload.id === userStore.info.id) {
                  send(JSON.stringify({ cmd: OpCode.GetRoomUsers }))
                } else {
                  users.value.push(newUser(payload))
                }
                break
              case OpCode.UserLeave: // 离开
                users.value = users.value.filter((user) => {
                  const isLeave = user.id === payload.id
                  if (isLeave) {
                    if (user.stream) {
                      stopStream(user.stream)
                    }
                    if (user.pc) {
                      user.pc.close()
                    }
                  }
                  return !isLeave
                })
                break
              case OpCode.RTCEvent:
                user = users.value.find((user) => user.id === payload.FromUser) as User
                if (user) {
                  const pc = user.pc
                  switch (payload.Event) {
                    case 'icecandidate':
                      await pc.addIceCandidate(payload.Payload)
                      break
                    case 'offer':
                      await pc.setRemoteDescription(new RTCSessionDescription(payload.Payload))
                      pc.createAnswer().then((answer) => {
                        pc.setLocalDescription(answer).then(() =>
                          RTCEvent(user.id, 'answer', answer)
                        )
                      })
                      break
                    case 'answer':
                      await pc.setRemoteDescription(new RTCSessionDescription(payload.Payload))
                      break
                  }
                }
                break
              case 10008:
                if (payload.id === userStore.info.id) {
                  if (stream.value?.getAudioTracks()) {
                    muted.value = stream.value.getAudioTracks()[0].enabled = payload.muted
                  }
                  muted.value = payload.muted
                } else {
                  let user = users.value.find((user) => user.id === payload.id)
                  if (user) {
                    Object.assign(user, payload)
                  }
                }
                break
              case OpCode.GetRoomUsers: // 所有用户列表
                for (let i = 0; i < payload.length; i++) {
                  let user = payload[i] as User
                  if (user.id === userStore.info.id) {
                    continue
                  }
                  try {
                    user = newUser(user)
                    const offer = await user.pc.createOffer(offerOptions)
                    await user.pc.setLocalDescription(offer)
                    RTCEvent(user.id, 'offer', offer)
                    users.value.push(user)
                  } catch (e) {
                    alert(`getUserMedia() error: ${e}`)
                  }
                }
                break
            }
          })
        }
      }
    )
    send = ws.send
  })
})

async function getStream(mediaSource: MediaSource) {
  let stream
  if (mediaSource === MediaSource.UserMedia) {
    stream = await navigator.mediaDevices.getUserMedia(constraints)
  } else {
    stream = await navigator.mediaDevices.getDisplayMedia(constraints)
  }
  if (muted.value) {
    stream.getTracks().forEach((track: MediaStreamTrack) => {
      if (track.kind === 'audio') {
        track.enabled = !muted.value
      }
    })
  }

  return stream
}

function newUser(user: User) {
  user.pc = new RTCPeerConnection(configuration)
  user.pc.addEventListener('icecandidate', (e: RTCPeerConnectionIceEvent) => {
    if (e.candidate) {
      RTCEvent(user.id, 'icecandidate', e.candidate)
    }
  })
  user.pc.addEventListener('track', (e: RTCTrackEvent) => {
    console.log('track', e)
    if (user.stream !== e.streams[0]) {
      users.value = users.value.map((u) => {
        if (u.id === user.id) {
          u.stream = e.streams[0]
        }

        return u
      })
    }
  })

  if (stream.value) {
    stream.value.getTracks().forEach((track: MediaStreamTrack) => {
      user.pc.addTrack(track, stream.value as MediaStream)
    })
  }

  return user
}

function RTCEvent(ToUser: string, Event: string, Payload: object) {
  send(
    JSON.stringify({
      cmd: OpCode.RTCEvent,
      payload: { Event, ToUser, Payload, FromUser: userStore.info.id }
    })
  )
}

function sendMessage() {
  send(
    JSON.stringify({
      cmd: OpCode.UpBroadCast,
      payload: {
        user: userStore.info,
        message: message.value
      }
    })
  )
  message.value = ''
}

async function renegotiate(user: User) {
  const offer = await user.pc.createOffer()
  await user.pc.setLocalDescription(offer)
  RTCEvent(user.id, 'offer', offer)
}

watch(constraints, async (newValue, oldValue, onCleanup) => {
  try {
    if (!newValue.video && !newValue.audio) {
      stopStream(stream.value)
      stream.value = undefined
      return
    }
    const newStream = await navigator.mediaDevices.getUserMedia({
      video: newValue.video,
      audio: newValue.audio ? { echoCancellation: true } : false
    })
    stopStream(stream.value)
    stream.value = newStream

    const [videoTrack] = newStream.getVideoTracks()
    const [audioTrack] = newStream.getAudioTracks()
    users.value.forEach((user) => {
      if (videoTrack) {
        console.log('Got video track ', videoTrack)
        const videoSender = user.pc.getSenders().find((s) => s.track?.kind === videoTrack.kind)
        if (videoSender) {
          console.log('Got video sender')
          videoSender.replaceTrack(videoTrack)
        } else {
          user.pc.addTrack(videoTrack, newStream)
          renegotiate(user)
        }
      }
      if (audioTrack) {
        console.log('Got audio track ', audioTrack)
        const audioSender = user.pc
          .getSenders()
          .find((s: RTCRtpSender) => s.track?.kind === audioTrack.kind)
        if (audioSender) {
          console.log('Got audio sender ', audioTrack)
          audioSender.replaceTrack(audioTrack)
        }
      }
    })
  } catch (e: any) {
    toast.add({
      severity: 'error',
      summary: '出错了',
      detail: e.message,
      group: 'br',
      life: 3000
    })
  }
})

const ShareMode = {
  Camera: 1,
  Screen: 2
}
const shareMode = ref(ShareMode.Camera)
const isShareScreen = ref(false)
const shareScreen = () => {
  if (!isShareScreen.value) {
    console.log('share screen')
    navigator.mediaDevices
      .getDisplayMedia({ video: true, audio: constraints.audio })
      .then((newStream) => {
        stopStream(stream.value)
        stream.value = newStream
        newStream.addEventListener('inactive', (e) => {
          stopStream(newStream)
          stream.value = undefined
        })

        users.value.forEach((user) => {
          const [videoTrack] = newStream.getVideoTracks()
          if (videoTrack) {
            const videoSender = user.pc.getSenders().find((s) => s.track?.kind === videoTrack.kind)
            if (videoSender) {
              videoSender.replaceTrack(videoTrack)
            } else {
              user.pc.addTrack(videoTrack, newStream)
            }
          }

          const [audioTrack] = newStream.getAudioTracks()
          if (audioTrack) {
            const audioSender = user.pc.getSenders().find((s) => s.track?.kind === audioTrack.kind)
            if (audioSender) {
              audioSender.replaceTrack(audioTrack)
            }
          }
          renegotiate(user)
        })

        isShareScreen.value = true
      })
  } else {
    stopStream(stream.value)
    stream.value = undefined
    isShareScreen.value = false
  }
}

const blobs = ref<Blob[]>([])
const recording = ref(false)
const recordScreen = async () => {
  if (recording.value) {
    const blob = new Blob(blobs.value, { type: 'video/webm' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.style.display = 'none'
    a.download = `屏幕录制${dayjs().format('YYYYMMDDHHmm')}.webm`
    a.click()
  } else {
    blobs.value = []
    recordStream.value = await navigator.mediaDevices.getDisplayMedia({
      video: true,
      audio: { echoCancellation: true }
    })
    const mediaRecorder = new MediaRecorder(recordStream.value, {
      mimeType: 'video/webm'
    })
    mediaRecorder.ondataavailable = (e: BlobEvent) => blobs.value.push(e.data)
    mediaRecorder.start(100)
    toast.add({
      severity: 'success',
      summary: '录制中...',
      detail: '再次点击录制按钮结束录制',
      life: 3000
    })
  }
  recording.value = !recording.value
}

const stopStream = (stream: MediaStream | undefined) => {
  if (stream) {
    stream.getTracks().forEach(function (track) {
      if (track.readyState === 'live') {
        track.stop()
      }
    })
  }
}
const previewStream = ref<MediaStream>()
const previewConfig = ref({ video: false, audio: false })
watch(previewConfig.value, async (newValue) => {
  if (!newValue.audio && !newValue.video) {
    stopStream(previewStream.value)
    previewStream.value = undefined
  } else {
    const oldStream = previewStream.value
    previewStream.value = await navigator.mediaDevices.getUserMedia(newValue)
    stopStream(oldStream)
  }
})
const entered = ref(false)
const enter = () => {
  stream.value = previewStream.value
  previewStream.value = undefined
  entered.value = true
}
</script>

<template>
  <div class="hidden flex justify-center items-center w-full h-full">
    <div class="rounded-md flex flex-col gap-2 justify-between bg-gray-100 w-1/2 h-96">
      <video class="flex-1 h-80" :srcObject.prop="previewStream" playsinline autoplay></video>
      <div class="flex justify-between items-center w-full bg-gray-50 p-2">
        <div class="flex justify-between items-center gap-2">
          <i class="text-lg pi pi-video"></i>
          <ToggleSwitch v-model="previewConfig.video" />
          <i class="text-lg pi pi-microphone"></i>
          <ToggleSwitch v-model="previewConfig.audio" />
        </div>
        <div>
          <Button @click="enter" class="px-3 py-1 text-sm" label="进入会议" />
        </div>
      </div>
    </div>
  </div>
  <div class="w-full h-full relative border border-surface rounded-2xl overflow-hidden flex">
    <div class="w-full sm:w-9/12 border-r border-surface flex flex-col relative p-4">
      <div
        class="flex h-full w-full user-list flex-wrap justify-center items-center content-center"
      >
        <div class="box bg-gray-100 rounded-md overflow-hidden relative">
          <!-- <div class="w-full h-full absolute flex items-center justify-center">
            <img :src="userStore.info.avatar" alt="" class="h-32 w-32">
          </div> -->
          <video :srcObject.prop="stream" playsinline muted autoplay></video>
          <div class="absolute w-full bottom-0 flex gap-1 items-center justify-between py-2 px-4">
            <Avatar :image="userStore.info.avatar" shape="circle" />
            <div class="flex gap-2">
              <i class="pi pi-video"></i>
              <i class="pi pi-microphone"></i>
            </div>
          </div>
        </div>
        <div
          class="box bg-gray-100 rounded-md overflow-hidden relative"
          :key="user.id"
          v-for="user in users"
        >
          <video
            @resize="() => onRemoteVideoResize(user.id)"
            :srcObject.prop="user.stream"
            :id="user.id"
            :key="user.id"
            playsinline
            autoplay
          ></video>
          <div class="absolute w-full bottom-0 flex gap-1 items-center justify-between py-2 px-4">
            <Avatar :image="user.avatar" shape="circle" />
            <div class="flex gap-2">
              <i class="pi pi-video"></i>
              <i class="pi pi-microphone"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="absolute top-8 left-0 right-0 flex items-center justify-center">
        <div class="bg-gray-200 flex gap-2 rounded-md h-10 p-1 opacity-90">
          <Button
            v-tooltip.bottom="{ value: '屏幕录制' }"
            @click="recordScreen"
            icon="pi pi-stop-circle"
            text
            class="py-0 hidden sm:block"
          />
          <Button
            v-tooltip.bottom="{ value: '分享屏幕' }"
            icon="pi pi-share-alt"
            class="py-0 hidden sm:block"
            text
            @click="shareScreen"
          />
          <Button
            class="py-0"
            v-tooltip.bottom="{ value: '摄像头' }"
            icon="pi pi-video"
            text
            @click="() => (constraints.video = !constraints.video)"
          />
          <Button
            v-tooltip.bottom="{ value: '麦克风' }"
            icon="pi pi-microphone"
            class="py-0"
            :class="muted ? '' : 'bg-green-100'"
            text
            @click="
              () =>
                send(
                  JSON.stringify({
                    cmd: OpCode.UserState,
                    payload: { id: userStore.info.id, muted: !muted }
                  })
                )
            "
          />
          <Button type="button" icon="pi pi-cog" text @click="(e) => op.toggle(e)" />
          <Popover ref="op">
            <div class="flex flex-col gap-4 w-fit">
              <div>
                <span class="font-medium block mb-2">麦克风</span>
                <div
                  v-for="audioDevice in audioDevices"
                  :key="audioDevice.deviceId"
                  class="flex items-center"
                >
                  <RadioButton
                    @click="() => (constraints.audio = { deviceId: audioDevice.deviceId })"
                    v-model="devices.audio"
                    :inputId="audioDevice.deviceId"
                    name="audio"
                    :value="audioDevice.deviceId"
                  />
                  <label :for="audioDevice.deviceId" class="ml-2">{{ audioDevice.label }}</label>
                </div>
                <span class="font-medium block mb-2">摄像头</span>
                <div
                  v-for="videoDevice in videoDevices"
                  :key="videoDevice.deviceId"
                  class="flex items-center"
                >
                  <RadioButton
                    @click="() => (constraints.video = { deviceId: videoDevice.deviceId })"
                    v-model="devices.video"
                    :inputId="videoDevice.deviceId"
                    name="video"
                    :value="videoDevice.deviceId"
                  />
                  <label :for="videoDevice.deviceId" class="ml-2">{{ videoDevice.label }}</label>
                </div>
                <!--                <span class="font-medium block mb-2">扬声器</span>-->
                <!--                <div-->
                <!--                    v-for="audioOutputDevice in audioOutputDevices"-->
                <!--                    :key="audioOutputDevice.deviceId"-->
                <!--                    class="flex items-center"-->
                <!--                >-->
                <!--                  <RadioButton-->
                <!--                      v-model="devices.video"-->
                <!--                      :inputId="audioOutputDevice.deviceId"-->
                <!--                      name="audioOutput"-->
                <!--                      :value="audioOutputDevice.deviceId"-->
                <!--                  />-->
                <!--                  <label :for="audioOutputDevice.deviceId" class="ml-2">{{-->
                <!--                      audioOutputDevice.label-->
                <!--                    }}</label>-->
                <!--                </div>-->
              </div>
            </div>
          </Popover>
        </div>
      </div>
    </div>
    <div class="hidden sm:flex w-3/12 min-w-40 flex-col gap-6 p-4">
      <!--      <SelectButton-->
      <!--        model-value="Chat"-->
      <!--        :options="['Chat', 'Call']"-->
      <!--        aria-labelledby="basic"-->
      <!--        class="w-full"-->
      <!--      />-->
      <div class="flex-1 overflow-auto">
        <div
          v-for="item in messages"
          :key="item.id"
          class="flex py-1.5 px-0"
          :style="{ flexDirection: item.user.id === userStore.info.id ? 'row-reverse' : 'row' }"
        >
          <a target="_blank" class="block my-0 mx-2" href=""
            ><img
              :src="item.user.avatar"
              alt=""
              class="h-10 w-10 border-2 border-white rounded-full"
          /></a>
          <div
            class="flex flex-col w-fit"
            :class="item.user.id === userStore.info.id ? 'items-end' : 'items-start'"
          >
            <div class="text-sm w-fit mb-2">{{ item.user.nickname }}</div>
            <div
              class="p-3 w-fit text-sm bg-gray-100"
              :style="{
                borderRadius:
                  item.user.id === userStore.info.id
                    ? '1.8em 0.1em 1.8em 1.8em'
                    : '0.1em 1.8em 1.8em'
              }"
            >
              {{ item.message }}
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-between items-end gap-2">
        <Textarea v-model="message" autoResize rows="1" class="w-full max-h-20 min-h-8" />
        <Button icon="pi pi-face-smile" text />
        <Button icon="pi pi-send" text class="" @click="sendMessage" />
      </div>
    </div>
  </div>
</template>

<style lang="less">
.user-list {
  @gap: 1rem;
  gap: @gap;

  .box {
    height: 100%;
    width: 100%;

    video {
      width: 100%;
      height: 100%;
    }
  }

  &:has(> .box:nth-child(2)) {
    .box {
      height: 50%;
      width: calc(50% - @gap);
    }
  }

  &:has(> .box:nth-child(3)) {
    .box {
      width: 35%;
      height: 35%;
    }
  }
}
</style>
