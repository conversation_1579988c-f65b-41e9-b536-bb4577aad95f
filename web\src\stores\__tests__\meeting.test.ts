import { describe, it, expect, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, createP<PERSON> } from 'pinia'
import { useMeetingStore } from '../meeting'
import type { Meeting, Participant, MediaState } from '@/types/meeting'
import type { ChatMessage } from '@/types/message'

describe('Meeting Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should initialize with default state', () => {
    const store = useMeetingStore()
    
    expect(store.currentMeeting).toBeNull()
    expect(store.inMeeting).toBe(false)
    expect(store.isHost).toBe(false)
    expect(store.participants).toEqual([])
    expect(store.chatMessages).toEqual([])
    expect(store.remoteStreams).toEqual([])
    expect(store.connectionState).toBe('disconnected')
  })

  it('should set meeting correctly', () => {
    const store = useMeetingStore()
    const meeting: Meeting = {
      id: 'test-meeting',
      name: 'Test Meeting',
      hostId: 'host-123',
      participants: [],
      createdAt: Date.now(),
      isActive: true
    }

    store.setMeeting(meeting)

    expect(store.currentMeeting).toEqual(meeting)
    expect(store.inMeeting).toBe(true)
  })

  it('should add and remove participants', () => {
    const store = useMeetingStore()
    const participant: Participant = {
      id: 'participant-1',
      name: 'John Doe',
      role: 'member',
      mediaState: { microphone: true, camera: true, screenSharing: false },
      connectionState: 'connected',
      joinedAt: Date.now()
    }

    store.addParticipant(participant)
    expect(store.participants).toHaveLength(1)
    expect(store.participants[0]).toEqual(participant)

    store.removeParticipant('participant-1')
    expect(store.participants).toHaveLength(0)
  })

  it('should update participant media state', () => {
    const store = useMeetingStore()
    const participant: Participant = {
      id: 'participant-1',
      name: 'John Doe',
      role: 'member',
      mediaState: { microphone: true, camera: true, screenSharing: false },
      connectionState: 'connected',
      joinedAt: Date.now()
    }

    store.addParticipant(participant)
    store.updateParticipantMediaState('participant-1', { microphone: false })

    expect(store.participants[0].mediaState.microphone).toBe(false)
    expect(store.participants[0].mediaState.camera).toBe(true)
  })

  it('should add chat messages', () => {
    const store = useMeetingStore()
    const message: ChatMessage = {
      id: 'msg-1',
      type: 'text',
      content: 'Hello world',
      senderId: 'user-1',
      senderName: 'John',
      timestamp: Date.now()
    }

    store.addChatMessage(message)
    expect(store.chatMessages).toHaveLength(1)
    expect(store.chatMessages[0]).toEqual(message)
  })

  it('should track unread messages', () => {
    const store = useMeetingStore()
    const message: ChatMessage = {
      id: 'msg-1',
      type: 'text',
      content: 'Hello world',
      senderId: 'user-1',
      senderName: 'John',
      timestamp: Date.now()
    }

    store.showChatPanel = false
    store.addChatMessage(message)
    expect(store.unreadMessages).toBe(1)

    store.clearChatUnread()
    expect(store.unreadMessages).toBe(0)
  })

  it('should manage remote streams', () => {
    const store = useMeetingStore()
    const mockStream = new MediaStream()
    const remoteStream = {
      id: 'stream-1',
      participantId: 'participant-1',
      stream: mockStream,
      type: 'camera' as const
    }

    store.addRemoteStream(remoteStream)
    expect(store.remoteStreams).toHaveLength(1)
    expect(store.remoteStreams[0]).toEqual(remoteStream)

    store.removeRemoteStream('stream-1')
    expect(store.remoteStreams).toHaveLength(0)
  })

  it('should reset meeting state', () => {
    const store = useMeetingStore()
    
    // Set up some state
    store.setMeeting({
      id: 'test-meeting',
      name: 'Test Meeting',
      hostId: 'host-123',
      participants: [],
      createdAt: Date.now(),
      isActive: true
    })
    store.addChatMessage({
      id: 'msg-1',
      type: 'text',
      content: 'Hello',
      senderId: 'user-1',
      senderName: 'John',
      timestamp: Date.now()
    })

    store.resetMeeting()

    expect(store.currentMeeting).toBeNull()
    expect(store.inMeeting).toBe(false)
    expect(store.participants).toEqual([])
    expect(store.chatMessages).toEqual([])
    expect(store.remoteStreams).toEqual([])
  })

  it('should compute participant count correctly', () => {
    const store = useMeetingStore()
    
    expect(store.participantCount).toBe(0)

    store.addParticipant({
      id: 'participant-1',
      name: 'John',
      role: 'member',
      mediaState: { microphone: true, camera: true, screenSharing: false },
      connectionState: 'connected',
      joinedAt: Date.now()
    })

    expect(store.participantCount).toBe(1)
  })

  it('should set connection state', () => {
    const store = useMeetingStore()
    
    store.setConnectionState('connecting')
    expect(store.connectionState).toBe('connecting')
    expect(store.signalServerConnected).toBe(false)

    store.setConnectionState('connected')
    expect(store.connectionState).toBe('connected')
    expect(store.signalServerConnected).toBe(true)
  })
})
