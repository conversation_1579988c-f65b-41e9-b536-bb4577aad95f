package main

import (
	"fmt"
	"os"
	"syscall"
)

func main() {
	//create()
	exist()
}

func create() {
	//// 创建文件
	//file, err := os.Create("./access.log")
	//if err == nil {
	//	// 写入内容
	//	fmt.Println(file.WriteString("Hello, world."))
	//} else {
	//	fmt.Println(err)
	//}
	file, err := os.Open("./access.log")
	if err == nil {
		defer file.Close()
		// 方法一
		//var data = make([]byte, 100)
		//fmt.Println(file.Read(data))
		//fmt.Println(string(data))
		// 方法二
		//data, err := io.ReadAll(file)
		//fmt.Println(string(data), err)
		// 方法三
		data, err := os.ReadFile("./access.log")
		fmt.Println(string(data), err)
	}
	fmt.Println(err)
}

func exist() {
	fmt.Println(syscall.Access("./file.go", syscall.F_OK))
	//_, err := os.Lstat("./file.go")
	//fmt.Println(os.IsNotExist(err))
}
