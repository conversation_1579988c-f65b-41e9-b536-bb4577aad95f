package auth

import (
	"context"
	"meeting/internal/entity"
	"meeting/pkg/core"

	"github.com/gin-gonic/gin"
)

const ctxUserKey = "ctx_user_key"

// User 从上下文中获取当前用户
func User(ctx *gin.Context) *entity.User {
	if value, ok := ctx.Get(ctxUserKey); ok {
		if user, ok := value.(*entity.User); ok {
			return user
		}
	}
	return nil
}

// Login 将用户信息设置到上下文中
func Login(ctx *gin.Context, user *entity.User) {
	ctx.Set(ctxUserKey, user)
}

// GetUserByID 根据用户ID从数据库获取用户信息
func GetUserByID(userID string) (*entity.User, error) {
	var user entity.User
	err := core.DB(context.Background()).Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// GetUserByUUID 根据用户UUID从数据库获取用户信息
func GetUserByUUID(uuid string) (*entity.User, error) {
	var user entity.User
	err := core.DB(context.Background()).Where("uuid = ?", uuid).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}
