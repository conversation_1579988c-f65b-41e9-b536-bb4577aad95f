const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/MeetingView-CJuehxxu.js","assets/index-MIcNnIzF.js","assets/index-CX10IlxR.js","assets/MeetingView-DX5h0ymB.css","assets/LoginView-fCbw3AFU.js","assets/ChatView-D55HfngU.js","assets/InboxView-DzMQXMJn.js","assets/_plugin-vue_export-helper-DlAUqK2U.js","assets/CustomersView-D32FbDsq.js","assets/CardsView-CUYE4ppE.js","assets/MoviesView-C9RBTOBx.js"])))=>i.map(i=>d[i]);
function Gd(e,t){for(var r=0;r<t.length;r++){const o=t[r];if(typeof o!="string"&&!Array.isArray(o)){for(const n in o)if(n!=="default"&&!(n in e)){const i=Object.getOwnPropertyDescriptor(o,n);i&&Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:()=>o[n]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))o(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&o(s)}).observe(document,{childList:!0,subtree:!0});function r(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(n){if(n.ep)return;n.ep=!0;const i=r(n);fetch(n.href,i)}})();let Bc=!0,Ac=!0;function Mo(e,t,r){const o=e.match(t);return o&&o.length>=r&&parseInt(o[r],10)}function ur(e,t,r){if(!e.RTCPeerConnection)return;const o=e.RTCPeerConnection.prototype,n=o.addEventListener;o.addEventListener=function(s,a){if(s!==t)return n.apply(this,arguments);const c=u=>{const l=r(u);l&&(a.handleEvent?a.handleEvent(l):a(l))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(a,c),n.apply(this,[s,c])};const i=o.removeEventListener;o.removeEventListener=function(s,a){if(s!==t||!this._eventMap||!this._eventMap[t])return i.apply(this,arguments);if(!this._eventMap[t].has(a))return i.apply(this,arguments);const c=this._eventMap[t].get(a);return this._eventMap[t].delete(a),this._eventMap[t].size===0&&delete this._eventMap[t],Object.keys(this._eventMap).length===0&&delete this._eventMap,i.apply(this,[s,c])},Object.defineProperty(o,"on"+t,{get(){return this["_on"+t]},set(s){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),s&&this.addEventListener(t,this["_on"+t]=s)},enumerable:!0,configurable:!0})}function Kd(e){return typeof e!="boolean"?new Error("Argument type: "+typeof e+". Please use a boolean."):(Bc=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function qd(e){return typeof e!="boolean"?new Error("Argument type: "+typeof e+". Please use a boolean."):(Ac=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function Lc(){if(typeof window=="object"){if(Bc)return;typeof console<"u"&&typeof console.log=="function"&&console.log.apply(console,arguments)}}function Vi(e,t){Ac&&console.warn(e+" is deprecated, please use "+t+" instead.")}function Jd(e){const t={browser:null,version:null};if(typeof e>"u"||!e.navigator||!e.navigator.userAgent)return t.browser="Not a browser.",t;const{navigator:r}=e;if(r.userAgentData&&r.userAgentData.brands){const o=r.userAgentData.brands.find(n=>n.brand==="Chromium");if(o)return{browser:"chrome",version:parseInt(o.version,10)}}if(r.mozGetUserMedia)t.browser="firefox",t.version=Mo(r.userAgent,/Firefox\/(\d+)\./,1);else if(r.webkitGetUserMedia||e.isSecureContext===!1&&e.webkitRTCPeerConnection)t.browser="chrome",t.version=Mo(r.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(e.RTCPeerConnection&&r.userAgent.match(/AppleWebKit\/(\d+)\./))t.browser="safari",t.version=Mo(r.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype;else return t.browser="Not a supported browser.",t;return t}function $s(e){return Object.prototype.toString.call(e)==="[object Object]"}function Ic(e){return $s(e)?Object.keys(e).reduce(function(t,r){const o=$s(e[r]),n=o?Ic(e[r]):e[r],i=o&&!Object.keys(n).length;return n===void 0||i?t:Object.assign(t,{[r]:n})},{}):e}function ni(e,t,r){!t||r.has(t.id)||(r.set(t.id,t),Object.keys(t).forEach(o=>{o.endsWith("Id")?ni(e,e.get(t[o]),r):o.endsWith("Ids")&&t[o].forEach(n=>{ni(e,e.get(n),r)})}))}function Bs(e,t,r){const o=r?"outbound-rtp":"inbound-rtp",n=new Map;if(t===null)return n;const i=[];return e.forEach(s=>{s.type==="track"&&s.trackIdentifier===t.id&&i.push(s)}),i.forEach(s=>{e.forEach(a=>{a.type===o&&a.trackId===s.id&&ni(e,a,n)})}),n}const As=Lc;function Dc(e,t){const r=e&&e.navigator;if(!r.mediaDevices)return;const o=function(a){if(typeof a!="object"||a.mandatory||a.optional)return a;const c={};return Object.keys(a).forEach(u=>{if(u==="require"||u==="advanced"||u==="mediaSource")return;const l=typeof a[u]=="object"?a[u]:{ideal:a[u]};l.exact!==void 0&&typeof l.exact=="number"&&(l.min=l.max=l.exact);const d=function(f,p){return f?f+p.charAt(0).toUpperCase()+p.slice(1):p==="deviceId"?"sourceId":p};if(l.ideal!==void 0){c.optional=c.optional||[];let f={};typeof l.ideal=="number"?(f[d("min",u)]=l.ideal,c.optional.push(f),f={},f[d("max",u)]=l.ideal,c.optional.push(f)):(f[d("",u)]=l.ideal,c.optional.push(f))}l.exact!==void 0&&typeof l.exact!="number"?(c.mandatory=c.mandatory||{},c.mandatory[d("",u)]=l.exact):["min","max"].forEach(f=>{l[f]!==void 0&&(c.mandatory=c.mandatory||{},c.mandatory[d(f,u)]=l[f])})}),a.advanced&&(c.optional=(c.optional||[]).concat(a.advanced)),c},n=function(a,c){if(t.version>=61)return c(a);if(a=JSON.parse(JSON.stringify(a)),a&&typeof a.audio=="object"){const u=function(l,d,f){d in l&&!(f in l)&&(l[f]=l[d],delete l[d])};a=JSON.parse(JSON.stringify(a)),u(a.audio,"autoGainControl","googAutoGainControl"),u(a.audio,"noiseSuppression","googNoiseSuppression"),a.audio=o(a.audio)}if(a&&typeof a.video=="object"){let u=a.video.facingMode;u=u&&(typeof u=="object"?u:{ideal:u});const l=t.version<66;if(u&&(u.exact==="user"||u.exact==="environment"||u.ideal==="user"||u.ideal==="environment")&&!(r.mediaDevices.getSupportedConstraints&&r.mediaDevices.getSupportedConstraints().facingMode&&!l)){delete a.video.facingMode;let d;if(u.exact==="environment"||u.ideal==="environment"?d=["back","rear"]:(u.exact==="user"||u.ideal==="user")&&(d=["front"]),d)return r.mediaDevices.enumerateDevices().then(f=>{f=f.filter(m=>m.kind==="videoinput");let p=f.find(m=>d.some(b=>m.label.toLowerCase().includes(b)));return!p&&f.length&&d.includes("back")&&(p=f[f.length-1]),p&&(a.video.deviceId=u.exact?{exact:p.deviceId}:{ideal:p.deviceId}),a.video=o(a.video),As("chrome: "+JSON.stringify(a)),c(a)})}a.video=o(a.video)}return As("chrome: "+JSON.stringify(a)),c(a)},i=function(a){return t.version>=64?a:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[a.name]||a.name,message:a.message,constraint:a.constraint||a.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}},s=function(a,c,u){n(a,l=>{r.webkitGetUserMedia(l,c,d=>{u&&u(i(d))})})};if(r.getUserMedia=s.bind(r),r.mediaDevices.getUserMedia){const a=r.mediaDevices.getUserMedia.bind(r.mediaDevices);r.mediaDevices.getUserMedia=function(c){return n(c,u=>a(u).then(l=>{if(u.audio&&!l.getAudioTracks().length||u.video&&!l.getVideoTracks().length)throw l.getTracks().forEach(d=>{d.stop()}),new DOMException("","NotFoundError");return l},l=>Promise.reject(i(l))))}}}function jc(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function Mc(e){if(typeof e=="object"&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(r){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=r)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=o=>{o.stream.addEventListener("addtrack",n=>{let i;e.RTCPeerConnection.prototype.getReceivers?i=this.getReceivers().find(a=>a.track&&a.track.id===n.track.id):i={track:n.track};const s=new Event("track");s.track=n.track,s.receiver=i,s.transceiver={receiver:i},s.streams=[o.stream],this.dispatchEvent(s)}),o.stream.getTracks().forEach(n=>{let i;e.RTCPeerConnection.prototype.getReceivers?i=this.getReceivers().find(a=>a.track&&a.track.id===n.id):i={track:n};const s=new Event("track");s.track=n,s.receiver=i,s.transceiver={receiver:i},s.streams=[o.stream],this.dispatchEvent(s)})},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else ur(e,"track",t=>(t.transceiver||Object.defineProperty(t,"transceiver",{value:{receiver:t.receiver}}),t))}function Nc(e){if(typeof e=="object"&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(n,i){return{track:i,get dtmf(){return this._dtmf===void 0&&(i.kind==="audio"?this._dtmf=n.createDTMFSender(i):this._dtmf=null),this._dtmf},_pc:n}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(a,c){let u=n.apply(this,arguments);return u||(u=t(this,a),this._senders.push(u)),u};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(a){i.apply(this,arguments);const c=this._senders.indexOf(a);c!==-1&&this._senders.splice(c,1)}}const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(i){this._senders=this._senders||[],r.apply(this,[i]),i.getTracks().forEach(s=>{this._senders.push(t(this,s))})};const o=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(i){this._senders=this._senders||[],o.apply(this,[i]),i.getTracks().forEach(s=>{const a=this._senders.find(c=>c.track===s);a&&this._senders.splice(this._senders.indexOf(a),1)})}}else if(typeof e=="object"&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const o=t.apply(this,[]);return o.forEach(n=>n._pc=this),o},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return this._dtmf===void 0&&(this.track.kind==="audio"?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function Fc(e){if(!(typeof e=="object"&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const r=e.RTCPeerConnection.prototype.getSenders;r&&(e.RTCPeerConnection.prototype.getSenders=function(){const i=r.apply(this,[]);return i.forEach(s=>s._pc=this),i});const o=e.RTCPeerConnection.prototype.addTrack;o&&(e.RTCPeerConnection.prototype.addTrack=function(){const i=o.apply(this,arguments);return i._pc=this,i}),e.RTCRtpSender.prototype.getStats=function(){const i=this;return this._pc.getStats().then(s=>Bs(s,i.track,!0))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const r=e.RTCPeerConnection.prototype.getReceivers;r&&(e.RTCPeerConnection.prototype.getReceivers=function(){const n=r.apply(this,[]);return n.forEach(i=>i._pc=this),n}),ur(e,"track",o=>(o.receiver._pc=o.srcElement,o)),e.RTCRtpReceiver.prototype.getStats=function(){const n=this;return this._pc.getStats().then(i=>Bs(i,n.track,!1))}}if(!("getStats"in e.RTCRtpSender.prototype&&"getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const o=arguments[0];let n,i,s;return this.getSenders().forEach(a=>{a.track===o&&(n?s=!0:n=a)}),this.getReceivers().forEach(a=>(a.track===o&&(i?s=!0:i=a),a.track===o)),s||n&&i?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):n?n.getStats():i?i.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function zc(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(s=>this._shimmedLocalStreams[s][0])};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(s,a){if(!a)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const c=t.apply(this,arguments);return this._shimmedLocalStreams[a.id]?this._shimmedLocalStreams[a.id].indexOf(c)===-1&&this._shimmedLocalStreams[a.id].push(c):this._shimmedLocalStreams[a.id]=[a,c],c};const r=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(s){this._shimmedLocalStreams=this._shimmedLocalStreams||{},s.getTracks().forEach(u=>{if(this.getSenders().find(d=>d.track===u))throw new DOMException("Track already exists.","InvalidAccessError")});const a=this.getSenders();r.apply(this,arguments);const c=this.getSenders().filter(u=>a.indexOf(u)===-1);this._shimmedLocalStreams[s.id]=[s].concat(c)};const o=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(s){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[s.id],o.apply(this,arguments)};const n=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(s){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},s&&Object.keys(this._shimmedLocalStreams).forEach(a=>{const c=this._shimmedLocalStreams[a].indexOf(s);c!==-1&&this._shimmedLocalStreams[a].splice(c,1),this._shimmedLocalStreams[a].length===1&&delete this._shimmedLocalStreams[a]}),n.apply(this,arguments)}}function Uc(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return zc(e);const r=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const l=r.apply(this);return this._reverseStreams=this._reverseStreams||{},l.map(d=>this._reverseStreams[d.id])};const o=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(l){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},l.getTracks().forEach(d=>{if(this.getSenders().find(p=>p.track===d))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[l.id]){const d=new e.MediaStream(l.getTracks());this._streams[l.id]=d,this._reverseStreams[d.id]=l,l=d}o.apply(this,[l])};const n=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(l){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},n.apply(this,[this._streams[l.id]||l]),delete this._reverseStreams[this._streams[l.id]?this._streams[l.id].id:l.id],delete this._streams[l.id]},e.RTCPeerConnection.prototype.addTrack=function(l,d){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const f=[].slice.call(arguments,1);if(f.length!==1||!f[0].getTracks().find(b=>b===l))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(b=>b.track===l))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const m=this._streams[d.id];if(m)m.addTrack(l),Promise.resolve().then(()=>{this.dispatchEvent(new Event("negotiationneeded"))});else{const b=new e.MediaStream([l]);this._streams[d.id]=b,this._reverseStreams[b.id]=d,this.addStream(b)}return this.getSenders().find(b=>b.track===l)};function i(u,l){let d=l.sdp;return Object.keys(u._reverseStreams||[]).forEach(f=>{const p=u._reverseStreams[f],m=u._streams[p.id];d=d.replace(new RegExp(m.id,"g"),p.id)}),new RTCSessionDescription({type:l.type,sdp:d})}function s(u,l){let d=l.sdp;return Object.keys(u._reverseStreams||[]).forEach(f=>{const p=u._reverseStreams[f],m=u._streams[p.id];d=d.replace(new RegExp(p.id,"g"),m.id)}),new RTCSessionDescription({type:l.type,sdp:d})}["createOffer","createAnswer"].forEach(function(u){const l=e.RTCPeerConnection.prototype[u],d={[u](){const f=arguments;return arguments.length&&typeof arguments[0]=="function"?l.apply(this,[m=>{const b=i(this,m);f[0].apply(null,[b])},m=>{f[1]&&f[1].apply(null,m)},arguments[2]]):l.apply(this,arguments).then(m=>i(this,m))}};e.RTCPeerConnection.prototype[u]=d[u]});const a=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return!arguments.length||!arguments[0].type?a.apply(this,arguments):(arguments[0]=s(this,arguments[0]),a.apply(this,arguments))};const c=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const u=c.get.apply(this);return u.type===""?u:i(this,u)}}),e.RTCPeerConnection.prototype.removeTrack=function(l){if(this.signalingState==="closed")throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!l._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(l._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};let f;Object.keys(this._streams).forEach(p=>{this._streams[p].getTracks().find(b=>l.track===b)&&(f=this._streams[p])}),f&&(f.getTracks().length===1?this.removeStream(this._reverseStreams[f.id]):f.removeTrack(l.track),this.dispatchEvent(new Event("negotiationneeded")))}}function ii(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(r){const o=e.RTCPeerConnection.prototype[r],n={[r](){return arguments[0]=new(r==="addIceCandidate"?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),o.apply(this,arguments)}};e.RTCPeerConnection.prototype[r]=n[r]})}function Hc(e,t){ur(e,"negotiationneeded",r=>{const o=r.target;if(!((t.version<72||o.getConfiguration&&o.getConfiguration().sdpSemantics==="plan-b")&&o.signalingState!=="stable"))return r})}const Ls=Object.freeze(Object.defineProperty({__proto__:null,fixNegotiationNeeded:Hc,shimAddTrackRemoveTrack:Uc,shimAddTrackRemoveTrackWithNative:zc,shimGetSendersWithDtmf:Nc,shimGetUserMedia:Dc,shimMediaStream:jc,shimOnTrack:Mc,shimPeerConnection:ii,shimSenderReceiverGetStats:Fc},Symbol.toStringTag,{value:"Module"}));function Vc(e,t){const r=e&&e.navigator,o=e&&e.MediaStreamTrack;if(r.getUserMedia=function(n,i,s){Vi("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),r.mediaDevices.getUserMedia(n).then(i,s)},!(t.version>55&&"autoGainControl"in r.mediaDevices.getSupportedConstraints())){const n=function(s,a,c){a in s&&!(c in s)&&(s[c]=s[a],delete s[a])},i=r.mediaDevices.getUserMedia.bind(r.mediaDevices);if(r.mediaDevices.getUserMedia=function(s){return typeof s=="object"&&typeof s.audio=="object"&&(s=JSON.parse(JSON.stringify(s)),n(s.audio,"autoGainControl","mozAutoGainControl"),n(s.audio,"noiseSuppression","mozNoiseSuppression")),i(s)},o&&o.prototype.getSettings){const s=o.prototype.getSettings;o.prototype.getSettings=function(){const a=s.apply(this,arguments);return n(a,"mozAutoGainControl","autoGainControl"),n(a,"mozNoiseSuppression","noiseSuppression"),a}}if(o&&o.prototype.applyConstraints){const s=o.prototype.applyConstraints;o.prototype.applyConstraints=function(a){return this.kind==="audio"&&typeof a=="object"&&(a=JSON.parse(JSON.stringify(a)),n(a,"autoGainControl","mozAutoGainControl"),n(a,"noiseSuppression","mozNoiseSuppression")),s.apply(this,[a])}}}}function Yd(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(o){if(!(o&&o.video)){const n=new DOMException("getDisplayMedia without video constraints is undefined");return n.name="NotFoundError",n.code=8,Promise.reject(n)}return o.video===!0?o.video={mediaSource:t}:o.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(o)})}function Wc(e){typeof e=="object"&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function si(e,t){if(typeof e!="object"||!(e.RTCPeerConnection||e.mozRTCPeerConnection))return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(n){const i=e.RTCPeerConnection.prototype[n],s={[n](){return arguments[0]=new(n==="addIceCandidate"?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),i.apply(this,arguments)}};e.RTCPeerConnection.prototype[n]=s[n]});const r={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},o=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[i,s,a]=arguments;return o.apply(this,[i||null]).then(c=>{if(t.version<53&&!s)try{c.forEach(u=>{u.type=r[u.type]||u.type})}catch(u){if(u.name!=="TypeError")throw u;c.forEach((l,d)=>{c.set(d,Object.assign({},l,{type:r[l.type]||l.type}))})}return c}).then(s,a)}}function Gc(e){if(!(typeof e=="object"&&e.RTCPeerConnection&&e.RTCRtpSender)||e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const n=t.apply(this,[]);return n.forEach(i=>i._pc=this),n});const r=e.RTCPeerConnection.prototype.addTrack;r&&(e.RTCPeerConnection.prototype.addTrack=function(){const n=r.apply(this,arguments);return n._pc=this,n}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function Kc(e){if(!(typeof e=="object"&&e.RTCPeerConnection&&e.RTCRtpSender)||e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const o=t.apply(this,[]);return o.forEach(n=>n._pc=this),o}),ur(e,"track",r=>(r.receiver._pc=r.srcElement,r)),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function qc(e){!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(r){Vi("removeStream","removeTrack"),this.getSenders().forEach(o=>{o.track&&r.getTracks().includes(o.track)&&this.removeTrack(o)})})}function Jc(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function Yc(e){if(!(typeof e=="object"&&e.RTCPeerConnection))return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let o=arguments[1]&&arguments[1].sendEncodings;o===void 0&&(o=[]),o=[...o];const n=o.length>0;n&&o.forEach(s=>{if("rid"in s&&!/^[a-z0-9]{0,16}$/i.test(s.rid))throw new TypeError("Invalid RID value provided.");if("scaleResolutionDownBy"in s&&!(parseFloat(s.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in s&&!(parseFloat(s.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")});const i=t.apply(this,arguments);if(n){const{sender:s}=i,a=s.getParameters();(!("encodings"in a)||a.encodings.length===1&&Object.keys(a.encodings[0]).length===0)&&(a.encodings=o,s.sendEncodings=o,this.setParametersPromises.push(s.setParameters(a).then(()=>{delete s.sendEncodings}).catch(()=>{delete s.sendEncodings})))}return i})}function Zc(e){if(!(typeof e=="object"&&e.RTCRtpSender))return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const o=t.apply(this,arguments);return"encodings"in o||(o.encodings=[].concat(this.sendEncodings||[{}])),o})}function Xc(e){if(!(typeof e=="object"&&e.RTCPeerConnection))return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}function Qc(e){if(!(typeof e=="object"&&e.RTCPeerConnection))return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(()=>t.apply(this,arguments)).finally(()=>{this.setParametersPromises=[]}):t.apply(this,arguments)}}const Is=Object.freeze(Object.defineProperty({__proto__:null,shimAddTransceiver:Yc,shimCreateAnswer:Qc,shimCreateOffer:Xc,shimGetDisplayMedia:Yd,shimGetParameters:Zc,shimGetUserMedia:Vc,shimOnTrack:Wc,shimPeerConnection:si,shimRTCDataChannel:Jc,shimReceiverGetStats:Kc,shimRemoveStream:qc,shimSenderGetStats:Gc},Symbol.toStringTag,{value:"Module"}));function el(e){if(!(typeof e!="object"||!e.RTCPeerConnection)){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(o){this._localStreams||(this._localStreams=[]),this._localStreams.includes(o)||this._localStreams.push(o),o.getAudioTracks().forEach(n=>t.call(this,n,o)),o.getVideoTracks().forEach(n=>t.call(this,n,o))},e.RTCPeerConnection.prototype.addTrack=function(o,...n){return n&&n.forEach(i=>{this._localStreams?this._localStreams.includes(i)||this._localStreams.push(i):this._localStreams=[i]}),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(r){this._localStreams||(this._localStreams=[]);const o=this._localStreams.indexOf(r);if(o===-1)return;this._localStreams.splice(o,1);const n=r.getTracks();this.getSenders().forEach(i=>{n.includes(i.track)&&this.removeTrack(i)})})}}function tl(e){if(!(typeof e!="object"||!e.RTCPeerConnection)&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(r){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=r),this.addEventListener("track",this._onaddstreampoly=o=>{o.streams.forEach(n=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(n))return;this._remoteStreams.push(n);const i=new Event("addstream");i.stream=n,this.dispatchEvent(i)})})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const o=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(n){n.streams.forEach(i=>{if(o._remoteStreams||(o._remoteStreams=[]),o._remoteStreams.indexOf(i)>=0)return;o._remoteStreams.push(i);const s=new Event("addstream");s.stream=i,o.dispatchEvent(s)})}),t.apply(o,arguments)}}}function rl(e){if(typeof e!="object"||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,r=t.createOffer,o=t.createAnswer,n=t.setLocalDescription,i=t.setRemoteDescription,s=t.addIceCandidate;t.createOffer=function(u,l){const d=arguments.length>=2?arguments[2]:arguments[0],f=r.apply(this,[d]);return l?(f.then(u,l),Promise.resolve()):f},t.createAnswer=function(u,l){const d=arguments.length>=2?arguments[2]:arguments[0],f=o.apply(this,[d]);return l?(f.then(u,l),Promise.resolve()):f};let a=function(c,u,l){const d=n.apply(this,[c]);return l?(d.then(u,l),Promise.resolve()):d};t.setLocalDescription=a,a=function(c,u,l){const d=i.apply(this,[c]);return l?(d.then(u,l),Promise.resolve()):d},t.setRemoteDescription=a,a=function(c,u,l){const d=s.apply(this,[c]);return l?(d.then(u,l),Promise.resolve()):d},t.addIceCandidate=a}function ol(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const r=t.mediaDevices,o=r.getUserMedia.bind(r);t.mediaDevices.getUserMedia=n=>o(nl(n))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=(function(o,n,i){t.mediaDevices.getUserMedia(o).then(n,i)}).bind(t))}function nl(e){return e&&e.video!==void 0?Object.assign({},e,{video:Ic(e.video)}):e}function il(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(o,n){if(o&&o.iceServers){const i=[];for(let s=0;s<o.iceServers.length;s++){let a=o.iceServers[s];a.urls===void 0&&a.url?(Vi("RTCIceServer.url","RTCIceServer.urls"),a=JSON.parse(JSON.stringify(a)),a.urls=a.url,delete a.url,i.push(a)):i.push(o.iceServers[s])}o.iceServers=i}return new t(o,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get(){return t.generateCertificate}})}function sl(e){typeof e=="object"&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function al(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(o){if(o){typeof o.offerToReceiveAudio<"u"&&(o.offerToReceiveAudio=!!o.offerToReceiveAudio);const n=this.getTransceivers().find(s=>s.receiver.track.kind==="audio");o.offerToReceiveAudio===!1&&n?n.direction==="sendrecv"?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":n.direction==="recvonly"&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):o.offerToReceiveAudio===!0&&!n&&this.addTransceiver("audio",{direction:"recvonly"}),typeof o.offerToReceiveVideo<"u"&&(o.offerToReceiveVideo=!!o.offerToReceiveVideo);const i=this.getTransceivers().find(s=>s.receiver.track.kind==="video");o.offerToReceiveVideo===!1&&i?i.direction==="sendrecv"?i.setDirection?i.setDirection("sendonly"):i.direction="sendonly":i.direction==="recvonly"&&(i.setDirection?i.setDirection("inactive"):i.direction="inactive"):o.offerToReceiveVideo===!0&&!i&&this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function cl(e){typeof e!="object"||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}const Ds=Object.freeze(Object.defineProperty({__proto__:null,shimAudioContext:cl,shimCallbacksAPI:rl,shimConstraints:nl,shimCreateOfferLegacy:al,shimGetUserMedia:ol,shimLocalStreamsAPI:el,shimRTCIceServerUrls:il,shimRemoteStreamsAPI:tl,shimTrackEventTransceiver:sl},Symbol.toStringTag,{value:"Module"}));var eC=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Zd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ll={exports:{}};(function(e){const t={};t.generateIdentifier=function(){return Math.random().toString(36).substring(2,12)},t.localCName=t.generateIdentifier(),t.splitLines=function(r){return r.trim().split(`
`).map(o=>o.trim())},t.splitSections=function(r){return r.split(`
m=`).map((n,i)=>(i>0?"m="+n:n).trim()+`\r
`)},t.getDescription=function(r){const o=t.splitSections(r);return o&&o[0]},t.getMediaSections=function(r){const o=t.splitSections(r);return o.shift(),o},t.matchPrefix=function(r,o){return t.splitLines(r).filter(n=>n.indexOf(o)===0)},t.parseCandidate=function(r){let o;r.indexOf("a=candidate:")===0?o=r.substring(12).split(" "):o=r.substring(10).split(" ");const n={foundation:o[0],component:{1:"rtp",2:"rtcp"}[o[1]]||o[1],protocol:o[2].toLowerCase(),priority:parseInt(o[3],10),ip:o[4],address:o[4],port:parseInt(o[5],10),type:o[7]};for(let i=8;i<o.length;i+=2)switch(o[i]){case"raddr":n.relatedAddress=o[i+1];break;case"rport":n.relatedPort=parseInt(o[i+1],10);break;case"tcptype":n.tcpType=o[i+1];break;case"ufrag":n.ufrag=o[i+1],n.usernameFragment=o[i+1];break;default:n[o[i]]===void 0&&(n[o[i]]=o[i+1]);break}return n},t.writeCandidate=function(r){const o=[];o.push(r.foundation);const n=r.component;n==="rtp"?o.push(1):n==="rtcp"?o.push(2):o.push(n),o.push(r.protocol.toUpperCase()),o.push(r.priority),o.push(r.address||r.ip),o.push(r.port);const i=r.type;return o.push("typ"),o.push(i),i!=="host"&&r.relatedAddress&&r.relatedPort&&(o.push("raddr"),o.push(r.relatedAddress),o.push("rport"),o.push(r.relatedPort)),r.tcpType&&r.protocol.toLowerCase()==="tcp"&&(o.push("tcptype"),o.push(r.tcpType)),(r.usernameFragment||r.ufrag)&&(o.push("ufrag"),o.push(r.usernameFragment||r.ufrag)),"candidate:"+o.join(" ")},t.parseIceOptions=function(r){return r.substring(14).split(" ")},t.parseRtpMap=function(r){let o=r.substring(9).split(" ");const n={payloadType:parseInt(o.shift(),10)};return o=o[0].split("/"),n.name=o[0],n.clockRate=parseInt(o[1],10),n.channels=o.length===3?parseInt(o[2],10):1,n.numChannels=n.channels,n},t.writeRtpMap=function(r){let o=r.payloadType;r.preferredPayloadType!==void 0&&(o=r.preferredPayloadType);const n=r.channels||r.numChannels||1;return"a=rtpmap:"+o+" "+r.name+"/"+r.clockRate+(n!==1?"/"+n:"")+`\r
`},t.parseExtmap=function(r){const o=r.substring(9).split(" ");return{id:parseInt(o[0],10),direction:o[0].indexOf("/")>0?o[0].split("/")[1]:"sendrecv",uri:o[1],attributes:o.slice(2).join(" ")}},t.writeExtmap=function(r){return"a=extmap:"+(r.id||r.preferredId)+(r.direction&&r.direction!=="sendrecv"?"/"+r.direction:"")+" "+r.uri+(r.attributes?" "+r.attributes:"")+`\r
`},t.parseFmtp=function(r){const o={};let n;const i=r.substring(r.indexOf(" ")+1).split(";");for(let s=0;s<i.length;s++)n=i[s].trim().split("="),o[n[0].trim()]=n[1];return o},t.writeFmtp=function(r){let o="",n=r.payloadType;if(r.preferredPayloadType!==void 0&&(n=r.preferredPayloadType),r.parameters&&Object.keys(r.parameters).length){const i=[];Object.keys(r.parameters).forEach(s=>{r.parameters[s]!==void 0?i.push(s+"="+r.parameters[s]):i.push(s)}),o+="a=fmtp:"+n+" "+i.join(";")+`\r
`}return o},t.parseRtcpFb=function(r){const o=r.substring(r.indexOf(" ")+1).split(" ");return{type:o.shift(),parameter:o.join(" ")}},t.writeRtcpFb=function(r){let o="",n=r.payloadType;return r.preferredPayloadType!==void 0&&(n=r.preferredPayloadType),r.rtcpFeedback&&r.rtcpFeedback.length&&r.rtcpFeedback.forEach(i=>{o+="a=rtcp-fb:"+n+" "+i.type+(i.parameter&&i.parameter.length?" "+i.parameter:"")+`\r
`}),o},t.parseSsrcMedia=function(r){const o=r.indexOf(" "),n={ssrc:parseInt(r.substring(7,o),10)},i=r.indexOf(":",o);return i>-1?(n.attribute=r.substring(o+1,i),n.value=r.substring(i+1)):n.attribute=r.substring(o+1),n},t.parseSsrcGroup=function(r){const o=r.substring(13).split(" ");return{semantics:o.shift(),ssrcs:o.map(n=>parseInt(n,10))}},t.getMid=function(r){const o=t.matchPrefix(r,"a=mid:")[0];if(o)return o.substring(6)},t.parseFingerprint=function(r){const o=r.substring(14).split(" ");return{algorithm:o[0].toLowerCase(),value:o[1].toUpperCase()}},t.getDtlsParameters=function(r,o){return{role:"auto",fingerprints:t.matchPrefix(r+o,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(r,o){let n="a=setup:"+o+`\r
`;return r.fingerprints.forEach(i=>{n+="a=fingerprint:"+i.algorithm+" "+i.value+`\r
`}),n},t.parseCryptoLine=function(r){const o=r.substring(9).split(" ");return{tag:parseInt(o[0],10),cryptoSuite:o[1],keyParams:o[2],sessionParams:o.slice(3)}},t.writeCryptoLine=function(r){return"a=crypto:"+r.tag+" "+r.cryptoSuite+" "+(typeof r.keyParams=="object"?t.writeCryptoKeyParams(r.keyParams):r.keyParams)+(r.sessionParams?" "+r.sessionParams.join(" "):"")+`\r
`},t.parseCryptoKeyParams=function(r){if(r.indexOf("inline:")!==0)return null;const o=r.substring(7).split("|");return{keyMethod:"inline",keySalt:o[0],lifeTime:o[1],mkiValue:o[2]?o[2].split(":")[0]:void 0,mkiLength:o[2]?o[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(r){return r.keyMethod+":"+r.keySalt+(r.lifeTime?"|"+r.lifeTime:"")+(r.mkiValue&&r.mkiLength?"|"+r.mkiValue+":"+r.mkiLength:"")},t.getCryptoParameters=function(r,o){return t.matchPrefix(r+o,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(r,o){const n=t.matchPrefix(r+o,"a=ice-ufrag:")[0],i=t.matchPrefix(r+o,"a=ice-pwd:")[0];return n&&i?{usernameFragment:n.substring(12),password:i.substring(10)}:null},t.writeIceParameters=function(r){let o="a=ice-ufrag:"+r.usernameFragment+`\r
a=ice-pwd:`+r.password+`\r
`;return r.iceLite&&(o+=`a=ice-lite\r
`),o},t.parseRtpParameters=function(r){const o={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},i=t.splitLines(r)[0].split(" ");o.profile=i[2];for(let a=3;a<i.length;a++){const c=i[a],u=t.matchPrefix(r,"a=rtpmap:"+c+" ")[0];if(u){const l=t.parseRtpMap(u),d=t.matchPrefix(r,"a=fmtp:"+c+" ");switch(l.parameters=d.length?t.parseFmtp(d[0]):{},l.rtcpFeedback=t.matchPrefix(r,"a=rtcp-fb:"+c+" ").map(t.parseRtcpFb),o.codecs.push(l),l.name.toUpperCase()){case"RED":case"ULPFEC":o.fecMechanisms.push(l.name.toUpperCase());break}}}t.matchPrefix(r,"a=extmap:").forEach(a=>{o.headerExtensions.push(t.parseExtmap(a))});const s=t.matchPrefix(r,"a=rtcp-fb:* ").map(t.parseRtcpFb);return o.codecs.forEach(a=>{s.forEach(c=>{a.rtcpFeedback.find(l=>l.type===c.type&&l.parameter===c.parameter)||a.rtcpFeedback.push(c)})}),o},t.writeRtpDescription=function(r,o){let n="";n+="m="+r+" ",n+=o.codecs.length>0?"9":"0",n+=" "+(o.profile||"UDP/TLS/RTP/SAVPF")+" ",n+=o.codecs.map(s=>s.preferredPayloadType!==void 0?s.preferredPayloadType:s.payloadType).join(" ")+`\r
`,n+=`c=IN IP4 0.0.0.0\r
`,n+=`a=rtcp:9 IN IP4 0.0.0.0\r
`,o.codecs.forEach(s=>{n+=t.writeRtpMap(s),n+=t.writeFmtp(s),n+=t.writeRtcpFb(s)});let i=0;return o.codecs.forEach(s=>{s.maxptime>i&&(i=s.maxptime)}),i>0&&(n+="a=maxptime:"+i+`\r
`),o.headerExtensions&&o.headerExtensions.forEach(s=>{n+=t.writeExtmap(s)}),n},t.parseRtpEncodingParameters=function(r){const o=[],n=t.parseRtpParameters(r),i=n.fecMechanisms.indexOf("RED")!==-1,s=n.fecMechanisms.indexOf("ULPFEC")!==-1,a=t.matchPrefix(r,"a=ssrc:").map(f=>t.parseSsrcMedia(f)).filter(f=>f.attribute==="cname"),c=a.length>0&&a[0].ssrc;let u;const l=t.matchPrefix(r,"a=ssrc-group:FID").map(f=>f.substring(17).split(" ").map(m=>parseInt(m,10)));l.length>0&&l[0].length>1&&l[0][0]===c&&(u=l[0][1]),n.codecs.forEach(f=>{if(f.name.toUpperCase()==="RTX"&&f.parameters.apt){let p={ssrc:c,codecPayloadType:parseInt(f.parameters.apt,10)};c&&u&&(p.rtx={ssrc:u}),o.push(p),i&&(p=JSON.parse(JSON.stringify(p)),p.fec={ssrc:c,mechanism:s?"red+ulpfec":"red"},o.push(p))}}),o.length===0&&c&&o.push({ssrc:c});let d=t.matchPrefix(r,"b=");return d.length&&(d[0].indexOf("b=TIAS:")===0?d=parseInt(d[0].substring(7),10):d[0].indexOf("b=AS:")===0?d=parseInt(d[0].substring(5),10)*1e3*.95-50*40*8:d=void 0,o.forEach(f=>{f.maxBitrate=d})),o},t.parseRtcpParameters=function(r){const o={},n=t.matchPrefix(r,"a=ssrc:").map(a=>t.parseSsrcMedia(a)).filter(a=>a.attribute==="cname")[0];n&&(o.cname=n.value,o.ssrc=n.ssrc);const i=t.matchPrefix(r,"a=rtcp-rsize");o.reducedSize=i.length>0,o.compound=i.length===0;const s=t.matchPrefix(r,"a=rtcp-mux");return o.mux=s.length>0,o},t.writeRtcpParameters=function(r){let o="";return r.reducedSize&&(o+=`a=rtcp-rsize\r
`),r.mux&&(o+=`a=rtcp-mux\r
`),r.ssrc!==void 0&&r.cname&&(o+="a=ssrc:"+r.ssrc+" cname:"+r.cname+`\r
`),o},t.parseMsid=function(r){let o;const n=t.matchPrefix(r,"a=msid:");if(n.length===1)return o=n[0].substring(7).split(" "),{stream:o[0],track:o[1]};const i=t.matchPrefix(r,"a=ssrc:").map(s=>t.parseSsrcMedia(s)).filter(s=>s.attribute==="msid");if(i.length>0)return o=i[0].value.split(" "),{stream:o[0],track:o[1]}},t.parseSctpDescription=function(r){const o=t.parseMLine(r),n=t.matchPrefix(r,"a=max-message-size:");let i;n.length>0&&(i=parseInt(n[0].substring(19),10)),isNaN(i)&&(i=65536);const s=t.matchPrefix(r,"a=sctp-port:");if(s.length>0)return{port:parseInt(s[0].substring(12),10),protocol:o.fmt,maxMessageSize:i};const a=t.matchPrefix(r,"a=sctpmap:");if(a.length>0){const c=a[0].substring(10).split(" ");return{port:parseInt(c[0],10),protocol:c[1],maxMessageSize:i}}},t.writeSctpDescription=function(r,o){let n=[];return r.protocol!=="DTLS/SCTP"?n=["m="+r.kind+" 9 "+r.protocol+" "+o.protocol+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctp-port:"+o.port+`\r
`]:n=["m="+r.kind+" 9 "+r.protocol+" "+o.port+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctpmap:"+o.port+" "+o.protocol+` 65535\r
`],o.maxMessageSize!==void 0&&n.push("a=max-message-size:"+o.maxMessageSize+`\r
`),n.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,22)},t.writeSessionBoilerplate=function(r,o,n){let i;const s=o!==void 0?o:2;return r?i=r:i=t.generateSessionId(),`v=0\r
o=`+(n||"thisisadapterortc")+" "+i+" "+s+` IN IP4 127.0.0.1\r
s=-\r
t=0 0\r
`},t.getDirection=function(r,o){const n=t.splitLines(r);for(let i=0;i<n.length;i++)switch(n[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return n[i].substring(2)}return o?t.getDirection(o):"sendrecv"},t.getKind=function(r){return t.splitLines(r)[0].split(" ")[0].substring(2)},t.isRejected=function(r){return r.split(" ",2)[1]==="0"},t.parseMLine=function(r){const n=t.splitLines(r)[0].substring(2).split(" ");return{kind:n[0],port:parseInt(n[1],10),protocol:n[2],fmt:n.slice(3).join(" ")}},t.parseOLine=function(r){const n=t.matchPrefix(r,"o=")[0].substring(2).split(" ");return{username:n[0],sessionId:n[1],sessionVersion:parseInt(n[2],10),netType:n[3],addressType:n[4],address:n[5]}},t.isValidSDP=function(r){if(typeof r!="string"||r.length===0)return!1;const o=t.splitLines(r);for(let n=0;n<o.length;n++)if(o[n].length<2||o[n].charAt(1)!=="=")return!1;return!0},e.exports=t})(ll);var ul=ll.exports;const br=Zd(ul),Xd=Gd({__proto__:null,default:br},[ul]);function No(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(o){if(typeof o=="object"&&o.candidate&&o.candidate.indexOf("a=")===0&&(o=JSON.parse(JSON.stringify(o)),o.candidate=o.candidate.substring(2)),o.candidate&&o.candidate.length){const n=new t(o),i=br.parseCandidate(o.candidate);for(const s in i)s in n||Object.defineProperty(n,s,{value:i[s]});return n.toJSON=function(){return{candidate:n.candidate,sdpMid:n.sdpMid,sdpMLineIndex:n.sdpMLineIndex,usernameFragment:n.usernameFragment}},n}return new t(o)},e.RTCIceCandidate.prototype=t.prototype,ur(e,"icecandidate",r=>(r.candidate&&Object.defineProperty(r,"candidate",{value:new e.RTCIceCandidate(r.candidate),writable:"false"}),r))}function ai(e){!e.RTCIceCandidate||e.RTCIceCandidate&&"relayProtocol"in e.RTCIceCandidate.prototype||ur(e,"icecandidate",t=>{if(t.candidate){const r=br.parseCandidate(t.candidate.candidate);r.type==="relay"&&(t.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[r.priority>>24])}return t})}function Fo(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp}});const r=function(a){if(!a||!a.sdp)return!1;const c=br.splitSections(a.sdp);return c.shift(),c.some(u=>{const l=br.parseMLine(u);return l&&l.kind==="application"&&l.protocol.indexOf("SCTP")!==-1})},o=function(a){const c=a.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(c===null||c.length<2)return-1;const u=parseInt(c[1],10);return u!==u?-1:u},n=function(a){let c=65536;return t.browser==="firefox"&&(t.version<57?a===-1?c=16384:c=2147483637:t.version<60?c=t.version===57?65535:65536:c=2147483637),c},i=function(a,c){let u=65536;t.browser==="firefox"&&t.version===57&&(u=65535);const l=br.matchPrefix(a.sdp,"a=max-message-size:");return l.length>0?u=parseInt(l[0].substring(19),10):t.browser==="firefox"&&c!==-1&&(u=2147483637),u},s=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,t.browser==="chrome"&&t.version>=76){const{sdpSemantics:c}=this.getConfiguration();c==="plan-b"&&Object.defineProperty(this,"sctp",{get(){return typeof this._sctp>"u"?null:this._sctp},enumerable:!0,configurable:!0})}if(r(arguments[0])){const c=o(arguments[0]),u=n(c),l=i(arguments[0],c);let d;u===0&&l===0?d=Number.POSITIVE_INFINITY:u===0||l===0?d=Math.max(u,l):d=Math.min(u,l);const f={};Object.defineProperty(f,"maxMessageSize",{get(){return d}}),this._sctp=f}return s.apply(this,arguments)}}function zo(e){if(!(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype))return;function t(o,n){const i=o.send;o.send=function(){const a=arguments[0],c=a.length||a.size||a.byteLength;if(o.readyState==="open"&&n.sctp&&c>n.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+n.sctp.maxMessageSize+" bytes)");return i.apply(o,arguments)}}const r=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const n=r.apply(this,arguments);return t(n,this),n},ur(e,"datachannel",o=>(t(o.channel,o.target),o))}function ci(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(r){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),r&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=r)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(r=>{const o=t[r];t[r]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=n=>{const i=n.target;if(i._lastConnectionState!==i.connectionState){i._lastConnectionState=i.connectionState;const s=new Event("connectionstatechange",n);i.dispatchEvent(s)}return n},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),o.apply(this,arguments)}})}function li(e,t){if(!e.RTCPeerConnection||t.browser==="chrome"&&t.version>=71||t.browser==="safari"&&t.version>=605)return;const r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(n){if(n&&n.sdp&&n.sdp.indexOf(`
a=extmap-allow-mixed`)!==-1){const i=n.sdp.split(`
`).filter(s=>s.trim()!=="a=extmap-allow-mixed").join(`
`);e.RTCSessionDescription&&n instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:n.type,sdp:i}):n.sdp=i}return r.apply(this,arguments)}}function Uo(e,t){if(!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype))return;const r=e.RTCPeerConnection.prototype.addIceCandidate;!r||r.length===0||(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?(t.browser==="chrome"&&t.version<78||t.browser==="firefox"&&t.version<68||t.browser==="safari")&&arguments[0]&&arguments[0].candidate===""?Promise.resolve():r.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function Ho(e,t){if(!(e.RTCPeerConnection&&e.RTCPeerConnection.prototype))return;const r=e.RTCPeerConnection.prototype.setLocalDescription;!r||r.length===0||(e.RTCPeerConnection.prototype.setLocalDescription=function(){let n=arguments[0]||{};if(typeof n!="object"||n.type&&n.sdp)return r.apply(this,arguments);if(n={type:n.type,sdp:n.sdp},!n.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":n.type="offer";break;default:n.type="answer";break}return n.sdp||n.type!=="offer"&&n.type!=="answer"?r.apply(this,[n]):(n.type==="offer"?this.createOffer:this.createAnswer).apply(this).then(s=>r.apply(this,[s]))})}const Qd=Object.freeze(Object.defineProperty({__proto__:null,removeExtmapAllowMixed:li,shimAddIceCandidateNullOrEmpty:Uo,shimConnectionState:ci,shimMaxMessageSize:Fo,shimParameterlessSetLocalDescription:Ho,shimRTCIceCandidate:No,shimRTCIceCandidateRelayProtocol:ai,shimSendThrowTypeError:zo},Symbol.toStringTag,{value:"Module"}));function ef({window:e}={},t={shimChrome:!0,shimFirefox:!0,shimSafari:!0}){const r=Lc,o=Jd(e),n={browserDetails:o,commonShim:Qd,extractVersion:Mo,disableLog:Kd,disableWarnings:qd,sdp:Xd};switch(o.browser){case"chrome":if(!Ls||!ii||!t.shimChrome)return r("Chrome shim is not included in this adapter release."),n;if(o.version===null)return r("Chrome shim can not determine version, not shimming."),n;r("adapter.js shimming chrome."),n.browserShim=Ls,Uo(e,o),Ho(e),Dc(e,o),jc(e),ii(e,o),Mc(e),Uc(e,o),Nc(e),Fc(e),Hc(e,o),No(e),ai(e),ci(e),Fo(e,o),zo(e),li(e,o);break;case"firefox":if(!Is||!si||!t.shimFirefox)return r("Firefox shim is not included in this adapter release."),n;r("adapter.js shimming firefox."),n.browserShim=Is,Uo(e,o),Ho(e),Vc(e,o),si(e,o),Wc(e),qc(e),Gc(e),Kc(e),Jc(e),Yc(e),Zc(e),Xc(e),Qc(e),No(e),ci(e),Fo(e,o),zo(e);break;case"safari":if(!Ds||!t.shimSafari)return r("Safari shim is not included in this adapter release."),n;r("adapter.js shimming safari."),n.browserShim=Ds,Uo(e,o),Ho(e),il(e),al(e),rl(e),el(e),tl(e),sl(e),ol(e),cl(e),No(e),ai(e),Fo(e,o),zo(e),li(e,o);break;default:r("Unsupported browser!");break}return n}const tf=ef({window:typeof window>"u"?void 0:window});/**
* @vue/shared v3.4.33
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Wi(e,t){const r=new Set(e.split(","));return o=>r.has(o)}const ve={},vr=[],Xe=()=>{},rf=()=>!1,gn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Gi=e=>e.startsWith("onUpdate:"),we=Object.assign,Ki=(e,t)=>{const r=e.indexOf(t);r>-1&&e.splice(r,1)},of=Object.prototype.hasOwnProperty,ie=(e,t)=>of.call(e,t),G=Array.isArray,yr=e=>hn(e)==="[object Map]",dl=e=>hn(e)==="[object Set]",q=e=>typeof e=="function",ke=e=>typeof e=="string",Kt=e=>typeof e=="symbol",he=e=>e!==null&&typeof e=="object",fl=e=>(he(e)||q(e))&&q(e.then)&&q(e.catch),pl=Object.prototype.toString,hn=e=>pl.call(e),nf=e=>hn(e).slice(8,-1),gl=e=>hn(e)==="[object Object]",qi=e=>ke(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ur=Wi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),mn=e=>{const t=Object.create(null);return r=>t[r]||(t[r]=e(r))},sf=/-(\w)/g,ct=mn(e=>e.replace(sf,(t,r)=>r?r.toUpperCase():"")),af=/\B([A-Z])/g,dr=mn(e=>e.replace(af,"-$1").toLowerCase()),bn=mn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Mn=mn(e=>e?`on${bn(e)}`:""),Vt=(e,t)=>!Object.is(e,t),Nn=(e,...t)=>{for(let r=0;r<e.length;r++)e[r](...t)},hl=(e,t,r,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:r})},cf=e=>{const t=parseFloat(e);return isNaN(t)?e:t},lf=e=>{const t=ke(e)?Number(e):NaN;return isNaN(t)?e:t};let js;const ml=()=>js||(js=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function vn(e){if(G(e)){const t={};for(let r=0;r<e.length;r++){const o=e[r],n=ke(o)?pf(o):vn(o);if(n)for(const i in n)t[i]=n[i]}return t}else if(ke(e)||he(e))return e}const uf=/;(?![^(]*\))/g,df=/:([^]+)/,ff=/\/\*[^]*?\*\//g;function pf(e){const t={};return e.replace(ff,"").split(uf).forEach(r=>{if(r){const o=r.split(df);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function wr(e){let t="";if(ke(e))t=e;else if(G(e))for(let r=0;r<e.length;r++){const o=wr(e[r]);o&&(t+=o+" ")}else if(he(e))for(const r in e)e[r]&&(t+=r+" ");return t.trim()}function gf(e){if(!e)return null;let{class:t,style:r}=e;return t&&!ke(t)&&(e.class=wr(t)),r&&(e.style=vn(r)),e}const hf="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",mf=Wi(hf);function bl(e){return!!e||e===""}const vl=e=>!!(e&&e.__v_isRef===!0),eo=e=>ke(e)?e:e==null?"":G(e)||he(e)&&(e.toString===pl||!q(e.toString))?vl(e)?eo(e.value):JSON.stringify(e,yl,2):String(e),yl=(e,t)=>vl(t)?yl(e,t.value):yr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((r,[o,n],i)=>(r[Fn(o,i)+" =>"]=n,r),{})}:dl(t)?{[`Set(${t.size})`]:[...t.values()].map(r=>Fn(r))}:Kt(t)?Fn(t):he(t)&&!G(t)&&!gl(t)?String(t):t,Fn=(e,t="")=>{var r;return Kt(e)?`Symbol(${(r=e.description)!=null?r:t})`:e};/**
* @vue/reactivity v3.4.33
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let We;class Cl{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=We,!t&&We&&(this.index=(We.scopes||(We.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const r=We;try{return We=this,t()}finally{We=r}}}on(){We=this}off(){We=this.parent}stop(t){if(this._active){let r,o;for(r=0,o=this.effects.length;r<o;r++)this.effects[r].stop();for(r=0,o=this.cleanups.length;r<o;r++)this.cleanups[r]();if(this.scopes)for(r=0,o=this.scopes.length;r<o;r++)this.scopes[r].stop(!0);if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0,this._active=!1}}}function kl(e){return new Cl(e)}function bf(e,t=We){t&&t.active&&t.effects.push(e)}function Sl(){return We}function vf(e){We&&We.cleanups.push(e)}let or;class Ji{constructor(t,r,o,n){this.fn=t,this.trigger=r,this.scheduler=o,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,bf(this,n)}get dirty(){if(this._dirtyLevel===2||this._dirtyLevel===3){this._dirtyLevel=1,qt();for(let t=0;t<this._depsLength;t++){const r=this.deps[t];if(r.computed&&(yf(r.computed),this._dirtyLevel>=4))break}this._dirtyLevel===1&&(this._dirtyLevel=0),Jt()}return this._dirtyLevel>=4}set dirty(t){this._dirtyLevel=t?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let t=zt,r=or;try{return zt=!0,or=this,this._runnings++,Ms(this),this.fn()}finally{Ns(this),this._runnings--,or=r,zt=t}}stop(){this.active&&(Ms(this),Ns(this),this.onStop&&this.onStop(),this.active=!1)}}function yf(e){return e.value}function Ms(e){e._trackId++,e._depsLength=0}function Ns(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)xl(e.deps[t],e);e.deps.length=e._depsLength}}function xl(e,t){const r=e.get(t);r!==void 0&&t._trackId!==r&&(e.delete(t),e.size===0&&e.cleanup())}let zt=!0,ui=0;const _l=[];function qt(){_l.push(zt),zt=!1}function Jt(){const e=_l.pop();zt=e===void 0?!0:e}function Yi(){ui++}function Zi(){for(ui--;!ui&&di.length;)di.shift()()}function Rl(e,t,r){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const o=e.deps[e._depsLength];o!==t?(o&&xl(o,e),e.deps[e._depsLength++]=t):e._depsLength++}}const di=[];function Tl(e,t,r){Yi();for(const o of e.keys()){let n;o._dirtyLevel<t&&(n??(n=e.get(o)===o._trackId))&&(o._shouldSchedule||(o._shouldSchedule=o._dirtyLevel===0),o._dirtyLevel=t),o._shouldSchedule&&(n??(n=e.get(o)===o._trackId))&&(o.trigger(),(!o._runnings||o.allowRecurse)&&o._dirtyLevel!==2&&(o._shouldSchedule=!1,o.scheduler&&di.push(o.scheduler)))}Zi()}const wl=(e,t)=>{const r=new Map;return r.cleanup=e,r.computed=t,r},Qo=new WeakMap,nr=Symbol(""),fi=Symbol("");function Ue(e,t,r){if(zt&&or){let o=Qo.get(e);o||Qo.set(e,o=new Map);let n=o.get(r);n||o.set(r,n=wl(()=>o.delete(r))),Rl(or,n)}}function xt(e,t,r,o,n,i){const s=Qo.get(e);if(!s)return;let a=[];if(t==="clear")a=[...s.values()];else if(r==="length"&&G(e)){const c=Number(o);s.forEach((u,l)=>{(l==="length"||!Kt(l)&&l>=c)&&a.push(u)})}else switch(r!==void 0&&a.push(s.get(r)),t){case"add":G(e)?qi(r)&&a.push(s.get("length")):(a.push(s.get(nr)),yr(e)&&a.push(s.get(fi)));break;case"delete":G(e)||(a.push(s.get(nr)),yr(e)&&a.push(s.get(fi)));break;case"set":yr(e)&&a.push(s.get(nr));break}Yi();for(const c of a)c&&Tl(c,4);Zi()}function Cf(e,t){const r=Qo.get(e);return r&&r.get(t)}const kf=Wi("__proto__,__v_isRef,__isVue"),Pl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Kt)),Fs=Sf();function Sf(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...r){const o=oe(this);for(let i=0,s=this.length;i<s;i++)Ue(o,"get",i+"");const n=o[t](...r);return n===-1||n===!1?o[t](...r.map(oe)):n}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...r){qt(),Yi();const o=oe(this)[t].apply(this,r);return Zi(),Jt(),o}}),e}function xf(e){Kt(e)||(e=String(e));const t=oe(this);return Ue(t,"has",e),t.hasOwnProperty(e)}class El{constructor(t=!1,r=!1){this._isReadonly=t,this._isShallow=r}get(t,r,o){const n=this._isReadonly,i=this._isShallow;if(r==="__v_isReactive")return!n;if(r==="__v_isReadonly")return n;if(r==="__v_isShallow")return i;if(r==="__v_raw")return o===(n?i?Df:Al:i?Bl:$l).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const s=G(t);if(!n){if(s&&ie(Fs,r))return Reflect.get(Fs,r,o);if(r==="hasOwnProperty")return xf}const a=Reflect.get(t,r,o);return(Kt(r)?Pl.has(r):kf(r))||(n||Ue(t,"get",r),i)?a:_e(a)?s&&qi(r)?a:a.value:he(a)?n?es(a):Pr(a):a}}class Ol extends El{constructor(t=!1){super(!1,t)}set(t,r,o,n){let i=t[r];if(!this._isShallow){const c=ar(i);if(!Sr(o)&&!ar(o)&&(i=oe(i),o=oe(o)),!G(t)&&_e(i)&&!_e(o))return c?!1:(i.value=o,!0)}const s=G(t)&&qi(r)?Number(r)<t.length:ie(t,r),a=Reflect.set(t,r,o,n);return t===oe(n)&&(s?Vt(o,i)&&xt(t,"set",r,o):xt(t,"add",r,o)),a}deleteProperty(t,r){const o=ie(t,r);t[r];const n=Reflect.deleteProperty(t,r);return n&&o&&xt(t,"delete",r,void 0),n}has(t,r){const o=Reflect.has(t,r);return(!Kt(r)||!Pl.has(r))&&Ue(t,"has",r),o}ownKeys(t){return Ue(t,"iterate",G(t)?"length":nr),Reflect.ownKeys(t)}}class _f extends El{constructor(t=!1){super(!0,t)}set(t,r){return!0}deleteProperty(t,r){return!0}}const Rf=new Ol,Tf=new _f,wf=new Ol(!0);const Xi=e=>e,yn=e=>Reflect.getPrototypeOf(e);function wo(e,t,r=!1,o=!1){e=e.__v_raw;const n=oe(e),i=oe(t);r||(Vt(t,i)&&Ue(n,"get",t),Ue(n,"get",i));const{has:s}=yn(n),a=o?Xi:r?os:to;if(s.call(n,t))return a(e.get(t));if(s.call(n,i))return a(e.get(i));e!==n&&e.get(t)}function Po(e,t=!1){const r=this.__v_raw,o=oe(r),n=oe(e);return t||(Vt(e,n)&&Ue(o,"has",e),Ue(o,"has",n)),e===n?r.has(e):r.has(e)||r.has(n)}function Eo(e,t=!1){return e=e.__v_raw,!t&&Ue(oe(e),"iterate",nr),Reflect.get(e,"size",e)}function zs(e,t=!1){!t&&!Sr(e)&&!ar(e)&&(e=oe(e));const r=oe(this);return yn(r).has.call(r,e)||(r.add(e),xt(r,"add",e,e)),this}function Us(e,t,r=!1){!r&&!Sr(t)&&!ar(t)&&(t=oe(t));const o=oe(this),{has:n,get:i}=yn(o);let s=n.call(o,e);s||(e=oe(e),s=n.call(o,e));const a=i.call(o,e);return o.set(e,t),s?Vt(t,a)&&xt(o,"set",e,t):xt(o,"add",e,t),this}function Hs(e){const t=oe(this),{has:r,get:o}=yn(t);let n=r.call(t,e);n||(e=oe(e),n=r.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return n&&xt(t,"delete",e,void 0),i}function Vs(){const e=oe(this),t=e.size!==0,r=e.clear();return t&&xt(e,"clear",void 0,void 0),r}function Oo(e,t){return function(o,n){const i=this,s=i.__v_raw,a=oe(s),c=t?Xi:e?os:to;return!e&&Ue(a,"iterate",nr),s.forEach((u,l)=>o.call(n,c(u),c(l),i))}}function $o(e,t,r){return function(...o){const n=this.__v_raw,i=oe(n),s=yr(i),a=e==="entries"||e===Symbol.iterator&&s,c=e==="keys"&&s,u=n[e](...o),l=r?Xi:t?os:to;return!t&&Ue(i,"iterate",c?fi:nr),{next(){const{value:d,done:f}=u.next();return f?{value:d,done:f}:{value:a?[l(d[0]),l(d[1])]:l(d),done:f}},[Symbol.iterator](){return this}}}}function Tt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Pf(){const e={get(i){return wo(this,i)},get size(){return Eo(this)},has:Po,add:zs,set:Us,delete:Hs,clear:Vs,forEach:Oo(!1,!1)},t={get(i){return wo(this,i,!1,!0)},get size(){return Eo(this)},has:Po,add(i){return zs.call(this,i,!0)},set(i,s){return Us.call(this,i,s,!0)},delete:Hs,clear:Vs,forEach:Oo(!1,!0)},r={get(i){return wo(this,i,!0)},get size(){return Eo(this,!0)},has(i){return Po.call(this,i,!0)},add:Tt("add"),set:Tt("set"),delete:Tt("delete"),clear:Tt("clear"),forEach:Oo(!0,!1)},o={get(i){return wo(this,i,!0,!0)},get size(){return Eo(this,!0)},has(i){return Po.call(this,i,!0)},add:Tt("add"),set:Tt("set"),delete:Tt("delete"),clear:Tt("clear"),forEach:Oo(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(i=>{e[i]=$o(i,!1,!1),r[i]=$o(i,!0,!1),t[i]=$o(i,!1,!0),o[i]=$o(i,!0,!0)}),[e,r,t,o]}const[Ef,Of,$f,Bf]=Pf();function Qi(e,t){const r=t?e?Bf:$f:e?Of:Ef;return(o,n,i)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?o:Reflect.get(ie(r,n)&&n in o?r:o,n,i)}const Af={get:Qi(!1,!1)},Lf={get:Qi(!1,!0)},If={get:Qi(!0,!1)};const $l=new WeakMap,Bl=new WeakMap,Al=new WeakMap,Df=new WeakMap;function jf(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Mf(e){return e.__v_skip||!Object.isExtensible(e)?0:jf(nf(e))}function Pr(e){return ar(e)?e:ts(e,!1,Rf,Af,$l)}function Ll(e){return ts(e,!1,wf,Lf,Bl)}function es(e){return ts(e,!0,Tf,If,Al)}function ts(e,t,r,o,n){if(!he(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=n.get(e);if(i)return i;const s=Mf(e);if(s===0)return e;const a=new Proxy(e,s===2?o:r);return n.set(e,a),a}function ir(e){return ar(e)?ir(e.__v_raw):!!(e&&e.__v_isReactive)}function ar(e){return!!(e&&e.__v_isReadonly)}function Sr(e){return!!(e&&e.__v_isShallow)}function Il(e){return e?!!e.__v_raw:!1}function oe(e){const t=e&&e.__v_raw;return t?oe(t):e}function rs(e){return Object.isExtensible(e)&&hl(e,"__v_skip",!0),e}const to=e=>he(e)?Pr(e):e,os=e=>he(e)?es(e):e;class Dl{constructor(t,r,o,n){this.getter=t,this._setter=r,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Ji(()=>t(this._value),()=>Hr(this,this.effect._dirtyLevel===2?2:3)),this.effect.computed=this,this.effect.active=this._cacheable=!n,this.__v_isReadonly=o}get value(){const t=oe(this);return(!t._cacheable||t.effect.dirty)&&Vt(t._value,t._value=t.effect.run())&&Hr(t,4),ns(t),t.effect._dirtyLevel>=2&&Hr(t,2),t._value}set value(t){this._setter(t)}get _dirty(){return this.effect.dirty}set _dirty(t){this.effect.dirty=t}}function Nf(e,t,r=!1){let o,n;const i=q(e);return i?(o=e,n=Xe):(o=e.get,n=e.set),new Dl(o,n,i||!n,r)}function ns(e){var t;zt&&or&&(e=oe(e),Rl(or,(t=e.dep)!=null?t:e.dep=wl(()=>e.dep=void 0,e instanceof Dl?e:void 0)))}function Hr(e,t=4,r,o){e=oe(e);const n=e.dep;n&&Tl(n,t)}function _e(e){return!!(e&&e.__v_isRef===!0)}function bt(e){return jl(e,!1)}function Ff(e){return jl(e,!0)}function jl(e,t){return _e(e)?e:new zf(e,t)}class zf{constructor(t,r){this.__v_isShallow=r,this.dep=void 0,this.__v_isRef=!0,this._rawValue=r?t:oe(t),this._value=r?t:to(t)}get value(){return ns(this),this._value}set value(t){const r=this.__v_isShallow||Sr(t)||ar(t);t=r?t:oe(t),Vt(t,this._rawValue)&&(this._rawValue,this._rawValue=t,this._value=r?t:to(t),Hr(this,4))}}function it(e){return _e(e)?e.value:e}const Uf={get:(e,t,r)=>it(Reflect.get(e,t,r)),set:(e,t,r,o)=>{const n=e[t];return _e(n)&&!_e(r)?(n.value=r,!0):Reflect.set(e,t,r,o)}};function Ml(e){return ir(e)?e:new Proxy(e,Uf)}class Hf{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:r,set:o}=t(()=>ns(this),()=>Hr(this));this._get=r,this._set=o}get value(){return this._get()}set value(t){this._set(t)}}function tC(e){return new Hf(e)}function Vf(e){const t=G(e)?new Array(e.length):{};for(const r in e)t[r]=Nl(e,r);return t}class Wf{constructor(t,r,o){this._object=t,this._key=r,this._defaultValue=o,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Cf(oe(this._object),this._key)}}class Gf{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function rC(e,t,r){return _e(e)?e:q(e)?new Gf(e):he(e)&&arguments.length>1?Nl(e,t,r):bt(e)}function Nl(e,t,r){const o=e[t];return _e(o)?o:new Wf(e,t,r)}/**
* @vue/runtime-core v3.4.33
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ut(e,t,r,o){try{return o?e(...o):e()}catch(n){Cn(n,t,r)}}function Qe(e,t,r,o){if(q(e)){const n=Ut(e,t,r,o);return n&&fl(n)&&n.catch(i=>{Cn(i,t,r)}),n}if(G(e)){const n=[];for(let i=0;i<e.length;i++)n.push(Qe(e[i],t,r,o));return n}}function Cn(e,t,r,o=!0){const n=t?t.vnode:null;if(t){let i=t.parent;const s=t.proxy,a=`https://vuejs.org/error-reference/#runtime-${r}`;for(;i;){const u=i.ec;if(u){for(let l=0;l<u.length;l++)if(u[l](e,s,a)===!1)return}i=i.parent}const c=t.appContext.config.errorHandler;if(c){qt(),Ut(c,null,10,[e,s,a]),Jt();return}}Kf(e,r,n,o)}function Kf(e,t,r,o=!0){console.error(e)}let ro=!1,pi=!1;const Ie=[];let ht=0;const Cr=[];let It=null,er=0;const Fl=Promise.resolve();let is=null;function kn(e){const t=is||Fl;return e?t.then(this?e.bind(this):e):t}function qf(e){let t=ht+1,r=Ie.length;for(;t<r;){const o=t+r>>>1,n=Ie[o],i=oo(n);i<e||i===e&&n.pre?t=o+1:r=o}return t}function ss(e){(!Ie.length||!Ie.includes(e,ro&&e.allowRecurse?ht+1:ht))&&(e.id==null?Ie.push(e):Ie.splice(qf(e.id),0,e),zl())}function zl(){!ro&&!pi&&(pi=!0,is=Fl.then(Hl))}function Jf(e){const t=Ie.indexOf(e);t>ht&&Ie.splice(t,1)}function Yf(e){G(e)?Cr.push(...e):(!It||!It.includes(e,e.allowRecurse?er+1:er))&&Cr.push(e),zl()}function Ws(e,t,r=ro?ht+1:0){for(;r<Ie.length;r++){const o=Ie[r];if(o&&o.pre){if(e&&o.id!==e.uid)continue;Ie.splice(r,1),r--,o()}}}function Ul(e){if(Cr.length){const t=[...new Set(Cr)].sort((r,o)=>oo(r)-oo(o));if(Cr.length=0,It){It.push(...t);return}for(It=t,er=0;er<It.length;er++){const r=It[er];r.active!==!1&&r()}It=null,er=0}}const oo=e=>e.id==null?1/0:e.id,Zf=(e,t)=>{const r=oo(e)-oo(t);if(r===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return r};function Hl(e){pi=!1,ro=!0,Ie.sort(Zf);try{for(ht=0;ht<Ie.length;ht++){const t=Ie[ht];t&&t.active!==!1&&Ut(t,t.i,t.i?15:14)}}finally{ht=0,Ie.length=0,Ul(),ro=!1,is=null,(Ie.length||Cr.length)&&Hl()}}let Te=null,Vl=null;function en(e){const t=Te;return Te=e,Vl=e&&e.type.__scopeId||null,t}function tn(e,t=Te,r){if(!t||e._n)return e;const o=(...n)=>{o._d&&sa(-1);const i=en(t);let s;try{s=e(...n)}finally{en(i),o._d&&sa(1)}return s};return o._n=!0,o._c=!0,o._d=!0,o}function Xf(e,t){if(Te===null)return e;const r=Pn(Te),o=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[i,s,a,c=ve]=t[n];i&&(q(i)&&(i={mounted:i,updated:i}),i.deep&&Nt(s),o.push({dir:i,instance:r,value:s,oldValue:void 0,arg:a,modifiers:c}))}return e}function Yt(e,t,r,o){const n=e.dirs,i=t&&t.dirs;for(let s=0;s<n.length;s++){const a=n[s];i&&(a.oldValue=i[s].value);let c=a.dir[o];c&&(qt(),Qe(c,r,8,[e.el,a,e,t]),Jt())}}const Dt=Symbol("_leaveCb"),Bo=Symbol("_enterCb");function Wl(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return ls(()=>{e.isMounted=!0}),Zl(()=>{e.isUnmounting=!0}),e}const Je=[Function,Array],Gl={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Je,onEnter:Je,onAfterEnter:Je,onEnterCancelled:Je,onBeforeLeave:Je,onLeave:Je,onAfterLeave:Je,onLeaveCancelled:Je,onBeforeAppear:Je,onAppear:Je,onAfterAppear:Je,onAppearCancelled:Je},Kl=e=>{const t=e.subTree;return t.component?Kl(t.component):t},Qf={name:"BaseTransition",props:Gl,setup(e,{slots:t}){const r=ms(),o=Wl();return()=>{const n=t.default&&as(t.default(),!0);if(!n||!n.length)return;let i=n[0];if(n.length>1){for(const f of n)if(f.type!==Ne){i=f;break}}const s=oe(e),{mode:a}=s;if(o.isLeaving)return zn(i);const c=Gs(i);if(!c)return zn(i);let u=no(c,s,o,r,f=>u=f);xr(c,u);const l=r.subTree,d=l&&Gs(l);if(d&&d.type!==Ne&&!tr(c,d)&&Kl(r).type!==Ne){const f=no(d,s,o,r);if(xr(d,f),a==="out-in"&&c.type!==Ne)return o.isLeaving=!0,f.afterLeave=()=>{o.isLeaving=!1,r.update.active!==!1&&(r.effect.dirty=!0,r.update())},zn(i);a==="in-out"&&c.type!==Ne&&(f.delayLeave=(p,m,b)=>{const _=ql(o,d);_[String(d.key)]=d,p[Dt]=()=>{m(),p[Dt]=void 0,delete u.delayedLeave},u.delayedLeave=b})}return i}}},ep=Qf;function ql(e,t){const{leavingVNodes:r}=e;let o=r.get(t.type);return o||(o=Object.create(null),r.set(t.type,o)),o}function no(e,t,r,o,n){const{appear:i,mode:s,persisted:a=!1,onBeforeEnter:c,onEnter:u,onAfterEnter:l,onEnterCancelled:d,onBeforeLeave:f,onLeave:p,onAfterLeave:m,onLeaveCancelled:b,onBeforeAppear:_,onAppear:R,onAfterAppear:y,onAppearCancelled:k}=t,M=String(e.key),U=ql(r,e),B=(E,V)=>{E&&Qe(E,o,9,V)},N=(E,V)=>{const K=V[1];B(E,V),G(E)?E.every(A=>A.length<=1)&&K():E.length<=1&&K()},F={mode:s,persisted:a,beforeEnter(E){let V=c;if(!r.isMounted)if(i)V=_||c;else return;E[Dt]&&E[Dt](!0);const K=U[M];K&&tr(e,K)&&K.el[Dt]&&K.el[Dt](),B(V,[E])},enter(E){let V=u,K=l,A=d;if(!r.isMounted)if(i)V=R||u,K=y||l,A=k||d;else return;let ee=!1;const me=E[Bo]=Se=>{ee||(ee=!0,Se?B(A,[E]):B(K,[E]),F.delayedLeave&&F.delayedLeave(),E[Bo]=void 0)};V?N(V,[E,me]):me()},leave(E,V){const K=String(e.key);if(E[Bo]&&E[Bo](!0),r.isUnmounting)return V();B(f,[E]);let A=!1;const ee=E[Dt]=me=>{A||(A=!0,V(),me?B(b,[E]):B(m,[E]),E[Dt]=void 0,U[K]===e&&delete U[K])};U[K]=e,p?N(p,[E,ee]):ee()},clone(E){const V=no(E,t,r,o,n);return n&&n(V),V}};return F}function zn(e){if(Sn(e))return e=Wt(e),e.children=null,e}function Gs(e){if(!Sn(e))return e;const{shapeFlag:t,children:r}=e;if(r){if(t&16)return r[0];if(t&32&&q(r.default))return r.default()}}function xr(e,t){e.shapeFlag&6&&e.component?xr(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function as(e,t=!1,r){let o=[],n=0;for(let i=0;i<e.length;i++){let s=e[i];const a=r==null?s.key:String(r)+String(s.key!=null?s.key:i);s.type===Le?(s.patchFlag&128&&n++,o=o.concat(as(s.children,t,a))):(t||s.type!==Ne)&&o.push(a!=null?Wt(s,{key:a}):s)}if(n>1)for(let i=0;i<o.length;i++)o[i].patchFlag=-2;return o}/*! #__NO_SIDE_EFFECTS__ */function cs(e,t){return q(e)?we({name:e.name},t,{setup:e}):e}const Vr=e=>!!e.type.__asyncLoader,Sn=e=>e.type.__isKeepAlive;function tp(e,t){Jl(e,"a",t)}function rp(e,t){Jl(e,"da",t)}function Jl(e,t,r=Ee){const o=e.__wdc||(e.__wdc=()=>{let n=r;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(xn(t,o,r),r){let n=r.parent;for(;n&&n.parent;)Sn(n.parent.vnode)&&op(o,t,r,n),n=n.parent}}function op(e,t,r,o){const n=xn(t,e,o,!0);Xl(()=>{Ki(o[t],n)},r)}function xn(e,t,r=Ee,o=!1){if(r){const n=r[e]||(r[e]=[]),i=t.__weh||(t.__weh=(...s)=>{qt();const a=ko(r),c=Qe(t,r,e,s);return a(),Jt(),c});return o?n.unshift(i):n.push(i),i}}const _t=e=>(t,r=Ee)=>{(!wn||e==="sp")&&xn(e,(...o)=>t(...o),r)},np=_t("bm"),ls=_t("m"),ip=_t("bu"),Yl=_t("u"),Zl=_t("bum"),Xl=_t("um"),sp=_t("sp"),ap=_t("rtg"),cp=_t("rtc");function lp(e,t=Ee){xn("ec",e,t)}const us="components",up="directives";function gi(e,t){return ds(us,e,!0,t)||e}const Ql=Symbol.for("v-ndc");function Nr(e){return ke(e)?ds(us,e,!1)||e:e||Ql}function dp(e){return ds(up,e)}function ds(e,t,r=!0,o=!1){const n=Te||Ee;if(n){const i=n.type;if(e===us){const a=og(i,!1);if(a&&(a===t||a===ct(t)||a===bn(ct(t))))return i}const s=Ks(n[e]||i[e],t)||Ks(n.appContext[e],t);return!s&&o?i:s}}function Ks(e,t){return e&&(e[t]||e[ct(t)]||e[bn(ct(t))])}function eu(e,t,r,o){let n;const i=r;if(G(e)||ke(e)){n=new Array(e.length);for(let s=0,a=e.length;s<a;s++)n[s]=t(e[s],s,void 0,i)}else if(typeof e=="number"){n=new Array(e);for(let s=0;s<e;s++)n[s]=t(s+1,s,void 0,i)}else if(he(e))if(e[Symbol.iterator])n=Array.from(e,(s,a)=>t(s,a,void 0,i));else{const s=Object.keys(e);n=new Array(s.length);for(let a=0,c=s.length;a<c;a++){const u=s[a];n[a]=t(e[u],u,a,i)}}else n=[];return n}function oC(e,t){for(let r=0;r<t.length;r++){const o=t[r];if(G(o))for(let n=0;n<o.length;n++)e[o[n].name]=o[n].fn;else o&&(e[o.name]=o.key?(...n)=>{const i=o.fn(...n);return i&&(i.key=o.key),i}:o.fn)}return e}function rn(e,t,r={},o,n){if(Te.isCE||Te.parent&&Vr(Te.parent)&&Te.parent.isCE)return t!=="default"&&(r.name=t),xe("slot",r,o&&o());let i=e[t];i&&i._c&&(i._d=!1),ce();const s=i&&tu(i(r)),a=st(Le,{key:(r.key||s&&s.key||`_${t}`)+(!s&&o?"_fb":"")},s||(o?o():[]),s&&e._===1?64:-2);return a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function tu(e){return e.some(t=>nn(t)?!(t.type===Ne||t.type===Le&&!tu(t.children)):!0)?e:null}const hi=e=>e?ku(e)?Pn(e):hi(e.parent):null,Wr=we(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>hi(e.parent),$root:e=>hi(e.root),$emit:e=>e.emit,$options:e=>fs(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,ss(e.update)}),$nextTick:e=>e.n||(e.n=kn.bind(e.proxy)),$watch:e=>jp.bind(e)}),Un=(e,t)=>e!==ve&&!e.__isScriptSetup&&ie(e,t),fp={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:r,setupState:o,data:n,props:i,accessCache:s,type:a,appContext:c}=e;let u;if(t[0]!=="$"){const p=s[t];if(p!==void 0)switch(p){case 1:return o[t];case 2:return n[t];case 4:return r[t];case 3:return i[t]}else{if(Un(o,t))return s[t]=1,o[t];if(n!==ve&&ie(n,t))return s[t]=2,n[t];if((u=e.propsOptions[0])&&ie(u,t))return s[t]=3,i[t];if(r!==ve&&ie(r,t))return s[t]=4,r[t];mi&&(s[t]=0)}}const l=Wr[t];let d,f;if(l)return t==="$attrs"&&Ue(e.attrs,"get",""),l(e);if((d=a.__cssModules)&&(d=d[t]))return d;if(r!==ve&&ie(r,t))return s[t]=4,r[t];if(f=c.config.globalProperties,ie(f,t))return f[t]},set({_:e},t,r){const{data:o,setupState:n,ctx:i}=e;return Un(n,t)?(n[t]=r,!0):o!==ve&&ie(o,t)?(o[t]=r,!0):ie(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=r,!0)},has({_:{data:e,setupState:t,accessCache:r,ctx:o,appContext:n,propsOptions:i}},s){let a;return!!r[s]||e!==ve&&ie(e,s)||Un(t,s)||(a=i[0])&&ie(a,s)||ie(o,s)||ie(Wr,s)||ie(n.config.globalProperties,s)},defineProperty(e,t,r){return r.get!=null?e._.accessCache[t]=0:ie(r,"value")&&this.set(e,t,r.value,null),Reflect.defineProperty(e,t,r)}};function qs(e){return G(e)?e.reduce((t,r)=>(t[r]=null,t),{}):e}let mi=!0;function pp(e){const t=fs(e),r=e.proxy,o=e.ctx;mi=!1,t.beforeCreate&&Js(t.beforeCreate,e,"bc");const{data:n,computed:i,methods:s,watch:a,provide:c,inject:u,created:l,beforeMount:d,mounted:f,beforeUpdate:p,updated:m,activated:b,deactivated:_,beforeDestroy:R,beforeUnmount:y,destroyed:k,unmounted:M,render:U,renderTracked:B,renderTriggered:N,errorCaptured:F,serverPrefetch:E,expose:V,inheritAttrs:K,components:A,directives:ee,filters:me}=t;if(u&&gp(u,o,null),s)for(const ne in s){const te=s[ne];q(te)&&(o[ne]=te.bind(r))}if(n){const ne=n.call(r,r);he(ne)&&(e.data=Pr(ne))}if(mi=!0,i)for(const ne in i){const te=i[ne],He=q(te)?te.bind(r,r):q(te.get)?te.get.bind(r,r):Xe,De=!q(te)&&q(te.set)?te.set.bind(r):Xe,$e=Ze({get:He,set:De});Object.defineProperty(o,ne,{enumerable:!0,configurable:!0,get:()=>$e.value,set:Oe=>$e.value=Oe})}if(a)for(const ne in a)ru(a[ne],o,r,ne);if(c){const ne=q(c)?c.call(r):c;Reflect.ownKeys(ne).forEach(te=>{Vo(te,ne[te])})}l&&Js(l,e,"c");function X(ne,te){G(te)?te.forEach(He=>ne(He.bind(r))):te&&ne(te.bind(r))}if(X(np,d),X(ls,f),X(ip,p),X(Yl,m),X(tp,b),X(rp,_),X(lp,F),X(cp,B),X(ap,N),X(Zl,y),X(Xl,M),X(sp,E),G(V))if(V.length){const ne=e.exposed||(e.exposed={});V.forEach(te=>{Object.defineProperty(ne,te,{get:()=>r[te],set:He=>r[te]=He})})}else e.exposed||(e.exposed={});U&&e.render===Xe&&(e.render=U),K!=null&&(e.inheritAttrs=K),A&&(e.components=A),ee&&(e.directives=ee)}function gp(e,t,r=Xe){G(e)&&(e=bi(e));for(const o in e){const n=e[o];let i;he(n)?"default"in n?i=Ke(n.from||o,n.default,!0):i=Ke(n.from||o):i=Ke(n),_e(i)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>i.value,set:s=>i.value=s}):t[o]=i}}function Js(e,t,r){Qe(G(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,r)}function ru(e,t,r,o){const n=o.includes(".")?bu(r,o):()=>r[o];if(ke(e)){const i=t[e];q(i)&&mt(n,i)}else if(q(e))mt(n,e.bind(r));else if(he(e))if(G(e))e.forEach(i=>ru(i,t,r,o));else{const i=q(e.handler)?e.handler.bind(r):t[e.handler];q(i)&&mt(n,i,e)}}function fs(e){const t=e.type,{mixins:r,extends:o}=t,{mixins:n,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let c;return a?c=a:!n.length&&!r&&!o?c=t:(c={},n.length&&n.forEach(u=>on(c,u,s,!0)),on(c,t,s)),he(t)&&i.set(t,c),c}function on(e,t,r,o=!1){const{mixins:n,extends:i}=t;i&&on(e,i,r,!0),n&&n.forEach(s=>on(e,s,r,!0));for(const s in t)if(!(o&&s==="expose")){const a=hp[s]||r&&r[s];e[s]=a?a(e[s],t[s]):t[s]}return e}const hp={data:Ys,props:Zs,emits:Zs,methods:Fr,computed:Fr,beforeCreate:je,created:je,beforeMount:je,mounted:je,beforeUpdate:je,updated:je,beforeDestroy:je,beforeUnmount:je,destroyed:je,unmounted:je,activated:je,deactivated:je,errorCaptured:je,serverPrefetch:je,components:Fr,directives:Fr,watch:bp,provide:Ys,inject:mp};function Ys(e,t){return t?e?function(){return we(q(e)?e.call(this,this):e,q(t)?t.call(this,this):t)}:t:e}function mp(e,t){return Fr(bi(e),bi(t))}function bi(e){if(G(e)){const t={};for(let r=0;r<e.length;r++)t[e[r]]=e[r];return t}return e}function je(e,t){return e?[...new Set([].concat(e,t))]:t}function Fr(e,t){return e?we(Object.create(null),e,t):t}function Zs(e,t){return e?G(e)&&G(t)?[...new Set([...e,...t])]:we(Object.create(null),qs(e),qs(t??{})):t}function bp(e,t){if(!e)return t;if(!t)return e;const r=we(Object.create(null),e);for(const o in t)r[o]=je(e[o],t[o]);return r}function ou(){return{app:null,config:{isNativeTag:rf,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let vp=0;function yp(e,t){return function(o,n=null){q(o)||(o=we({},o)),n!=null&&!he(n)&&(n=null);const i=ou(),s=new WeakSet;let a=!1;const c=i.app={_uid:vp++,_component:o,_props:n,_container:null,_context:i,_instance:null,version:ig,get config(){return i.config},set config(u){},use(u,...l){return s.has(u)||(u&&q(u.install)?(s.add(u),u.install(c,...l)):q(u)&&(s.add(u),u(c,...l))),c},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),c},component(u,l){return l?(i.components[u]=l,c):i.components[u]},directive(u,l){return l?(i.directives[u]=l,c):i.directives[u]},mount(u,l,d){if(!a){const f=xe(o,n);return f.appContext=i,d===!0?d="svg":d===!1&&(d=void 0),l&&t?t(f,u):e(f,u,d),a=!0,c._container=u,u.__vue_app__=c,Pn(f.component)}},unmount(){a&&(e(null,c._container),delete c._container.__vue_app__)},provide(u,l){return i.provides[u]=l,c},runWithContext(u){const l=kr;kr=c;try{return u()}finally{kr=l}}};return c}}let kr=null;function Vo(e,t){if(Ee){let r=Ee.provides;const o=Ee.parent&&Ee.parent.provides;o===r&&(r=Ee.provides=Object.create(o)),r[e]=t}}function Ke(e,t,r=!1){const o=Ee||Te;if(o||kr){const n=o?o.parent==null?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:kr._context.provides;if(n&&e in n)return n[e];if(arguments.length>1)return r&&q(t)?t.call(o&&o.proxy):t}}function Cp(){return!!(Ee||Te||kr)}const nu={},iu=()=>Object.create(nu),su=e=>Object.getPrototypeOf(e)===nu;function kp(e,t,r,o=!1){const n={},i=iu();e.propsDefaults=Object.create(null),au(e,t,n,i);for(const s in e.propsOptions[0])s in n||(n[s]=void 0);r?e.props=o?n:Ll(n):e.type.props?e.props=n:e.props=i,e.attrs=i}function Sp(e,t,r,o){const{props:n,attrs:i,vnode:{patchFlag:s}}=e,a=oe(n),[c]=e.propsOptions;let u=!1;if((o||s>0)&&!(s&16)){if(s&8){const l=e.vnode.dynamicProps;for(let d=0;d<l.length;d++){let f=l[d];if(_n(e.emitsOptions,f))continue;const p=t[f];if(c)if(ie(i,f))p!==i[f]&&(i[f]=p,u=!0);else{const m=ct(f);n[m]=vi(c,a,m,p,e,!1)}else p!==i[f]&&(i[f]=p,u=!0)}}}else{au(e,t,n,i)&&(u=!0);let l;for(const d in a)(!t||!ie(t,d)&&((l=dr(d))===d||!ie(t,l)))&&(c?r&&(r[d]!==void 0||r[l]!==void 0)&&(n[d]=vi(c,a,d,void 0,e,!0)):delete n[d]);if(i!==a)for(const d in i)(!t||!ie(t,d))&&(delete i[d],u=!0)}u&&xt(e.attrs,"set","")}function au(e,t,r,o){const[n,i]=e.propsOptions;let s=!1,a;if(t)for(let c in t){if(Ur(c))continue;const u=t[c];let l;n&&ie(n,l=ct(c))?!i||!i.includes(l)?r[l]=u:(a||(a={}))[l]=u:_n(e.emitsOptions,c)||(!(c in o)||u!==o[c])&&(o[c]=u,s=!0)}if(i){const c=oe(r),u=a||ve;for(let l=0;l<i.length;l++){const d=i[l];r[d]=vi(n,c,d,u[d],e,!ie(u,d))}}return s}function vi(e,t,r,o,n,i){const s=e[r];if(s!=null){const a=ie(s,"default");if(a&&o===void 0){const c=s.default;if(s.type!==Function&&!s.skipFactory&&q(c)){const{propsDefaults:u}=n;if(r in u)o=u[r];else{const l=ko(n);o=u[r]=c.call(null,t),l()}}else o=c}s[0]&&(i&&!a?o=!1:s[1]&&(o===""||o===dr(r))&&(o=!0))}return o}const xp=new WeakMap;function cu(e,t,r=!1){const o=r?xp:t.propsCache,n=o.get(e);if(n)return n;const i=e.props,s={},a=[];let c=!1;if(!q(e)){const l=d=>{c=!0;const[f,p]=cu(d,t,!0);we(s,f),p&&a.push(...p)};!r&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}if(!i&&!c)return he(e)&&o.set(e,vr),vr;if(G(i))for(let l=0;l<i.length;l++){const d=ct(i[l]);Xs(d)&&(s[d]=ve)}else if(i)for(const l in i){const d=ct(l);if(Xs(d)){const f=i[l],p=s[d]=G(f)||q(f)?{type:f}:we({},f);if(p){const m=ta(Boolean,p.type),b=ta(String,p.type);p[0]=m>-1,p[1]=b<0||m<b,(m>-1||ie(p,"default"))&&a.push(d)}}}const u=[s,a];return he(e)&&o.set(e,u),u}function Xs(e){return e[0]!=="$"&&!Ur(e)}function Qs(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function ea(e,t){return Qs(e)===Qs(t)}function ta(e,t){return G(t)?t.findIndex(r=>ea(r,e)):q(t)&&ea(t,e)?0:-1}const lu=e=>e[0]==="_"||e==="$stable",ps=e=>G(e)?e.map(gt):[gt(e)],_p=(e,t,r)=>{if(t._n)return t;const o=tn((...n)=>ps(t(...n)),r);return o._c=!1,o},uu=(e,t,r)=>{const o=e._ctx;for(const n in e){if(lu(n))continue;const i=e[n];if(q(i))t[n]=_p(n,i,o);else if(i!=null){const s=ps(i);t[n]=()=>s}}},du=(e,t)=>{const r=ps(t);e.slots.default=()=>r},fu=(e,t,r)=>{for(const o in t)(r||o!=="_")&&(e[o]=t[o])},Rp=(e,t,r)=>{const o=e.slots=iu();if(e.vnode.shapeFlag&32){const n=t._;n?(fu(o,t,r),r&&hl(o,"_",n,!0)):uu(t,o)}else t&&du(e,t)},Tp=(e,t,r)=>{const{vnode:o,slots:n}=e;let i=!0,s=ve;if(o.shapeFlag&32){const a=t._;a?r&&a===1?i=!1:fu(n,t,r):(i=!t.$stable,uu(t,n)),s=t}else t&&(du(e,t),s={default:1});if(i)for(const a in n)!lu(a)&&s[a]==null&&delete n[a]};function yi(e,t,r,o,n=!1){if(G(e)){e.forEach((f,p)=>yi(f,t&&(G(t)?t[p]:t),r,o,n));return}if(Vr(o)&&!n)return;const i=o.shapeFlag&4?Pn(o.component):o.el,s=n?null:i,{i:a,r:c}=e,u=t&&t.r,l=a.refs===ve?a.refs={}:a.refs,d=a.setupState;if(u!=null&&u!==c&&(ke(u)?(l[u]=null,ie(d,u)&&(d[u]=null)):_e(u)&&(u.value=null)),q(c))Ut(c,a,12,[s,l]);else{const f=ke(c),p=_e(c);if(f||p){const m=()=>{if(e.f){const b=f?ie(d,c)?d[c]:l[c]:c.value;n?G(b)&&Ki(b,i):G(b)?b.includes(i)||b.push(i):f?(l[c]=[i],ie(d,c)&&(d[c]=l[c])):(c.value=[i],e.k&&(l[e.k]=c.value))}else f?(l[c]=s,ie(d,c)&&(d[c]=s)):p&&(c.value=s,e.k&&(l[e.k]=s))};s?(m.id=-1,Me(m,r)):m()}}}const pu=Symbol("_vte"),wp=e=>e.__isTeleport,Gr=e=>e&&(e.disabled||e.disabled===""),ra=e=>typeof SVGElement<"u"&&e instanceof SVGElement,oa=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Ci=(e,t)=>{const r=e&&e.to;return ke(r)?t?t(r):null:r},Pp={name:"Teleport",__isTeleport:!0,process(e,t,r,o,n,i,s,a,c,u){const{mc:l,pc:d,pbc:f,o:{insert:p,querySelector:m,createText:b,createComment:_}}=u,R=Gr(t.props);let{shapeFlag:y,children:k,dynamicChildren:M}=t;if(e==null){const U=t.el=b(""),B=t.anchor=b(""),N=t.target=Ci(t.props,m),F=t.targetStart=b(""),E=t.targetAnchor=b("");p(U,r,o),p(B,r,o),F[pu]=E,N&&(p(F,N),p(E,N),s==="svg"||ra(N)?s="svg":(s==="mathml"||oa(N))&&(s="mathml"));const V=(K,A)=>{y&16&&l(k,K,A,n,i,s,a,c)};R?V(r,B):N&&V(N,E)}else{t.el=e.el,t.targetStart=e.targetStart;const U=t.anchor=e.anchor,B=t.target=e.target,N=t.targetAnchor=e.targetAnchor,F=Gr(e.props),E=F?r:B,V=F?U:N;if(s==="svg"||ra(B)?s="svg":(s==="mathml"||oa(B))&&(s="mathml"),M?(f(e.dynamicChildren,M,E,n,i,s,a),gs(e,t,!0)):c||d(e,t,E,V,n,i,s,a,!1),R)F?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Ao(t,r,U,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const K=t.target=Ci(t.props,m);K&&Ao(t,K,null,u,0)}else F&&Ao(t,B,N,u,1)}gu(t)},remove(e,t,r,{um:o,o:{remove:n}},i){const{shapeFlag:s,children:a,anchor:c,targetStart:u,targetAnchor:l,target:d,props:f}=e;if(d&&(n(u),n(l)),i&&n(c),s&16){const p=i||!Gr(f);for(let m=0;m<a.length;m++){const b=a[m];o(b,t,r,p,!!b.dynamicChildren)}}},move:Ao,hydrate:Ep};function Ao(e,t,r,{o:{insert:o},m:n},i=2){i===0&&o(e.targetAnchor,t,r);const{el:s,anchor:a,shapeFlag:c,children:u,props:l}=e,d=i===2;if(d&&o(s,t,r),(!d||Gr(l))&&c&16)for(let f=0;f<u.length;f++)n(u[f],t,r,2);d&&o(a,t,r)}function Ep(e,t,r,o,n,i,{o:{nextSibling:s,parentNode:a,querySelector:c}},u){const l=t.target=Ci(t.props,c);if(l){const d=l._lpa||l.firstChild;if(t.shapeFlag&16)if(Gr(t.props))t.anchor=u(s(e),t,a(e),r,o,n,i),t.targetAnchor=d;else{t.anchor=s(e);let f=d;for(;f;)if(f=s(f),f&&f.nodeType===8&&f.data==="teleport anchor"){t.targetAnchor=f,l._lpa=t.targetAnchor&&s(t.targetAnchor);break}u(d,t,l,r,o,n,i)}gu(t)}return t.anchor&&s(t.anchor)}const Op=Pp;function gu(e){const t=e.ctx;if(t&&t.ut){let r=e.children[0].el;for(;r&&r!==e.targetAnchor;)r.nodeType===1&&r.setAttribute("data-v-owner",t.uid),r=r.nextSibling;t.ut()}}const Me=Wp;function $p(e){return Bp(e)}function Bp(e,t){const r=ml();r.__VUE__=!0;const{insert:o,remove:n,patchProp:i,createElement:s,createText:a,createComment:c,setText:u,setElementText:l,parentNode:d,nextSibling:f,setScopeId:p=Xe,insertStaticContent:m}=e,b=(g,h,v,T=null,S=null,P=null,L=void 0,O=null,$=!!h.dynamicChildren)=>{if(g===h)return;g&&!tr(g,h)&&(T=x(g),Oe(g,S,P,!0),g=null),h.patchFlag===-2&&($=!1,h.dynamicChildren=null);const{type:w,ref:j,shapeFlag:W}=h;switch(w){case Rn:_(g,h,v,T);break;case Ne:R(g,h,v,T);break;case Wo:g==null&&y(h,v,T,L);break;case Le:A(g,h,v,T,S,P,L,O,$);break;default:W&1?U(g,h,v,T,S,P,L,O,$):W&6?ee(g,h,v,T,S,P,L,O,$):(W&64||W&128)&&w.process(g,h,v,T,S,P,L,O,$,z)}j!=null&&S&&yi(j,g&&g.ref,P,h||g,!h)},_=(g,h,v,T)=>{if(g==null)o(h.el=a(h.children),v,T);else{const S=h.el=g.el;h.children!==g.children&&u(S,h.children)}},R=(g,h,v,T)=>{g==null?o(h.el=c(h.children||""),v,T):h.el=g.el},y=(g,h,v,T)=>{[g.el,g.anchor]=m(g.children,h,v,T,g.el,g.anchor)},k=({el:g,anchor:h},v,T)=>{let S;for(;g&&g!==h;)S=f(g),o(g,v,T),g=S;o(h,v,T)},M=({el:g,anchor:h})=>{let v;for(;g&&g!==h;)v=f(g),n(g),g=v;n(h)},U=(g,h,v,T,S,P,L,O,$)=>{h.type==="svg"?L="svg":h.type==="math"&&(L="mathml"),g==null?B(h,v,T,S,P,L,O,$):E(g,h,S,P,L,O,$)},B=(g,h,v,T,S,P,L,O)=>{let $,w;const{props:j,shapeFlag:W,transition:H,dirs:J}=g;if($=g.el=s(g.type,P,j&&j.is,j),W&8?l($,g.children):W&16&&F(g.children,$,null,T,S,Hn(g,P),L,O),J&&Yt(g,null,T,"created"),N($,g,g.scopeId,L,T),j){for(const be in j)be!=="value"&&!Ur(be)&&i($,be,null,j[be],P,T);"value"in j&&i($,"value",null,j.value,P),(w=j.onVnodeBeforeMount)&&pt(w,T,g)}J&&Yt(g,null,T,"beforeMount");const Q=Ap(S,H);Q&&H.beforeEnter($),o($,h,v),((w=j&&j.onVnodeMounted)||Q||J)&&Me(()=>{w&&pt(w,T,g),Q&&H.enter($),J&&Yt(g,null,T,"mounted")},S)},N=(g,h,v,T,S)=>{if(v&&p(g,v),T)for(let P=0;P<T.length;P++)p(g,T[P]);if(S){let P=S.subTree;if(h===P){const L=S.vnode;N(g,L,L.scopeId,L.slotScopeIds,S.parent)}}},F=(g,h,v,T,S,P,L,O,$=0)=>{for(let w=$;w<g.length;w++){const j=g[w]=O?jt(g[w]):gt(g[w]);b(null,j,h,v,T,S,P,L,O)}},E=(g,h,v,T,S,P,L)=>{const O=h.el=g.el;let{patchFlag:$,dynamicChildren:w,dirs:j}=h;$|=g.patchFlag&16;const W=g.props||ve,H=h.props||ve;let J;if(v&&Zt(v,!1),(J=H.onVnodeBeforeUpdate)&&pt(J,v,h,g),j&&Yt(h,g,v,"beforeUpdate"),v&&Zt(v,!0),(W.innerHTML&&H.innerHTML==null||W.textContent&&H.textContent==null)&&l(O,""),w?V(g.dynamicChildren,w,O,v,T,Hn(h,S),P):L||te(g,h,O,null,v,T,Hn(h,S),P,!1),$>0){if($&16)K(O,W,H,v,S);else if($&2&&W.class!==H.class&&i(O,"class",null,H.class,S),$&4&&i(O,"style",W.style,H.style,S),$&8){const Q=h.dynamicProps;for(let be=0;be<Q.length;be++){const ae=Q[be],Pe=W[ae],tt=H[ae];(tt!==Pe||ae==="value")&&i(O,ae,Pe,tt,S,v)}}$&1&&g.children!==h.children&&l(O,h.children)}else!L&&w==null&&K(O,W,H,v,S);((J=H.onVnodeUpdated)||j)&&Me(()=>{J&&pt(J,v,h,g),j&&Yt(h,g,v,"updated")},T)},V=(g,h,v,T,S,P,L)=>{for(let O=0;O<h.length;O++){const $=g[O],w=h[O],j=$.el&&($.type===Le||!tr($,w)||$.shapeFlag&70)?d($.el):v;b($,w,j,null,T,S,P,L,!0)}},K=(g,h,v,T,S)=>{if(h!==v){if(h!==ve)for(const P in h)!Ur(P)&&!(P in v)&&i(g,P,h[P],null,S,T);for(const P in v){if(Ur(P))continue;const L=v[P],O=h[P];L!==O&&P!=="value"&&i(g,P,O,L,S,T)}"value"in v&&i(g,"value",h.value,v.value,S)}},A=(g,h,v,T,S,P,L,O,$)=>{const w=h.el=g?g.el:a(""),j=h.anchor=g?g.anchor:a("");let{patchFlag:W,dynamicChildren:H,slotScopeIds:J}=h;J&&(O=O?O.concat(J):J),g==null?(o(w,v,T),o(j,v,T),F(h.children||[],v,j,S,P,L,O,$)):W>0&&W&64&&H&&g.dynamicChildren?(V(g.dynamicChildren,H,v,S,P,L,O),(h.key!=null||S&&h===S.subTree)&&gs(g,h,!0)):te(g,h,v,j,S,P,L,O,$)},ee=(g,h,v,T,S,P,L,O,$)=>{h.slotScopeIds=O,g==null?h.shapeFlag&512?S.ctx.activate(h,v,T,L,$):me(h,v,T,S,P,L,$):Se(g,h,$)},me=(g,h,v,T,S,P,L)=>{const O=g.component=Xp(g,T,S);if(Sn(g)&&(O.ctx.renderer=z),Qp(O,!1,L),O.asyncDep){if(S&&S.registerDep(O,X,L),!g.el){const $=O.subTree=xe(Ne);R(null,$,h,v)}}else X(O,g,h,v,S,P,L)},Se=(g,h,v)=>{const T=h.component=g.component;if(Up(g,h,v))if(T.asyncDep&&!T.asyncResolved){ne(T,h,v);return}else T.next=h,Jf(T.update),T.effect.dirty=!0,T.update();else h.el=g.el,T.vnode=h},X=(g,h,v,T,S,P,L)=>{const O=()=>{if(g.isMounted){let{next:j,bu:W,u:H,parent:J,vnode:Q}=g;{const fr=hu(g);if(fr){j&&(j.el=Q.el,ne(g,j,L)),fr.asyncDep.then(()=>{g.isUnmounted||O()});return}}let be=j,ae;Zt(g,!1),j?(j.el=Q.el,ne(g,j,L)):j=Q,W&&Nn(W),(ae=j.props&&j.props.onVnodeBeforeUpdate)&&pt(ae,J,j,Q),Zt(g,!0);const Pe=Vn(g),tt=g.subTree;g.subTree=Pe,b(tt,Pe,d(tt.el),x(tt),g,S,P),j.el=Pe.el,be===null&&Hp(g,Pe.el),H&&Me(H,S),(ae=j.props&&j.props.onVnodeUpdated)&&Me(()=>pt(ae,J,j,Q),S)}else{let j;const{el:W,props:H}=h,{bm:J,m:Q,parent:be}=g,ae=Vr(h);if(Zt(g,!1),J&&Nn(J),!ae&&(j=H&&H.onVnodeBeforeMount)&&pt(j,be,h),Zt(g,!0),W&&ye){const Pe=()=>{g.subTree=Vn(g),ye(W,g.subTree,g,S,null)};ae?h.type.__asyncLoader().then(()=>!g.isUnmounted&&Pe()):Pe()}else{const Pe=g.subTree=Vn(g);b(null,Pe,v,T,g,S,P),h.el=Pe.el}if(Q&&Me(Q,S),!ae&&(j=H&&H.onVnodeMounted)){const Pe=h;Me(()=>pt(j,be,Pe),S)}(h.shapeFlag&256||be&&Vr(be.vnode)&&be.vnode.shapeFlag&256)&&g.a&&Me(g.a,S),g.isMounted=!0,h=v=T=null}},$=g.effect=new Ji(O,Xe,()=>ss(w),g.scope),w=g.update=()=>{$.dirty&&$.run()};w.i=g,w.id=g.uid,Zt(g,!0),w()},ne=(g,h,v)=>{h.component=g;const T=g.vnode.props;g.vnode=h,g.next=null,Sp(g,h.props,T,v),Tp(g,h.children,v),qt(),Ws(g),Jt()},te=(g,h,v,T,S,P,L,O,$=!1)=>{const w=g&&g.children,j=g?g.shapeFlag:0,W=h.children,{patchFlag:H,shapeFlag:J}=h;if(H>0){if(H&128){De(w,W,v,T,S,P,L,O,$);return}else if(H&256){He(w,W,v,T,S,P,L,O,$);return}}J&8?(j&16&&qe(w,S,P),W!==w&&l(v,W)):j&16?J&16?De(w,W,v,T,S,P,L,O,$):qe(w,S,P,!0):(j&8&&l(v,""),J&16&&F(W,v,T,S,P,L,O,$))},He=(g,h,v,T,S,P,L,O,$)=>{g=g||vr,h=h||vr;const w=g.length,j=h.length,W=Math.min(w,j);let H;for(H=0;H<W;H++){const J=h[H]=$?jt(h[H]):gt(h[H]);b(g[H],J,v,null,S,P,L,O,$)}w>j?qe(g,S,P,!0,!1,W):F(h,v,T,S,P,L,O,$,W)},De=(g,h,v,T,S,P,L,O,$)=>{let w=0;const j=h.length;let W=g.length-1,H=j-1;for(;w<=W&&w<=H;){const J=g[w],Q=h[w]=$?jt(h[w]):gt(h[w]);if(tr(J,Q))b(J,Q,v,null,S,P,L,O,$);else break;w++}for(;w<=W&&w<=H;){const J=g[W],Q=h[H]=$?jt(h[H]):gt(h[H]);if(tr(J,Q))b(J,Q,v,null,S,P,L,O,$);else break;W--,H--}if(w>W){if(w<=H){const J=H+1,Q=J<j?h[J].el:T;for(;w<=H;)b(null,h[w]=$?jt(h[w]):gt(h[w]),v,Q,S,P,L,O,$),w++}}else if(w>H)for(;w<=W;)Oe(g[w],S,P,!0),w++;else{const J=w,Q=w,be=new Map;for(w=Q;w<=H;w++){const Ve=h[w]=$?jt(h[w]):gt(h[w]);Ve.key!=null&&be.set(Ve.key,w)}let ae,Pe=0;const tt=H-Q+1;let fr=!1,Ps=0;const Ar=new Array(tt);for(w=0;w<tt;w++)Ar[w]=0;for(w=J;w<=W;w++){const Ve=g[w];if(Pe>=tt){Oe(Ve,S,P,!0);continue}let ft;if(Ve.key!=null)ft=be.get(Ve.key);else for(ae=Q;ae<=H;ae++)if(Ar[ae-Q]===0&&tr(Ve,h[ae])){ft=ae;break}ft===void 0?Oe(Ve,S,P,!0):(Ar[ft-Q]=w+1,ft>=Ps?Ps=ft:fr=!0,b(Ve,h[ft],v,null,S,P,L,O,$),Pe++)}const Es=fr?Lp(Ar):vr;for(ae=Es.length-1,w=tt-1;w>=0;w--){const Ve=Q+w,ft=h[Ve],Os=Ve+1<j?h[Ve+1].el:T;Ar[w]===0?b(null,ft,v,Os,S,P,L,O,$):fr&&(ae<0||w!==Es[ae]?$e(ft,v,Os,2):ae--)}}},$e=(g,h,v,T,S=null)=>{const{el:P,type:L,transition:O,children:$,shapeFlag:w}=g;if(w&6){$e(g.component.subTree,h,v,T);return}if(w&128){g.suspense.move(h,v,T);return}if(w&64){L.move(g,h,v,z);return}if(L===Le){o(P,h,v);for(let W=0;W<$.length;W++)$e($[W],h,v,T);o(g.anchor,h,v);return}if(L===Wo){k(g,h,v);return}if(T!==2&&w&1&&O)if(T===0)O.beforeEnter(P),o(P,h,v),Me(()=>O.enter(P),S);else{const{leave:W,delayLeave:H,afterLeave:J}=O,Q=()=>o(P,h,v),be=()=>{W(P,()=>{Q(),J&&J()})};H?H(P,Q,be):be()}else o(P,h,v)},Oe=(g,h,v,T=!1,S=!1)=>{const{type:P,props:L,ref:O,children:$,dynamicChildren:w,shapeFlag:j,patchFlag:W,dirs:H,cacheIndex:J}=g;if(W===-2&&(S=!1),O!=null&&yi(O,null,v,g,!0),J!=null&&(h.renderCache[J]=void 0),j&256){h.ctx.deactivate(g);return}const Q=j&1&&H,be=!Vr(g);let ae;if(be&&(ae=L&&L.onVnodeBeforeUnmount)&&pt(ae,h,g),j&6)To(g.component,v,T);else{if(j&128){g.suspense.unmount(v,T);return}Q&&Yt(g,null,h,"beforeUnmount"),j&64?g.type.remove(g,h,v,z,T):w&&!w.hasOnce&&(P!==Le||W>0&&W&64)?qe(w,h,v,!1,!0):(P===Le&&W&384||!S&&j&16)&&qe($,h,v),T&&Rt(g)}(be&&(ae=L&&L.onVnodeUnmounted)||Q)&&Me(()=>{ae&&pt(ae,h,g),Q&&Yt(g,null,h,"unmounted")},v)},Rt=g=>{const{type:h,el:v,anchor:T,transition:S}=g;if(h===Le){dt(v,T);return}if(h===Wo){M(g);return}const P=()=>{n(v),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(g.shapeFlag&1&&S&&!S.persisted){const{leave:L,delayLeave:O}=S,$=()=>L(v,P);O?O(g.el,P,$):$()}else P()},dt=(g,h)=>{let v;for(;g!==h;)v=f(g),n(g),g=v;n(h)},To=(g,h,v)=>{const{bum:T,scope:S,update:P,subTree:L,um:O,m:$,a:w}=g;na($),na(w),T&&Nn(T),S.stop(),P&&(P.active=!1,Oe(L,g,h,v)),O&&Me(O,h),Me(()=>{g.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&g.asyncDep&&!g.asyncResolved&&g.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},qe=(g,h,v,T=!1,S=!1,P=0)=>{for(let L=P;L<g.length;L++)Oe(g[L],h,v,T,S)},x=g=>{if(g.shapeFlag&6)return x(g.component.subTree);if(g.shapeFlag&128)return g.suspense.next();const h=f(g.anchor||g.el),v=h&&h[pu];return v?f(v):h};let D=!1;const I=(g,h,v)=>{g==null?h._vnode&&Oe(h._vnode,null,null,!0):b(h._vnode||null,g,h,null,null,null,v),D||(D=!0,Ws(),Ul(),D=!1),h._vnode=g},z={p:b,um:Oe,m:$e,r:Rt,mt:me,mc:F,pc:te,pbc:V,n:x,o:e};let le,ye;return{render:I,hydrate:le,createApp:yp(I,le)}}function Hn({type:e,props:t},r){return r==="svg"&&e==="foreignObject"||r==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:r}function Zt({effect:e,update:t},r){e.allowRecurse=t.allowRecurse=r}function Ap(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function gs(e,t,r=!1){const o=e.children,n=t.children;if(G(o)&&G(n))for(let i=0;i<o.length;i++){const s=o[i];let a=n[i];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=n[i]=jt(n[i]),a.el=s.el),!r&&a.patchFlag!==-2&&gs(s,a)),a.type===Rn&&(a.el=s.el)}}function Lp(e){const t=e.slice(),r=[0];let o,n,i,s,a;const c=e.length;for(o=0;o<c;o++){const u=e[o];if(u!==0){if(n=r[r.length-1],e[n]<u){t[o]=n,r.push(o);continue}for(i=0,s=r.length-1;i<s;)a=i+s>>1,e[r[a]]<u?i=a+1:s=a;u<e[r[i]]&&(i>0&&(t[o]=r[i-1]),r[i]=o)}}for(i=r.length,s=r[i-1];i-- >0;)r[i]=s,s=t[s];return r}function hu(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:hu(t)}function na(e){if(e)for(let t=0;t<e.length;t++)e[t].active=!1}const Ip=Symbol.for("v-scx"),Dp=()=>Ke(Ip),Lo={};function mt(e,t,r){return mu(e,t,r)}function mu(e,t,{immediate:r,deep:o,flush:n,once:i,onTrack:s,onTrigger:a}=ve){if(t&&i){const B=t;t=(...N)=>{B(...N),U()}}const c=Ee,u=B=>o===!0?B:Nt(B,o===!1?1:void 0);let l,d=!1,f=!1;if(_e(e)?(l=()=>e.value,d=Sr(e)):ir(e)?(l=()=>u(e),d=!0):G(e)?(f=!0,d=e.some(B=>ir(B)||Sr(B)),l=()=>e.map(B=>{if(_e(B))return B.value;if(ir(B))return u(B);if(q(B))return Ut(B,c,2)})):q(e)?t?l=()=>Ut(e,c,2):l=()=>(p&&p(),Qe(e,c,3,[m])):l=Xe,t&&o){const B=l;l=()=>Nt(B())}let p,m=B=>{p=k.onStop=()=>{Ut(B,c,4),p=k.onStop=void 0}},b;if(wn)if(m=Xe,t?r&&Qe(t,c,3,[l(),f?[]:void 0,m]):l(),n==="sync"){const B=Dp();b=B.__watcherHandles||(B.__watcherHandles=[])}else return Xe;let _=f?new Array(e.length).fill(Lo):Lo;const R=()=>{if(!(!k.active||!k.dirty))if(t){const B=k.run();(o||d||(f?B.some((N,F)=>Vt(N,_[F])):Vt(B,_)))&&(p&&p(),Qe(t,c,3,[B,_===Lo?void 0:f&&_[0]===Lo?[]:_,m]),_=B)}else k.run()};R.allowRecurse=!!t;let y;n==="sync"?y=R:n==="post"?y=()=>Me(R,c&&c.suspense):(R.pre=!0,c&&(R.id=c.uid),y=()=>ss(R));const k=new Ji(l,Xe,y),M=Sl(),U=()=>{k.stop(),M&&Ki(M.effects,k)};return t?r?R():_=k.run():n==="post"?Me(k.run.bind(k),c&&c.suspense):k.run(),b&&b.push(U),U}function jp(e,t,r){const o=this.proxy,n=ke(e)?e.includes(".")?bu(o,e):()=>o[e]:e.bind(o,o);let i;q(t)?i=t:(i=t.handler,r=t);const s=ko(this),a=mu(n,i.bind(o),r);return s(),a}function bu(e,t){const r=t.split(".");return()=>{let o=e;for(let n=0;n<r.length&&o;n++)o=o[r[n]];return o}}function Nt(e,t=1/0,r){if(t<=0||!he(e)||e.__v_skip||(r=r||new Set,r.has(e)))return e;if(r.add(e),t--,_e(e))Nt(e.value,t,r);else if(G(e))for(let o=0;o<e.length;o++)Nt(e[o],t,r);else if(dl(e)||yr(e))e.forEach(o=>{Nt(o,t,r)});else if(gl(e)){for(const o in e)Nt(e[o],t,r);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&Nt(e[o],t,r)}return e}const Mp=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ct(t)}Modifiers`]||e[`${dr(t)}Modifiers`];function Np(e,t,...r){if(e.isUnmounted)return;const o=e.vnode.props||ve;let n=r;const i=t.startsWith("update:"),s=i&&Mp(o,t.slice(7));s&&(s.trim&&(n=r.map(l=>ke(l)?l.trim():l)),s.number&&(n=r.map(cf)));let a,c=o[a=Mn(t)]||o[a=Mn(ct(t))];!c&&i&&(c=o[a=Mn(dr(t))]),c&&Qe(c,e,6,n);const u=o[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Qe(u,e,6,n)}}function vu(e,t,r=!1){const o=t.emitsCache,n=o.get(e);if(n!==void 0)return n;const i=e.emits;let s={},a=!1;if(!q(e)){const c=u=>{const l=vu(u,t,!0);l&&(a=!0,we(s,l))};!r&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!a?(he(e)&&o.set(e,null),null):(G(i)?i.forEach(c=>s[c]=null):we(s,i),he(e)&&o.set(e,s),s)}function _n(e,t){return!e||!gn(t)?!1:(t=t.slice(2).replace(/Once$/,""),ie(e,t[0].toLowerCase()+t.slice(1))||ie(e,dr(t))||ie(e,t))}function Vn(e){const{type:t,vnode:r,proxy:o,withProxy:n,propsOptions:[i],slots:s,attrs:a,emit:c,render:u,renderCache:l,props:d,data:f,setupState:p,ctx:m,inheritAttrs:b}=e,_=en(e);let R,y;try{if(r.shapeFlag&4){const M=n||o,U=M;R=gt(u.call(U,M,l,d,p,f,m)),y=a}else{const M=t;R=gt(M.length>1?M(d,{attrs:a,slots:s,emit:c}):M(d,null)),y=t.props?a:Fp(a)}}catch(M){Kr.length=0,Cn(M,e,1),R=xe(Ne)}let k=R;if(y&&b!==!1){const M=Object.keys(y),{shapeFlag:U}=k;M.length&&U&7&&(i&&M.some(Gi)&&(y=zp(y,i)),k=Wt(k,y,!1,!0))}return r.dirs&&(k=Wt(k,null,!1,!0),k.dirs=k.dirs?k.dirs.concat(r.dirs):r.dirs),r.transition&&(k.transition=r.transition),R=k,en(_),R}const Fp=e=>{let t;for(const r in e)(r==="class"||r==="style"||gn(r))&&((t||(t={}))[r]=e[r]);return t},zp=(e,t)=>{const r={};for(const o in e)(!Gi(o)||!(o.slice(9)in t))&&(r[o]=e[o]);return r};function Up(e,t,r){const{props:o,children:n,component:i}=e,{props:s,children:a,patchFlag:c}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(r&&c>=0){if(c&1024)return!0;if(c&16)return o?ia(o,s,u):!!s;if(c&8){const l=t.dynamicProps;for(let d=0;d<l.length;d++){const f=l[d];if(s[f]!==o[f]&&!_n(u,f))return!0}}}else return(n||a)&&(!a||!a.$stable)?!0:o===s?!1:o?s?ia(o,s,u):!0:!!s;return!1}function ia(e,t,r){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let n=0;n<o.length;n++){const i=o[n];if(t[i]!==e[i]&&!_n(r,i))return!0}return!1}function Hp({vnode:e,parent:t},r){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=r,t=t.parent;else break}}const Vp=e=>e.__isSuspense;function Wp(e,t){t&&t.pendingBranch?G(e)?t.effects.push(...e):t.effects.push(e):Yf(e)}const Le=Symbol.for("v-fgt"),Rn=Symbol.for("v-txt"),Ne=Symbol.for("v-cmt"),Wo=Symbol.for("v-stc"),Kr=[];let Ge=null;function ce(e=!1){Kr.push(Ge=e?null:[])}function Gp(){Kr.pop(),Ge=Kr[Kr.length-1]||null}let io=1;function sa(e){io+=e,e<0&&Ge&&(Ge.hasOnce=!0)}function yu(e){return e.dynamicChildren=io>0?Ge||vr:null,Gp(),io>0&&Ge&&Ge.push(e),e}function Re(e,t,r,o,n,i){return yu(fe(e,t,r,o,n,i,!0))}function st(e,t,r,o,n){return yu(xe(e,t,r,o,n,!0))}function nn(e){return e?e.__v_isVNode===!0:!1}function tr(e,t){return e.type===t.type&&e.key===t.key}const Cu=({key:e})=>e??null,Go=({ref:e,ref_key:t,ref_for:r})=>(typeof e=="number"&&(e=""+e),e!=null?ke(e)||_e(e)||q(e)?{i:Te,r:e,k:t,f:!!r}:e:null);function fe(e,t=null,r=null,o=0,n=null,i=e===Le?0:1,s=!1,a=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Cu(t),ref:t&&Go(t),scopeId:Vl,slotScopeIds:null,children:r,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:Te};return a?(hs(c,r),i&128&&e.normalize(c)):r&&(c.shapeFlag|=ke(r)?8:16),io>0&&!s&&Ge&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&Ge.push(c),c}const xe=Kp;function Kp(e,t=null,r=null,o=0,n=null,i=!1){if((!e||e===Ql)&&(e=Ne),nn(e)){const a=Wt(e,t,!0);return r&&hs(a,r),io>0&&!i&&Ge&&(a.shapeFlag&6?Ge[Ge.indexOf(e)]=a:Ge.push(a)),a.patchFlag=-2,a}if(ng(e)&&(e=e.__vccOpts),t){t=qp(t);let{class:a,style:c}=t;a&&!ke(a)&&(t.class=wr(a)),he(c)&&(Il(c)&&!G(c)&&(c=we({},c)),t.style=vn(c))}const s=ke(e)?1:Vp(e)?128:wp(e)?64:he(e)?4:q(e)?2:0;return fe(e,t,r,o,n,s,i,!0)}function qp(e){return e?Il(e)||su(e)?we({},e):e:null}function Wt(e,t,r=!1,o=!1){const{props:n,ref:i,patchFlag:s,children:a,transition:c}=e,u=t?pe(n||{},t):n,l={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Cu(u),ref:t&&t.ref?r&&i?G(i)?i.concat(Go(t)):[i,Go(t)]:Go(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Le?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Wt(e.ssContent),ssFallback:e.ssFallback&&Wt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&o&&xr(l,c.clone(l)),l}function Jp(e=" ",t=0){return xe(Rn,null,e,t)}function nC(e,t){const r=xe(Wo,null,e);return r.staticCount=t,r}function Tn(e="",t=!1){return t?(ce(),st(Ne,null,e)):xe(Ne,null,e)}function gt(e){return e==null||typeof e=="boolean"?xe(Ne):G(e)?xe(Le,null,e.slice()):typeof e=="object"?jt(e):xe(Rn,null,String(e))}function jt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Wt(e)}function hs(e,t){let r=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(G(t))r=16;else if(typeof t=="object")if(o&65){const n=t.default;n&&(n._c&&(n._d=!1),hs(e,n()),n._c&&(n._d=!0));return}else{r=32;const n=t._;!n&&!su(t)?t._ctx=Te:n===3&&Te&&(Te.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else q(t)?(t={default:t,_ctx:Te},r=32):(t=String(t),o&64?(r=16,t=[Jp(t)]):r=8);e.children=t,e.shapeFlag|=r}function pe(...e){const t={};for(let r=0;r<e.length;r++){const o=e[r];for(const n in o)if(n==="class")t.class!==o.class&&(t.class=wr([t.class,o.class]));else if(n==="style")t.style=vn([t.style,o.style]);else if(gn(n)){const i=t[n],s=o[n];s&&i!==s&&!(G(i)&&i.includes(s))&&(t[n]=i?[].concat(i,s):s)}else n!==""&&(t[n]=o[n])}return t}function pt(e,t,r,o=null){Qe(e,t,7,[r,o])}const Yp=ou();let Zp=0;function Xp(e,t,r){const o=e.type,n=(t?t.appContext:e.appContext)||Yp,i={uid:Zp++,vnode:e,type:o,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,scope:new Cl(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:cu(o,n),emitsOptions:vu(o,n),emit:null,emitted:null,propsDefaults:ve,inheritAttrs:o.inheritAttrs,ctx:ve,data:ve,props:ve,attrs:ve,slots:ve,refs:ve,setupState:ve,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Np.bind(null,i),e.ce&&e.ce(i),i}let Ee=null;const ms=()=>Ee||Te;let sn,ki;{const e=ml(),t=(r,o)=>{let n;return(n=e[r])||(n=e[r]=[]),n.push(o),i=>{n.length>1?n.forEach(s=>s(i)):n[0](i)}};sn=t("__VUE_INSTANCE_SETTERS__",r=>Ee=r),ki=t("__VUE_SSR_SETTERS__",r=>wn=r)}const ko=e=>{const t=Ee;return sn(e),e.scope.on(),()=>{e.scope.off(),sn(t)}},aa=()=>{Ee&&Ee.scope.off(),sn(null)};function ku(e){return e.vnode.shapeFlag&4}let wn=!1;function Qp(e,t=!1,r=!1){t&&ki(t);const{props:o,children:n}=e.vnode,i=ku(e);kp(e,o,i,t),Rp(e,n,r);const s=i?eg(e,t):void 0;return t&&ki(!1),s}function eg(e,t){const r=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,fp);const{setup:o}=r;if(o){const n=e.setupContext=o.length>1?rg(e):null,i=ko(e);qt();const s=Ut(o,e,0,[e.props,n]);if(Jt(),i(),fl(s)){if(s.then(aa,aa),t)return s.then(a=>{ca(e,a,t)}).catch(a=>{Cn(a,e,0)});e.asyncDep=s}else ca(e,s,t)}else Su(e,t)}function ca(e,t,r){q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:he(t)&&(e.setupState=Ml(t)),Su(e,r)}let la;function Su(e,t,r){const o=e.type;if(!e.render){if(!t&&la&&!o.render){const n=o.template||fs(e).template;if(n){const{isCustomElement:i,compilerOptions:s}=e.appContext.config,{delimiters:a,compilerOptions:c}=o,u=we(we({isCustomElement:i,delimiters:a},s),c);o.render=la(n,u)}}e.render=o.render||Xe}{const n=ko(e);qt();try{pp(e)}finally{Jt(),n()}}}const tg={get(e,t){return Ue(e,"get",""),e[t]}};function rg(e){const t=r=>{e.exposed=r||{}};return{attrs:new Proxy(e.attrs,tg),slots:e.slots,emit:e.emit,expose:t}}function Pn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Ml(rs(e.exposed)),{get(t,r){if(r in t)return t[r];if(r in Wr)return Wr[r](e)},has(t,r){return r in t||r in Wr}})):e.proxy}function og(e,t=!0){return q(e)?e.displayName||e.name:e.name||t&&e.__name}function ng(e){return q(e)&&"__vccOpts"in e}const Ze=(e,t)=>Nf(e,t,wn);function bs(e,t,r){const o=arguments.length;return o===2?he(t)&&!G(t)?nn(t)?xe(e,null,[t]):xe(e,t):xe(e,null,t):(o>3?r=Array.prototype.slice.call(arguments,2):o===3&&nn(r)&&(r=[r]),xe(e,t,r))}const ig="3.4.33";/**
* @vue/runtime-dom v3.4.33
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const sg="http://www.w3.org/2000/svg",ag="http://www.w3.org/1998/Math/MathML",kt=typeof document<"u"?document:null,ua=kt&&kt.createElement("template"),cg={insert:(e,t,r)=>{t.insertBefore(e,r||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,r,o)=>{const n=t==="svg"?kt.createElementNS(sg,e):t==="mathml"?kt.createElementNS(ag,e):r?kt.createElement(e,{is:r}):kt.createElement(e);return e==="select"&&o&&o.multiple!=null&&n.setAttribute("multiple",o.multiple),n},createText:e=>kt.createTextNode(e),createComment:e=>kt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>kt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,r,o,n,i){const s=r?r.previousSibling:t.lastChild;if(n&&(n===i||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),r),!(n===i||!(n=n.nextSibling)););else{ua.innerHTML=o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e;const a=ua.content;if(o==="svg"||o==="mathml"){const c=a.firstChild;for(;c.firstChild;)a.appendChild(c.firstChild);a.removeChild(c)}t.insertBefore(a,r)}return[s?s.nextSibling:t.firstChild,r?r.previousSibling:t.lastChild]}},wt="transition",Lr="animation",_r=Symbol("_vtc"),xu=(e,{slots:t})=>bs(ep,Ru(e),t);xu.displayName="Transition";const _u={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},lg=xu.props=we({},Gl,_u),Xt=(e,t=[])=>{G(e)?e.forEach(r=>r(...t)):e&&e(...t)},da=e=>e?G(e)?e.some(t=>t.length>1):e.length>1:!1;function Ru(e){const t={};for(const A in e)A in _u||(t[A]=e[A]);if(e.css===!1)return t;const{name:r="v",type:o,duration:n,enterFromClass:i=`${r}-enter-from`,enterActiveClass:s=`${r}-enter-active`,enterToClass:a=`${r}-enter-to`,appearFromClass:c=i,appearActiveClass:u=s,appearToClass:l=a,leaveFromClass:d=`${r}-leave-from`,leaveActiveClass:f=`${r}-leave-active`,leaveToClass:p=`${r}-leave-to`}=e,m=ug(n),b=m&&m[0],_=m&&m[1],{onBeforeEnter:R,onEnter:y,onEnterCancelled:k,onLeave:M,onLeaveCancelled:U,onBeforeAppear:B=R,onAppear:N=y,onAppearCancelled:F=k}=t,E=(A,ee,me)=>{$t(A,ee?l:a),$t(A,ee?u:s),me&&me()},V=(A,ee)=>{A._isLeaving=!1,$t(A,d),$t(A,p),$t(A,f),ee&&ee()},K=A=>(ee,me)=>{const Se=A?N:y,X=()=>E(ee,A,me);Xt(Se,[ee,X]),fa(()=>{$t(ee,A?c:i),Ct(ee,A?l:a),da(Se)||pa(ee,o,b,X)})};return we(t,{onBeforeEnter(A){Xt(R,[A]),Ct(A,i),Ct(A,s)},onBeforeAppear(A){Xt(B,[A]),Ct(A,c),Ct(A,u)},onEnter:K(!1),onAppear:K(!0),onLeave(A,ee){A._isLeaving=!0;const me=()=>V(A,ee);Ct(A,d),Ct(A,f),wu(),fa(()=>{A._isLeaving&&($t(A,d),Ct(A,p),da(M)||pa(A,o,_,me))}),Xt(M,[A,me])},onEnterCancelled(A){E(A,!1),Xt(k,[A])},onAppearCancelled(A){E(A,!0),Xt(F,[A])},onLeaveCancelled(A){V(A),Xt(U,[A])}})}function ug(e){if(e==null)return null;if(he(e))return[Wn(e.enter),Wn(e.leave)];{const t=Wn(e);return[t,t]}}function Wn(e){return lf(e)}function Ct(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.add(r)),(e[_r]||(e[_r]=new Set)).add(t)}function $t(e,t){t.split(/\s+/).forEach(o=>o&&e.classList.remove(o));const r=e[_r];r&&(r.delete(t),r.size||(e[_r]=void 0))}function fa(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let dg=0;function pa(e,t,r,o){const n=e._endId=++dg,i=()=>{n===e._endId&&o()};if(r)return setTimeout(i,r);const{type:s,timeout:a,propCount:c}=Tu(e,t);if(!s)return o();const u=s+"end";let l=0;const d=()=>{e.removeEventListener(u,f),i()},f=p=>{p.target===e&&++l>=c&&d()};setTimeout(()=>{l<c&&d()},a+1),e.addEventListener(u,f)}function Tu(e,t){const r=window.getComputedStyle(e),o=m=>(r[m]||"").split(", "),n=o(`${wt}Delay`),i=o(`${wt}Duration`),s=ga(n,i),a=o(`${Lr}Delay`),c=o(`${Lr}Duration`),u=ga(a,c);let l=null,d=0,f=0;t===wt?s>0&&(l=wt,d=s,f=i.length):t===Lr?u>0&&(l=Lr,d=u,f=c.length):(d=Math.max(s,u),l=d>0?s>u?wt:Lr:null,f=l?l===wt?i.length:c.length:0);const p=l===wt&&/\b(transform|all)(,|$)/.test(o(`${wt}Property`).toString());return{type:l,timeout:d,propCount:f,hasTransform:p}}function ga(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((r,o)=>ha(r)+ha(e[o])))}function ha(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function wu(){return document.body.offsetHeight}function fg(e,t,r){const o=e[_r];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):r?e.setAttribute("class",t):e.className=t}const ma=Symbol("_vod"),pg=Symbol("_vsh"),gg=Symbol(""),hg=/(^|;)\s*display\s*:/;function mg(e,t,r){const o=e.style,n=ke(r);let i=!1;if(r&&!n){if(t)if(ke(t))for(const s of t.split(";")){const a=s.slice(0,s.indexOf(":")).trim();r[a]==null&&Ko(o,a,"")}else for(const s in t)r[s]==null&&Ko(o,s,"");for(const s in r)s==="display"&&(i=!0),Ko(o,s,r[s])}else if(n){if(t!==r){const s=o[gg];s&&(r+=";"+s),o.cssText=r,i=hg.test(r)}}else t&&e.removeAttribute("style");ma in e&&(e[ma]=i?o.display:"",e[pg]&&(o.display="none"))}const ba=/\s*!important$/;function Ko(e,t,r){if(G(r))r.forEach(o=>Ko(e,t,o));else if(r==null&&(r=""),t.startsWith("--"))e.setProperty(t,r);else{const o=bg(e,t);ba.test(r)?e.setProperty(dr(o),r.replace(ba,""),"important"):e[o]=r}}const va=["Webkit","Moz","ms"],Gn={};function bg(e,t){const r=Gn[t];if(r)return r;let o=ct(t);if(o!=="filter"&&o in e)return Gn[t]=o;o=bn(o);for(let n=0;n<va.length;n++){const i=va[n]+o;if(i in e)return Gn[t]=i}return t}const ya="http://www.w3.org/1999/xlink";function Ca(e,t,r,o,n,i=mf(t)){o&&t.startsWith("xlink:")?r==null?e.removeAttributeNS(ya,t.slice(6,t.length)):e.setAttributeNS(ya,t,r):r==null||i&&!bl(r)?e.removeAttribute(t):e.setAttribute(t,i?"":Kt(r)?String(r):r)}function vg(e,t,r,o){if(t==="innerHTML"||t==="textContent"){if(r==null)return;e[t]=r;return}const n=e.tagName;if(t==="value"&&n!=="PROGRESS"&&!n.includes("-")){const s=n==="OPTION"?e.getAttribute("value")||"":e.value,a=r==null?"":String(r);(s!==a||!("_value"in e))&&(e.value=a),r==null&&e.removeAttribute(t),e._value=r;return}let i=!1;if(r===""||r==null){const s=typeof e[t];s==="boolean"?r=bl(r):r==null&&s==="string"?(r="",i=!0):s==="number"&&(r=0,i=!0)}try{e[t]=r}catch{}i&&e.removeAttribute(t)}function yg(e,t,r,o){e.addEventListener(t,r,o)}function Cg(e,t,r,o){e.removeEventListener(t,r,o)}const ka=Symbol("_vei");function kg(e,t,r,o,n=null){const i=e[ka]||(e[ka]={}),s=i[t];if(o&&s)s.value=o;else{const[a,c]=Sg(t);if(o){const u=i[t]=Rg(o,n);yg(e,a,u,c)}else s&&(Cg(e,a,s,c),i[t]=void 0)}}const Sa=/(?:Once|Passive|Capture)$/;function Sg(e){let t;if(Sa.test(e)){t={};let o;for(;o=e.match(Sa);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):dr(e.slice(2)),t]}let Kn=0;const xg=Promise.resolve(),_g=()=>Kn||(xg.then(()=>Kn=0),Kn=Date.now());function Rg(e,t){const r=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=r.attached)return;Qe(Tg(o,r.value),t,5,[o])};return r.value=e,r.attached=_g(),r}function Tg(e,t){if(G(t)){const r=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{r.call(e),e._stopped=!0},t.map(o=>n=>!n._stopped&&o&&o(n))}else return t}const xa=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,wg=(e,t,r,o,n,i)=>{const s=n==="svg";t==="class"?fg(e,o,s):t==="style"?mg(e,r,o):gn(t)?Gi(t)||kg(e,t,r,o,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Pg(e,t,o,s))?(vg(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ca(e,t,o,s,i,t!=="value")):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),Ca(e,t,o,s))};function Pg(e,t,r,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&xa(t)&&q(r));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return xa(t)&&ke(r)?!1:t in e}const Pu=new WeakMap,Eu=new WeakMap,an=Symbol("_moveCb"),_a=Symbol("_enterCb"),Ou={name:"TransitionGroup",props:we({},lg,{tag:String,moveClass:String}),setup(e,{slots:t}){const r=ms(),o=Wl();let n,i;return Yl(()=>{if(!n.length)return;const s=e.moveClass||`${e.name||"v"}-move`;if(!Lg(n[0].el,r.vnode.el,s))return;n.forEach($g),n.forEach(Bg);const a=n.filter(Ag);wu(),a.forEach(c=>{const u=c.el,l=u.style;Ct(u,s),l.transform=l.webkitTransform=l.transitionDuration="";const d=u[an]=f=>{f&&f.target!==u||(!f||/transform$/.test(f.propertyName))&&(u.removeEventListener("transitionend",d),u[an]=null,$t(u,s))};u.addEventListener("transitionend",d)})}),()=>{const s=oe(e),a=Ru(s);let c=s.tag||Le;if(n=[],i)for(let u=0;u<i.length;u++){const l=i[u];l.el&&l.el instanceof Element&&(n.push(l),xr(l,no(l,a,o,r)),Pu.set(l,l.el.getBoundingClientRect()))}i=t.default?as(t.default()):[];for(let u=0;u<i.length;u++){const l=i[u];l.key!=null&&xr(l,no(l,a,o,r))}return xe(c,null,i)}}},Eg=e=>delete e.mode;Ou.props;const Og=Ou;function $g(e){const t=e.el;t[an]&&t[an](),t[_a]&&t[_a]()}function Bg(e){Eu.set(e,e.el.getBoundingClientRect())}function Ag(e){const t=Pu.get(e),r=Eu.get(e),o=t.left-r.left,n=t.top-r.top;if(o||n){const i=e.el.style;return i.transform=i.webkitTransform=`translate(${o}px,${n}px)`,i.transitionDuration="0s",e}}function Lg(e,t,r){const o=e.cloneNode(),n=e[_r];n&&n.forEach(a=>{a.split(/\s+/).forEach(c=>c&&o.classList.remove(c))}),r.split(/\s+/).forEach(a=>a&&o.classList.add(a)),o.style.display="none";const i=t.nodeType===1?t:t.parentNode;i.appendChild(o);const{hasTransform:s}=Tu(o);return i.removeChild(o),s}const Ig=we({patchProp:wg},cg);let Ra;function Dg(){return Ra||(Ra=$p(Ig))}const jg=(...e)=>{const t=Dg().createApp(...e),{mount:r}=t;return t.mount=o=>{const n=Ng(o);if(!n)return;const i=t._component;!q(i)&&!i.render&&!i.template&&(i.template=n.innerHTML),n.innerHTML="";const s=r(n,!1,Mg(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),s},t};function Mg(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Ng(e){return ke(e)?document.querySelector(e):e}var Fg=!1;/*!
 * pinia v2.1.7
 * (c) 2023 Eduardo San Martin Morote
 * @license MIT
 */let $u;const En=e=>$u=e,Bu=Symbol();function Si(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var qr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(qr||(qr={}));function zg(){const e=kl(!0),t=e.run(()=>bt({}));let r=[],o=[];const n=rs({install(i){En(n),n._a=i,i.provide(Bu,n),i.config.globalProperties.$pinia=n,o.forEach(s=>r.push(s)),o=[]},use(i){return!this._a&&!Fg?o.push(i):r.push(i),this},_p:r,_a:null,_e:e,_s:new Map,state:t});return n}const Au=()=>{};function Ta(e,t,r,o=Au){e.push(t);const n=()=>{const i=e.indexOf(t);i>-1&&(e.splice(i,1),o())};return!r&&Sl()&&vf(n),n}function pr(e,...t){e.slice().forEach(r=>{r(...t)})}const Ug=e=>e();function xi(e,t){e instanceof Map&&t instanceof Map&&t.forEach((r,o)=>e.set(o,r)),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const r in t){if(!t.hasOwnProperty(r))continue;const o=t[r],n=e[r];Si(n)&&Si(o)&&e.hasOwnProperty(r)&&!_e(o)&&!ir(o)?e[r]=xi(n,o):e[r]=o}return e}const Hg=Symbol();function Vg(e){return!Si(e)||!e.hasOwnProperty(Hg)}const{assign:Bt}=Object;function Wg(e){return!!(_e(e)&&e.effect)}function Gg(e,t,r,o){const{state:n,actions:i,getters:s}=t,a=r.state.value[e];let c;function u(){a||(r.state.value[e]=n?n():{});const l=Vf(r.state.value[e]);return Bt(l,i,Object.keys(s||{}).reduce((d,f)=>(d[f]=rs(Ze(()=>{En(r);const p=r._s.get(e);return s[f].call(p,p)})),d),{}))}return c=Lu(e,u,t,r,o,!0),c}function Lu(e,t,r={},o,n,i){let s;const a=Bt({actions:{}},r),c={deep:!0};let u,l,d=[],f=[],p;const m=o.state.value[e];!i&&!m&&(o.state.value[e]={}),bt({});let b;function _(F){let E;u=l=!1,typeof F=="function"?(F(o.state.value[e]),E={type:qr.patchFunction,storeId:e,events:p}):(xi(o.state.value[e],F),E={type:qr.patchObject,payload:F,storeId:e,events:p});const V=b=Symbol();kn().then(()=>{b===V&&(u=!0)}),l=!0,pr(d,E,o.state.value[e])}const R=i?function(){const{state:E}=r,V=E?E():{};this.$patch(K=>{Bt(K,V)})}:Au;function y(){s.stop(),d=[],f=[],o._s.delete(e)}function k(F,E){return function(){En(o);const V=Array.from(arguments),K=[],A=[];function ee(X){K.push(X)}function me(X){A.push(X)}pr(f,{args:V,name:F,store:U,after:ee,onError:me});let Se;try{Se=E.apply(this&&this.$id===e?this:U,V)}catch(X){throw pr(A,X),X}return Se instanceof Promise?Se.then(X=>(pr(K,X),X)).catch(X=>(pr(A,X),Promise.reject(X))):(pr(K,Se),Se)}}const M={_p:o,$id:e,$onAction:Ta.bind(null,f),$patch:_,$reset:R,$subscribe(F,E={}){const V=Ta(d,F,E.detached,()=>K()),K=s.run(()=>mt(()=>o.state.value[e],A=>{(E.flush==="sync"?l:u)&&F({storeId:e,type:qr.direct,events:p},A)},Bt({},c,E)));return V},$dispose:y},U=Pr(M);o._s.set(e,U);const N=(o._a&&o._a.runWithContext||Ug)(()=>o._e.run(()=>(s=kl()).run(t)));for(const F in N){const E=N[F];if(_e(E)&&!Wg(E)||ir(E))i||(m&&Vg(E)&&(_e(E)?E.value=m[F]:xi(E,m[F])),o.state.value[e][F]=E);else if(typeof E=="function"){const V=k(F,E);N[F]=V,a.actions[F]=E}}return Bt(U,N),Bt(oe(U),N),Object.defineProperty(U,"$state",{get:()=>o.state.value[e],set:F=>{_(E=>{Bt(E,F)})}}),o._p.forEach(F=>{Bt(U,s.run(()=>F({store:U,app:o._a,pinia:o,options:a})))}),m&&i&&r.hydrate&&r.hydrate(U.$state,m),u=!0,l=!0,U}function Kg(e,t,r){let o,n;const i=typeof t=="function";o=e,n=i?r:t;function s(a,c){const u=Cp();return a=a||(u?Ke(Bu,null):null),a&&En(a),a=$u,a._s.has(o)||(i?Lu(o,t,n,a):Gg(o,n,a)),a._s.get(o)}return s.$id=o,s}/*!
  * vue-router v4.4.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const gr=typeof document<"u";function qg(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const ue=Object.assign;function qn(e,t){const r={};for(const o in t){const n=t[o];r[o]=lt(n)?n.map(e):e(n)}return r}const Jr=()=>{},lt=Array.isArray,Iu=/#/g,Jg=/&/g,Yg=/\//g,Zg=/=/g,Xg=/\?/g,Du=/\+/g,Qg=/%5B/g,eh=/%5D/g,ju=/%5E/g,th=/%60/g,Mu=/%7B/g,rh=/%7C/g,Nu=/%7D/g,oh=/%20/g;function vs(e){return encodeURI(""+e).replace(rh,"|").replace(Qg,"[").replace(eh,"]")}function nh(e){return vs(e).replace(Mu,"{").replace(Nu,"}").replace(ju,"^")}function _i(e){return vs(e).replace(Du,"%2B").replace(oh,"+").replace(Iu,"%23").replace(Jg,"%26").replace(th,"`").replace(Mu,"{").replace(Nu,"}").replace(ju,"^")}function ih(e){return _i(e).replace(Zg,"%3D")}function sh(e){return vs(e).replace(Iu,"%23").replace(Xg,"%3F")}function ah(e){return e==null?"":sh(e).replace(Yg,"%2F")}function so(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const ch=/\/$/,lh=e=>e.replace(ch,"");function Jn(e,t,r="/"){let o,n={},i="",s="";const a=t.indexOf("#");let c=t.indexOf("?");return a<c&&a>=0&&(c=-1),c>-1&&(o=t.slice(0,c),i=t.slice(c+1,a>-1?a:t.length),n=e(i)),a>-1&&(o=o||t.slice(0,a),s=t.slice(a,t.length)),o=ph(o??t,r),{fullPath:o+(i&&"?")+i+s,path:o,query:n,hash:so(s)}}function uh(e,t){const r=t.query?e(t.query):"";return t.path+(r&&"?")+r+(t.hash||"")}function wa(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function dh(e,t,r){const o=t.matched.length-1,n=r.matched.length-1;return o>-1&&o===n&&Rr(t.matched[o],r.matched[n])&&Fu(t.params,r.params)&&e(t.query)===e(r.query)&&t.hash===r.hash}function Rr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Fu(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const r in e)if(!fh(e[r],t[r]))return!1;return!0}function fh(e,t){return lt(e)?Pa(e,t):lt(t)?Pa(t,e):e===t}function Pa(e,t){return lt(t)?e.length===t.length&&e.every((r,o)=>r===t[o]):e.length===1&&e[0]===t}function ph(e,t){if(e.startsWith("/"))return e;if(!e)return t;const r=t.split("/"),o=e.split("/"),n=o[o.length-1];(n===".."||n===".")&&o.push("");let i=r.length-1,s,a;for(s=0;s<o.length;s++)if(a=o[s],a!==".")if(a==="..")i>1&&i--;else break;return r.slice(0,i).join("/")+"/"+o.slice(s).join("/")}const Pt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var ao;(function(e){e.pop="pop",e.push="push"})(ao||(ao={}));var Yr;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Yr||(Yr={}));function gh(e){if(!e)if(gr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),lh(e)}const hh=/^[^#]+#/;function mh(e,t){return e.replace(hh,"#")+t}function bh(e,t){const r=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-r.left-(t.left||0),top:o.top-r.top-(t.top||0)}}const On=()=>({left:window.scrollX,top:window.scrollY});function vh(e){let t;if("el"in e){const r=e.el,o=typeof r=="string"&&r.startsWith("#"),n=typeof r=="string"?o?document.getElementById(r.slice(1)):document.querySelector(r):r;if(!n)return;t=bh(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Ea(e,t){return(history.state?history.state.position-t:-1)+e}const Ri=new Map;function yh(e,t){Ri.set(e,t)}function Ch(e){const t=Ri.get(e);return Ri.delete(e),t}let kh=()=>location.protocol+"//"+location.host;function zu(e,t){const{pathname:r,search:o,hash:n}=t,i=e.indexOf("#");if(i>-1){let a=n.includes(e.slice(i))?e.slice(i).length:1,c=n.slice(a);return c[0]!=="/"&&(c="/"+c),wa(c,"")}return wa(r,e)+o+n}function Sh(e,t,r,o){let n=[],i=[],s=null;const a=({state:f})=>{const p=zu(e,location),m=r.value,b=t.value;let _=0;if(f){if(r.value=p,t.value=f,s&&s===m){s=null;return}_=b?f.position-b.position:0}else o(p);n.forEach(R=>{R(r.value,m,{delta:_,type:ao.pop,direction:_?_>0?Yr.forward:Yr.back:Yr.unknown})})};function c(){s=r.value}function u(f){n.push(f);const p=()=>{const m=n.indexOf(f);m>-1&&n.splice(m,1)};return i.push(p),p}function l(){const{history:f}=window;f.state&&f.replaceState(ue({},f.state,{scroll:On()}),"")}function d(){for(const f of i)f();i=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",l)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:c,listen:u,destroy:d}}function Oa(e,t,r,o=!1,n=!1){return{back:e,current:t,forward:r,replaced:o,position:window.history.length,scroll:n?On():null}}function xh(e){const{history:t,location:r}=window,o={value:zu(e,r)},n={value:t.state};n.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(c,u,l){const d=e.indexOf("#"),f=d>-1?(r.host&&document.querySelector("base")?e:e.slice(d))+c:kh()+e+c;try{t[l?"replaceState":"pushState"](u,"",f),n.value=u}catch(p){console.error(p),r[l?"replace":"assign"](f)}}function s(c,u){const l=ue({},t.state,Oa(n.value.back,c,n.value.forward,!0),u,{position:n.value.position});i(c,l,!0),o.value=c}function a(c,u){const l=ue({},n.value,t.state,{forward:c,scroll:On()});i(l.current,l,!0);const d=ue({},Oa(o.value,c,null),{position:l.position+1},u);i(c,d,!1),o.value=c}return{location:o,state:n,push:a,replace:s}}function _h(e){e=gh(e);const t=xh(e),r=Sh(e,t.state,t.location,t.replace);function o(i,s=!0){s||r.pauseListeners(),history.go(i)}const n=ue({location:"",base:e,go:o,createHref:mh.bind(null,e)},t,r);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function Rh(e){return typeof e=="string"||e&&typeof e=="object"}function Uu(e){return typeof e=="string"||typeof e=="symbol"}const Hu=Symbol("");var $a;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})($a||($a={}));function Tr(e,t){return ue(new Error,{type:e,[Hu]:!0},t)}function yt(e,t){return e instanceof Error&&Hu in e&&(t==null||!!(e.type&t))}const Ba="[^/]+?",Th={sensitive:!1,strict:!1,start:!0,end:!0},wh=/[.+*?^${}()[\]/\\]/g;function Ph(e,t){const r=ue({},Th,t),o=[];let n=r.start?"^":"";const i=[];for(const u of e){const l=u.length?[]:[90];r.strict&&!u.length&&(n+="/");for(let d=0;d<u.length;d++){const f=u[d];let p=40+(r.sensitive?.25:0);if(f.type===0)d||(n+="/"),n+=f.value.replace(wh,"\\$&"),p+=40;else if(f.type===1){const{value:m,repeatable:b,optional:_,regexp:R}=f;i.push({name:m,repeatable:b,optional:_});const y=R||Ba;if(y!==Ba){p+=10;try{new RegExp(`(${y})`)}catch(M){throw new Error(`Invalid custom RegExp for param "${m}" (${y}): `+M.message)}}let k=b?`((?:${y})(?:/(?:${y}))*)`:`(${y})`;d||(k=_&&u.length<2?`(?:/${k})`:"/"+k),_&&(k+="?"),n+=k,p+=20,_&&(p+=-8),b&&(p+=-20),y===".*"&&(p+=-50)}l.push(p)}o.push(l)}if(r.strict&&r.end){const u=o.length-1;o[u][o[u].length-1]+=.7000000000000001}r.strict||(n+="/?"),r.end?n+="$":r.strict&&(n+="(?:/|$)");const s=new RegExp(n,r.sensitive?"":"i");function a(u){const l=u.match(s),d={};if(!l)return null;for(let f=1;f<l.length;f++){const p=l[f]||"",m=i[f-1];d[m.name]=p&&m.repeatable?p.split("/"):p}return d}function c(u){let l="",d=!1;for(const f of e){(!d||!l.endsWith("/"))&&(l+="/"),d=!1;for(const p of f)if(p.type===0)l+=p.value;else if(p.type===1){const{value:m,repeatable:b,optional:_}=p,R=m in u?u[m]:"";if(lt(R)&&!b)throw new Error(`Provided param "${m}" is an array but it is not repeatable (* or + modifiers)`);const y=lt(R)?R.join("/"):R;if(!y)if(_)f.length<2&&(l.endsWith("/")?l=l.slice(0,-1):d=!0);else throw new Error(`Missing required param "${m}"`);l+=y}}return l||"/"}return{re:s,score:o,keys:i,parse:a,stringify:c}}function Eh(e,t){let r=0;for(;r<e.length&&r<t.length;){const o=t[r]-e[r];if(o)return o;r++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Vu(e,t){let r=0;const o=e.score,n=t.score;for(;r<o.length&&r<n.length;){const i=Eh(o[r],n[r]);if(i)return i;r++}if(Math.abs(n.length-o.length)===1){if(Aa(o))return 1;if(Aa(n))return-1}return n.length-o.length}function Aa(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Oh={type:0,value:""},$h=/[a-zA-Z0-9_]/;function Bh(e){if(!e)return[[]];if(e==="/")return[[Oh]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${r})/"${u}": ${p}`)}let r=0,o=r;const n=[];let i;function s(){i&&n.push(i),i=[]}let a=0,c,u="",l="";function d(){u&&(r===0?i.push({type:0,value:u}):r===1||r===2||r===3?(i.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:u,regexp:l,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),u="")}function f(){u+=c}for(;a<e.length;){if(c=e[a++],c==="\\"&&r!==2){o=r,r=4;continue}switch(r){case 0:c==="/"?(u&&d(),s()):c===":"?(d(),r=1):f();break;case 4:f(),r=o;break;case 1:c==="("?r=2:$h.test(c)?f():(d(),r=0,c!=="*"&&c!=="?"&&c!=="+"&&a--);break;case 2:c===")"?l[l.length-1]=="\\"?l=l.slice(0,-1)+c:r=3:l+=c;break;case 3:d(),r=0,c!=="*"&&c!=="?"&&c!=="+"&&a--,l="";break;default:t("Unknown state");break}}return r===2&&t(`Unfinished custom RegExp for param "${u}"`),d(),s(),n}function Ah(e,t,r){const o=Ph(Bh(e.path),r),n=ue(o,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function Lh(e,t){const r=[],o=new Map;t=Da({strict:!1,end:!0,sensitive:!1},t);function n(d){return o.get(d)}function i(d,f,p){const m=!p,b=Ih(d);b.aliasOf=p&&p.record;const _=Da(t,d),R=[b];if("alias"in d){const M=typeof d.alias=="string"?[d.alias]:d.alias;for(const U of M)R.push(ue({},b,{components:p?p.record.components:b.components,path:U,aliasOf:p?p.record:b}))}let y,k;for(const M of R){const{path:U}=M;if(f&&U[0]!=="/"){const B=f.record.path,N=B[B.length-1]==="/"?"":"/";M.path=f.record.path+(U&&N+U)}if(y=Ah(M,f,_),p?p.alias.push(y):(k=k||y,k!==y&&k.alias.push(y),m&&d.name&&!Ia(y)&&s(d.name)),Wu(y)&&c(y),b.children){const B=b.children;for(let N=0;N<B.length;N++)i(B[N],y,p&&p.children[N])}p=p||y}return k?()=>{s(k)}:Jr}function s(d){if(Uu(d)){const f=o.get(d);f&&(o.delete(d),r.splice(r.indexOf(f),1),f.children.forEach(s),f.alias.forEach(s))}else{const f=r.indexOf(d);f>-1&&(r.splice(f,1),d.record.name&&o.delete(d.record.name),d.children.forEach(s),d.alias.forEach(s))}}function a(){return r}function c(d){const f=Mh(d,r);r.splice(f,0,d),d.record.name&&!Ia(d)&&o.set(d.record.name,d)}function u(d,f){let p,m={},b,_;if("name"in d&&d.name){if(p=o.get(d.name),!p)throw Tr(1,{location:d});_=p.record.name,m=ue(La(f.params,p.keys.filter(k=>!k.optional).concat(p.parent?p.parent.keys.filter(k=>k.optional):[]).map(k=>k.name)),d.params&&La(d.params,p.keys.map(k=>k.name))),b=p.stringify(m)}else if(d.path!=null)b=d.path,p=r.find(k=>k.re.test(b)),p&&(m=p.parse(b),_=p.record.name);else{if(p=f.name?o.get(f.name):r.find(k=>k.re.test(f.path)),!p)throw Tr(1,{location:d,currentLocation:f});_=p.record.name,m=ue({},f.params,d.params),b=p.stringify(m)}const R=[];let y=p;for(;y;)R.unshift(y.record),y=y.parent;return{name:_,path:b,params:m,matched:R,meta:jh(R)}}e.forEach(d=>i(d));function l(){r.length=0,o.clear()}return{addRoute:i,resolve:u,removeRoute:s,clearRoutes:l,getRoutes:a,getRecordMatcher:n}}function La(e,t){const r={};for(const o of t)o in e&&(r[o]=e[o]);return r}function Ih(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:Dh(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function Dh(e){const t={},r=e.props||!1;if("component"in e)t.default=r;else for(const o in e.components)t[o]=typeof r=="object"?r[o]:r;return t}function Ia(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function jh(e){return e.reduce((t,r)=>ue(t,r.meta),{})}function Da(e,t){const r={};for(const o in e)r[o]=o in t?t[o]:e[o];return r}function Mh(e,t){let r=0,o=t.length;for(;r!==o;){const i=r+o>>1;Vu(e,t[i])<0?o=i:r=i+1}const n=Nh(e);return n&&(o=t.lastIndexOf(n,o-1)),o}function Nh(e){let t=e;for(;t=t.parent;)if(Wu(t)&&Vu(e,t)===0)return t}function Wu({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Fh(e){const t={};if(e===""||e==="?")return t;const o=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<o.length;++n){const i=o[n].replace(Du," "),s=i.indexOf("="),a=so(s<0?i:i.slice(0,s)),c=s<0?null:so(i.slice(s+1));if(a in t){let u=t[a];lt(u)||(u=t[a]=[u]),u.push(c)}else t[a]=c}return t}function ja(e){let t="";for(let r in e){const o=e[r];if(r=ih(r),o==null){o!==void 0&&(t+=(t.length?"&":"")+r);continue}(lt(o)?o.map(i=>i&&_i(i)):[o&&_i(o)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+r,i!=null&&(t+="="+i))})}return t}function zh(e){const t={};for(const r in e){const o=e[r];o!==void 0&&(t[r]=lt(o)?o.map(n=>n==null?null:""+n):o==null?o:""+o)}return t}const Uh=Symbol(""),Ma=Symbol(""),$n=Symbol(""),ys=Symbol(""),Ti=Symbol("");function Ir(){let e=[];function t(o){return e.push(o),()=>{const n=e.indexOf(o);n>-1&&e.splice(n,1)}}function r(){e=[]}return{add:t,list:()=>e.slice(),reset:r}}function Mt(e,t,r,o,n,i=s=>s()){const s=o&&(o.enterCallbacks[n]=o.enterCallbacks[n]||[]);return()=>new Promise((a,c)=>{const u=f=>{f===!1?c(Tr(4,{from:r,to:t})):f instanceof Error?c(f):Rh(f)?c(Tr(2,{from:t,to:f})):(s&&o.enterCallbacks[n]===s&&typeof f=="function"&&s.push(f),a())},l=i(()=>e.call(o&&o.instances[n],t,r,u));let d=Promise.resolve(l);e.length<3&&(d=d.then(u)),d.catch(f=>c(f))})}function Yn(e,t,r,o,n=i=>i()){const i=[];for(const s of e)for(const a in s.components){let c=s.components[a];if(!(t!=="beforeRouteEnter"&&!s.instances[a]))if(Hh(c)){const l=(c.__vccOpts||c)[t];l&&i.push(Mt(l,r,o,s,a,n))}else{let u=c();i.push(()=>u.then(l=>{if(!l)return Promise.reject(new Error(`Couldn't resolve component "${a}" at "${s.path}"`));const d=qg(l)?l.default:l;s.components[a]=d;const p=(d.__vccOpts||d)[t];return p&&Mt(p,r,o,s,a,n)()}))}}return i}function Hh(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Na(e){const t=Ke($n),r=Ke(ys),o=Ze(()=>{const c=it(e.to);return t.resolve(c)}),n=Ze(()=>{const{matched:c}=o.value,{length:u}=c,l=c[u-1],d=r.matched;if(!l||!d.length)return-1;const f=d.findIndex(Rr.bind(null,l));if(f>-1)return f;const p=Fa(c[u-2]);return u>1&&Fa(l)===p&&d[d.length-1].path!==p?d.findIndex(Rr.bind(null,c[u-2])):f}),i=Ze(()=>n.value>-1&&Kh(r.params,o.value.params)),s=Ze(()=>n.value>-1&&n.value===r.matched.length-1&&Fu(r.params,o.value.params));function a(c={}){return Gh(c)?t[it(e.replace)?"replace":"push"](it(e.to)).catch(Jr):Promise.resolve()}return{route:o,href:Ze(()=>o.value.href),isActive:i,isExactActive:s,navigate:a}}const Vh=cs({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Na,setup(e,{slots:t}){const r=Pr(Na(e)),{options:o}=Ke($n),n=Ze(()=>({[za(e.activeClass,o.linkActiveClass,"router-link-active")]:r.isActive,[za(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:r.isExactActive}));return()=>{const i=t.default&&t.default(r);return e.custom?i:bs("a",{"aria-current":r.isExactActive?e.ariaCurrentValue:null,href:r.href,onClick:r.navigate,class:n.value},i)}}}),Wh=Vh;function Gh(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Kh(e,t){for(const r in t){const o=t[r],n=e[r];if(typeof o=="string"){if(o!==n)return!1}else if(!lt(n)||n.length!==o.length||o.some((i,s)=>i!==n[s]))return!1}return!0}function Fa(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const za=(e,t,r)=>e??t??r,qh=cs({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:r}){const o=Ke(Ti),n=Ze(()=>e.route||o.value),i=Ke(Ma,0),s=Ze(()=>{let u=it(i);const{matched:l}=n.value;let d;for(;(d=l[u])&&!d.components;)u++;return u}),a=Ze(()=>n.value.matched[s.value]);Vo(Ma,Ze(()=>s.value+1)),Vo(Uh,a),Vo(Ti,n);const c=bt();return mt(()=>[c.value,a.value,e.name],([u,l,d],[f,p,m])=>{l&&(l.instances[d]=u,p&&p!==l&&u&&u===f&&(l.leaveGuards.size||(l.leaveGuards=p.leaveGuards),l.updateGuards.size||(l.updateGuards=p.updateGuards))),u&&l&&(!p||!Rr(l,p)||!f)&&(l.enterCallbacks[d]||[]).forEach(b=>b(u))},{flush:"post"}),()=>{const u=n.value,l=e.name,d=a.value,f=d&&d.components[l];if(!f)return Ua(r.default,{Component:f,route:u});const p=d.props[l],m=p?p===!0?u.params:typeof p=="function"?p(u):p:null,_=bs(f,ue({},m,t,{onVnodeUnmounted:R=>{R.component.isUnmounted&&(d.instances[l]=null)},ref:c}));return Ua(r.default,{Component:_,route:u})||_}}});function Ua(e,t){if(!e)return null;const r=e(t);return r.length===1?r[0]:r}const Gu=qh;function Jh(e){const t=Lh(e.routes,e),r=e.parseQuery||Fh,o=e.stringifyQuery||ja,n=e.history,i=Ir(),s=Ir(),a=Ir(),c=Ff(Pt);let u=Pt;gr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const l=qn.bind(null,x=>""+x),d=qn.bind(null,ah),f=qn.bind(null,so);function p(x,D){let I,z;return Uu(x)?(I=t.getRecordMatcher(x),z=D):z=x,t.addRoute(z,I)}function m(x){const D=t.getRecordMatcher(x);D&&t.removeRoute(D)}function b(){return t.getRoutes().map(x=>x.record)}function _(x){return!!t.getRecordMatcher(x)}function R(x,D){if(D=ue({},D||c.value),typeof x=="string"){const h=Jn(r,x,D.path),v=t.resolve({path:h.path},D),T=n.createHref(h.fullPath);return ue(h,v,{params:f(v.params),hash:so(h.hash),redirectedFrom:void 0,href:T})}let I;if(x.path!=null)I=ue({},x,{path:Jn(r,x.path,D.path).path});else{const h=ue({},x.params);for(const v in h)h[v]==null&&delete h[v];I=ue({},x,{params:d(h)}),D.params=d(D.params)}const z=t.resolve(I,D),le=x.hash||"";z.params=l(f(z.params));const ye=uh(o,ue({},x,{hash:nh(le),path:z.path})),g=n.createHref(ye);return ue({fullPath:ye,hash:le,query:o===ja?zh(x.query):x.query||{}},z,{redirectedFrom:void 0,href:g})}function y(x){return typeof x=="string"?Jn(r,x,c.value.path):ue({},x)}function k(x,D){if(u!==x)return Tr(8,{from:D,to:x})}function M(x){return N(x)}function U(x){return M(ue(y(x),{replace:!0}))}function B(x){const D=x.matched[x.matched.length-1];if(D&&D.redirect){const{redirect:I}=D;let z=typeof I=="function"?I(x):I;return typeof z=="string"&&(z=z.includes("?")||z.includes("#")?z=y(z):{path:z},z.params={}),ue({query:x.query,hash:x.hash,params:z.path!=null?{}:x.params},z)}}function N(x,D){const I=u=R(x),z=c.value,le=x.state,ye=x.force,g=x.replace===!0,h=B(I);if(h)return N(ue(y(h),{state:typeof h=="object"?ue({},le,h.state):le,force:ye,replace:g}),D||I);const v=I;v.redirectedFrom=D;let T;return!ye&&dh(o,z,I)&&(T=Tr(16,{to:v,from:z}),$e(z,z,!0,!1)),(T?Promise.resolve(T):V(v,z)).catch(S=>yt(S)?yt(S,2)?S:De(S):te(S,v,z)).then(S=>{if(S){if(yt(S,2))return N(ue({replace:g},y(S.to),{state:typeof S.to=="object"?ue({},le,S.to.state):le,force:ye}),D||v)}else S=A(v,z,!0,g,le);return K(v,z,S),S})}function F(x,D){const I=k(x,D);return I?Promise.reject(I):Promise.resolve()}function E(x){const D=dt.values().next().value;return D&&typeof D.runWithContext=="function"?D.runWithContext(x):x()}function V(x,D){let I;const[z,le,ye]=Yh(x,D);I=Yn(z.reverse(),"beforeRouteLeave",x,D);for(const h of z)h.leaveGuards.forEach(v=>{I.push(Mt(v,x,D))});const g=F.bind(null,x,D);return I.push(g),qe(I).then(()=>{I=[];for(const h of i.list())I.push(Mt(h,x,D));return I.push(g),qe(I)}).then(()=>{I=Yn(le,"beforeRouteUpdate",x,D);for(const h of le)h.updateGuards.forEach(v=>{I.push(Mt(v,x,D))});return I.push(g),qe(I)}).then(()=>{I=[];for(const h of ye)if(h.beforeEnter)if(lt(h.beforeEnter))for(const v of h.beforeEnter)I.push(Mt(v,x,D));else I.push(Mt(h.beforeEnter,x,D));return I.push(g),qe(I)}).then(()=>(x.matched.forEach(h=>h.enterCallbacks={}),I=Yn(ye,"beforeRouteEnter",x,D,E),I.push(g),qe(I))).then(()=>{I=[];for(const h of s.list())I.push(Mt(h,x,D));return I.push(g),qe(I)}).catch(h=>yt(h,8)?h:Promise.reject(h))}function K(x,D,I){a.list().forEach(z=>E(()=>z(x,D,I)))}function A(x,D,I,z,le){const ye=k(x,D);if(ye)return ye;const g=D===Pt,h=gr?history.state:{};I&&(z||g?n.replace(x.fullPath,ue({scroll:g&&h&&h.scroll},le)):n.push(x.fullPath,le)),c.value=x,$e(x,D,I,g),De()}let ee;function me(){ee||(ee=n.listen((x,D,I)=>{if(!To.listening)return;const z=R(x),le=B(z);if(le){N(ue(le,{replace:!0}),z).catch(Jr);return}u=z;const ye=c.value;gr&&yh(Ea(ye.fullPath,I.delta),On()),V(z,ye).catch(g=>yt(g,12)?g:yt(g,2)?(N(g.to,z).then(h=>{yt(h,20)&&!I.delta&&I.type===ao.pop&&n.go(-1,!1)}).catch(Jr),Promise.reject()):(I.delta&&n.go(-I.delta,!1),te(g,z,ye))).then(g=>{g=g||A(z,ye,!1),g&&(I.delta&&!yt(g,8)?n.go(-I.delta,!1):I.type===ao.pop&&yt(g,20)&&n.go(-1,!1)),K(z,ye,g)}).catch(Jr)}))}let Se=Ir(),X=Ir(),ne;function te(x,D,I){De(x);const z=X.list();return z.length?z.forEach(le=>le(x,D,I)):console.error(x),Promise.reject(x)}function He(){return ne&&c.value!==Pt?Promise.resolve():new Promise((x,D)=>{Se.add([x,D])})}function De(x){return ne||(ne=!x,me(),Se.list().forEach(([D,I])=>x?I(x):D()),Se.reset()),x}function $e(x,D,I,z){const{scrollBehavior:le}=e;if(!gr||!le)return Promise.resolve();const ye=!I&&Ch(Ea(x.fullPath,0))||(z||!I)&&history.state&&history.state.scroll||null;return kn().then(()=>le(x,D,ye)).then(g=>g&&vh(g)).catch(g=>te(g,x,D))}const Oe=x=>n.go(x);let Rt;const dt=new Set,To={currentRoute:c,listening:!0,addRoute:p,removeRoute:m,clearRoutes:t.clearRoutes,hasRoute:_,getRoutes:b,resolve:R,options:e,push:M,replace:U,go:Oe,back:()=>Oe(-1),forward:()=>Oe(1),beforeEach:i.add,beforeResolve:s.add,afterEach:a.add,onError:X.add,isReady:He,install(x){const D=this;x.component("RouterLink",Wh),x.component("RouterView",Gu),x.config.globalProperties.$router=D,Object.defineProperty(x.config.globalProperties,"$route",{enumerable:!0,get:()=>it(c)}),gr&&!Rt&&c.value===Pt&&(Rt=!0,M(n.location).catch(le=>{}));const I={};for(const le in Pt)Object.defineProperty(I,le,{get:()=>c.value[le],enumerable:!0});x.provide($n,D),x.provide(ys,Ll(I)),x.provide(Ti,c);const z=x.unmount;dt.add(x),x.unmount=function(){dt.delete(x),dt.size<1&&(u=Pt,ee&&ee(),ee=null,c.value=Pt,Rt=!1,ne=!1),z()}}};function qe(x){return x.reduce((D,I)=>D.then(()=>E(I)),Promise.resolve())}return To}function Yh(e,t){const r=[],o=[],n=[],i=Math.max(t.matched.length,e.matched.length);for(let s=0;s<i;s++){const a=t.matched[s];a&&(e.matched.find(u=>Rr(u,a))?o.push(a):r.push(a));const c=e.matched[s];c&&(t.matched.find(u=>Rr(u,c))||n.push(c))}return[r,o,n]}function iC(){return Ke($n)}function sC(e){return Ke(ys)}const Zh=Kg("user",()=>{const e=bt({});return{info:e,updateInfo:r=>{Object.assign(e.value,r)}}}),cn="https://meeting.codeemo.cn",aC="e7af3610-a6a8-441c-96d4-8b75d44acca2",Ku="token";function qu(){return localStorage.getItem(Ku)}function Ha(e){localStorage.setItem(Ku,e)}function cC(e=""){window.location.href=`${cn}/login?redirect_uri=${encodeURIComponent(e||window.location.origin)}`}async function lC(){const e=[],t=[],r=[],o=await navigator.mediaDevices.enumerateDevices();for(let n=0;n<o.length;n++){const i=o[n];if(!(i.deviceId==="default"||i.deviceId==="communications"))switch(i.kind){case"videoinput":e.push(i);break;case"audioinput":t.push(i);break;case"audiooutput":r.push(i);break}}return[e,t,r]}function Ju(e,t){return function(){return e.apply(t,arguments)}}const{toString:Xh}=Object.prototype,{getPrototypeOf:Cs}=Object,Bn=(e=>t=>{const r=Xh.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),ut=e=>(e=e.toLowerCase(),t=>Bn(t)===e),An=e=>t=>typeof t===e,{isArray:Er}=Array,co=An("undefined");function Qh(e){return e!==null&&!co(e)&&e.constructor!==null&&!co(e.constructor)&&et(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Yu=ut("ArrayBuffer");function em(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Yu(e.buffer),t}const tm=An("string"),et=An("function"),Zu=An("number"),Ln=e=>e!==null&&typeof e=="object",rm=e=>e===!0||e===!1,qo=e=>{if(Bn(e)!=="object")return!1;const t=Cs(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},om=ut("Date"),nm=ut("File"),im=ut("Blob"),sm=ut("FileList"),am=e=>Ln(e)&&et(e.pipe),cm=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||et(e.append)&&((t=Bn(e))==="formdata"||t==="object"&&et(e.toString)&&e.toString()==="[object FormData]"))},lm=ut("URLSearchParams"),[um,dm,fm,pm]=["ReadableStream","Request","Response","Headers"].map(ut),gm=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function So(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let o,n;if(typeof e!="object"&&(e=[e]),Er(e))for(o=0,n=e.length;o<n;o++)t.call(null,e[o],o,e);else{const i=r?Object.getOwnPropertyNames(e):Object.keys(e),s=i.length;let a;for(o=0;o<s;o++)a=i[o],t.call(null,e[a],a,e)}}function Xu(e,t){t=t.toLowerCase();const r=Object.keys(e);let o=r.length,n;for(;o-- >0;)if(n=r[o],t===n.toLowerCase())return n;return null}const Qu=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ed=e=>!co(e)&&e!==Qu;function wi(){const{caseless:e}=ed(this)&&this||{},t={},r=(o,n)=>{const i=e&&Xu(t,n)||n;qo(t[i])&&qo(o)?t[i]=wi(t[i],o):qo(o)?t[i]=wi({},o):Er(o)?t[i]=o.slice():t[i]=o};for(let o=0,n=arguments.length;o<n;o++)arguments[o]&&So(arguments[o],r);return t}const hm=(e,t,r,{allOwnKeys:o}={})=>(So(t,(n,i)=>{r&&et(n)?e[i]=Ju(n,r):e[i]=n},{allOwnKeys:o}),e),mm=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),bm=(e,t,r,o)=>{e.prototype=Object.create(t.prototype,o),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},vm=(e,t,r,o)=>{let n,i,s;const a={};if(t=t||{},e==null)return t;do{for(n=Object.getOwnPropertyNames(e),i=n.length;i-- >0;)s=n[i],(!o||o(s,e,t))&&!a[s]&&(t[s]=e[s],a[s]=!0);e=r!==!1&&Cs(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},ym=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const o=e.indexOf(t,r);return o!==-1&&o===r},Cm=e=>{if(!e)return null;if(Er(e))return e;let t=e.length;if(!Zu(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},km=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Cs(Uint8Array)),Sm=(e,t)=>{const o=(e&&e[Symbol.iterator]).call(e);let n;for(;(n=o.next())&&!n.done;){const i=n.value;t.call(e,i[0],i[1])}},xm=(e,t)=>{let r;const o=[];for(;(r=e.exec(t))!==null;)o.push(r);return o},_m=ut("HTMLFormElement"),Rm=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,o,n){return o.toUpperCase()+n}),Va=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Tm=ut("RegExp"),td=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),o={};So(r,(n,i)=>{let s;(s=t(n,i,e))!==!1&&(o[i]=s||n)}),Object.defineProperties(e,o)},wm=e=>{td(e,(t,r)=>{if(et(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const o=e[r];if(et(o)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Pm=(e,t)=>{const r={},o=n=>{n.forEach(i=>{r[i]=!0})};return Er(e)?o(e):o(String(e).split(t)),r},Em=()=>{},Om=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t,Zn="abcdefghijklmnopqrstuvwxyz",Wa="0123456789",rd={DIGIT:Wa,ALPHA:Zn,ALPHA_DIGIT:Zn+Zn.toUpperCase()+Wa},$m=(e=16,t=rd.ALPHA_DIGIT)=>{let r="";const{length:o}=t;for(;e--;)r+=t[Math.random()*o|0];return r};function Bm(e){return!!(e&&et(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const Am=e=>{const t=new Array(10),r=(o,n)=>{if(Ln(o)){if(t.indexOf(o)>=0)return;if(!("toJSON"in o)){t[n]=o;const i=Er(o)?[]:{};return So(o,(s,a)=>{const c=r(s,n+1);!co(c)&&(i[a]=c)}),t[n]=void 0,i}}return o};return r(e,0)},Lm=ut("AsyncFunction"),Im=e=>e&&(Ln(e)||et(e))&&et(e.then)&&et(e.catch),C={isArray:Er,isArrayBuffer:Yu,isBuffer:Qh,isFormData:cm,isArrayBufferView:em,isString:tm,isNumber:Zu,isBoolean:rm,isObject:Ln,isPlainObject:qo,isReadableStream:um,isRequest:dm,isResponse:fm,isHeaders:pm,isUndefined:co,isDate:om,isFile:nm,isBlob:im,isRegExp:Tm,isFunction:et,isStream:am,isURLSearchParams:lm,isTypedArray:km,isFileList:sm,forEach:So,merge:wi,extend:hm,trim:gm,stripBOM:mm,inherits:bm,toFlatObject:vm,kindOf:Bn,kindOfTest:ut,endsWith:ym,toArray:Cm,forEachEntry:Sm,matchAll:xm,isHTMLForm:_m,hasOwnProperty:Va,hasOwnProp:Va,reduceDescriptors:td,freezeMethods:wm,toObjectSet:Pm,toCamelCase:Rm,noop:Em,toFiniteNumber:Om,findKey:Xu,global:Qu,isContextDefined:ed,ALPHABET:rd,generateString:$m,isSpecCompliantForm:Bm,toJSONObject:Am,isAsyncFn:Lm,isThenable:Im};function Y(e,t,r,o,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),o&&(this.request=o),n&&(this.response=n)}C.inherits(Y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:C.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const od=Y.prototype,nd={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{nd[e]={value:e}});Object.defineProperties(Y,nd);Object.defineProperty(od,"isAxiosError",{value:!0});Y.from=(e,t,r,o,n,i)=>{const s=Object.create(od);return C.toFlatObject(e,s,function(c){return c!==Error.prototype},a=>a!=="isAxiosError"),Y.call(s,e.message,t,r,o,n),s.cause=e,s.name=e.name,i&&Object.assign(s,i),s};const Dm=null;function Pi(e){return C.isPlainObject(e)||C.isArray(e)}function id(e){return C.endsWith(e,"[]")?e.slice(0,-2):e}function Ga(e,t,r){return e?e.concat(t).map(function(n,i){return n=id(n),!r&&i?"["+n+"]":n}).join(r?".":""):t}function jm(e){return C.isArray(e)&&!e.some(Pi)}const Mm=C.toFlatObject(C,{},null,function(t){return/^is[A-Z]/.test(t)});function In(e,t,r){if(!C.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=C.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(b,_){return!C.isUndefined(_[b])});const o=r.metaTokens,n=r.visitor||l,i=r.dots,s=r.indexes,c=(r.Blob||typeof Blob<"u"&&Blob)&&C.isSpecCompliantForm(t);if(!C.isFunction(n))throw new TypeError("visitor must be a function");function u(m){if(m===null)return"";if(C.isDate(m))return m.toISOString();if(!c&&C.isBlob(m))throw new Y("Blob is not supported. Use a Buffer instead.");return C.isArrayBuffer(m)||C.isTypedArray(m)?c&&typeof Blob=="function"?new Blob([m]):Buffer.from(m):m}function l(m,b,_){let R=m;if(m&&!_&&typeof m=="object"){if(C.endsWith(b,"{}"))b=o?b:b.slice(0,-2),m=JSON.stringify(m);else if(C.isArray(m)&&jm(m)||(C.isFileList(m)||C.endsWith(b,"[]"))&&(R=C.toArray(m)))return b=id(b),R.forEach(function(k,M){!(C.isUndefined(k)||k===null)&&t.append(s===!0?Ga([b],M,i):s===null?b:b+"[]",u(k))}),!1}return Pi(m)?!0:(t.append(Ga(_,b,i),u(m)),!1)}const d=[],f=Object.assign(Mm,{defaultVisitor:l,convertValue:u,isVisitable:Pi});function p(m,b){if(!C.isUndefined(m)){if(d.indexOf(m)!==-1)throw Error("Circular reference detected in "+b.join("."));d.push(m),C.forEach(m,function(R,y){(!(C.isUndefined(R)||R===null)&&n.call(t,R,C.isString(y)?y.trim():y,b,f))===!0&&p(R,b?b.concat(y):[y])}),d.pop()}}if(!C.isObject(e))throw new TypeError("data must be an object");return p(e),t}function Ka(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(o){return t[o]})}function ks(e,t){this._pairs=[],e&&In(e,this,t)}const sd=ks.prototype;sd.append=function(t,r){this._pairs.push([t,r])};sd.toString=function(t){const r=t?function(o){return t.call(this,o,Ka)}:Ka;return this._pairs.map(function(n){return r(n[0])+"="+r(n[1])},"").join("&")};function Nm(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ad(e,t,r){if(!t)return e;const o=r&&r.encode||Nm,n=r&&r.serialize;let i;if(n?i=n(t,r):i=C.isURLSearchParams(t)?t.toString():new ks(t,r).toString(o),i){const s=e.indexOf("#");s!==-1&&(e=e.slice(0,s)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class qa{constructor(){this.handlers=[]}use(t,r,o){return this.handlers.push({fulfilled:t,rejected:r,synchronous:o?o.synchronous:!1,runWhen:o?o.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){C.forEach(this.handlers,function(o){o!==null&&t(o)})}}const cd={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Fm=typeof URLSearchParams<"u"?URLSearchParams:ks,zm=typeof FormData<"u"?FormData:null,Um=typeof Blob<"u"?Blob:null,Hm={isBrowser:!0,classes:{URLSearchParams:Fm,FormData:zm,Blob:Um},protocols:["http","https","file","blob","url","data"]},Ss=typeof window<"u"&&typeof document<"u",Vm=(e=>Ss&&["ReactNative","NativeScript","NS"].indexOf(e)<0)(typeof navigator<"u"&&navigator.product),Wm=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Gm=Ss&&window.location.href||"http://localhost",Km=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ss,hasStandardBrowserEnv:Vm,hasStandardBrowserWebWorkerEnv:Wm,origin:Gm},Symbol.toStringTag,{value:"Module"})),at={...Km,...Hm};function qm(e,t){return In(e,new at.classes.URLSearchParams,Object.assign({visitor:function(r,o,n,i){return at.isNode&&C.isBuffer(r)?(this.append(o,r.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function Jm(e){return C.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Ym(e){const t={},r=Object.keys(e);let o;const n=r.length;let i;for(o=0;o<n;o++)i=r[o],t[i]=e[i];return t}function ld(e){function t(r,o,n,i){let s=r[i++];if(s==="__proto__")return!0;const a=Number.isFinite(+s),c=i>=r.length;return s=!s&&C.isArray(n)?n.length:s,c?(C.hasOwnProp(n,s)?n[s]=[n[s],o]:n[s]=o,!a):((!n[s]||!C.isObject(n[s]))&&(n[s]=[]),t(r,o,n[s],i)&&C.isArray(n[s])&&(n[s]=Ym(n[s])),!a)}if(C.isFormData(e)&&C.isFunction(e.entries)){const r={};return C.forEachEntry(e,(o,n)=>{t(Jm(o),n,r,0)}),r}return null}function Zm(e,t,r){if(C.isString(e))try{return(t||JSON.parse)(e),C.trim(e)}catch(o){if(o.name!=="SyntaxError")throw o}return(r||JSON.stringify)(e)}const xo={transitional:cd,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const o=r.getContentType()||"",n=o.indexOf("application/json")>-1,i=C.isObject(t);if(i&&C.isHTMLForm(t)&&(t=new FormData(t)),C.isFormData(t))return n?JSON.stringify(ld(t)):t;if(C.isArrayBuffer(t)||C.isBuffer(t)||C.isStream(t)||C.isFile(t)||C.isBlob(t)||C.isReadableStream(t))return t;if(C.isArrayBufferView(t))return t.buffer;if(C.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(i){if(o.indexOf("application/x-www-form-urlencoded")>-1)return qm(t,this.formSerializer).toString();if((a=C.isFileList(t))||o.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return In(a?{"files[]":t}:t,c&&new c,this.formSerializer)}}return i||n?(r.setContentType("application/json",!1),Zm(t)):t}],transformResponse:[function(t){const r=this.transitional||xo.transitional,o=r&&r.forcedJSONParsing,n=this.responseType==="json";if(C.isResponse(t)||C.isReadableStream(t))return t;if(t&&C.isString(t)&&(o&&!this.responseType||n)){const s=!(r&&r.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(a){if(s)throw a.name==="SyntaxError"?Y.from(a,Y.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:at.classes.FormData,Blob:at.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};C.forEach(["delete","get","head","post","put","patch"],e=>{xo.headers[e]={}});const Xm=C.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Qm=e=>{const t={};let r,o,n;return e&&e.split(`
`).forEach(function(s){n=s.indexOf(":"),r=s.substring(0,n).trim().toLowerCase(),o=s.substring(n+1).trim(),!(!r||t[r]&&Xm[r])&&(r==="set-cookie"?t[r]?t[r].push(o):t[r]=[o]:t[r]=t[r]?t[r]+", "+o:o)}),t},Ja=Symbol("internals");function Dr(e){return e&&String(e).trim().toLowerCase()}function Jo(e){return e===!1||e==null?e:C.isArray(e)?e.map(Jo):String(e)}function e0(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=r.exec(e);)t[o[1]]=o[2];return t}const t0=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Xn(e,t,r,o,n){if(C.isFunction(o))return o.call(this,t,r);if(n&&(t=r),!!C.isString(t)){if(C.isString(o))return t.indexOf(o)!==-1;if(C.isRegExp(o))return o.test(t)}}function r0(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,o)=>r.toUpperCase()+o)}function o0(e,t){const r=C.toCamelCase(" "+t);["get","set","has"].forEach(o=>{Object.defineProperty(e,o+r,{value:function(n,i,s){return this[o].call(this,t,n,i,s)},configurable:!0})})}class Fe{constructor(t){t&&this.set(t)}set(t,r,o){const n=this;function i(a,c,u){const l=Dr(c);if(!l)throw new Error("header name must be a non-empty string");const d=C.findKey(n,l);(!d||n[d]===void 0||u===!0||u===void 0&&n[d]!==!1)&&(n[d||c]=Jo(a))}const s=(a,c)=>C.forEach(a,(u,l)=>i(u,l,c));if(C.isPlainObject(t)||t instanceof this.constructor)s(t,r);else if(C.isString(t)&&(t=t.trim())&&!t0(t))s(Qm(t),r);else if(C.isHeaders(t))for(const[a,c]of t.entries())i(c,a,o);else t!=null&&i(r,t,o);return this}get(t,r){if(t=Dr(t),t){const o=C.findKey(this,t);if(o){const n=this[o];if(!r)return n;if(r===!0)return e0(n);if(C.isFunction(r))return r.call(this,n,o);if(C.isRegExp(r))return r.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=Dr(t),t){const o=C.findKey(this,t);return!!(o&&this[o]!==void 0&&(!r||Xn(this,this[o],o,r)))}return!1}delete(t,r){const o=this;let n=!1;function i(s){if(s=Dr(s),s){const a=C.findKey(o,s);a&&(!r||Xn(o,o[a],a,r))&&(delete o[a],n=!0)}}return C.isArray(t)?t.forEach(i):i(t),n}clear(t){const r=Object.keys(this);let o=r.length,n=!1;for(;o--;){const i=r[o];(!t||Xn(this,this[i],i,t,!0))&&(delete this[i],n=!0)}return n}normalize(t){const r=this,o={};return C.forEach(this,(n,i)=>{const s=C.findKey(o,i);if(s){r[s]=Jo(n),delete r[i];return}const a=t?r0(i):String(i).trim();a!==i&&delete r[i],r[a]=Jo(n),o[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return C.forEach(this,(o,n)=>{o!=null&&o!==!1&&(r[n]=t&&C.isArray(o)?o.join(", "):o)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const o=new this(t);return r.forEach(n=>o.set(n)),o}static accessor(t){const o=(this[Ja]=this[Ja]={accessors:{}}).accessors,n=this.prototype;function i(s){const a=Dr(s);o[a]||(o0(n,s),o[a]=!0)}return C.isArray(t)?t.forEach(i):i(t),this}}Fe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);C.reduceDescriptors(Fe.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(o){this[r]=o}}});C.freezeMethods(Fe);function Qn(e,t){const r=this||xo,o=t||r,n=Fe.from(o.headers);let i=o.data;return C.forEach(e,function(a){i=a.call(r,i,n.normalize(),t?t.status:void 0)}),n.normalize(),i}function ud(e){return!!(e&&e.__CANCEL__)}function Or(e,t,r){Y.call(this,e??"canceled",Y.ERR_CANCELED,t,r),this.name="CanceledError"}C.inherits(Or,Y,{__CANCEL__:!0});function dd(e,t,r){const o=r.config.validateStatus;!r.status||!o||o(r.status)?e(r):t(new Y("Request failed with status code "+r.status,[Y.ERR_BAD_REQUEST,Y.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function n0(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function i0(e,t){e=e||10;const r=new Array(e),o=new Array(e);let n=0,i=0,s;return t=t!==void 0?t:1e3,function(c){const u=Date.now(),l=o[i];s||(s=u),r[n]=c,o[n]=u;let d=i,f=0;for(;d!==n;)f+=r[d++],d=d%e;if(n=(n+1)%e,n===i&&(i=(i+1)%e),u-s<t)return;const p=l&&u-l;return p?Math.round(f*1e3/p):void 0}}function s0(e,t){let r=0;const o=1e3/t;let n=null;return function(){const s=this===!0,a=Date.now();if(s||a-r>o)return n&&(clearTimeout(n),n=null),r=a,e.apply(null,arguments);n||(n=setTimeout(()=>(n=null,r=Date.now(),e.apply(null,arguments)),o-(a-r)))}}const ln=(e,t,r=3)=>{let o=0;const n=i0(50,250);return s0(i=>{const s=i.loaded,a=i.lengthComputable?i.total:void 0,c=s-o,u=n(c),l=s<=a;o=s;const d={loaded:s,total:a,progress:a?s/a:void 0,bytes:c,rate:u||void 0,estimated:u&&a&&l?(a-s)/u:void 0,event:i,lengthComputable:a!=null};d[t?"download":"upload"]=!0,e(d)},r)},a0=at.hasStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");let o;function n(i){let s=i;return t&&(r.setAttribute("href",s),s=r.href),r.setAttribute("href",s),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:r.pathname.charAt(0)==="/"?r.pathname:"/"+r.pathname}}return o=n(window.location.href),function(s){const a=C.isString(s)?n(s):s;return a.protocol===o.protocol&&a.host===o.host}}():function(){return function(){return!0}}(),c0=at.hasStandardBrowserEnv?{write(e,t,r,o,n,i){const s=[e+"="+encodeURIComponent(t)];C.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),C.isString(o)&&s.push("path="+o),C.isString(n)&&s.push("domain="+n),i===!0&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function l0(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function u0(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function fd(e,t){return e&&!l0(t)?u0(e,t):t}const Ya=e=>e instanceof Fe?{...e}:e;function cr(e,t){t=t||{};const r={};function o(u,l,d){return C.isPlainObject(u)&&C.isPlainObject(l)?C.merge.call({caseless:d},u,l):C.isPlainObject(l)?C.merge({},l):C.isArray(l)?l.slice():l}function n(u,l,d){if(C.isUndefined(l)){if(!C.isUndefined(u))return o(void 0,u,d)}else return o(u,l,d)}function i(u,l){if(!C.isUndefined(l))return o(void 0,l)}function s(u,l){if(C.isUndefined(l)){if(!C.isUndefined(u))return o(void 0,u)}else return o(void 0,l)}function a(u,l,d){if(d in t)return o(u,l);if(d in e)return o(void 0,u)}const c={url:i,method:i,data:i,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(u,l)=>n(Ya(u),Ya(l),!0)};return C.forEach(Object.keys(Object.assign({},e,t)),function(l){const d=c[l]||n,f=d(e[l],t[l],l);C.isUndefined(f)&&d!==a||(r[l]=f)}),r}const pd=e=>{const t=cr({},e);let{data:r,withXSRFToken:o,xsrfHeaderName:n,xsrfCookieName:i,headers:s,auth:a}=t;t.headers=s=Fe.from(s),t.url=ad(fd(t.baseURL,t.url),e.params,e.paramsSerializer),a&&s.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let c;if(C.isFormData(r)){if(at.hasStandardBrowserEnv||at.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if((c=s.getContentType())!==!1){const[u,...l]=c?c.split(";").map(d=>d.trim()).filter(Boolean):[];s.setContentType([u||"multipart/form-data",...l].join("; "))}}if(at.hasStandardBrowserEnv&&(o&&C.isFunction(o)&&(o=o(t)),o||o!==!1&&a0(t.url))){const u=n&&i&&c0.read(i);u&&s.set(n,u)}return t},d0=typeof XMLHttpRequest<"u",f0=d0&&function(e){return new Promise(function(r,o){const n=pd(e);let i=n.data;const s=Fe.from(n.headers).normalize();let{responseType:a}=n,c;function u(){n.cancelToken&&n.cancelToken.unsubscribe(c),n.signal&&n.signal.removeEventListener("abort",c)}let l=new XMLHttpRequest;l.open(n.method.toUpperCase(),n.url,!0),l.timeout=n.timeout;function d(){if(!l)return;const p=Fe.from("getAllResponseHeaders"in l&&l.getAllResponseHeaders()),b={data:!a||a==="text"||a==="json"?l.responseText:l.response,status:l.status,statusText:l.statusText,headers:p,config:e,request:l};dd(function(R){r(R),u()},function(R){o(R),u()},b),l=null}"onloadend"in l?l.onloadend=d:l.onreadystatechange=function(){!l||l.readyState!==4||l.status===0&&!(l.responseURL&&l.responseURL.indexOf("file:")===0)||setTimeout(d)},l.onabort=function(){l&&(o(new Y("Request aborted",Y.ECONNABORTED,n,l)),l=null)},l.onerror=function(){o(new Y("Network Error",Y.ERR_NETWORK,n,l)),l=null},l.ontimeout=function(){let m=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const b=n.transitional||cd;n.timeoutErrorMessage&&(m=n.timeoutErrorMessage),o(new Y(m,b.clarifyTimeoutError?Y.ETIMEDOUT:Y.ECONNABORTED,n,l)),l=null},i===void 0&&s.setContentType(null),"setRequestHeader"in l&&C.forEach(s.toJSON(),function(m,b){l.setRequestHeader(b,m)}),C.isUndefined(n.withCredentials)||(l.withCredentials=!!n.withCredentials),a&&a!=="json"&&(l.responseType=n.responseType),typeof n.onDownloadProgress=="function"&&l.addEventListener("progress",ln(n.onDownloadProgress,!0)),typeof n.onUploadProgress=="function"&&l.upload&&l.upload.addEventListener("progress",ln(n.onUploadProgress)),(n.cancelToken||n.signal)&&(c=p=>{l&&(o(!p||p.type?new Or(null,e,l):p),l.abort(),l=null)},n.cancelToken&&n.cancelToken.subscribe(c),n.signal&&(n.signal.aborted?c():n.signal.addEventListener("abort",c)));const f=n0(n.url);if(f&&at.protocols.indexOf(f)===-1){o(new Y("Unsupported protocol "+f+":",Y.ERR_BAD_REQUEST,e));return}l.send(i||null)})},p0=(e,t)=>{let r=new AbortController,o;const n=function(c){if(!o){o=!0,s();const u=c instanceof Error?c:this.reason;r.abort(u instanceof Y?u:new Or(u instanceof Error?u.message:u))}};let i=t&&setTimeout(()=>{n(new Y(`timeout ${t} of ms exceeded`,Y.ETIMEDOUT))},t);const s=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(c=>{c&&(c.removeEventListener?c.removeEventListener("abort",n):c.unsubscribe(n))}),e=null)};e.forEach(c=>c&&c.addEventListener&&c.addEventListener("abort",n));const{signal:a}=r;return a.unsubscribe=s,[a,()=>{i&&clearTimeout(i),i=null}]},g0=function*(e,t){let r=e.byteLength;if(!t||r<t){yield e;return}let o=0,n;for(;o<r;)n=o+t,yield e.slice(o,n),o=n},h0=async function*(e,t,r){for await(const o of e)yield*g0(ArrayBuffer.isView(o)?o:await r(String(o)),t)},Za=(e,t,r,o,n)=>{const i=h0(e,t,n);let s=0;return new ReadableStream({type:"bytes",async pull(a){const{done:c,value:u}=await i.next();if(c){a.close(),o();return}let l=u.byteLength;r&&r(s+=l),a.enqueue(new Uint8Array(u))},cancel(a){return o(a),i.return()}},{highWaterMark:2})},Xa=(e,t)=>{const r=e!=null;return o=>setTimeout(()=>t({lengthComputable:r,total:e,loaded:o}))},Dn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",gd=Dn&&typeof ReadableStream=="function",Ei=Dn&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),m0=gd&&(()=>{let e=!1;const t=new Request(at.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})(),Qa=64*1024,Oi=gd&&!!(()=>{try{return C.isReadableStream(new Response("").body)}catch{}})(),un={stream:Oi&&(e=>e.body)};Dn&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!un[t]&&(un[t]=C.isFunction(e[t])?r=>r[t]():(r,o)=>{throw new Y(`Response type '${t}' is not supported`,Y.ERR_NOT_SUPPORT,o)})})})(new Response);const b0=async e=>{if(e==null)return 0;if(C.isBlob(e))return e.size;if(C.isSpecCompliantForm(e))return(await new Request(e).arrayBuffer()).byteLength;if(C.isArrayBufferView(e))return e.byteLength;if(C.isURLSearchParams(e)&&(e=e+""),C.isString(e))return(await Ei(e)).byteLength},v0=async(e,t)=>{const r=C.toFiniteNumber(e.getContentLength());return r??b0(t)},y0=Dn&&(async e=>{let{url:t,method:r,data:o,signal:n,cancelToken:i,timeout:s,onDownloadProgress:a,onUploadProgress:c,responseType:u,headers:l,withCredentials:d="same-origin",fetchOptions:f}=pd(e);u=u?(u+"").toLowerCase():"text";let[p,m]=n||i||s?p0([n,i],s):[],b,_;const R=()=>{!b&&setTimeout(()=>{p&&p.unsubscribe()}),b=!0};let y;try{if(c&&m0&&r!=="get"&&r!=="head"&&(y=await v0(l,o))!==0){let B=new Request(t,{method:"POST",body:o,duplex:"half"}),N;C.isFormData(o)&&(N=B.headers.get("content-type"))&&l.setContentType(N),B.body&&(o=Za(B.body,Qa,Xa(y,ln(c)),null,Ei))}C.isString(d)||(d=d?"cors":"omit"),_=new Request(t,{...f,signal:p,method:r.toUpperCase(),headers:l.normalize().toJSON(),body:o,duplex:"half",withCredentials:d});let k=await fetch(_);const M=Oi&&(u==="stream"||u==="response");if(Oi&&(a||M)){const B={};["status","statusText","headers"].forEach(F=>{B[F]=k[F]});const N=C.toFiniteNumber(k.headers.get("content-length"));k=new Response(Za(k.body,Qa,a&&Xa(N,ln(a,!0)),M&&R,Ei),B)}u=u||"text";let U=await un[C.findKey(un,u)||"text"](k,e);return!M&&R(),m&&m(),await new Promise((B,N)=>{dd(B,N,{data:U,headers:Fe.from(k.headers),status:k.status,statusText:k.statusText,config:e,request:_})})}catch(k){throw R(),k&&k.name==="TypeError"&&/fetch/i.test(k.message)?Object.assign(new Y("Network Error",Y.ERR_NETWORK,e,_),{cause:k.cause||k}):Y.from(k,k&&k.code,e,_)}}),$i={http:Dm,xhr:f0,fetch:y0};C.forEach($i,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const ec=e=>`- ${e}`,C0=e=>C.isFunction(e)||e===null||e===!1,hd={getAdapter:e=>{e=C.isArray(e)?e:[e];const{length:t}=e;let r,o;const n={};for(let i=0;i<t;i++){r=e[i];let s;if(o=r,!C0(r)&&(o=$i[(s=String(r)).toLowerCase()],o===void 0))throw new Y(`Unknown adapter '${s}'`);if(o)break;n[s||"#"+i]=o}if(!o){const i=Object.entries(n).map(([a,c])=>`adapter ${a} `+(c===!1?"is not supported by the environment":"is not available in the build"));let s=t?i.length>1?`since :
`+i.map(ec).join(`
`):" "+ec(i[0]):"as no adapter specified";throw new Y("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return o},adapters:$i};function ei(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Or(null,e)}function tc(e){return ei(e),e.headers=Fe.from(e.headers),e.data=Qn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),hd.getAdapter(e.adapter||xo.adapter)(e).then(function(o){return ei(e),o.data=Qn.call(e,e.transformResponse,o),o.headers=Fe.from(o.headers),o},function(o){return ud(o)||(ei(e),o&&o.response&&(o.response.data=Qn.call(e,e.transformResponse,o.response),o.response.headers=Fe.from(o.response.headers))),Promise.reject(o)})}const md="1.7.2",xs={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{xs[e]=function(o){return typeof o===e||"a"+(t<1?"n ":" ")+e}});const rc={};xs.transitional=function(t,r,o){function n(i,s){return"[Axios v"+md+"] Transitional option '"+i+"'"+s+(o?". "+o:"")}return(i,s,a)=>{if(t===!1)throw new Y(n(s," has been removed"+(r?" in "+r:"")),Y.ERR_DEPRECATED);return r&&!rc[s]&&(rc[s]=!0,console.warn(n(s," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(i,s,a):!0}};function k0(e,t,r){if(typeof e!="object")throw new Y("options must be an object",Y.ERR_BAD_OPTION_VALUE);const o=Object.keys(e);let n=o.length;for(;n-- >0;){const i=o[n],s=t[i];if(s){const a=e[i],c=a===void 0||s(a,i,e);if(c!==!0)throw new Y("option "+i+" must be "+c,Y.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new Y("Unknown option "+i,Y.ERR_BAD_OPTION)}}const Bi={assertOptions:k0,validators:xs},Et=Bi.validators;class sr{constructor(t){this.defaults=t,this.interceptors={request:new qa,response:new qa}}async request(t,r){try{return await this._request(t,r)}catch(o){if(o instanceof Error){let n;Error.captureStackTrace?Error.captureStackTrace(n={}):n=new Error;const i=n.stack?n.stack.replace(/^.+\n/,""):"";try{o.stack?i&&!String(o.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(o.stack+=`
`+i):o.stack=i}catch{}}throw o}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=cr(this.defaults,r);const{transitional:o,paramsSerializer:n,headers:i}=r;o!==void 0&&Bi.assertOptions(o,{silentJSONParsing:Et.transitional(Et.boolean),forcedJSONParsing:Et.transitional(Et.boolean),clarifyTimeoutError:Et.transitional(Et.boolean)},!1),n!=null&&(C.isFunction(n)?r.paramsSerializer={serialize:n}:Bi.assertOptions(n,{encode:Et.function,serialize:Et.function},!0)),r.method=(r.method||this.defaults.method||"get").toLowerCase();let s=i&&C.merge(i.common,i[r.method]);i&&C.forEach(["delete","get","head","post","put","patch","common"],m=>{delete i[m]}),r.headers=Fe.concat(s,i);const a=[];let c=!0;this.interceptors.request.forEach(function(b){typeof b.runWhen=="function"&&b.runWhen(r)===!1||(c=c&&b.synchronous,a.unshift(b.fulfilled,b.rejected))});const u=[];this.interceptors.response.forEach(function(b){u.push(b.fulfilled,b.rejected)});let l,d=0,f;if(!c){const m=[tc.bind(this),void 0];for(m.unshift.apply(m,a),m.push.apply(m,u),f=m.length,l=Promise.resolve(r);d<f;)l=l.then(m[d++],m[d++]);return l}f=a.length;let p=r;for(d=0;d<f;){const m=a[d++],b=a[d++];try{p=m(p)}catch(_){b.call(this,_);break}}try{l=tc.call(this,p)}catch(m){return Promise.reject(m)}for(d=0,f=u.length;d<f;)l=l.then(u[d++],u[d++]);return l}getUri(t){t=cr(this.defaults,t);const r=fd(t.baseURL,t.url);return ad(r,t.params,t.paramsSerializer)}}C.forEach(["delete","get","head","options"],function(t){sr.prototype[t]=function(r,o){return this.request(cr(o||{},{method:t,url:r,data:(o||{}).data}))}});C.forEach(["post","put","patch"],function(t){function r(o){return function(i,s,a){return this.request(cr(a||{},{method:t,headers:o?{"Content-Type":"multipart/form-data"}:{},url:i,data:s}))}}sr.prototype[t]=r(),sr.prototype[t+"Form"]=r(!0)});class _s{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(i){r=i});const o=this;this.promise.then(n=>{if(!o._listeners)return;let i=o._listeners.length;for(;i-- >0;)o._listeners[i](n);o._listeners=null}),this.promise.then=n=>{let i;const s=new Promise(a=>{o.subscribe(a),i=a}).then(n);return s.cancel=function(){o.unsubscribe(i)},s},t(function(i,s,a){o.reason||(o.reason=new Or(i,s,a),r(o.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}static source(){let t;return{token:new _s(function(n){t=n}),cancel:t}}}function S0(e){return function(r){return e.apply(null,r)}}function x0(e){return C.isObject(e)&&e.isAxiosError===!0}const Ai={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ai).forEach(([e,t])=>{Ai[t]=e});function bd(e){const t=new sr(e),r=Ju(sr.prototype.request,t);return C.extend(r,sr.prototype,t,{allOwnKeys:!0}),C.extend(r,t,null,{allOwnKeys:!0}),r.create=function(n){return bd(cr(e,n))},r}const ge=bd(xo);ge.Axios=sr;ge.CanceledError=Or;ge.CancelToken=_s;ge.isCancel=ud;ge.VERSION=md;ge.toFormData=In;ge.AxiosError=Y;ge.Cancel=ge.CanceledError;ge.all=function(t){return Promise.all(t)};ge.spread=S0;ge.isAxiosError=x0;ge.mergeConfig=cr;ge.AxiosHeaders=Fe;ge.formToJSON=e=>ld(C.isHTMLForm(e)?new FormData(e):e);ge.getAdapter=hd.getAdapter;ge.HttpStatusCode=Ai;ge.default=ge;function uC(e){const t=new URLSearchParams(e);return`${cn}?${t}`}function _0(){return ge.get("/login/token")}function dC(){return ge.get("/api/meetings")}function fC(e){return ge.get(`/api/meetings/${e}`)}function lr(e){return e==null||e===""||Array.isArray(e)&&e.length===0||!(e instanceof Date)&&typeof e=="object"&&Object.keys(e).length===0}function Li(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){var r=Array.isArray(e),o=Array.isArray(t),n,i,s;if(r&&o){if(i=e.length,i!=t.length)return!1;for(n=i;n--!==0;)if(!Li(e[n],t[n]))return!1;return!0}if(r!=o)return!1;var a=e instanceof Date,c=t instanceof Date;if(a!=c)return!1;if(a&&c)return e.getTime()==t.getTime();var u=e instanceof RegExp,l=t instanceof RegExp;if(u!=l)return!1;if(u&&l)return e.toString()==t.toString();var d=Object.keys(e);if(i=d.length,i!==Object.keys(t).length)return!1;for(n=i;n--!==0;)if(!Object.prototype.hasOwnProperty.call(t,d[n]))return!1;for(n=i;n--!==0;)if(s=d[n],!Li(e[s],t[s]))return!1;return!0}return e!==e&&t!==t}function jn(e){return!!(e&&e.constructor&&e.call&&e.apply)}function Ae(e){return!lr(e)}function oc(e,t){if(!e||!t)return null;try{const r=e[t];if(Ae(r))return r}catch{}if(Object.keys(e).length){if(jn(t))return t(e);if(t.indexOf(".")===-1)return e[t];{let r=t.split("."),o=e;for(let n=0,i=r.length;n<i;++n){if(o==null)return null;o=o[r[n]]}return o}}return null}function pC(e,t,r){return r?oc(e,r)===oc(t,r):Li(e,t)}function Gt(e,t=!0){return e instanceof Object&&e.constructor===Object&&(t||Object.keys(e).length!==0)}function vt(e,...t){return jn(e)?e(...t):e}function ze(e,t=!0){return typeof e=="string"&&(t||e!=="")}function nt(e){return ze(e)?e.replace(/(-|_)/g,"").toLowerCase():e}function Rs(e,t="",r={}){const o=nt(t).split("."),n=o.shift();return n?Gt(e)?Rs(vt(e[Object.keys(e).find(i=>nt(i)===n)||""],r),o.join("."),r):void 0:vt(e,r)}function Ts(e,t=!0){return Array.isArray(e)&&(t||e.length!==0)}function vd(e){return Ae(e)&&!isNaN(e)}function St(e,t){if(t){const r=t.test(e);return t.lastIndex=0,r}return!1}function Zr(e){return e&&e.replace(/\/\*(?:(?!\*\/)[\s\S])*\*\/|[\r\n\t]+/g,"").replace(/ {2,}/g," ").replace(/ ([{:}]) /g,"$1").replace(/([;,]) /g,"$1").replace(/ !/g,"!").replace(/: /g,":")}function R0(e){return ze(e,!1)?e[0].toUpperCase()+e.slice(1):e}function yd(e){return ze(e)?e.replace(/(_)/g,"-").replace(/[A-Z]/g,(t,r)=>r===0?t:"-"+t.toLowerCase()).toLowerCase():e}function nc(e){return ze(e)?e.replace(/[A-Z]/g,(t,r)=>r===0?t:"."+t.toLowerCase()).toLowerCase():e}function ws(){const e=new Map;return{on(t,r){let o=e.get(t);return o?o.push(r):o=[r],e.set(t,o),this},off(t,r){let o=e.get(t);return o&&o.splice(o.indexOf(r)>>>0,1),this},emit(t,r){let o=e.get(t);o&&o.slice().map(n=>{n(r)})},clear(){e.clear()}}}var T0=Object.defineProperty,w0=Object.defineProperties,P0=Object.getOwnPropertyDescriptors,dn=Object.getOwnPropertySymbols,Cd=Object.prototype.hasOwnProperty,kd=Object.prototype.propertyIsEnumerable,ic=(e,t,r)=>t in e?T0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,jr=(e,t)=>{for(var r in t||(t={}))Cd.call(t,r)&&ic(e,r,t[r]);if(dn)for(var r of dn(t))kd.call(t,r)&&ic(e,r,t[r]);return e},ti=(e,t)=>w0(e,P0(t)),Mr=(e,t)=>{var r={};for(var o in e)Cd.call(e,o)&&t.indexOf(o)<0&&(r[o]=e[o]);if(e!=null&&dn)for(var o of dn(e))t.indexOf(o)<0&&kd.call(e,o)&&(r[o]=e[o]);return r},E0=ws(),ot=E0;function sc(e,t){Ts(e)?e.push(...t||[]):Gt(e)&&Object.assign(e,t)}function O0(e){return Gt(e)&&e.hasOwnProperty("value")&&e.hasOwnProperty("type")?e.value:e}function ac(e,t=""){return["opacity","z-index","line-height","font-weight","flex","flex-grow","flex-shrink","order"].some(o=>t.endsWith(o))?e:`${e}`.trim().split(" ").map(i=>vd(i)?`${i}px`:i).join(" ")}function $0(e){return e.replaceAll(/ /g,"").replace(/[^\w]/g,"-")}function Ii(e="",t=""){return $0(`${ze(e,!1)&&ze(t,!1)?`${e}-`:e}${t}`)}function Sd(e="",t=""){return`--${Ii(e,t)}`}function xd(e,t="",r="",o=[],n){if(ze(e)){const i=/{([^}]*)}/g,s=e.trim();if(St(s,i)){const a=s.replaceAll(i,l=>{const f=l.replace(/{|}/g,"").split(".").filter(p=>!o.some(m=>St(p,m)));return`var(${Sd(r,yd(f.join("-")))}${Ae(n)?`, ${n}`:""})`}),c=/(\d+\s+[\+\-\*\/]\s+\d+)/g,u=/var\([^)]+\)/g;return St(a.replace(u,"0"),c)?`calc(${a})`:a}return ac(s,t)}else if(vd(e))return ac(e,t)}function B0(e,t,r){ze(t,!1)&&e.push(`${t}:${r};`)}function zr(e,t){return e?`${e}{${t}}`:""}var gC=e=>{var t;const r=de.getTheme(),o=Di(r,e,void 0,"variable"),n=(t=o.match(/--[\w-]+/g))==null?void 0:t[0],i=Di(r,e,void 0,"value");return{name:n,variable:o,value:i}},ri=(...e)=>Di(de.getTheme(),...e),Di=(e={},t,r,o="variable")=>{if(t){const{variable:n,options:i}=de.defaults||{},{prefix:s,transform:a}=(e==null?void 0:e.options)||i||{},u=St(t,/{([^}]*)}/g)?t:`{${t}}`;return o==="value"||a==="strict"?de.getTokenValue(t):xd(u,void 0,s,[n.excludedKeyRegex],r)}return""};function A0(e,t={}){const r=de.defaults.variable,{prefix:o=r.prefix,selector:n=r.selector,excludedKeyRegex:i=r.excludedKeyRegex}=t,s=(u,l="")=>Object.entries(u).reduce((d,[f,p])=>{const m=St(f,i)?Ii(l):Ii(l,yd(f)),b=O0(p);if(Gt(b)){const{variables:_,tokens:R}=s(b,m);sc(d.tokens,R),sc(d.variables,_)}else d.tokens.push((o?m.replace(`${o}-`,""):m).replaceAll("-",".")),B0(d.variables,Sd(m),xd(b,m,o,[i]));return d},{variables:[],tokens:[]}),{variables:a,tokens:c}=s(e,o);return{value:a,tokens:c,declarations:a.join(""),css:zr(n,a.join(""))}}var rt={regex:{rules:{class:{pattern:/^\.([a-zA-Z][\w-]*)$/,resolve(e){return{type:"class",selector:e,matched:this.pattern.test(e.trim())}}},attr:{pattern:/^\[(.*)\]$/,resolve(e){return{type:"attr",selector:`:root${e}`,matched:this.pattern.test(e.trim())}}},media:{pattern:/^@media (.*)$/,resolve(e){return{type:"media",selector:`${e}{:root{[CSS]}}`,matched:this.pattern.test(e.trim())}}},system:{pattern:/^system$/,resolve(e){return{type:"system",selector:"@media (prefers-color-scheme: dark){:root{[CSS]}}",matched:this.pattern.test(e.trim())}}},custom:{resolve(e){return{type:"custom",selector:e,matched:!0}}}},resolve(e){const t=Object.keys(this.rules).filter(r=>r!=="custom").map(r=>this.rules[r]);return[e].flat().map(r=>{var o;return(o=t.map(n=>n.resolve(r)).find(n=>n.matched))!=null?o:this.rules.custom.resolve(r)})}},_toVariables(e,t){return A0(e,{prefix:t==null?void 0:t.prefix})},getCommon({name:e="",theme:t={},params:r,set:o,defaults:n}){var i,s,a,c;const{preset:u,options:l}=t;let d,f,p,m;if(Ae(u)){const{primitive:b,semantic:_}=u,R=_||{},{colorScheme:y}=R,k=Mr(R,["colorScheme"]),M=y||{},{dark:U}=M,B=Mr(M,["dark"]),N=Ae(b)?this._toVariables({primitive:b},l):{},F=Ae(k)?this._toVariables({semantic:k},l):{},E=Ae(B)?this._toVariables({light:B},l):{},V=Ae(U)?this._toVariables({dark:U},l):{},[K,A]=[(i=N.declarations)!=null?i:"",N.tokens],[ee,me]=[(s=F.declarations)!=null?s:"",F.tokens||[]],[Se,X]=[(a=E.declarations)!=null?a:"",E.tokens||[]],[ne,te]=[(c=V.declarations)!=null?c:"",V.tokens||[]];d=this.transformCSS(e,K,"light","variable",l,o,n),f=A;const He=this.transformCSS(e,`${ee}${Se}color-scheme:light`,"light","variable",l,o,n),De=this.transformCSS(e,`${ne}color-scheme:dark`,"dark","variable",l,o,n);p=`${He}${De}`,m=[...new Set([...me,...X,...te])]}return{primitive:{css:d,tokens:f},semantic:{css:p,tokens:m}}},getPreset({name:e="",preset:t={},options:r,params:o,set:n,defaults:i,selector:s}){var a,c,u;const l=e.replace("-directive",""),d=t,{colorScheme:f}=d,p=Mr(d,["colorScheme"]),m=f||{},{dark:b}=m,_=Mr(m,["dark"]),R=Ae(p)?this._toVariables({[l]:p},r):{},y=Ae(_)?this._toVariables({[l]:_},r):{},k=Ae(b)?this._toVariables({[l]:b},r):{},[M,U]=[(a=R.declarations)!=null?a:"",R.tokens||[]],[B,N]=[(c=y.declarations)!=null?c:"",y.tokens||[]],[F,E]=[(u=k.declarations)!=null?u:"",k.tokens||[]],V=[...new Set([...U,...N,...E])],K=this.transformCSS(l,`${M}${B}`,"light","variable",r,n,i,s),A=this.transformCSS(l,F,"dark","variable",r,n,i,s);return{css:`${K}${A}`,tokens:V}},getPresetC({name:e="",theme:t={},params:r,set:o,defaults:n}){var i;const{preset:s,options:a}=t,c=(i=s==null?void 0:s.components)==null?void 0:i[e];return this.getPreset({name:e,preset:c,options:a,params:r,set:o,defaults:n})},getPresetD({name:e="",theme:t={},params:r,set:o,defaults:n}){var i;const s=e.replace("-directive",""),{preset:a,options:c}=t,u=(i=a==null?void 0:a.directives)==null?void 0:i[s];return this.getPreset({name:s,preset:u,options:c,params:r,set:o,defaults:n})},getColorSchemeOption(e,t){var r;return this.regex.resolve((r=e.darkModeSelector)!=null?r:t.options.darkModeSelector)},getLayerOrder(e,t={},r,o){const{cssLayer:n}=t;return n?`@layer ${vt(n.order||"primeui",r)}`:""},getCommonStyleSheet({name:e="",theme:t={},params:r,props:o={},set:n,defaults:i}){const s=this.getCommon({name:e,theme:t,params:r,set:n,defaults:i}),a=Object.entries(o).reduce((c,[u,l])=>c.push(`${u}="${l}"`)&&c,[]).join(" ");return Object.entries(s||{}).reduce((c,[u,l])=>{if(l!=null&&l.css){const d=Zr(l==null?void 0:l.css),f=`${u}-variables`;c.push(`<style type="text/css" data-primevue-style-id="${f}" ${a}>${d}</style>`)}return c},[]).join("")},getStyleSheet({name:e="",theme:t={},params:r,props:o={},set:n,defaults:i}){var s;const a={name:e,theme:t,params:r,set:n,defaults:i},c=(s=e.includes("-directive")?this.getPresetD(a):this.getPresetC(a))==null?void 0:s.css,u=Object.entries(o).reduce((l,[d,f])=>l.push(`${d}="${f}"`)&&l,[]).join(" ");return c?`<style type="text/css" data-primevue-style-id="${e}-variables" ${u}>${Zr(c)}</style>`:""},createTokens(e={},t,r="",o="",n={}){return Object.entries(e).forEach(([i,s])=>{const a=St(i,t.variable.excludedKeyRegex)?r:r?`${r}.${nc(i)}`:nc(i),c=o?`${o}.${i}`:i;Gt(s)?this.createTokens(s,t,a,c,n):(n[a]||(n[a]={paths:[],computed(u,l={}){if(u){const d=this.paths.find(f=>f.scheme===u)||this.paths.find(f=>f.scheme==="none");return d==null?void 0:d.computed(u,l.binding)}return this.paths.map(d=>d.computed(d.scheme,l[d.scheme]))}}),n[a].paths.push({path:c,value:s,scheme:c.includes("colorScheme.light")?"light":c.includes("colorScheme.dark")?"dark":"none",computed(u,l={}){const d=/{([^}]*)}/g;let f=s;if(l.name=this.path,l.binding||(l.binding={}),St(s,d)){const m=s.trim().replaceAll(d,R=>{var y,k;const M=R.replace(/{|}/g,"");return(k=(y=n[M])==null?void 0:y.computed(u,l))==null?void 0:k.value}),b=/(\d+\w*\s+[\+\-\*\/]\s+\d+\w*)/g,_=/var\([^)]+\)/g;f=St(m.replace(_,"0"),b)?`calc(${m})`:m}return lr(l.binding)&&delete l.binding,{colorScheme:u,path:this.path,paths:l,value:f.includes("undefined")?void 0:f}}}))}),n},getTokenValue(e,t,r){var o;const i=(c=>c.split(".").filter(l=>!St(l.toLowerCase(),r.variable.excludedKeyRegex)).join("."))(t),s=t.includes("colorScheme.light")?"light":t.includes("colorScheme.dark")?"dark":void 0,a=[(o=e[i])==null?void 0:o.computed(s)].flat().filter(c=>c);return a.length===1?a[0].value:a.reduce((c={},u)=>{const l=u,{colorScheme:d}=l,f=Mr(l,["colorScheme"]);return c[d]=f,c},void 0)},transformCSS(e,t,r,o,n={},i,s,a){if(Ae(t)){const{cssLayer:c}=n;if(o!=="style"){const u=this.getColorSchemeOption(n,s),l=a?zr(a,t):t;t=r==="dark"?u.reduce((d,{selector:f})=>(Ae(f)&&(d+=f.includes("[CSS]")?f.replace("[CSS]",l):zr(f,l)),d),""):zr(a??":root",t)}if(c){const u={name:"primeui",order:"primeui"};Gt(c)&&(u.name=vt(c.name,{name:e,type:o})),Ae(u.name)&&(t=zr(`@layer ${u.name}`,t),i==null||i.layerNames(u.name))}return t}return""}},de={defaults:{variable:{prefix:"p",selector:":root",excludedKeyRegex:/^(primitive|semantic|components|directives|variables|colorscheme|light|dark|common|root|states)$/gi},options:{prefix:"p",darkModeSelector:"system",cssLayer:!1}},_theme:void 0,_layerNames:new Set,_loadedStyleNames:new Set,_loadingStyles:new Set,_tokens:{},update(e={}){const{theme:t}=e;t&&(this._theme=ti(jr({},t),{options:jr(jr({},this.defaults.options),t.options)}),this._tokens=rt.createTokens(this.preset,this.defaults),this.clearLoadedStyleNames())},get theme(){return this._theme},get preset(){var e;return((e=this.theme)==null?void 0:e.preset)||{}},get options(){var e;return((e=this.theme)==null?void 0:e.options)||{}},get tokens(){return this._tokens},getTheme(){return this.theme},setTheme(e){this.update({theme:e}),ot.emit("theme:change",e)},getPreset(){return this.preset},setPreset(e){this._theme=ti(jr({},this.theme),{preset:e}),this._tokens=rt.createTokens(e,this.defaults),this.clearLoadedStyleNames(),ot.emit("preset:change",e),ot.emit("theme:change",this.theme)},getOptions(){return this.options},setOptions(e){this._theme=ti(jr({},this.theme),{options:e}),this.clearLoadedStyleNames(),ot.emit("options:change",e),ot.emit("theme:change",this.theme)},getLayerNames(){return[...this._layerNames]},setLayerNames(e){this._layerNames.add(e)},getLoadedStyleNames(){return this._loadedStyleNames},isStyleNameLoaded(e){return this._loadedStyleNames.has(e)},setLoadedStyleName(e){this._loadedStyleNames.add(e)},deleteLoadedStyleName(e){this._loadedStyleNames.delete(e)},clearLoadedStyleNames(){this._loadedStyleNames.clear()},getTokenValue(e){return rt.getTokenValue(this.tokens,e,this.defaults)},getCommon(e="",t){return rt.getCommon({name:e,theme:this.theme,params:t,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},getComponent(e="",t){const r={name:e,theme:this.theme,params:t,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return rt.getPresetC(r)},getDirective(e="",t){const r={name:e,theme:this.theme,params:t,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return rt.getPresetD(r)},getCustomPreset(e="",t,r,o){const n={name:e,preset:t,options:this.options,selector:r,params:o,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}};return rt.getPreset(n)},getLayerOrderCSS(e=""){return rt.getLayerOrder(e,this.options,{names:this.getLayerNames()},this.defaults)},transformCSS(e="",t,r="style",o){return rt.transformCSS(e,t,o,r,this.options,{layerNames:this.setLayerNames.bind(this)},this.defaults)},getCommonStyleSheet(e="",t,r={}){return rt.getCommonStyleSheet({name:e,theme:this.theme,params:t,props:r,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},getStyleSheet(e,t,r={}){return rt.getStyleSheet({name:e,theme:this.theme,params:t,props:r,defaults:this.defaults,set:{layerNames:this.setLayerNames.bind(this)}})},onStyleMounted(e){this._loadingStyles.add(e)},onStyleUpdated(e){this._loadingStyles.add(e)},onStyleLoaded(e,{name:t}){this._loadingStyles.size&&(this._loadingStyles.delete(t),ot.emit(`theme:${t}:load`,e),!this._loadingStyles.size&&ot.emit("theme:load"))}};function _d(e,t){return e?e.classList?e.classList.contains(t):new RegExp("(^| )"+t+"( |$)","gi").test(e.className):!1}function Rd(e,t){if(e&&t){const r=o=>{_d(e,o)||(e.classList?e.classList.add(o):e.className+=" "+o)};[t].flat().filter(Boolean).forEach(o=>o.split(" ").forEach(r))}}function cc(e){for(const t of document==null?void 0:document.styleSheets)try{for(const r of t==null?void 0:t.cssRules)for(const o of r==null?void 0:r.style)if(e.test(o))return{name:o,value:r.style.getPropertyValue(o).trim()}}catch{}return null}function Yo(e,t){if(e&&t){const r=o=>{e.classList?e.classList.remove(o):e.className=e.className.replace(new RegExp("(^|\\b)"+o.split(" ").join("|")+"(\\b|$)","gi")," ")};[t].flat().filter(Boolean).forEach(o=>o.split(" ").forEach(r))}}function L0(e){let t={width:0,height:0};return e&&(e.style.visibility="hidden",e.style.display="block",t.width=e.offsetWidth,t.height=e.offsetHeight,e.style.display="none",e.style.visibility="visible"),t}function Td(){let e=window,t=document,r=t.documentElement,o=t.getElementsByTagName("body")[0],n=e.innerWidth||r.clientWidth||o.clientWidth,i=e.innerHeight||r.clientHeight||o.clientHeight;return{width:n,height:i}}function wd(){let e=document.documentElement;return(window.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}function Pd(){let e=document.documentElement;return(window.pageYOffset||e.scrollTop)-(e.clientTop||0)}function hC(e,t,r=!0){var o,n,i,s;if(e){const a=e.offsetParent?{width:e.offsetWidth,height:e.offsetHeight}:L0(e),c=a.height,u=a.width,l=t.offsetHeight,d=t.offsetWidth,f=t.getBoundingClientRect(),p=Pd(),m=wd(),b=Td();let _,R,y="top";f.top+l+c>b.height?(_=f.top+p-c,y="bottom",_<0&&(_=p)):_=l+f.top+p,f.left+u>b.width?R=Math.max(0,f.left+m+d-u):R=f.left+m,e.style.top=_+"px",e.style.left=R+"px",e.style.transformOrigin=y,r&&(e.style.marginTop=y==="bottom"?`calc(${(n=(o=cc(/-anchor-gutter$/))==null?void 0:o.value)!=null?n:"2px"} * -1)`:(s=(i=cc(/-anchor-gutter$/))==null?void 0:i.value)!=null?s:"")}}function mC(e,t){e&&(typeof t=="string"?e.style.cssText=t:Object.entries(t||{}).forEach(([r,o])=>e.style[r]=o))}function At(e,t){return e instanceof HTMLElement?e.offsetWidth:0}function $r(e){return typeof HTMLElement=="object"?e instanceof HTMLElement:e&&typeof e=="object"&&e!==null&&e.nodeType===1&&typeof e.nodeName=="string"}function fn(e,t={}){if($r(e)){const r=(o,n)=>{var i,s;const a=(i=e==null?void 0:e.$attrs)!=null&&i[o]?[(s=e==null?void 0:e.$attrs)==null?void 0:s[o]]:[];return[n].flat().reduce((c,u)=>{if(u!=null){const l=typeof u;if(l==="string"||l==="number")c.push(u);else if(l==="object"){const d=Array.isArray(u)?r(o,u):Object.entries(u).map(([f,p])=>o==="style"&&(p||p===0)?`${f.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}:${p}`:p?f:void 0);c=d.length?c.concat(d.filter(f=>!!f)):c}}return c},a)};Object.entries(t).forEach(([o,n])=>{if(n!=null){const i=o.match(/^on(.+)/);i?e.addEventListener(i[1].toLowerCase(),n):o==="p-bind"?fn(e,n):(n=o==="class"?[...new Set(r("class",n))].join(" ").trim():o==="style"?r("style",n).join(";").trim():n,(e.$attrs=e.$attrs||{})&&(e.$attrs[o]=n),e.setAttribute(o,n))}})}}function Zo(e,t={},...r){if(e){const o=document.createElement(e);return fn(o,t),o.append(...r),o}}function I0(e,t){if(e){e.style.opacity="0";let r=+new Date,o="0",n=function(){o=`${+e.style.opacity+(new Date().getTime()-r)/t}`,e.style.opacity=o,r=+new Date,+o<1&&(window.requestAnimationFrame&&requestAnimationFrame(n)||setTimeout(n,16))};n()}}function D0(e,t){return $r(e)?Array.from(e.querySelectorAll(t)):[]}function pn(e,t){return $r(e)?e.matches(t)?e:e.querySelector(t):null}function bC(e,t){e&&document.activeElement!==e&&e.focus(t)}function Qt(e,t){if($r(e)){const r=e.getAttribute(t);return isNaN(r)?r==="true"||r==="false"?r==="true":r:+r}}function Ed(e,t=""){let r=D0(e,`button:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [href][clientHeight][clientWidth]:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            input:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            select:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            textarea:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [tabIndex]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [contenteditable]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t}`),o=[];for(let n of r)getComputedStyle(n).display!="none"&&getComputedStyle(n).visibility!="hidden"&&o.push(n);return o}function vC(e,t){const r=Ed(e,t);return r.length>0?r[0]:null}function lc(e){if(e){let t=e.offsetHeight,r=getComputedStyle(e);return t-=parseFloat(r.paddingTop)+parseFloat(r.paddingBottom)+parseFloat(r.borderTopWidth)+parseFloat(r.borderBottomWidth),t}return 0}function Od(e){if(e){let t=e.parentNode;return t&&t instanceof ShadowRoot&&t.host&&(t=t.host),t}return null}function yC(e,t){const r=Ed(e,t);return r.length>0?r[r.length-1]:null}function j0(e){if(e){let t=e.getBoundingClientRect();return{top:t.top+(window.pageYOffset||document.documentElement.scrollTop||document.body.scrollTop||0),left:t.left+(window.pageXOffset||document.documentElement.scrollLeft||document.body.scrollLeft||0)}}return{top:"auto",left:"auto"}}function Lt(e,t){return e?e.offsetHeight:0}function $d(e,t=[]){const r=Od(e);return r===null?t:$d(r,t.concat([r]))}function M0(e){let t=[];if(e){let r=$d(e);const o=/(auto|scroll)/,n=i=>{try{let s=window.getComputedStyle(i,null);return o.test(s.getPropertyValue("overflow"))||o.test(s.getPropertyValue("overflowX"))||o.test(s.getPropertyValue("overflowY"))}catch{return!1}};for(let i of r){let s=i.nodeType===1&&i.dataset.scrollselectors;if(s){let a=s.split(",");for(let c of a){let u=pn(i,c);u&&n(u)&&t.push(u)}}i.nodeType!==9&&n(i)&&t.push(i)}}return t}function Bd(e){return!!(e!==null&&typeof e<"u"&&e.nodeName&&Od(e))}function uc(e){if(e){let t=e.offsetWidth,r=getComputedStyle(e);return t-=parseFloat(r.paddingLeft)+parseFloat(r.paddingRight)+parseFloat(r.borderLeftWidth)+parseFloat(r.borderRightWidth),t}return 0}function Ad(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}function CC(e,t=""){return $r(e)?e.matches(`button:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [href][clientHeight][clientWidth]:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            input:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            select:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            textarea:not([tabindex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [tabIndex]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t},
            [contenteditable]:not([tabIndex = "-1"]):not([disabled]):not([style*="display:none"]):not([hidden])${t}`):!1}function N0(){return"ontouchstart"in window||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0}function Ld(e,t="",r){$r(e)&&r!==null&&r!==void 0&&e.setAttribute(t,r)}var Ft={_loadedStyleNames:new Set,getLoadedStyleNames:function(){return this._loadedStyleNames},isStyleNameLoaded:function(t){return this._loadedStyleNames.has(t)},setLoadedStyleName:function(t){this._loadedStyleNames.add(t)},deleteLoadedStyleName:function(t){this._loadedStyleNames.delete(t)},clearLoadedStyleNames:function(){this._loadedStyleNames.clear()}};function lo(e){"@babel/helpers - typeof";return lo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},lo(e)}function dc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function fc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?dc(Object(r),!0).forEach(function(o){F0(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dc(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function F0(e,t,r){return(t=z0(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function z0(e){var t=U0(e,"string");return lo(t)=="symbol"?t:t+""}function U0(e,t){if(lo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(lo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function H0(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;ms()?ls(e):t?e():kn(e)}var V0=0;function W0(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=bt(!1),o=bt(e),n=bt(null),i=Ad()?window.document:void 0,s=t.document,a=s===void 0?i:s,c=t.immediate,u=c===void 0?!0:c,l=t.manual,d=l===void 0?!1:l,f=t.name,p=f===void 0?"style_".concat(++V0):f,m=t.id,b=m===void 0?void 0:m,_=t.media,R=_===void 0?void 0:_,y=t.nonce,k=y===void 0?void 0:y,M=t.first,U=M===void 0?!1:M,B=t.onMounted,N=B===void 0?void 0:B,F=t.onUpdated,E=F===void 0?void 0:F,V=t.onLoad,K=V===void 0?void 0:V,A=t.props,ee=A===void 0?{}:A,me=function(){},Se=function(te){var He=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(a){var De=fc(fc({},ee),He),$e=De.name||p,Oe=De.id||b,Rt=De.nonce||k;n.value=a.querySelector('style[data-primevue-style-id="'.concat($e,'"]'))||a.getElementById(Oe)||a.createElement("style"),n.value.isConnected||(o.value=te||e,fn(n.value,{type:"text/css",id:Oe,media:R,nonce:Rt}),U?a.head.prepend(n.value):a.head.appendChild(n.value),Ld(n.value,"data-primevue-style-id",$e),fn(n.value,De),n.value.onload=function(dt){return K==null?void 0:K(dt,{name:$e})},N==null||N($e)),!r.value&&(me=mt(o,function(dt){n.value.textContent=dt,E==null||E($e)},{immediate:!0}),r.value=!0)}},X=function(){!a||!r.value||(me(),Bd(n.value)&&a.head.removeChild(n.value),r.value=!1)};return u&&!d&&H0(Se),{id:b,name:p,el:n,css:o,unload:X,load:Se,isLoaded:es(r)}}function uo(e){"@babel/helpers - typeof";return uo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},uo(e)}function pc(e,t){return J0(e)||q0(e,t)||K0(e,t)||G0()}function G0(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function K0(e,t){if(e){if(typeof e=="string")return gc(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?gc(e,t):void 0}}function gc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function q0(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var o,n,i,s,a=[],c=!0,u=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(o=i.call(r)).done)&&(a.push(o.value),a.length!==t);c=!0);}catch(l){u=!0,n=l}finally{try{if(!c&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return a}}function J0(e){if(Array.isArray(e))return e}function hc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function oi(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?hc(Object(r),!0).forEach(function(o){Y0(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hc(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function Y0(e,t,r){return(t=Z0(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Z0(e){var t=X0(e,"string");return uo(t)=="symbol"?t:t+""}function X0(e,t){if(uo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(uo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Q0=function(t){var r=t.dt;return`
* {
    box-sizing: border-box;
}

/* Non vue overlay animations */
.p-connected-overlay {
    opacity: 0;
    transform: scaleY(0.8);
    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1),
        opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.p-connected-overlay-visible {
    opacity: 1;
    transform: scaleY(1);
}

.p-connected-overlay-hidden {
    opacity: 0;
    transform: scaleY(1);
    transition: opacity 0.1s linear;
}

/* Vue based overlay animations */
.p-connected-overlay-enter-from {
    opacity: 0;
    transform: scaleY(0.8);
}

.p-connected-overlay-leave-to {
    opacity: 0;
}

.p-connected-overlay-enter-active {
    transition: transform 0.12s cubic-bezier(0, 0, 0.2, 1),
        opacity 0.12s cubic-bezier(0, 0, 0.2, 1);
}

.p-connected-overlay-leave-active {
    transition: opacity 0.1s linear;
}

/* Toggleable Content */
.p-toggleable-content-enter-from,
.p-toggleable-content-leave-to {
    max-height: 0;
}

.p-toggleable-content-enter-to,
.p-toggleable-content-leave-from {
    max-height: 1000px;
}

.p-toggleable-content-leave-active {
    overflow: hidden;
    transition: max-height 0.45s cubic-bezier(0, 1, 0, 1);
}

.p-toggleable-content-enter-active {
    overflow: hidden;
    transition: max-height 1s ease-in-out;
}

.p-disabled,
.p-disabled * {
    cursor: default;
    pointer-events: none;
    user-select: none;
}

.p-disabled,
.p-component:disabled {
    opacity: `.concat(r("disabled.opacity"),`;
}

.pi {
    font-size: `).concat(r("icon.size"),`;
}

.p-icon {
    width: `).concat(r("icon.size"),`;
    height: `).concat(r("icon.size"),`;
}

.p-overlay-mask {
    background: `).concat(r("mask.background"),`;
    color: `).concat(r("mask.color"),`;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.p-overlay-mask-enter {
    animation: p-overlay-mask-enter-animation `).concat(r("mask.transition.duration"),` forwards;
}

.p-overlay-mask-leave {
    animation: p-overlay-mask-leave-animation `).concat(r("mask.transition.duration"),` forwards;
}

@keyframes p-overlay-mask-enter-animation {
    from {
        background: transparent;
    }
    to {
        background: `).concat(r("mask.background"),`;
    }
}
@keyframes p-overlay-mask-leave-animation {
    from {
        background: `).concat(r("mask.background"),`;
    }
    to {
        background: transparent;
    }
}
`)},eb=function(t){var r=t.dt;return`
.p-hidden-accessible {
    border: 0;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
}

.p-hidden-accessible input,
.p-hidden-accessible select {
    transform: scale(0);
}

.p-overflow-hidden {
    overflow: hidden;
    padding-right: `.concat(r("scrollbar.width"),`;
}
`)},tb={},rb={},Ce={name:"base",css:eb,theme:Q0,classes:tb,inlineStyles:rb,load:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(i){return i},n=o(vt(t,{dt:ri}));return n?W0(Zr(n),oi({name:this.name},r)):{}},loadCSS:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return this.load(this.css,t)},loadTheme:function(){var t=this,r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return this.load(this.theme,r,function(o){return de.transformCSS(r.name||t.name,o)})},getCommonTheme:function(t){return de.getCommon(this.name,t)},getComponentTheme:function(t){return de.getComponent(this.name,t)},getDirectiveTheme:function(t){return de.getDirective(this.name,t)},getPresetTheme:function(t,r,o){return de.getCustomPreset(this.name,t,r,o)},getLayerOrderThemeCSS:function(){return de.getLayerOrderCSS(this.name)},getStyleSheet:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.css){var o=vt(this.css,{dt:ri}),n=Zr("".concat(o).concat(t)),i=Object.entries(r).reduce(function(s,a){var c=pc(a,2),u=c[0],l=c[1];return s.push("".concat(u,'="').concat(l,'"'))&&s},[]).join(" ");return'<style type="text/css" data-primevue-style-id="'.concat(this.name,'" ').concat(i,">").concat(n,"</style>")}return""},getCommonThemeStyleSheet:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return de.getCommonStyleSheet(this.name,t,r)},getThemeStyleSheet:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=[de.getStyleSheet(this.name,t,r)];if(this.theme){var n=this.name==="base"?"global-style":"".concat(this.name,"-style"),i=vt(this.theme,{dt:ri}),s=Zr(de.transformCSS(n,i)),a=Object.entries(r).reduce(function(c,u){var l=pc(u,2),d=l[0],f=l[1];return c.push("".concat(d,'="').concat(f,'"'))&&c},[]).join(" ");o.push('<style type="text/css" data-primevue-style-id="'.concat(n,'" ').concat(a,">").concat(s,"</style>"))}return o.join("")},extend:function(t){return oi(oi({},this),{},{css:void 0,theme:void 0},t)}},Io={};function ob(e="pui_id_"){return Io.hasOwnProperty(e)||(Io[e]=0),Io[e]++,`${e}${Io[e]}`}function nb(){let e=[];const t=(s,a,c=999)=>{const u=n(s,a,c),l=u.value+(u.key===s?0:c)+1;return e.push({key:s,value:l}),l},r=s=>{e=e.filter(a=>a.value!==s)},o=(s,a)=>n(s).value,n=(s,a,c=0)=>[...e].reverse().find(u=>!0)||{key:s,value:c},i=s=>s&&parseInt(s.style.zIndex,10)||0;return{get:i,set:(s,a,c)=>{a&&(a.style.zIndex=String(t(s,!0,c)))},clear:s=>{s&&(r(i(s)),s.style.zIndex="")},getCurrent:s=>o(s)}}var Xr=nb();function fo(e){"@babel/helpers - typeof";return fo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},fo(e)}function ib(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function sb(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,cb(o.key),o)}}function ab(e,t,r){return t&&sb(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function cb(e){var t=lb(e,"string");return fo(t)=="symbol"?t:t+""}function lb(e,t){if(fo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t);if(fo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}var ub=function(){function e(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:function(){};ib(this,e),this.element=t,this.listener=r}return ab(e,[{key:"bindScrollListener",value:function(){this.scrollableParents=M0(this.element);for(var r=0;r<this.scrollableParents.length;r++)this.scrollableParents[r].addEventListener("scroll",this.listener)}},{key:"unbindScrollListener",value:function(){if(this.scrollableParents)for(var r=0;r<this.scrollableParents.length;r++)this.scrollableParents[r].removeEventListener("scroll",this.listener)}},{key:"destroy",value:function(){this.unbindScrollListener(),this.element=null,this.listener=null,this.scrollableParents=null}}])}();function rr(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"pv_id_";return ob(e)}var mc=Ce.extend({name:"common"});function po(e){"@babel/helpers - typeof";return po=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},po(e)}function db(e){return jd(e)||fb(e)||Dd(e)||Id()}function fb(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Do(e,t){return jd(e)||pb(e,t)||Dd(e,t)||Id()}function Id(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Dd(e,t){if(e){if(typeof e=="string")return bc(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?bc(e,t):void 0}}function bc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function pb(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var o,n,i,s,a=[],c=!0,u=!1;try{if(i=(r=r.call(e)).next,t===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(o=i.call(r)).done)&&(a.push(o.value),a.length!==t);c=!0);}catch(l){u=!0,n=l}finally{try{if(!c&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return a}}function jd(e){if(Array.isArray(e))return e}function vc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function re(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?vc(Object(r),!0).forEach(function(o){Xo(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vc(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function Xo(e,t,r){return(t=gb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gb(e){var t=hb(e,"string");return po(t)=="symbol"?t:t+""}function hb(e,t){if(po(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(po(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var _o={name:"BaseComponent",props:{pt:{type:Object,default:void 0},ptOptions:{type:Object,default:void 0},unstyled:{type:Boolean,default:void 0},dt:{type:Object,default:void 0}},inject:{$parentInstance:{default:void 0}},watch:{isUnstyled:{immediate:!0,handler:function(t){t||(this._loadCoreStyles(),this._themeChangeListener(this._loadCoreStyles))}},dt:{immediate:!0,handler:function(t){var r=this;t?(this._loadScopedThemeStyles(t),this._themeChangeListener(function(){return r._loadScopedThemeStyles(t)})):this._unloadScopedThemeStyles()}}},scopedStyleEl:void 0,rootEl:void 0,beforeCreate:function(){var t,r,o,n,i,s,a,c,u,l,d,f=(t=this.pt)===null||t===void 0?void 0:t._usept,p=f?(r=this.pt)===null||r===void 0||(r=r.originalValue)===null||r===void 0?void 0:r[this.$.type.name]:void 0,m=f?(o=this.pt)===null||o===void 0||(o=o.value)===null||o===void 0?void 0:o[this.$.type.name]:this.pt;(n=m||p)===null||n===void 0||(n=n.hooks)===null||n===void 0||(i=n.onBeforeCreate)===null||i===void 0||i.call(n);var b=(s=this.$primevueConfig)===null||s===void 0||(s=s.pt)===null||s===void 0?void 0:s._usept,_=b?(a=this.$primevue)===null||a===void 0||(a=a.config)===null||a===void 0||(a=a.pt)===null||a===void 0?void 0:a.originalValue:void 0,R=b?(c=this.$primevue)===null||c===void 0||(c=c.config)===null||c===void 0||(c=c.pt)===null||c===void 0?void 0:c.value:(u=this.$primevue)===null||u===void 0||(u=u.config)===null||u===void 0?void 0:u.pt;(l=R||_)===null||l===void 0||(l=l[this.$.type.name])===null||l===void 0||(l=l.hooks)===null||l===void 0||(d=l.onBeforeCreate)===null||d===void 0||d.call(l)},created:function(){this._hook("onCreated")},beforeMount:function(){this._loadStyles(),this._hook("onBeforeMount")},mounted:function(){this.rootEl=pn(this.$el,'[data-pc-name="'.concat(nt(this.$.type.name),'"]')),this.rootEl&&(this.rootEl.setAttribute(this.$attrSelector,""),this.rootEl.$pc=re({name:this.$.type.name},this.$params)),this._hook("onMounted")},beforeUpdate:function(){this._hook("onBeforeUpdate")},updated:function(){this._hook("onUpdated")},beforeUnmount:function(){this._hook("onBeforeUnmount")},unmounted:function(){this._unloadScopedThemeStyles(),this._hook("onUnmounted")},methods:{_hook:function(t){if(!this.$options.hostName){var r=this._usePT(this._getPT(this.pt,this.$.type.name),this._getOptionValue,"hooks.".concat(t)),o=this._useDefaultPT(this._getOptionValue,"hooks.".concat(t));r==null||r(),o==null||o()}},_mergeProps:function(t){for(var r=arguments.length,o=new Array(r>1?r-1:0),n=1;n<r;n++)o[n-1]=arguments[n];return jn(t)?t.apply(void 0,o):pe.apply(void 0,o)},_loadStyles:function(){var t=this,r=function(){Ft.isStyleNameLoaded("base")||(Ce.loadCSS(t.$styleOptions),t._loadGlobalStyles(),Ft.setLoadedStyleName("base")),t._loadThemeStyles()};r(),this._themeChangeListener(r)},_loadCoreStyles:function(){var t,r;!Ft.isStyleNameLoaded((t=this.$style)===null||t===void 0?void 0:t.name)&&(r=this.$style)!==null&&r!==void 0&&r.name&&(mc.loadCSS(this.$styleOptions),this.$options.style&&this.$style.loadCSS(this.$styleOptions),Ft.setLoadedStyleName(this.$style.name))},_loadGlobalStyles:function(){var t=this._useGlobalPT(this._getOptionValue,"global.css",this.$params);Ae(t)&&Ce.load(t,re({name:"global"},this.$styleOptions))},_loadThemeStyles:function(){var t,r;if(!this.isUnstyled){if(!de.isStyleNameLoaded("common")){var o,n,i=((o=this.$style)===null||o===void 0||(n=o.getCommonTheme)===null||n===void 0?void 0:n.call(o))||{},s=i.primitive,a=i.semantic;Ce.load(s==null?void 0:s.css,re({name:"primitive-variables"},this.$styleOptions)),Ce.load(a==null?void 0:a.css,re({name:"semantic-variables"},this.$styleOptions)),Ce.loadTheme(re({name:"global-style"},this.$styleOptions)),de.setLoadedStyleName("common")}if(!de.isStyleNameLoaded((t=this.$style)===null||t===void 0?void 0:t.name)&&(r=this.$style)!==null&&r!==void 0&&r.name){var c,u,l,d,f=((c=this.$style)===null||c===void 0||(u=c.getComponentTheme)===null||u===void 0?void 0:u.call(c))||{},p=f.css;(l=this.$style)===null||l===void 0||l.load(p,re({name:"".concat(this.$style.name,"-variables")},this.$styleOptions)),(d=this.$style)===null||d===void 0||d.loadTheme(re({name:"".concat(this.$style.name,"-style")},this.$styleOptions)),de.setLoadedStyleName(this.$style.name)}if(!de.isStyleNameLoaded("layer-order")){var m,b,_=(m=this.$style)===null||m===void 0||(b=m.getLayerOrderThemeCSS)===null||b===void 0?void 0:b.call(m);Ce.load(_,re({name:"layer-order",first:!0},this.$styleOptions)),de.setLoadedStyleName("layer-order")}}},_loadScopedThemeStyles:function(t){var r,o,n,i=((r=this.$style)===null||r===void 0||(o=r.getPresetTheme)===null||o===void 0?void 0:o.call(r,t,"[".concat(this.$attrSelector,"]")))||{},s=i.css,a=(n=this.$style)===null||n===void 0?void 0:n.load(s,re({name:"".concat(this.$attrSelector,"-").concat(this.$style.name)},this.$styleOptions));this.scopedStyleEl=a.el},_unloadScopedThemeStyles:function(){var t;(t=this.scopedStyleEl)===null||t===void 0||(t=t.value)===null||t===void 0||t.remove()},_themeChangeListener:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(){};Ft.clearLoadedStyleNames(),ot.on("theme:change",t)},_getHostInstance:function(t){return t?this.$options.hostName?t.$.type.name===this.$options.hostName?t:this._getHostInstance(t.$parentInstance):t.$parentInstance:void 0},_getPropValue:function(t){var r;return this[t]||((r=this._getHostInstance(this))===null||r===void 0?void 0:r[t])},_getOptionValue:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Rs(t,r,o)},_getPTValue:function(){var t,r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,s=/./g.test(o)&&!!n[o.split(".")[0]],a=this._getPropValue("ptOptions")||((t=this.$primevueConfig)===null||t===void 0?void 0:t.ptOptions)||{},c=a.mergeSections,u=c===void 0?!0:c,l=a.mergeProps,d=l===void 0?!1:l,f=i?s?this._useGlobalPT(this._getPTClassValue,o,n):this._useDefaultPT(this._getPTClassValue,o,n):void 0,p=s?void 0:this._getPTSelf(r,this._getPTClassValue,o,re(re({},n),{},{global:f||{}})),m=this._getPTDatasets(o);return u||!u&&p?d?this._mergeProps(d,f,p,m):re(re(re({},f),p),m):re(re({},p),m)},_getPTSelf:function(){for(var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length,o=new Array(r>1?r-1:0),n=1;n<r;n++)o[n-1]=arguments[n];return pe(this._usePT.apply(this,[this._getPT(t,this.$name)].concat(o)),this._usePT.apply(this,[this.$_attrsPT].concat(o)))},_getPTDatasets:function(){var t,r,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n="data-pc-",i=o==="root"&&Ae((t=this.pt)===null||t===void 0?void 0:t["data-pc-section"]);return o!=="transition"&&re(re({},o==="root"&&re(Xo({},"".concat(n,"name"),nt(i?(r=this.pt)===null||r===void 0?void 0:r["data-pc-section"]:this.$.type.name)),i&&Xo({},"".concat(n,"extend"),nt(this.$.type.name)))),{},Xo({},"".concat(n,"section"),nt(o)))},_getPTClassValue:function(){var t=this._getOptionValue.apply(this,arguments);return ze(t)||Ts(t)?{class:t}:t},_getPT:function(t){var r=this,o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,i=function(a){var c,u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l=n?n(a):a,d=nt(o),f=nt(r.$name);return(c=u?d!==f?l==null?void 0:l[d]:void 0:l==null?void 0:l[d])!==null&&c!==void 0?c:l};return t!=null&&t.hasOwnProperty("_usept")?{_usept:t._usept,originalValue:i(t.originalValue),value:i(t.value)}:i(t,!0)},_usePT:function(t,r,o,n){var i=function(b){return r(b,o,n)};if(t!=null&&t.hasOwnProperty("_usept")){var s,a=t._usept||((s=this.$primevueConfig)===null||s===void 0?void 0:s.ptOptions)||{},c=a.mergeSections,u=c===void 0?!0:c,l=a.mergeProps,d=l===void 0?!1:l,f=i(t.originalValue),p=i(t.value);return f===void 0&&p===void 0?void 0:ze(p)?p:ze(f)?f:u||!u&&p?d?this._mergeProps(d,f,p):re(re({},f),p):p}return i(t)},_useGlobalPT:function(t,r,o){return this._usePT(this.globalPT,t,r,o)},_useDefaultPT:function(t,r,o){return this._usePT(this.defaultPT,t,r,o)},ptm:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this._getPTValue(this.pt,t,re(re({},this.$params),r))},ptmi:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return pe(this.$_attrsWithoutPT,this.ptm(t,r))},ptmo:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this._getPTValue(t,r,re({instance:this},o),!1)},cx:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this.isUnstyled?void 0:this._getOptionValue(this.$style.classes,t,re(re({},this.$params),r))},sx:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,o=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(r){var n=this._getOptionValue(this.$style.inlineStyles,t,re(re({},this.$params),o)),i=this._getOptionValue(mc.inlineStyles,t,re(re({},this.$params),o));return[i,n]}}},computed:{globalPT:function(){var t,r=this;return this._getPT((t=this.$primevueConfig)===null||t===void 0?void 0:t.pt,void 0,function(o){return vt(o,{instance:r})})},defaultPT:function(){var t,r=this;return this._getPT((t=this.$primevueConfig)===null||t===void 0?void 0:t.pt,void 0,function(o){return r._getOptionValue(o,r.$name,re({},r.$params))||vt(o,re({},r.$params))})},isUnstyled:function(){var t;return this.unstyled!==void 0?this.unstyled:(t=this.$primevueConfig)===null||t===void 0?void 0:t.unstyled},$theme:function(){var t;return(t=this.$primevueConfig)===null||t===void 0?void 0:t.theme},$style:function(){return re(re({classes:void 0,inlineStyles:void 0,load:function(){},loadCSS:function(){},loadTheme:function(){}},(this._getHostInstance(this)||{}).$style),this.$options.style)},$styleOptions:function(){var t;return{nonce:(t=this.$primevueConfig)===null||t===void 0||(t=t.csp)===null||t===void 0?void 0:t.nonce}},$primevueConfig:function(){var t;return(t=this.$primevue)===null||t===void 0?void 0:t.config},$name:function(){return this.$options.hostName||this.$.type.name},$params:function(){var t=this._getHostInstance(this)||this.$parent;return{instance:this,props:this.$props,state:this.$data,attrs:this.$attrs,parent:{instance:t,props:t==null?void 0:t.$props,state:t==null?void 0:t.$data,attrs:t==null?void 0:t.$attrs}}},$_attrsPT:function(){return Object.entries(this.$attrs||{}).filter(function(t){var r=Do(t,1),o=r[0];return o==null?void 0:o.startsWith("pt:")}).reduce(function(t,r){var o=Do(r,2),n=o[0],i=o[1],s=n.split(":"),a=db(s),c=a.slice(1);return c==null||c.reduce(function(u,l,d,f){return!u[l]&&(u[l]=d===f.length-1?i:{}),u[l]},t),t},{})},$_attrsWithoutPT:function(){return Object.entries(this.$attrs||{}).filter(function(t){var r=Do(t,1),o=r[0];return!(o!=null&&o.startsWith("pt:"))}).reduce(function(t,r){var o=Do(r,2),n=o[0],i=o[1];return t[n]=i,t},{})},$attrSelector:function(){return rr("pc")}}},mb=function(t){var r=t.dt;return`
.p-divider-horizontal {
    display: flex;
    width: 100%;
    position: relative;
    align-items: center;
    margin: `.concat(r("divider.horizontal.margin"),`;
    padding: `).concat(r("divider.horizontal.padding"),`;
}

.p-divider-horizontal:before {
    position: absolute;
    display: block;
    top: 50%;
    left: 0;
    width: 100%;
    content: "";
    border-top: 1px solid `).concat(r("divider.border.color"),`;
}

.p-divider-horizontal .p-divider-content {
    padding: `).concat(r("divider.horizontal.content.padding"),`;
}

.p-divider-vertical {
    min-height: 100%;
    margin: 0 1rem;
    display: flex;
    position: relative;
    justify-content: center;
    margin: `).concat(r("divider.vertical.margin"),`;
    padding: `).concat(r("divider.vertical.padding"),`;
}

.p-divider-vertical:before {
    position: absolute;
    display: block;
    top: 0;
    left: 50%;
    height: 100%;
    content: "";
    border-left: 1px solid `).concat(r("divider.border.color"),`;
}

.p-divider.p-divider-vertical .p-divider-content {
    padding: `).concat(r("divider.vertical.content.padding"),`;
}

.p-divider-content {
    z-index: 1;
    background: `).concat(r("divider.content.background"),`;
    color: `).concat(r("divider.content.color"),`;
}

.p-divider-solid.p-divider-horizontal:before {
    border-top-style: solid;
}

.p-divider-solid.p-divider-vertical:before {
    border-left-style: solid;
}

.p-divider-dashed.p-divider-horizontal:before {
    border-top-style: dashed;
}

.p-divider-dashed.p-divider-vertical:before {
    border-left-style: dashed;
}

.p-divider-dotted.p-divider-horizontal:before {
    border-top-style: dotted;
}

.p-divider-dotted.p-divider-vertical:before {
    border-left-style: dotted;
}
`)},bb={root:function(t){var r=t.props;return{justifyContent:r.layout==="horizontal"?r.align==="center"||r.align===null?"center":r.align==="left"?"flex-start":r.align==="right"?"flex-end":null:null,alignItems:r.layout==="vertical"?r.align==="center"||r.align===null?"center":r.align==="top"?"flex-start":r.align==="bottom"?"flex-end":null:null}}},vb={root:function(t){var r=t.props;return["p-divider p-component","p-divider-"+r.layout,"p-divider-"+r.type,{"p-divider-left":r.layout==="horizontal"&&(!r.align||r.align==="left")},{"p-divider-center":r.layout==="horizontal"&&r.align==="center"},{"p-divider-right":r.layout==="horizontal"&&r.align==="right"},{"p-divider-top":r.layout==="vertical"&&r.align==="top"},{"p-divider-center":r.layout==="vertical"&&(!r.align||r.align==="center")},{"p-divider-bottom":r.layout==="vertical"&&r.align==="bottom"}]},content:"p-divider-content"},yb=Ce.extend({name:"divider",theme:mb,classes:vb,inlineStyles:bb}),Cb={name:"BaseDivider",extends:_o,props:{align:{type:String,default:null},layout:{type:String,default:"horizontal"},type:{type:String,default:"solid"}},style:yb,provide:function(){return{$pcDivider:this,$parentInstance:this}}},Md={name:"Divider",extends:Cb,inheritAttrs:!1},kb=["aria-orientation"];function Sb(e,t,r,o,n,i){return ce(),Re("div",pe({class:e.cx("root"),style:e.sx("root"),role:"separator","aria-orientation":e.layout},e.ptmi("root")),[e.$slots.default?(ce(),Re("div",pe({key:0,class:e.cx("content")},e.ptm("content")),[rn(e.$slots,"default")],16)):Tn("",!0)],16,kb)}Md.render=Sb;var xb=function(t){var r=t.dt;return`
.p-avatar {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: `.concat(r("avatar.width"),`;
    height: `).concat(r("avatar.height"),`;
    font-size: `).concat(r("avatar.font.size"),`;
    background: `).concat(r("avatar.background"),`;
    border-radius: `).concat(r("avatar.border.radius"),`;
}

.p-avatar-image {
    background: transparent;
}

.p-avatar-circle {
    border-radius: 50%;
}

.p-avatar-circle img {
    border-radius: 50%;
}

.p-avatar-icon {
    font-size: `).concat(r("avatar.font.size"),`;
}

.p-avatar img {
    width: 100%;
    height: 100%;
}

.p-avatar-lg {
    width: `).concat(r("avatar.lg.width"),`;
    height: `).concat(r("avatar.lg.width"),`;
    font-size: `).concat(r("avatar.lg.font.size"),`;
}

.p-avatar-lg .p-avatar-icon {
    font-size: `).concat(r("avatar.lg.font.size"),`;
}

.p-avatar-xl {
    width: `).concat(r("avatar.xl.width"),`;
    height: `).concat(r("avatar.xl.width"),`;
    font-size: `).concat(r("avatar.xl.font.size"),`;
}

.p-avatar-xl .p-avatar-icon {
    font-size: `).concat(r("avatar.xl.font.size"),`;
}

.p-avatar-group {
    display: flex;
    align-items: center;
}

.p-avatar-group .p-avatar + .p-avatar {
    margin-left: `).concat(r("avatar.group.offset"),`;
}

.p-avatar-group .p-avatar {
    border: 2px solid `).concat(r("avatar.group.border.color"),`;
}
`)},_b={root:function(t){var r=t.props;return["p-avatar p-component",{"p-avatar-image":r.image!=null,"p-avatar-circle":r.shape==="circle","p-avatar-lg":r.size==="large","p-avatar-xl":r.size==="xlarge"}]},label:"p-avatar-label",icon:"p-avatar-icon"},Rb=Ce.extend({name:"avatar",theme:xb,classes:_b}),Tb={name:"BaseAvatar",extends:_o,props:{label:{type:String,default:null},icon:{type:String,default:null},image:{type:String,default:null},size:{type:String,default:"normal"},shape:{type:String,default:"square"},ariaLabelledby:{type:String,default:null},ariaLabel:{type:String,default:null}},style:Rb,provide:function(){return{$pcAvatar:this,$parentInstance:this}}},Nd={name:"Avatar",extends:Tb,inheritAttrs:!1,emits:["error"],methods:{onError:function(t){this.$emit("error",t)}}},wb=["aria-labelledby","aria-label"],Pb=["src","alt"];function Eb(e,t,r,o,n,i){return ce(),Re("div",pe({class:e.cx("root"),"aria-labelledby":e.ariaLabelledby,"aria-label":e.ariaLabel},e.ptmi("root")),[rn(e.$slots,"default",{},function(){return[e.label?(ce(),Re("span",pe({key:0,class:e.cx("label")},e.ptm("label")),eo(e.label),17)):e.$slots.icon?(ce(),st(Nr(e.$slots.icon),{key:1,class:wr(e.cx("icon"))},null,8,["class"])):e.icon?(ce(),Re("span",pe({key:2,class:[e.cx("icon"),e.icon]},e.ptm("icon")),null,16)):e.image?(ce(),Re("img",pe({key:3,src:e.image,alt:e.ariaLabel,onError:t[0]||(t[0]=function(){return i.onError&&i.onError.apply(i,arguments)})},e.ptm("image")),null,16,Pb)):Tn("",!0)]})],16,wb)}Nd.render=Eb;var Fd={name:"Portal",props:{appendTo:{type:[String,Object],default:"body"},disabled:{type:Boolean,default:!1}},data:function(){return{mounted:!1}},mounted:function(){this.mounted=Ad()},computed:{inline:function(){return this.disabled||this.appendTo==="self"}}};function Ob(e,t,r,o,n,i){return i.inline?rn(e.$slots,"default",{key:0}):n.mounted?(ce(),st(Op,{key:1,to:r.appendTo},[rn(e.$slots,"default")],8,["to"])):Tn("",!0)}Fd.render=Ob;var Ye=ws();function go(e){"@babel/helpers - typeof";return go=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},go(e)}function jo(e,t,r){return(t=$b(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $b(e){var t=Bb(e,"string");return go(t)=="symbol"?t:t+""}function Bb(e,t){if(go(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(go(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ab=function(t){var r=t.dt;return`
.p-toast {
    width: `.concat(r("toast.width"),`;
    white-space: pre-line;
    word-break: break-word;
}

.p-toast-message {
    margin: 0 0 1rem 0;
}

.p-toast-message-icon {
    flex-shrink: 0;
    font-size: `).concat(r("toast.icon.size"),`;
    width: `).concat(r("toast.icon.size"),`;
    height: `).concat(r("toast.icon.size"),`;
}

.p-toast-message-content {
    display: flex;
    align-items: flex-start;
    padding: `).concat(r("toast.content.padding"),`;
    gap: `).concat(r("toast.content.gap"),`;
}

.p-toast-message-text {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    gap: `).concat(r("toast.text.gap"),`;
}

.p-toast-summary {
    font-weight: `).concat(r("toast.summary.font.weight"),`;
    font-size: `).concat(r("toast.summary.font.size"),`;
}

.p-toast-detail {
    font-weight: `).concat(r("toast.detail.font.weight"),`;
    font-size: `).concat(r("toast.detail.font.size"),`;
}

.p-toast-close-button {
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    background: transparent;
    transition: background `).concat(r("toast.transition.duration"),", color ").concat(r("toast.transition.duration"),", outline-color ").concat(r("toast.transition.duration"),", box-shadow ").concat(r("toast.transition.duration"),`;
    outline-color: transparent;
    color: inherit;
    width: `).concat(r("toast.close.button.width"),`;
    height: `).concat(r("toast.close.button.height"),`;
    border-radius: `).concat(r("toast.close.button.border.radius"),`;
    margin: -25% 0 0 0;
    right: -25%;
    padding: 0;
    border: none;
    user-select: none;
}

.p-toast-message-info,
.p-toast-message-success,
.p-toast-message-warn,
.p-toast-message-error,
.p-toast-message-secondary,
.p-toast-message-contrast {
    border-width: `).concat(r("toast.border.width"),`;
    border-style: solid;
    backdrop-filter: blur(`).concat(r("toast.blur"),`);
    border-radius: `).concat(r("toast.border.radius"),`;
}

.p-toast-close-icon {
    font-size: `).concat(r("toast.close.icon.size"),`;
    width: `).concat(r("toast.close.icon.size"),`;
    height: `).concat(r("toast.close.icon.size"),`;
}

.p-toast-close-button:focus-visible {
    outline-width: `).concat(r("focus.ring.width"),`;
    outline-style: `).concat(r("focus.ring.style"),`;
    outline-offset: `).concat(r("focus.ring.offset"),`;
}

.p-toast-message-info {
    background: `).concat(r("toast.info.background"),`;
    border-color: `).concat(r("toast.info.border.color"),`;
    color: `).concat(r("toast.info.color"),`;
    box-shadow: `).concat(r("toast.info.shadow"),`;
}

.p-toast-message-info .p-toast-detail {
    color: `).concat(r("toast.info.detail.color"),`;
}

.p-toast-message-info .p-toast-close-button:focus-visible {
    outline-color: `).concat(r("toast.info.close.button.focus.ring.color"),`;
    box-shadow: `).concat(r("toast.info.close.button.focus.ring.shadow"),`;
}

.p-toast-message-info .p-toast-close-button:hover {
    background: `).concat(r("toast.info.close.button.hover.background"),`;
}

.p-toast-message-success {
    background: `).concat(r("toast.success.background"),`;
    border-color: `).concat(r("toast.success.border.color"),`;
    color: `).concat(r("toast.success.color"),`;
    box-shadow: `).concat(r("toast.success.shadow"),`;
}

.p-toast-message-success .p-toast-detail {
    color: `).concat(r("toast.success.detail.color"),`;
}

.p-toast-message-success .p-toast-close-button:focus-visible {
    outline-color: `).concat(r("toast.success.close.button.focus.ring.color"),`;
    box-shadow: `).concat(r("toast.success.close.button.focus.ring.shadow"),`;
}

.p-toast-message-success .p-toast-close-button:hover {
    background: `).concat(r("toast.success.close.button.hover.background"),`;
}

.p-toast-message-warn {
    background: `).concat(r("toast.warn.background"),`;
    border-color: `).concat(r("toast.warn.border.color"),`;
    color: `).concat(r("toast.warn.color"),`;
    box-shadow: `).concat(r("toast.warn.shadow"),`;
}

.p-toast-message-warn .p-toast-detail {
    color: `).concat(r("toast.warn.detail.color"),`;
}

.p-toast-message-warn .p-toast-close-button:focus-visible {
    outline-color: `).concat(r("toast.warn.close.button.focus.ring.color"),`;
    box-shadow: `).concat(r("toast.warn.close.button.focus.ring.shadow"),`;
}

.p-toast-message-warn .p-toast-close-button:hover {
    background: `).concat(r("toast.warn.close.button.hover.background"),`;
}

.p-toast-message-error {
    background: `).concat(r("toast.error.background"),`;
    border-color: `).concat(r("toast.error.border.color"),`;
    color: `).concat(r("toast.error.color"),`;
    box-shadow: `).concat(r("toast.error.shadow"),`;
}

.p-toast-message-error .p-toast-detail {
    color: `).concat(r("toast.error.detail.color"),`;
}

.p-toast-message-error .p-toast-close-button:focus-visible {
    outline-color: `).concat(r("toast.error.close.button.focus.ring.color"),`;
    box-shadow: `).concat(r("toast.error.close.button.focus.ring.shadow"),`;
}

.p-toast-message-error .p-toast-close-button:hover {
    background: `).concat(r("toast.error.close.button.hover.background"),`;
}

.p-toast-message-secondary {
    background: `).concat(r("toast.secondary.background"),`;
    border-color: `).concat(r("toast.secondary.border.color"),`;
    color: `).concat(r("toast.secondary.color"),`;
    box-shadow: `).concat(r("toast.secondary.shadow"),`;
}

.p-toast-message-secondary .p-toast-detail {
    color: `).concat(r("toast.secondary.detail.color"),`;
}

.p-toast-message-secondary .p-toast-close-button:focus-visible {
    outline-color: `).concat(r("toast.secondary.close.button.focus.ring.color"),`;
    box-shadow: `).concat(r("toast.secondary.close.button.focus.ring.shadow"),`;
}

.p-toast-message-secondary .p-toast-close-button:hover {
    background: `).concat(r("toast.secondary.close.button.hover.background"),`;
}

.p-toast-message-contrast {
    background: `).concat(r("toast.contrast.background"),`;
    border-color: `).concat(r("toast.contrast.border.color"),`;
    color: `).concat(r("toast.contrast.color"),`;
    box-shadow: `).concat(r("toast.contrast.shadow"),`;
}

.p-toast-message-contrast .p-toast-detail {
    color: `).concat(r("toast.contrast.detail.color"),`;
}

.p-toast-message-contrast .p-toast-close-button:focus-visible {
    outline-color: `).concat(r("toast.contrast.close.button.focus.ring.color"),`;
    box-shadow: `).concat(r("toast.contrast.close.button.focus.ring.shadow"),`;
}

.p-toast-message-contrast .p-toast-close-button:hover {
    background: `).concat(r("toast.contrast.close.button.hover.background"),`;
}

.p-toast-top-center {
    transform: translateX(-50%);
}

.p-toast-bottom-center {
    transform: translateX(-50%);
}

.p-toast-center {
    min-width: 20vw;
    transform: translate(-50%, -50%);
}

.p-toast-message-enter-from {
    opacity: 0;
    transform: translateY(50%);
}

.p-toast-message-leave-from {
    max-height: 1000px;
}

.p-toast .p-toast-message.p-toast-message-leave-to {
    max-height: 0;
    opacity: 0;
    margin-bottom: 0;
    overflow: hidden;
}

.p-toast-message-enter-active {
    transition: transform 0.3s, opacity 0.3s;
}

.p-toast-message-leave-active {
    transition: max-height 0.45s cubic-bezier(0, 1, 0, 1), opacity 0.3s, margin-bottom 0.3s;
}
`)},Lb={root:function(t){var r=t.position;return{position:"fixed",top:r==="top-right"||r==="top-left"||r==="top-center"?"20px":r==="center"?"50%":null,right:(r==="top-right"||r==="bottom-right")&&"20px",bottom:(r==="bottom-left"||r==="bottom-right"||r==="bottom-center")&&"20px",left:r==="top-left"||r==="bottom-left"?"20px":r==="center"||r==="top-center"||r==="bottom-center"?"50%":null}}},Ib={root:function(t){var r=t.props;return["p-toast p-component p-toast-"+r.position]},message:function(t){var r=t.props;return["p-toast-message",{"p-toast-message-info":r.message.severity==="info"||r.message.severity===void 0,"p-toast-message-warn":r.message.severity==="warn","p-toast-message-error":r.message.severity==="error","p-toast-message-success":r.message.severity==="success","p-toast-message-secondary":r.message.severity==="secondary","p-toast-message-contrast":r.message.severity==="contrast"}]},messageContent:"p-toast-message-content",messageIcon:function(t){var r=t.props;return["p-toast-message-icon",jo(jo(jo(jo({},r.infoIcon,r.message.severity==="info"),r.warnIcon,r.message.severity==="warn"),r.errorIcon,r.message.severity==="error"),r.successIcon,r.message.severity==="success")]},messageText:"p-toast-message-text",summary:"p-toast-summary",detail:"p-toast-detail",closeButton:"p-toast-close-button",closeIcon:"p-toast-close-icon"},Db=Ce.extend({name:"toast",theme:Ab,classes:Ib,inlineStyles:Lb}),jb=`
.p-icon {
    display: inline-block;
}

.p-icon-spin {
    -webkit-animation: p-icon-spin 2s infinite linear;
    animation: p-icon-spin 2s infinite linear;
}

@-webkit-keyframes p-icon-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}

@keyframes p-icon-spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}
`,Mb=Ce.extend({name:"baseicon",css:jb});function ho(e){"@babel/helpers - typeof";return ho=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ho(e)}function yc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function Cc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?yc(Object(r),!0).forEach(function(o){Nb(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yc(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function Nb(e,t,r){return(t=Fb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Fb(e){var t=zb(e,"string");return ho(t)=="symbol"?t:t+""}function zb(e,t){if(ho(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(ho(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ro={name:"BaseIcon",extends:_o,props:{label:{type:String,default:void 0},spin:{type:Boolean,default:!1}},style:Mb,provide:function(){return{$pcIcon:this,$parentInstance:this}},methods:{pti:function(){var t=lr(this.label);return Cc(Cc({},!this.isUnstyled&&{class:["p-icon",{"p-icon-spin":this.spin}]}),{},{role:t?void 0:"img","aria-label":t?void 0:this.label,"aria-hidden":t})}}},ji={name:"CheckIcon",extends:Ro},Ub=fe("path",{d:"M4.86199 11.5948C4.78717 11.5923 4.71366 11.5745 4.64596 11.5426C4.57826 11.5107 4.51779 11.4652 4.46827 11.4091L0.753985 7.69483C0.683167 7.64891 0.623706 7.58751 0.580092 7.51525C0.536478 7.44299 0.509851 7.36177 0.502221 7.27771C0.49459 7.19366 0.506156 7.10897 0.536046 7.03004C0.565935 6.95111 0.613367 6.88 0.674759 6.82208C0.736151 6.76416 0.8099 6.72095 0.890436 6.69571C0.970973 6.67046 1.05619 6.66385 1.13966 6.67635C1.22313 6.68886 1.30266 6.72017 1.37226 6.76792C1.44186 6.81567 1.4997 6.8786 1.54141 6.95197L4.86199 10.2503L12.6397 2.49483C12.7444 2.42694 12.8689 2.39617 12.9932 2.40745C13.1174 2.41873 13.2343 2.47141 13.3251 2.55705C13.4159 2.64268 13.4753 2.75632 13.4938 2.87973C13.5123 3.00315 13.4888 3.1292 13.4271 3.23768L5.2557 11.4091C5.20618 11.4652 5.14571 11.5107 5.07801 11.5426C5.01031 11.5745 4.9368 11.5923 4.86199 11.5948Z",fill:"currentColor"},null,-1),Hb=[Ub];function Vb(e,t,r,o,n,i){return ce(),Re("svg",pe({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.pti()),Hb,16)}ji.render=Vb;var Mi={name:"ExclamationTriangleIcon",extends:Ro},Wb=fe("path",{d:"M13.4018 13.1893H0.598161C0.49329 13.189 0.390283 13.1615 0.299143 13.1097C0.208003 13.0578 0.131826 12.9832 0.0780112 12.8932C0.0268539 12.8015 0 12.6982 0 12.5931C0 12.4881 0.0268539 12.3848 0.0780112 12.293L6.47985 1.08982C6.53679 1.00399 6.61408 0.933574 6.70484 0.884867C6.7956 0.836159 6.897 0.810669 7 0.810669C7.103 0.810669 7.2044 0.836159 7.29516 0.884867C7.38592 0.933574 7.46321 1.00399 7.52015 1.08982L13.922 12.293C13.9731 12.3848 14 12.4881 14 12.5931C14 12.6982 13.9731 12.8015 13.922 12.8932C13.8682 12.9832 13.792 13.0578 13.7009 13.1097C13.6097 13.1615 13.5067 13.189 13.4018 13.1893ZM1.63046 11.989H12.3695L7 2.59425L1.63046 11.989Z",fill:"currentColor"},null,-1),Gb=fe("path",{d:"M6.99996 8.78801C6.84143 8.78594 6.68997 8.72204 6.57787 8.60993C6.46576 8.49782 6.40186 8.34637 6.39979 8.18784V5.38703C6.39979 5.22786 6.46302 5.0752 6.57557 4.96265C6.68813 4.85009 6.84078 4.78686 6.99996 4.78686C7.15914 4.78686 7.31179 4.85009 7.42435 4.96265C7.5369 5.0752 7.60013 5.22786 7.60013 5.38703V8.18784C7.59806 8.34637 7.53416 8.49782 7.42205 8.60993C7.30995 8.72204 7.15849 8.78594 6.99996 8.78801Z",fill:"currentColor"},null,-1),Kb=fe("path",{d:"M6.99996 11.1887C6.84143 11.1866 6.68997 11.1227 6.57787 11.0106C6.46576 10.8985 6.40186 10.7471 6.39979 10.5885V10.1884C6.39979 10.0292 6.46302 9.87658 6.57557 9.76403C6.68813 9.65147 6.84078 9.58824 6.99996 9.58824C7.15914 9.58824 7.31179 9.65147 7.42435 9.76403C7.5369 9.87658 7.60013 10.0292 7.60013 10.1884V10.5885C7.59806 10.7471 7.53416 10.8985 7.42205 11.0106C7.30995 11.1227 7.15849 11.1866 6.99996 11.1887Z",fill:"currentColor"},null,-1),qb=[Wb,Gb,Kb];function Jb(e,t,r,o,n,i){return ce(),Re("svg",pe({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.pti()),qb,16)}Mi.render=Jb;var Ni={name:"InfoCircleIcon",extends:Ro},Yb=fe("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M3.11101 12.8203C4.26215 13.5895 5.61553 14 7 14C8.85652 14 10.637 13.2625 11.9497 11.9497C13.2625 10.637 14 8.85652 14 7C14 5.61553 13.5895 4.26215 12.8203 3.11101C12.0511 1.95987 10.9579 1.06266 9.67879 0.532846C8.3997 0.00303296 6.99224 -0.13559 5.63437 0.134506C4.2765 0.404603 3.02922 1.07129 2.05026 2.05026C1.07129 3.02922 0.404603 4.2765 0.134506 5.63437C-0.13559 6.99224 0.00303296 8.3997 0.532846 9.67879C1.06266 10.9579 1.95987 12.0511 3.11101 12.8203ZM3.75918 2.14976C4.71846 1.50879 5.84628 1.16667 7 1.16667C8.5471 1.16667 10.0308 1.78125 11.1248 2.87521C12.2188 3.96918 12.8333 5.45291 12.8333 7C12.8333 8.15373 12.4912 9.28154 11.8502 10.2408C11.2093 11.2001 10.2982 11.9478 9.23232 12.3893C8.16642 12.8308 6.99353 12.9463 5.86198 12.7212C4.73042 12.4962 3.69102 11.9406 2.87521 11.1248C2.05941 10.309 1.50384 9.26958 1.27876 8.13803C1.05367 7.00647 1.16919 5.83358 1.61071 4.76768C2.05222 3.70178 2.79989 2.79074 3.75918 2.14976ZM7.00002 4.8611C6.84594 4.85908 6.69873 4.79698 6.58977 4.68801C6.48081 4.57905 6.4187 4.43185 6.41669 4.27776V3.88888C6.41669 3.73417 6.47815 3.58579 6.58754 3.4764C6.69694 3.367 6.84531 3.30554 7.00002 3.30554C7.15473 3.30554 7.3031 3.367 7.4125 3.4764C7.52189 3.58579 7.58335 3.73417 7.58335 3.88888V4.27776C7.58134 4.43185 7.51923 4.57905 7.41027 4.68801C7.30131 4.79698 7.1541 4.85908 7.00002 4.8611ZM7.00002 10.6945C6.84594 10.6925 6.69873 10.6304 6.58977 10.5214C6.48081 10.4124 6.4187 10.2652 6.41669 10.1111V6.22225C6.41669 6.06754 6.47815 5.91917 6.58754 5.80977C6.69694 5.70037 6.84531 5.63892 7.00002 5.63892C7.15473 5.63892 7.3031 5.70037 7.4125 5.80977C7.52189 5.91917 7.58335 6.06754 7.58335 6.22225V10.1111C7.58134 10.2652 7.51923 10.4124 7.41027 10.5214C7.30131 10.6304 7.1541 10.6925 7.00002 10.6945Z",fill:"currentColor"},null,-1),Zb=[Yb];function Xb(e,t,r,o,n,i){return ce(),Re("svg",pe({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.pti()),Zb,16)}Ni.render=Xb;var zd={name:"TimesIcon",extends:Ro},Qb=fe("path",{d:"M8.01186 7.00933L12.27 2.75116C12.341 2.68501 12.398 2.60524 12.4375 2.51661C12.4769 2.42798 12.4982 2.3323 12.4999 2.23529C12.5016 2.13827 12.4838 2.0419 12.4474 1.95194C12.4111 1.86197 12.357 1.78024 12.2884 1.71163C12.2198 1.64302 12.138 1.58893 12.0481 1.55259C11.9581 1.51625 11.8617 1.4984 11.7647 1.50011C11.6677 1.50182 11.572 1.52306 11.4834 1.56255C11.3948 1.60204 11.315 1.65898 11.2488 1.72997L6.99067 5.98814L2.7325 1.72997C2.59553 1.60234 2.41437 1.53286 2.22718 1.53616C2.03999 1.53946 1.8614 1.61529 1.72901 1.74767C1.59663 1.88006 1.5208 2.05865 1.5175 2.24584C1.5142 2.43303 1.58368 2.61419 1.71131 2.75116L5.96948 7.00933L1.71131 11.2675C1.576 11.403 1.5 11.5866 1.5 11.7781C1.5 11.9696 1.576 12.1532 1.71131 12.2887C1.84679 12.424 2.03043 12.5 2.2219 12.5C2.41338 12.5 2.59702 12.424 2.7325 12.2887L6.99067 8.03052L11.2488 12.2887C11.3843 12.424 11.568 12.5 11.7594 12.5C11.9509 12.5 12.1346 12.424 12.27 12.2887C12.4053 12.1532 12.4813 11.9696 12.4813 11.7781C12.4813 11.5866 12.4053 11.403 12.27 11.2675L8.01186 7.00933Z",fill:"currentColor"},null,-1),ev=[Qb];function tv(e,t,r,o,n,i){return ce(),Re("svg",pe({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.pti()),ev,16)}zd.render=tv;var Fi={name:"TimesCircleIcon",extends:Ro},rv=fe("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7 14C5.61553 14 4.26215 13.5895 3.11101 12.8203C1.95987 12.0511 1.06266 10.9579 0.532846 9.67879C0.00303296 8.3997 -0.13559 6.99224 0.134506 5.63437C0.404603 4.2765 1.07129 3.02922 2.05026 2.05026C3.02922 1.07129 4.2765 0.404603 5.63437 0.134506C6.99224 -0.13559 8.3997 0.00303296 9.67879 0.532846C10.9579 1.06266 12.0511 1.95987 12.8203 3.11101C13.5895 4.26215 14 5.61553 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM7 1.16667C5.84628 1.16667 4.71846 1.50879 3.75918 2.14976C2.79989 2.79074 2.05222 3.70178 1.61071 4.76768C1.16919 5.83358 1.05367 7.00647 1.27876 8.13803C1.50384 9.26958 2.05941 10.309 2.87521 11.1248C3.69102 11.9406 4.73042 12.4962 5.86198 12.7212C6.99353 12.9463 8.16642 12.8308 9.23232 12.3893C10.2982 11.9478 11.2093 11.2001 11.8502 10.2408C12.4912 9.28154 12.8333 8.15373 12.8333 7C12.8333 5.45291 12.2188 3.96918 11.1248 2.87521C10.0308 1.78125 8.5471 1.16667 7 1.16667ZM4.66662 9.91668C4.58998 9.91704 4.51404 9.90209 4.44325 9.87271C4.37246 9.84333 4.30826 9.8001 4.2544 9.74557C4.14516 9.6362 4.0838 9.48793 4.0838 9.33335C4.0838 9.17876 4.14516 9.0305 4.2544 8.92113L6.17553 7L4.25443 5.07891C4.15139 4.96832 4.09529 4.82207 4.09796 4.67094C4.10063 4.51982 4.16185 4.37563 4.26872 4.26876C4.3756 4.16188 4.51979 4.10066 4.67091 4.09799C4.82204 4.09532 4.96829 4.15142 5.07887 4.25446L6.99997 6.17556L8.92106 4.25446C9.03164 4.15142 9.1779 4.09532 9.32903 4.09799C9.48015 4.10066 9.62434 4.16188 9.73121 4.26876C9.83809 4.37563 9.89931 4.51982 9.90198 4.67094C9.90464 4.82207 9.84855 4.96832 9.74551 5.07891L7.82441 7L9.74554 8.92113C9.85478 9.0305 9.91614 9.17876 9.91614 9.33335C9.91614 9.48793 9.85478 9.6362 9.74554 9.74557C9.69168 9.8001 9.62748 9.84333 9.55669 9.87271C9.4859 9.90209 9.40996 9.91704 9.33332 9.91668C9.25668 9.91704 9.18073 9.90209 9.10995 9.87271C9.03916 9.84333 8.97495 9.8001 8.9211 9.74557L6.99997 7.82444L5.07884 9.74557C5.02499 9.8001 4.96078 9.84333 4.88999 9.87271C4.81921 9.90209 4.74326 9.91704 4.66662 9.91668Z",fill:"currentColor"},null,-1),ov=[rv];function nv(e,t,r,o,n,i){return ce(),Re("svg",pe({width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e.pti()),ov,16)}Fi.render=nv;var hr=ws();function mo(e){"@babel/helpers - typeof";return mo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},mo(e)}function kc(e,t){return cv(e)||av(e,t)||sv(e,t)||iv()}function iv(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sv(e,t){if(e){if(typeof e=="string")return Sc(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Sc(e,t):void 0}}function Sc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function av(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var o,n,i,s,a=[],c=!0,u=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(o=i.call(r)).done)&&(a.push(o.value),a.length!==t);c=!0);}catch(l){u=!0,n=l}finally{try{if(!c&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return a}}function cv(e){if(Array.isArray(e))return e}function xc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function se(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?xc(Object(r),!0).forEach(function(o){zi(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xc(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function zi(e,t,r){return(t=lv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function lv(e){var t=uv(e,"string");return mo(t)=="symbol"?t:t+""}function uv(e,t){if(mo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(mo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Z={_getMeta:function(){return[Gt(arguments.length<=0?void 0:arguments[0])||arguments.length<=0?void 0:arguments[0],vt(Gt(arguments.length<=0?void 0:arguments[0])?arguments.length<=0?void 0:arguments[0]:arguments.length<=1?void 0:arguments[1])]},_getConfig:function(t,r){var o,n,i;return(o=(t==null||(n=t.instance)===null||n===void 0?void 0:n.$primevue)||(r==null||(i=r.ctx)===null||i===void 0||(i=i.appContext)===null||i===void 0||(i=i.config)===null||i===void 0||(i=i.globalProperties)===null||i===void 0?void 0:i.$primevue))===null||o===void 0?void 0:o.config},_getOptionValue:Rs,_getPTValue:function(){var t,r,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"",s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,c=function(){var y=Z._getOptionValue.apply(Z,arguments);return ze(y)||Ts(y)?{class:y}:y},u=((t=o.binding)===null||t===void 0||(t=t.value)===null||t===void 0?void 0:t.ptOptions)||((r=o.$primevueConfig)===null||r===void 0?void 0:r.ptOptions)||{},l=u.mergeSections,d=l===void 0?!0:l,f=u.mergeProps,p=f===void 0?!1:f,m=a?Z._useDefaultPT(o,o.defaultPT(),c,i,s):void 0,b=Z._usePT(o,Z._getPT(n,o.$name),c,i,se(se({},s),{},{global:m||{}})),_=Z._getPTDatasets(o,i);return d||!d&&b?p?Z._mergeProps(o,p,m,b,_):se(se(se({},m),b),_):se(se({},b),_)},_getPTDatasets:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o="data-pc-";return se(se({},r==="root"&&zi({},"".concat(o,"name"),nt(t.$name))),{},zi({},"".concat(o,"section"),nt(r)))},_getPT:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",o=arguments.length>2?arguments[2]:void 0,n=function(s){var a,c=o?o(s):s,u=nt(r);return(a=c==null?void 0:c[u])!==null&&a!==void 0?a:c};return t!=null&&t.hasOwnProperty("_usept")?{_usept:t._usept,originalValue:n(t.originalValue),value:n(t.value)}:n(t)},_usePT:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0,o=arguments.length>2?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0,s=function(_){return o(_,n,i)};if(r!=null&&r.hasOwnProperty("_usept")){var a,c=r._usept||((a=t.$primevueConfig)===null||a===void 0?void 0:a.ptOptions)||{},u=c.mergeSections,l=u===void 0?!0:u,d=c.mergeProps,f=d===void 0?!1:d,p=s(r.originalValue),m=s(r.value);return p===void 0&&m===void 0?void 0:ze(m)?m:ze(p)?p:l||!l&&m?f?Z._mergeProps(t,f,p,m):se(se({},p),m):m}return s(r)},_useDefaultPT:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=arguments.length>2?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;return Z._usePT(t,r,o,n,i)},_loadStyles:function(t,r,o){var n,i=Z._getConfig(r,o),s={nonce:i==null||(n=i.csp)===null||n===void 0?void 0:n.nonce};Z._loadCoreStyles(t.$instance,s),Z._loadThemeStyles(t.$instance,s),Z._loadScopedThemeStyles(t.$instance,s),Z._themeChangeListener(function(){return Z._loadThemeStyles(t.$instance,s)})},_loadCoreStyles:function(){var t,r,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;if(!Ft.isStyleNameLoaded((t=o.$style)===null||t===void 0?void 0:t.name)&&(r=o.$style)!==null&&r!==void 0&&r.name){var i;Ce.loadCSS(n),o.isUnstyled()&&((i=o.$style)===null||i===void 0||i.loadCSS(n)),Ft.setLoadedStyleName(o.$style.name)}},_loadThemeStyles:function(){var t,r,o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;if(!(o!=null&&o.isUnstyled())){if(!de.isStyleNameLoaded("common")){var i,s,a=((i=o.$style)===null||i===void 0||(s=i.getCommonTheme)===null||s===void 0?void 0:s.call(i))||{},c=a.primitive,u=a.semantic;Ce.load(c==null?void 0:c.css,se({name:"primitive-variables"},n)),Ce.load(u==null?void 0:u.css,se({name:"semantic-variables"},n)),Ce.loadTheme(se({name:"global-style"},n)),de.setLoadedStyleName("common")}if(!de.isStyleNameLoaded((t=o.$style)===null||t===void 0?void 0:t.name)&&(r=o.$style)!==null&&r!==void 0&&r.name){var l,d,f,p,m=((l=o.$style)===null||l===void 0||(d=l.getDirectiveTheme)===null||d===void 0?void 0:d.call(l))||{},b=m.css;(f=o.$style)===null||f===void 0||f.load(b,se({name:"".concat(o.$style.name,"-variables")},n)),(p=o.$style)===null||p===void 0||p.loadTheme(se({name:"".concat(o.$style.name,"-style")},n)),de.setLoadedStyleName(o.$style.name)}if(!de.isStyleNameLoaded("layer-order")){var _,R,y=(_=o.$style)===null||_===void 0||(R=_.getLayerOrderThemeCSS)===null||R===void 0?void 0:R.call(_);Ce.load(y,se({name:"layer-order",first:!0},n)),de.setLoadedStyleName("layer-order")}}},_loadScopedThemeStyles:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0,o=t.preset();if(o&&t.$attrSelector){var n,i,s,a=((n=t.$style)===null||n===void 0||(i=n.getPresetTheme)===null||i===void 0?void 0:i.call(n,o,"[".concat(t.$attrSelector,"]")))||{},c=a.css,u=(s=t.$style)===null||s===void 0?void 0:s.load(c,se({name:"".concat(t.$attrSelector,"-").concat(t.$style.name)},r));t.scopedStyleEl=u.el}},_themeChangeListener:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(){};Ft.clearLoadedStyleNames(),ot.on("theme:change",t)},_hook:function(t,r,o,n,i,s){var a,c,u="on".concat(R0(r)),l=Z._getConfig(n,i),d=o==null?void 0:o.$instance,f=Z._usePT(d,Z._getPT(n==null||(a=n.value)===null||a===void 0?void 0:a.pt,t),Z._getOptionValue,"hooks.".concat(u)),p=Z._useDefaultPT(d,l==null||(c=l.pt)===null||c===void 0||(c=c.directives)===null||c===void 0?void 0:c[t],Z._getOptionValue,"hooks.".concat(u)),m={el:o,binding:n,vnode:i,prevVnode:s};f==null||f(d,m),p==null||p(d,m)},_mergeProps:function(){for(var t=arguments.length>1?arguments[1]:void 0,r=arguments.length,o=new Array(r>2?r-2:0),n=2;n<r;n++)o[n-2]=arguments[n];return jn(t)?t.apply(void 0,o):pe.apply(void 0,o)},_extend:function(t){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=function(s,a,c,u,l){var d,f,p;a._$instances=a._$instances||{};var m=Z._getConfig(c,u),b=a._$instances[t]||{},_=lr(b)?se(se({},r),r==null?void 0:r.methods):{};a._$instances[t]=se(se({},b),{},{$name:t,$host:a,$binding:c,$modifiers:c==null?void 0:c.modifiers,$value:c==null?void 0:c.value,$el:b.$el||a||void 0,$style:se({classes:void 0,inlineStyles:void 0,load:function(){},loadCSS:function(){},loadTheme:function(){}},r==null?void 0:r.style),$primevueConfig:m,$attrSelector:a.$attrSelector,defaultPT:function(){return Z._getPT(m==null?void 0:m.pt,void 0,function(y){var k;return y==null||(k=y.directives)===null||k===void 0?void 0:k[t]})},isUnstyled:function(){var y,k;return((y=a.$instance)===null||y===void 0||(y=y.$binding)===null||y===void 0||(y=y.value)===null||y===void 0?void 0:y.unstyled)!==void 0?(k=a.$instance)===null||k===void 0||(k=k.$binding)===null||k===void 0||(k=k.value)===null||k===void 0?void 0:k.unstyled:m==null?void 0:m.unstyled},theme:function(){var y;return(y=a.$instance)===null||y===void 0||(y=y.$primevueConfig)===null||y===void 0?void 0:y.theme},preset:function(){var y;return(y=a.$instance)===null||y===void 0||(y=y.$binding)===null||y===void 0||(y=y.value)===null||y===void 0?void 0:y.dt},ptm:function(){var y,k=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",M=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return Z._getPTValue(a.$instance,(y=a.$instance)===null||y===void 0||(y=y.$binding)===null||y===void 0||(y=y.value)===null||y===void 0?void 0:y.pt,k,se({},M))},ptmo:function(){var y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},k=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",M=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Z._getPTValue(a.$instance,y,k,M,!1)},cx:function(){var y,k,M=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",U=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return(y=a.$instance)!==null&&y!==void 0&&y.isUnstyled()?void 0:Z._getOptionValue((k=a.$instance)===null||k===void 0||(k=k.$style)===null||k===void 0?void 0:k.classes,M,se({},U))},sx:function(){var y,k=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",M=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,U=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return M?Z._getOptionValue((y=a.$instance)===null||y===void 0||(y=y.$style)===null||y===void 0?void 0:y.inlineStyles,k,se({},U)):void 0}},_),a.$instance=a._$instances[t],(d=(f=a.$instance)[s])===null||d===void 0||d.call(f,a,c,u,l),a["$".concat(t)]=a.$instance,Z._hook(t,s,a,c,u,l),a.$pd||(a.$pd={}),a.$pd[t]=se(se({},(p=a.$pd)===null||p===void 0?void 0:p[t]),{},{name:t,instance:a.$instance})},n=function(s){var a,c,u,l,d,f=(a=s.$instance)===null||a===void 0?void 0:a.watch;f==null||(c=f.config)===null||c===void 0||c.call(s.$instance,(u=s.$instance)===null||u===void 0?void 0:u.$primevueConfig),hr.on("config:change",function(p){var m,b=p.newValue,_=p.oldValue;return f==null||(m=f.config)===null||m===void 0?void 0:m.call(s.$instance,b,_)}),f==null||(l=f["config.ripple"])===null||l===void 0||l.call(s.$instance,(d=s.$instance)===null||d===void 0||(d=d.$primevueConfig)===null||d===void 0?void 0:d.ripple),hr.on("config:ripple:change",function(p){var m,b=p.newValue,_=p.oldValue;return f==null||(m=f["config.ripple"])===null||m===void 0?void 0:m.call(s.$instance,b,_)})};return{created:function(s,a,c,u){o("created",s,a,c,u)},beforeMount:function(s,a,c,u){s.$attrSelector=rr("pd"),Z._loadStyles(s,a,c),o("beforeMount",s,a,c,u),n(s)},mounted:function(s,a,c,u){Z._loadStyles(s,a,c),o("mounted",s,a,c,u)},beforeUpdate:function(s,a,c,u){o("beforeUpdate",s,a,c,u)},updated:function(s,a,c,u){Z._loadStyles(s,a,c),o("updated",s,a,c,u)},beforeUnmount:function(s,a,c,u){o("beforeUnmount",s,a,c,u)},unmounted:function(s,a,c,u){var l;(l=s.$instance)===null||l===void 0||(l=l.scopedStyleEl)===null||l===void 0||(l=l.value)===null||l===void 0||l.remove(),o("unmounted",s,a,c,u)}}},extend:function(){var t=Z._getMeta.apply(Z,arguments),r=kc(t,2),o=r[0],n=r[1];return se({extend:function(){var s=Z._getMeta.apply(Z,arguments),a=kc(s,2),c=a[0],u=a[1];return Z.extend(c,se(se(se({},n),n==null?void 0:n.methods),u))}},Z._extend(o,n))}},dv=function(t){var r=t.dt;return`
.p-ink {
    display: block;
    position: absolute;
    background: `.concat(r("ripple.background"),`;
    border-radius: 100%;
    transform: scale(0);
    pointer-events: none;
}

.p-ink-active {
    animation: ripple 0.4s linear;
}

@keyframes ripple {
    100% {
        opacity: 0;
        transform: scale(2.5);
    }
}
`)},fv={root:"p-ink"},pv=Ce.extend({name:"ripple-directive",theme:dv,classes:fv}),gv=Z.extend({style:pv});function bo(e){"@babel/helpers - typeof";return bo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bo(e)}function hv(e){return yv(e)||vv(e)||bv(e)||mv()}function mv(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bv(e,t){if(e){if(typeof e=="string")return Ui(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ui(e,t):void 0}}function vv(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function yv(e){if(Array.isArray(e))return Ui(e)}function Ui(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function _c(e,t,r){return(t=Cv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Cv(e){var t=kv(e,"string");return bo(t)=="symbol"?t:t+""}function kv(e,t){if(bo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(bo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Sv=gv.extend("ripple",{watch:{"config.ripple":function(t){t?(this.createRipple(this.$host),this.bindEvents(this.$host),this.$host.setAttribute("data-pd-ripple",!0),this.$host.style.overflow="hidden",this.$host.style.position="relative"):(this.remove(this.$host),this.$host.removeAttribute("data-pd-ripple"))}},unmounted:function(t){this.remove(t)},timeout:void 0,methods:{bindEvents:function(t){t.addEventListener("mousedown",this.onMouseDown.bind(this))},unbindEvents:function(t){t.removeEventListener("mousedown",this.onMouseDown.bind(this))},createRipple:function(t){var r=Zo("span",_c(_c({role:"presentation","aria-hidden":!0,"data-p-ink":!0,"data-p-ink-active":!1,class:!this.isUnstyled()&&this.cx("root"),onAnimationEnd:this.onAnimationEnd.bind(this)},this.$attrSelector,""),"p-bind",this.ptm("root")));t.appendChild(r),this.$el=r},remove:function(t){var r=this.getInk(t);r&&(this.$host.style.overflow="",this.$host.style.position="",this.unbindEvents(t),r.removeEventListener("animationend",this.onAnimationEnd),r.remove())},onMouseDown:function(t){var r=this,o=t.currentTarget,n=this.getInk(o);if(!(!n||getComputedStyle(n,null).display==="none")){if(!this.isUnstyled()&&Yo(n,"p-ink-active"),n.setAttribute("data-p-ink-active","false"),!lc(n)&&!uc(n)){var i=Math.max(At(o),Lt(o));n.style.height=i+"px",n.style.width=i+"px"}var s=j0(o),a=t.pageX-s.left+document.body.scrollTop-uc(n)/2,c=t.pageY-s.top+document.body.scrollLeft-lc(n)/2;n.style.top=c+"px",n.style.left=a+"px",!this.isUnstyled()&&Rd(n,"p-ink-active"),n.setAttribute("data-p-ink-active","true"),this.timeout=setTimeout(function(){n&&(!r.isUnstyled()&&Yo(n,"p-ink-active"),n.setAttribute("data-p-ink-active","false"))},401)}},onAnimationEnd:function(t){this.timeout&&clearTimeout(this.timeout),!this.isUnstyled()&&Yo(t.currentTarget,"p-ink-active"),t.currentTarget.setAttribute("data-p-ink-active","false")},getInk:function(t){return t&&t.children?hv(t.children).find(function(r){return Qt(r,"data-pc-name")==="ripple"}):void 0}}}),xv={name:"BaseToast",extends:_o,props:{group:{type:String,default:null},position:{type:String,default:"top-right"},autoZIndex:{type:Boolean,default:!0},baseZIndex:{type:Number,default:0},breakpoints:{type:Object,default:null},closeIcon:{type:String,default:void 0},infoIcon:{type:String,default:void 0},warnIcon:{type:String,default:void 0},errorIcon:{type:String,default:void 0},successIcon:{type:String,default:void 0},closeButtonProps:{type:null,default:null}},style:Db,provide:function(){return{$pcToast:this,$parentInstance:this}}},Ud={name:"ToastMessage",hostName:"Toast",extends:_o,emits:["close"],closeTimeout:null,props:{message:{type:null,default:null},templates:{type:Object,default:null},closeIcon:{type:String,default:null},infoIcon:{type:String,default:null},warnIcon:{type:String,default:null},errorIcon:{type:String,default:null},successIcon:{type:String,default:null},closeButtonProps:{type:null,default:null}},mounted:function(){var t=this;this.message.life&&(this.closeTimeout=setTimeout(function(){t.close({message:t.message,type:"life-end"})},this.message.life))},beforeUnmount:function(){this.clearCloseTimeout()},methods:{close:function(t){this.$emit("close",t)},onCloseClick:function(){this.clearCloseTimeout(),this.close({message:this.message,type:"close"})},clearCloseTimeout:function(){this.closeTimeout&&(clearTimeout(this.closeTimeout),this.closeTimeout=null)}},computed:{iconComponent:function(){return{info:!this.infoIcon&&Ni,success:!this.successIcon&&ji,warn:!this.warnIcon&&Mi,error:!this.errorIcon&&Fi}[this.message.severity]},closeAriaLabel:function(){return this.$primevue.config.locale.aria?this.$primevue.config.locale.aria.close:void 0}},components:{TimesIcon:zd,InfoCircleIcon:Ni,CheckIcon:ji,ExclamationTriangleIcon:Mi,TimesCircleIcon:Fi},directives:{ripple:Sv}};function vo(e){"@babel/helpers - typeof";return vo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vo(e)}function Rc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function Tc(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Rc(Object(r),!0).forEach(function(o){_v(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Rc(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function _v(e,t,r){return(t=Rv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Rv(e){var t=Tv(e,"string");return vo(t)=="symbol"?t:t+""}function Tv(e,t){if(vo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(vo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var wv=["aria-label"];function Pv(e,t,r,o,n,i){var s=dp("ripple");return ce(),Re("div",pe({class:[e.cx("message"),r.message.styleClass],role:"alert","aria-live":"assertive","aria-atomic":"true"},e.ptm("message")),[r.templates.container?(ce(),st(Nr(r.templates.container),{key:0,message:r.message,closeCallback:i.onCloseClick},null,8,["message","closeCallback"])):(ce(),Re("div",pe({key:1,class:[e.cx("messageContent"),r.message.contentStyleClass]},e.ptm("messageContent")),[r.templates.message?(ce(),st(Nr(r.templates.message),{key:1,message:r.message},null,8,["message"])):(ce(),Re(Le,{key:0},[(ce(),st(Nr(r.templates.messageicon?r.templates.messageicon:r.templates.icon?r.templates.icon:i.iconComponent&&i.iconComponent.name?i.iconComponent:"span"),pe({class:e.cx("messageIcon")},e.ptm("messageIcon")),null,16,["class"])),fe("div",pe({class:e.cx("messageText")},e.ptm("messageText")),[fe("span",pe({class:e.cx("summary")},e.ptm("summary")),eo(r.message.summary),17),fe("div",pe({class:e.cx("detail")},e.ptm("detail")),eo(r.message.detail),17)],16)],64)),r.message.closable!==!1?(ce(),Re("div",gf(pe({key:2},e.ptm("buttonContainer"))),[Xf((ce(),Re("button",pe({class:e.cx("closeButton"),type:"button","aria-label":i.closeAriaLabel,onClick:t[0]||(t[0]=function(){return i.onCloseClick&&i.onCloseClick.apply(i,arguments)}),autofocus:""},Tc(Tc({},r.closeButtonProps),e.ptm("closeButton"))),[(ce(),st(Nr(r.templates.closeicon||"TimesIcon"),pe({class:[e.cx("closeIcon"),r.closeIcon]},e.ptm("closeIcon")),null,16,["class"]))],16,wv)),[[s]])],16)):Tn("",!0)],16))],16)}Ud.render=Pv;function Ev(e){return Av(e)||Bv(e)||$v(e)||Ov()}function Ov(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $v(e,t){if(e){if(typeof e=="string")return Hi(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Hi(e,t):void 0}}function Bv(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Av(e){if(Array.isArray(e))return Hi(e)}function Hi(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}var Lv=0,Hd={name:"Toast",extends:xv,inheritAttrs:!1,emits:["close","life-end"],data:function(){return{messages:[]}},styleElement:null,mounted:function(){Ye.on("add",this.onAdd),Ye.on("remove",this.onRemove),Ye.on("remove-group",this.onRemoveGroup),Ye.on("remove-all-groups",this.onRemoveAllGroups),this.breakpoints&&this.createStyle()},beforeUnmount:function(){this.destroyStyle(),this.$refs.container&&this.autoZIndex&&Xr.clear(this.$refs.container),Ye.off("add",this.onAdd),Ye.off("remove",this.onRemove),Ye.off("remove-group",this.onRemoveGroup),Ye.off("remove-all-groups",this.onRemoveAllGroups)},methods:{add:function(t){t.id==null&&(t.id=Lv++),this.messages=[].concat(Ev(this.messages),[t])},remove:function(t){var r=this.messages.findIndex(function(o){return o.id===t.message.id});r!==-1&&(this.messages.splice(r,1),this.$emit(t.type,{message:t.message}))},onAdd:function(t){this.group==t.group&&this.add(t)},onRemove:function(t){this.remove({message:t,type:"close"})},onRemoveGroup:function(t){this.group===t&&(this.messages=[])},onRemoveAllGroups:function(){this.messages=[]},onEnter:function(){this.$refs.container.setAttribute(this.attributeSelector,""),this.autoZIndex&&Xr.set("modal",this.$refs.container,this.baseZIndex||this.$primevue.config.zIndex.modal)},onLeave:function(){var t=this;this.$refs.container&&this.autoZIndex&&lr(this.messages)&&setTimeout(function(){Xr.clear(t.$refs.container)},200)},createStyle:function(){if(!this.styleElement&&!this.isUnstyled){var t;this.styleElement=document.createElement("style"),this.styleElement.type="text/css",Ld(this.styleElement,"nonce",(t=this.$primevue)===null||t===void 0||(t=t.config)===null||t===void 0||(t=t.csp)===null||t===void 0?void 0:t.nonce),document.head.appendChild(this.styleElement);var r="";for(var o in this.breakpoints){var n="";for(var i in this.breakpoints[o])n+=i+":"+this.breakpoints[o][i]+"!important;";r+=`
                        @media screen and (max-width: `.concat(o,`) {
                            .p-toast[`).concat(this.attributeSelector,`] {
                                `).concat(n,`
                            }
                        }
                    `)}this.styleElement.innerHTML=r}},destroyStyle:function(){this.styleElement&&(document.head.removeChild(this.styleElement),this.styleElement=null)}},computed:{attributeSelector:function(){return rr()}},components:{ToastMessage:Ud,Portal:Fd}};function yo(e){"@babel/helpers - typeof";return yo=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yo(e)}function wc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function Iv(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?wc(Object(r),!0).forEach(function(o){Dv(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):wc(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function Dv(e,t,r){return(t=jv(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function jv(e){var t=Mv(e,"string");return yo(t)=="symbol"?t:t+""}function Mv(e,t){if(yo(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(yo(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Nv(e,t,r,o,n,i){var s=gi("ToastMessage"),a=gi("Portal");return ce(),st(a,null,{default:tn(function(){return[fe("div",pe({ref:"container",class:e.cx("root"),style:e.sx("root",!0,{position:e.position})},e.ptmi("root")),[xe(Og,pe({name:"p-toast-message",tag:"div",onEnter:i.onEnter,onLeave:i.onLeave},Iv({},e.ptm("transition"))),{default:tn(function(){return[(ce(!0),Re(Le,null,eu(n.messages,function(c){return ce(),st(s,{key:c.id,message:c,templates:e.$slots,closeIcon:e.closeIcon,infoIcon:e.infoIcon,warnIcon:e.warnIcon,errorIcon:e.errorIcon,successIcon:e.successIcon,closeButtonProps:e.closeButtonProps,unstyled:e.unstyled,onClose:t[0]||(t[0]=function(u){return i.remove(u)}),pt:e.pt},null,8,["message","templates","closeIcon","infoIcon","warnIcon","errorIcon","successIcon","closeButtonProps","unstyled","pt"])}),128))]}),_:1},16,["onEnter","onLeave"])],16)]}),_:1})}Hd.render=Nv;var Vd=Symbol();function Fv(){var e=Ke(Vd);if(!e)throw new Error("No PrimeVue Toast provided!");return e}const zv={class:"bg-surface-0 border md:h-screen w-screen border-black/10 dark:border-white/20 dark:bg-surface-950 p-6 flex items-start gap-6 overflow-hidden flex-col md:flex-row h-screen sm:h-full"},Uv={class:"hidden sm:flex w-auto rounded-2xl p-5 bg-surface-50 dark:bg-surface-900 h-full flex-col justify-between"},Hv={class:"w-12 flex flex-col items-center"},Vv=fe("div",{class:"flex items-center gap-3"},[fe("div",{class:"w-11 h-11 border border-primary rounded-xl flex items-center justify-center"},[fe("img",{src:"https://www.chengyao.xyz/images/logo.png",alt:""})]),fe("div",{class:"hidden text-surface-950 dark:text-surface-0 font-medium text-3xl"},"Prime")],-1),Wv={class:"mt-10 flex flex-col gap-2"},Gv=fe("span",{class:"hidden"},"・",-1),Kv={class:"hidden"},qv={class:"w-12 flex flex-col items-center"},Jv={class:"justify-center flex items-center"},Yv=fe("div",null,[fe("div",{class:"hidden"},"Robin Jonas"),fe("div",{class:"hidden"},"<EMAIL>")],-1),Zv=cs({__name:"App",setup(e){const t=Fv(),r=Zh(),o=bt([]),n=["/login/token"];return ge.defaults.withCredentials=!0,ge.defaults.baseURL=cn,ge.interceptors.request.use(i=>{i.headers["Content-Type"]="application/json",i.headers.Accept="application/json";const s=qu();return s&&(i.headers.Authorization=`Bearer ${s}`),i},i=>Promise.reject(i)),ge.interceptors.response.use(i=>{const s=i.data;return s.code!==0?(t.add({severity:"error",summary:"出错了",detail:s.message,group:"br",life:3e3}),Promise.reject(new Error(s.message))):i.data.data},i=>{var s,a,c;if(!n.includes(i.config.url)&&((s=i.response)==null?void 0:s.status)===401){window.location.href=`${cn}/login?redirect_uri=${window.location.href}`;return}return t.add({severity:"error",summary:"出错了",detail:((c=(a=i.response)==null?void 0:a.data)==null?void 0:c.message)||i.message,group:"br",life:3e3}),Promise.reject(i)}),_0().then(i=>{const{user:s,token:a}=i;Ha(a),r.updateInfo(s)}).catch(i=>{Ha(""),r.updateInfo({})}),(i,s)=>{const a=gi("router-link");return ce(),Re("div",zv,[fe("div",Uv,[fe("div",Hv,[Vv,fe("div",Wv,[(ce(!0),Re(Le,null,eu(o.value,c=>(ce(),st(a,{to:c.path,key:c.name,class:"px-4 py-1 flex items-center gap-1 cursor-pointer text-base rounded-lg transition-all select-none w-12 justify-center py-4 hover:bg-emphasis text-muted-color bg-transparent","data-pd-tooltip":"true"},{default:tn(()=>[fe("i",{class:wr(c.icon)},null,2),Gv,fe("span",Kv,eo(c.name),1)]),_:2},1032,["to"]))),128))])]),fe("div",qv,[xe(it(Md)),fe("div",Jv,[xe(it(Nd),{class:"shrink-0",size:"large",shape:"circle",image:it(r).info.avatar},null,8,["image"]),Yv])])]),xe(it(Gu)),xe(it(Hd))])}}}),Xv="modulepreload",Qv=function(e){return"/"+e},Pc={},Ot=function(t,r,o){let n=Promise.resolve();if(r&&r.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),s=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));n=Promise.all(r.map(a=>{if(a=Qv(a),a in Pc)return;Pc[a]=!0;const c=a.endsWith(".css"),u=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${a}"]${u}`))return;const l=document.createElement("link");if(l.rel=c?"stylesheet":Xv,c||(l.as="script",l.crossOrigin=""),l.href=a,s&&l.setAttribute("nonce",s),document.head.appendChild(l),c)return new Promise((d,f)=>{l.addEventListener("load",d),l.addEventListener("error",()=>f(new Error(`Unable to preload CSS for ${a}`)))})}))}return n.then(()=>t()).catch(i=>{const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=i,window.dispatchEvent(s),!s.defaultPrevented)throw i})},Wd=Jh({history:_h("/"),routes:[{path:"/meeting/:id",component:()=>Ot(()=>import("./MeetingView-CJuehxxu.js"),__vite__mapDeps([0,1,2,3])),meta:{requireAuth:!0}},{path:"/login",component:()=>Ot(()=>import("./LoginView-fCbw3AFU.js"),__vite__mapDeps([4,1]))},{path:"/",component:()=>Ot(()=>import("./LoginView-fCbw3AFU.js"),__vite__mapDeps([4,1]))},{path:"/chats",component:()=>Ot(()=>import("./ChatView-D55HfngU.js"),__vite__mapDeps([5,2,1])),meta:{requireAuth:!0}},{path:"/inbox",component:()=>Ot(()=>import("./InboxView-DzMQXMJn.js"),__vite__mapDeps([6,7]))},{path:"/customers",component:()=>Ot(()=>import("./CustomersView-D32FbDsq.js"),__vite__mapDeps([8,7]))},{path:"/cards",component:()=>Ot(()=>import("./CardsView-CUYE4ppE.js"),__vite__mapDeps([9,7]))},{path:"/movies",component:()=>Ot(()=>import("./MoviesView-C9RBTOBx.js"),__vite__mapDeps([10,7]))}]});Wd.beforeEach((e,t,r)=>{var o;if((o=e.meta)!=null&&o.requireAuth&&!qu()){window.location.href=`/login?redirect_uri=${encodeURIComponent(window.location.href)}`;return}r()});var ey={install:function(t){var r={add:function(n){Ye.emit("add",n)},remove:function(n){Ye.emit("remove",n)},removeGroup:function(n){Ye.emit("remove-group",n)},removeAllGroups:function(){Ye.emit("remove-all-groups")}};t.config.globalProperties.$toast=r,t.provide(Vd,r)}},Be={STARTS_WITH:"startsWith",CONTAINS:"contains",NOT_CONTAINS:"notContains",ENDS_WITH:"endsWith",EQUALS:"equals",NOT_EQUALS:"notEquals",IN:"in",LESS_THAN:"lt",LESS_THAN_OR_EQUAL_TO:"lte",GREATER_THAN:"gt",GREATER_THAN_OR_EQUAL_TO:"gte",BETWEEN:"between",DATE_IS:"dateIs",DATE_IS_NOT:"dateIsNot",DATE_BEFORE:"dateBefore",DATE_AFTER:"dateAfter"};function Co(e){"@babel/helpers - typeof";return Co=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Co(e)}function Ec(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,o)}return r}function Qr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Ec(Object(r),!0).forEach(function(o){ty(e,o,r[o])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ec(Object(r)).forEach(function(o){Object.defineProperty(e,o,Object.getOwnPropertyDescriptor(r,o))})}return e}function ty(e,t,r){return(t=ry(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ry(e){var t=oy(e,"string");return Co(t)=="symbol"?t:t+""}function oy(e,t){if(Co(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(Co(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var ny={ripple:!1,inputStyle:null,inputVariant:null,locale:{startsWith:"Starts with",contains:"Contains",notContains:"Not contains",endsWith:"Ends with",equals:"Equals",notEquals:"Not equals",noFilter:"No Filter",lt:"Less than",lte:"Less than or equal to",gt:"Greater than",gte:"Greater than or equal to",dateIs:"Date is",dateIsNot:"Date is not",dateBefore:"Date is before",dateAfter:"Date is after",clear:"Clear",apply:"Apply",matchAll:"Match All",matchAny:"Match Any",addRule:"Add Rule",removeRule:"Remove Rule",accept:"Yes",reject:"No",choose:"Choose",upload:"Upload",cancel:"Cancel",completed:"Completed",pending:"Pending",fileSizeTypes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["Su","Mo","Tu","We","Th","Fr","Sa"],monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],chooseYear:"Choose Year",chooseMonth:"Choose Month",chooseDate:"Choose Date",prevDecade:"Previous Decade",nextDecade:"Next Decade",prevYear:"Previous Year",nextYear:"Next Year",prevMonth:"Previous Month",nextMonth:"Next Month",prevHour:"Previous Hour",nextHour:"Next Hour",prevMinute:"Previous Minute",nextMinute:"Next Minute",prevSecond:"Previous Second",nextSecond:"Next Second",am:"am",pm:"pm",today:"Today",weekHeader:"Wk",firstDayOfWeek:0,showMonthAfterYear:!1,dateFormat:"mm/dd/yy",weak:"Weak",medium:"Medium",strong:"Strong",passwordPrompt:"Enter a password",emptyFilterMessage:"No results found",searchMessage:"{0} results are available",selectionMessage:"{0} items selected",emptySelectionMessage:"No selected item",emptySearchMessage:"No results found",fileChosenMessage:"{0} files",noFileChosenMessage:"No file chosen",emptyMessage:"No available options",aria:{trueLabel:"True",falseLabel:"False",nullLabel:"Not Selected",star:"1 star",stars:"{star} stars",selectAll:"All items selected",unselectAll:"All items unselected",close:"Close",previous:"Previous",next:"Next",navigation:"Navigation",scrollTop:"Scroll Top",moveTop:"Move Top",moveUp:"Move Up",moveDown:"Move Down",moveBottom:"Move Bottom",moveToTarget:"Move to Target",moveToSource:"Move to Source",moveAllToTarget:"Move All to Target",moveAllToSource:"Move All to Source",pageLabel:"Page {page}",firstPageLabel:"First Page",lastPageLabel:"Last Page",nextPageLabel:"Next Page",prevPageLabel:"Previous Page",rowsPerPageLabel:"Rows per page",jumpToPageDropdownLabel:"Jump to Page Dropdown",jumpToPageInputLabel:"Jump to Page Input",selectRow:"Row Selected",unselectRow:"Row Unselected",expandRow:"Row Expanded",collapseRow:"Row Collapsed",showFilterMenu:"Show Filter Menu",hideFilterMenu:"Hide Filter Menu",filterOperator:"Filter Operator",filterConstraint:"Filter Constraint",editRow:"Row Edit",saveEdit:"Save Edit",cancelEdit:"Cancel Edit",listView:"List View",gridView:"Grid View",slide:"Slide",slideNumber:"{slideNumber}",zoomImage:"Zoom Image",zoomIn:"Zoom In",zoomOut:"Zoom Out",rotateRight:"Rotate Right",rotateLeft:"Rotate Left",listLabel:"Option List"}},filterMatchModeOptions:{text:[Be.STARTS_WITH,Be.CONTAINS,Be.NOT_CONTAINS,Be.ENDS_WITH,Be.EQUALS,Be.NOT_EQUALS],numeric:[Be.EQUALS,Be.NOT_EQUALS,Be.LESS_THAN,Be.LESS_THAN_OR_EQUAL_TO,Be.GREATER_THAN,Be.GREATER_THAN_OR_EQUAL_TO],date:[Be.DATE_IS,Be.DATE_IS_NOT,Be.DATE_BEFORE,Be.DATE_AFTER]},zIndex:{modal:1100,overlay:1e3,menu:1e3,tooltip:1100},theme:void 0,unstyled:!1,pt:void 0,ptOptions:{mergeSections:!0,mergeProps:!1},csp:{nonce:void 0}},iy=Symbol();function sy(e,t){var r={config:Pr(t)};return e.config.globalProperties.$primevue=r,e.provide(iy,r),ay(),cy(e,r),r}var mr=[];function ay(){ot.clear(),mr.forEach(function(e){return e==null?void 0:e()}),mr=[]}function cy(e,t){var r=bt(!1),o=function(){if(!de.isStyleNameLoaded("common")){var u,l,d=((u=Ce.getCommonTheme)===null||u===void 0?void 0:u.call(Ce))||{},f=d.primitive,p=d.semantic,m={nonce:(l=t.config)===null||l===void 0||(l=l.csp)===null||l===void 0?void 0:l.nonce};Ce.load(f==null?void 0:f.css,Qr({name:"primitive-variables"},m)),Ce.load(p==null?void 0:p.css,Qr({name:"semantic-variables"},m)),Ce.loadTheme(Qr({name:"global-style"},m)),de.setLoadedStyleName("common")}};ot.on("theme:change",function(c){r.value||(e.config.globalProperties.$primevue.config.theme=c,r.value=!0)});var n=mt(t.config,function(c,u){hr.emit("config:change",{newValue:c,oldValue:u})},{immediate:!0,deep:!0}),i=mt(function(){return t.config.ripple},function(c,u){hr.emit("config:ripple:change",{newValue:c,oldValue:u})},{immediate:!0,deep:!0}),s=mt(function(){return t.config.theme},function(c,u){r.value||de.setTheme(c),t.config.unstyled||o(),r.value=!1,hr.emit("config:theme:change",{newValue:c,oldValue:u})},{immediate:!0,deep:!0}),a=mt(function(){return t.config.unstyled},function(c,u){!c&&t.config.theme&&o(),hr.emit("config:unstyled:change",{newValue:c,oldValue:u})},{immediate:!0,deep:!0});mr.push(n),mr.push(i),mr.push(s),mr.push(a)}var ly={install:function(t,r){var o=Qr(Qr({},ny),r);sy(t,o)}},uy={root:{transitionDuration:"{transition.duration}"},panel:{borderWidth:"0 0 1px 0",borderColor:"{content.border.color}"},header:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{text.color}",padding:"1.125rem",fontWeight:"600",borderRadius:"0",borderWidth:"0",borderColor:"{content.border.color}",background:"{content.background}",hoverBackground:"{content.background}",activeBackground:"{content.background}",activeHoverBackground:"{content.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},toggleIcon:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{text.color}",activeHoverColor:"{text.color}"},first:{topBorderRadius:"{content.border.radius}",borderWidth:"0"},last:{bottomBorderRadius:"{content.border.radius}",activeBottomBorderRadius:"0"}},content:{borderWidth:"0",borderColor:"{content.border.color}",background:"{content.background}",color:"{text.color}",padding:"0 1.125rem 1.125rem 1.125rem"}},dy={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}"},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},dropdown:{width:"2.5rem",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},chip:{borderRadius:"{border.radius.sm}"},emptyMessage:{padding:"{list.option.padding}"},colorScheme:{light:{dropdown:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"}},dark:{dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"}}}},fy={root:{width:"2rem",height:"2rem",fontSize:"1rem",background:"{content.border.color}",borderRadius:"{content.border.radius}"},group:{borderColor:"{content.background}",offset:"-1rem"},lg:{width:"3rem",height:"3rem",fontSize:"1.5rem"},xl:{width:"4rem",height:"4rem",fontSize:"2rem"}},py={root:{borderRadius:"{border.radius.md}",padding:"0 0.5rem",fontSize:"0.75rem",fontWeight:"700",minWidth:"1.5rem",height:"1.5rem"},dot:{size:"0.5rem"},sm:{fontSize:"0.625rem",minWidth:"1.25rem",height:"1.25rem"},lg:{fontSize:"0.875rem",minWidth:"1.75rem",height:"1.75rem"},xl:{fontSize:"1rem",minWidth:"2rem",height:"2rem"},colorScheme:{light:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.100}",color:"{surface.600}"},success:{background:"{green.500}",color:"{surface.0}"},info:{background:"{sky.500}",color:"{surface.0}"},warn:{background:"{orange.500}",color:"{surface.0}"},danger:{background:"{red.500}",color:"{surface.0}"},contrast:{background:"{surface.950}",color:"{surface.0}"}},dark:{primary:{background:"{primary.color}",color:"{primary.contrast.color}"},secondary:{background:"{surface.800}",color:"{surface.300}"},success:{background:"{green.400}",color:"{green.950}"},info:{background:"{sky.400}",color:"{sky.950}"},warn:{background:"{orange.400}",color:"{orange.950}"},danger:{background:"{red.400}",color:"{red.950}"},contrast:{background:"{surface.0}",color:"{surface.950}"}}}},gy={root:{borderRadius:"{content.border.radius}"}},hy={root:{padding:"1rem",background:"{content.background}",gap:"0.5rem",transitionDuration:"{transition.duration}"},item:{color:"{text.muted.color}",hoverColor:"{text.color}",borderRadius:"{content.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",hoverColor:"{navigation.item.icon.focus.color}"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},separator:{color:"{navigation.item.icon.color}"}},my={root:{borderRadius:"{form.field.border.radius}",roundedBorderRadius:"2rem",gap:"0.5rem",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",iconOnlyWidth:"2.5rem",sm:{fontSize:"0.875rem",paddingX:"0.625rem",paddingY:"0.375rem"},lg:{fontSize:"1.125rem",paddingX:"0.875rem",paddingY:"0.625rem"},label:{fontWeight:"500"},raisedShadow:"0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"},badgeSize:"1rem",transitionDuration:"{form.field.transition.duration}"},colorScheme:{light:{root:{primary:{background:"{primary.color}",hoverBackground:"{primary.hover.color}",activeBackground:"{primary.active.color}",borderColor:"{primary.color}",hoverBorderColor:"{primary.hover.color}",activeBorderColor:"{primary.active.color}",color:"{primary.contrast.color}",hoverColor:"{primary.contrast.color}",activeColor:"{primary.contrast.color}",focusRing:{color:"{primary.color}",shadow:"none"}},secondary:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",borderColor:"{surface.100}",hoverBorderColor:"{surface.200}",activeBorderColor:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}",focusRing:{color:"{surface.600}",shadow:"none"}},info:{background:"{sky.500}",hoverBackground:"{sky.600}",activeBackground:"{sky.700}",borderColor:"{sky.500}",hoverBorderColor:"{sky.600}",activeBorderColor:"{sky.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{sky.500}",shadow:"none"}},success:{background:"{green.500}",hoverBackground:"{green.600}",activeBackground:"{green.700}",borderColor:"{green.500}",hoverBorderColor:"{green.600}",activeBorderColor:"{green.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{green.500}",shadow:"none"}},warn:{background:"{orange.500}",hoverBackground:"{orange.600}",activeBackground:"{orange.700}",borderColor:"{orange.500}",hoverBorderColor:"{orange.600}",activeBorderColor:"{orange.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{orange.500}",shadow:"none"}},help:{background:"{purple.500}",hoverBackground:"{purple.600}",activeBackground:"{purple.700}",borderColor:"{purple.500}",hoverBorderColor:"{purple.600}",activeBorderColor:"{purple.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{purple.500}",shadow:"none"}},danger:{background:"{red.500}",hoverBackground:"{red.600}",activeBackground:"{red.700}",borderColor:"{red.500}",hoverBorderColor:"{red.600}",activeBorderColor:"{red.700}",color:"#ffffff",hoverColor:"#ffffff",activeColor:"#ffffff",focusRing:{color:"{red.500}",shadow:"none"}},contrast:{background:"{surface.950}",hoverBackground:"{surface.900}",activeBackground:"{surface.800}",borderColor:"{surface.950}",hoverBorderColor:"{surface.900}",activeBorderColor:"{surface.800}",color:"{surface.0}",hoverColor:"{surface.0}",activeColor:"{surface.0}",focusRing:{color:"{surface.950}",shadow:"none"}}},outlined:{primary:{hoverBackground:"{primary.50}",activeBackground:"{primary.100}",borderColor:"{primary.200}",color:"{primary.color}"},secondary:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.200}",color:"{surface.500}"},success:{hoverBackground:"{green.50}",activeBackground:"{green.100}",borderColor:"{green.200}",color:"{green.500}"},info:{hoverBackground:"{sky.50}",activeBackground:"{sky.100}",borderColor:"{sky.200}",color:"{sky.500}"},warn:{hoverBackground:"{orange.50}",activeBackground:"{orange.100}",borderColor:"{orange.200}",color:"{orange.500}"},help:{hoverBackground:"{purple.50}",activeBackground:"{purple.100}",borderColor:"{purple.200}",color:"{purple.500}"},danger:{hoverBackground:"{red.50}",activeBackground:"{red.100}",borderColor:"{red.200}",color:"{red.500}"},contrast:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.700}",color:"{surface.950}"},plain:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",borderColor:"{surface.200}",color:"{surface.700}"}},text:{primary:{hoverBackground:"{primary.50}",activeBackground:"{primary.100}",color:"{primary.color}"},secondary:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.500}"},success:{hoverBackground:"{green.50}",activeBackground:"{green.100}",color:"{green.500}"},info:{hoverBackground:"{sky.50}",activeBackground:"{sky.100}",color:"{sky.500}"},warn:{hoverBackground:"{orange.50}",activeBackground:"{orange.100}",color:"{orange.500}"},help:{hoverBackground:"{purple.50}",activeBackground:"{purple.100}",color:"{purple.500}"},danger:{hoverBackground:"{red.50}",activeBackground:"{red.100}",color:"{red.500}"},plain:{hoverBackground:"{surface.50}",activeBackground:"{surface.100}",color:"{surface.700}"}},link:{color:"{primary.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}},dark:{root:{primary:{background:"{primary.color}",hoverBackground:"{primary.hover.color}",activeBackground:"{primary.active.color}",borderColor:"{primary.color}",hoverBorderColor:"{primary.hover.color}",activeBorderColor:"{primary.active.color}",color:"{primary.contrast.color}",hoverColor:"{primary.contrast.color}",activeColor:"{primary.contrast.color}",focusRing:{color:"{primary.color}",shadow:"none"}},secondary:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",borderColor:"{surface.800}",hoverBorderColor:"{surface.700}",activeBorderColor:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}",focusRing:{color:"{surface.300}",shadow:"none"}},info:{background:"{sky.400}",hoverBackground:"{sky.300}",activeBackground:"{sky.200}",borderColor:"{sky.400}",hoverBorderColor:"{sky.300}",activeBorderColor:"{sky.200}",color:"{sky.950}",hoverColor:"{sky.950}",activeColor:"{sky.950}",focusRing:{color:"{sky.400}",shadow:"none"}},success:{background:"{green.400}",hoverBackground:"{green.300}",activeBackground:"{green.200}",borderColor:"{green.400}",hoverBorderColor:"{green.300}",activeBorderColor:"{green.200}",color:"{green.950}",hoverColor:"{green.950}",activeColor:"{green.950}",focusRing:{color:"{green.400}",shadow:"none"}},warn:{background:"{orange.400}",hoverBackground:"{orange.300}",activeBackground:"{orange.200}",borderColor:"{orange.400}",hoverBorderColor:"{orange.300}",activeBorderColor:"{orange.200}",color:"{orange.950}",hoverColor:"{orange.950}",activeColor:"{orange.950}",focusRing:{color:"{orange.400}",shadow:"none"}},help:{background:"{purple.400}",hoverBackground:"{purple.300}",activeBackground:"{purple.200}",borderColor:"{purple.400}",hoverBorderColor:"{purple.300}",activeBorderColor:"{purple.200}",color:"{purple.950}",hoverColor:"{purple.950}",activeColor:"{purple.950}",focusRing:{color:"{purple.400}",shadow:"none"}},danger:{background:"{red.400}",hoverBackground:"{red.300}",activeBackground:"{red.200}",borderColor:"{red.400}",hoverBorderColor:"{red.300}",activeBorderColor:"{red.200}",color:"{red.950}",hoverColor:"{red.950}",activeColor:"{red.950}",focusRing:{color:"{red.400}",shadow:"none"}},contrast:{background:"{surface.0}",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",borderColor:"{surface.0}",hoverBorderColor:"{surface.100}",activeBorderColor:"{surface.200}",color:"{surface.950}",hoverColor:"{surface.950}",activeColor:"{surface.950}",focusRing:{color:"{surface.0}",shadow:"none"}}},outlined:{primary:{hoverBackground:"color-mix(in srgb, {primary.color}, transparent 96%)",activeBackground:"color-mix(in srgb, {primary.color}, transparent 84%)",borderColor:"{primary.700}",color:"{primary.color}"},secondary:{hoverBackground:"rgba(255,255,255,0.04)",activeBackground:"rgba(255,255,255,0.16)",borderColor:"{surface.700}",color:"{surface.400}"},success:{hoverBackground:"color-mix(in srgb, {green.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {green.400}, transparent 84%)",borderColor:"{green.700}",color:"{green.400}"},info:{hoverBackground:"color-mix(in srgb, {sky.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {sky.400}, transparent 84%)",borderColor:"{sky.700}",color:"{sky.400}"},warn:{hoverBackground:"color-mix(in srgb, {orange.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {orange.400}, transparent 84%)",borderColor:"{orange.700}",color:"{orange.400}"},help:{hoverBackground:"color-mix(in srgb, {help.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {help.400}, transparent 84%)",borderColor:"{purple.700}",color:"{purple.400}"},danger:{hoverBackground:"color-mix(in srgb, {danger.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {danger.400}, transparent 84%)",borderColor:"{red.700}",color:"{red.400}"},contrast:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.500}",color:"{surface.0}"},plain:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{surface.600}",color:"{surface.0}"}},text:{primary:{hoverBackground:"color-mix(in srgb, {primary.color}, transparent 96%)",activeBackground:"color-mix(in srgb, {primary.color}, transparent 84%)",color:"{primary.color}"},secondary:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.400}"},success:{hoverBackground:"color-mix(in srgb, {green.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {green.400}, transparent 84%)",color:"{green.400}"},info:{hoverBackground:"color-mix(in srgb, {sky.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {sky.400}, transparent 84%)",color:"{sky.400}"},warn:{hoverBackground:"color-mix(in srgb, {orange.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {orange.400}, transparent 84%)",color:"{orange.400}"},help:{hoverBackground:"color-mix(in srgb, {purple.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {purple.400}, transparent 84%)",color:"{purple.400}"},danger:{hoverBackground:"color-mix(in srgb, {red.400}, transparent 96%)",activeBackground:"color-mix(in srgb, {red.400}, transparent 84%)",color:"{red.400}"},plain:{hoverBackground:"{surface.800}",activeBackground:"{surface.700}",color:"{surface.0}"}},link:{color:"{primary.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}}}},by={root:{background:"{content.background}",borderRadius:"{border.radius.xl}",color:"{content.color}",shadow:"0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)"},body:{padding:"1.25rem",gap:"0.5rem"},caption:{gap:"0.5rem"},title:{fontSize:"1.25rem",fontWeight:"500"},subtitle:{color:"{text.muted.color}"}},vy={root:{transitionDuration:"{transition.duration}"},content:{gap:"0.25rem"},indicatorList:{padding:"1rem",gap:"0.5rem"},indicator:{width:"2rem",height:"0.5rem",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},colorScheme:{light:{indicator:{background:"{surface.200}",hoverBackground:"{surface.300}",activeBackground:"{primary.color}"}},dark:{indicator:{background:"{surface.700}",hoverBackground:"{surface.600}",activeBackground:"{primary.color}"}}}},yy={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}"},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}",icon:{color:"{list.option.icon.color}",focusColor:"{list.option.icon.focus.color}",size:"0.875rem"}}},Cy={root:{borderRadius:"{border.radius.sm}",width:"1.25rem",height:"1.25rem",background:"{form.field.background}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.border.color}",checkedBorderColor:"{primary.color}",checkedHoverBorderColor:"{primary.hover.color}",checkedFocusBorderColor:"{primary.color}",checkedDisabledBorderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},icon:{size:"0.875rem",color:"{form.field.color}",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}",disabledColor:"{form.field.disabled.color}"}},ky={root:{borderRadius:"16px",paddingX:"0.75rem",paddingY:"0.5rem",gap:"0.5rem",transitionDuration:"{transition.duration}"},image:{width:"2rem",height:"2rem"},icon:{size:"1rem"},removeIcon:{size:"1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"}},colorScheme:{light:{root:{background:"{surface.100}",color:"{surface.800}"},icon:{color:"{surface.800}"},removeIcon:{color:"{surface.800}"}},dark:{root:{background:"{surface.800}",color:"{surface.0}"},icon:{color:"{surface.0}"},removeIcon:{color:"{surface.0}"}}}},Sy={root:{transitionDuration:"{transition.duration}"},preview:{width:"1.5rem",height:"1.5rem",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},panel:{shadow:"{overlay.popover.shadow}",borderRadius:"{overlay.popover.borderRadius}"},colorScheme:{light:{panel:{background:"{surface.800}",borderColor:"{surface.900}"},handle:{color:"{surface.0}"}},dark:{panel:{background:"{surface.900}",borderColor:"{surface.700}"},handle:{color:"{surface.0}"}}}},xy={icon:{size:"2rem",color:"{overlay.modal.color}"},content:{gap:"1rem"}},_y={root:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",color:"{overlay.popover.color}",borderRadius:"{overlay.popover.border.radius}",shadow:"{overlay.popover.shadow}",gutter:"10px",arrowOffset:"1.25rem"},content:{padding:"{overlay.popover.padding}",gap:"1rem"},icon:{size:"1.5rem",color:"{overlay.popover.color}"},footer:{gap:"0.5rem",padding:"0 {overlay.popover.padding} {overlay.popover.padding} {overlay.popover.padding}"}},Ry={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},list:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},submenuIcon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},separator:{borderColor:"{content.border.color}"}},Ty={root:{transitionDuration:"{transition.duration}"},header:{background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},headerCell:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{datatable.border.color}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",gap:"0.5rem",padding:"0.75rem 1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},columnTitle:{fontWeight:"600"},row:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},bodyCell:{borderColor:"{datatable.border.color}",padding:"0.75rem 1rem"},footerCell:{background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",padding:"0.75rem 1rem"},columnFooter:{fontWeight:"600"},footer:{background:"{content.background}",borderColor:"{datatable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},dropPointColor:"{primary.color}",columnResizerWidth:"0.5rem",resizeIndicator:{width:"1px",color:"{primary.color}"},sortIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}"},loadingIcon:{size:"2rem"},rowToggleButton:{hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",selectedHoverColor:"{primary.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},filter:{inlineGap:"0.5rem",overlaySelect:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},overlayPopover:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",borderRadius:"{overlay.popover.border.radius}",color:"{overlay.popover.color}",shadow:"{overlay.popover.shadow}",padding:"{overlay.popover.padding}",gap:"0.5rem"},rule:{borderColor:"{content.border.color}"},constraintList:{padding:"{list.padding}",gap:"{list.gap}"},constraint:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",separator:{borderColor:"{content.border.color}"},padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"}},paginatorTop:{borderColor:"{datatable.border.color}",borderWidth:"0 0 1px 0"},paginatorBottom:{borderColor:"{datatable.border.color}",borderWidth:"0 0 1px 0"},colorScheme:{light:{root:{borderColor:"{content.border.color}"},row:{stripedBackground:"{surface.50}"},bodyCell:{selectedBorderColor:"{primary.100}"}},dark:{root:{borderColor:"{surface.800}"},row:{stripedBackground:"{surface.950}"},bodyCell:{selectedBorderColor:"{primary.900}"}}}},wy={root:{borderColor:"transparent",borderWidth:"0",borderRadius:"0",padding:"0"},header:{background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem",borderRadius:"0"},content:{background:"{content.background}",color:"{content.color}",borderColor:"transparent",borderWidth:"0",padding:"0",borderRadius:"0"},footer:{background:"{content.background}",color:"{content.color}",borderColor:"{content.border.color}",borderWidth:"1px 0 0 0",padding:"0.75rem 1rem",borderRadius:"0"},paginatorTop:{borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},paginatorBottom:{borderColor:"{content.border.color}",borderWidth:"1px 0 0 0"}},Py={root:{transitionDuration:"{transition.duration}"},panel:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.popover.shadow}",padding:"{overlay.popover.padding}"},header:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",padding:"0 0 0.5rem 0",fontWeight:"500",gap:"0.5rem"},title:{gap:"0.5rem",fontWeight:"500"},dropdown:{width:"2.5rem",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},inputIcon:{color:"{form.field.icon.color}"},selectMonth:{hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}"},selectYear:{hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}"},group:{borderColor:"{content.border.color}",gap:"{overlay.popover.padding}"},dayView:{margin:"0.5rem 0 0 0"},weekDay:{padding:"0.25rem",fontWeight:"500",color:"{content.color}"},date:{hoverBackground:"{content.hover.background}",selectedBackground:"{primary.color}",rangeSelectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{primary.contrast.color}",rangeSelectedColor:"{highlight.color}",width:"2rem",height:"2rem",borderRadius:"50%",padding:"0.25rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},monthView:{margin:"0.5rem 0 0 0"},month:{borderRadius:"{content.border.radius}"},yearView:{margin:"0.5rem 0 0 0"},year:{borderRadius:"{content.border.radius}"},buttonbar:{padding:"0.5rem 0 0 0",borderColor:"{content.border.color}"},timePicker:{padding:"0.5rem 0 0 0",borderColor:"{content.border.color}",gap:"0.5rem",buttonGap:"0.25rem"},colorScheme:{light:{dropdown:{background:"{surface.100}",hoverBackground:"{surface.200}",activeBackground:"{surface.300}",color:"{surface.600}",hoverColor:"{surface.700}",activeColor:"{surface.800}"},today:{background:"{surface.200}",color:"{surface.900}"}},dark:{dropdown:{background:"{surface.800}",hoverBackground:"{surface.700}",activeBackground:"{surface.600}",color:"{surface.300}",hoverColor:"{surface.200}",activeColor:"{surface.100}"},today:{background:"{surface.700}",color:"{surface.0}"}}}},Ey={root:{background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",borderRadius:"{overlay.modal.border.radius}",shadow:"{overlay.modal.shadow}"},header:{padding:"{overlay.modal.padding}",gap:"0.5rem"},title:{fontSize:"1.25rem",fontWeight:"600"},content:{padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"},footer:{padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}",gap:"0.5rem"}},Oy={root:{borderColor:"{content.border.color}"},content:{background:"{content.background}",color:"{text.color}"},horizontal:{margin:"1rem 0",padding:"0 1rem",content:{padding:"0 0.5rem"}},vertical:{margin:"0 1rem",padding:"0.5rem 0",content:{padding:"0.5rem 0"}}},$y={root:{background:"rgba(255, 255, 255, 0.1)",borderColor:"rgba(255, 255, 255, 0.2)",padding:"0.5rem",borderRadius:"{border.radius.xl}"},item:{borderRadius:"{content.border.radius}",padding:"0.5rem",size:"3rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}},By={root:{background:"{overlay.modal.background}",borderColor:"{overlay.modal.border.color}",color:"{overlay.modal.color}",borderRadius:"{overlay.modal.border.radius}",shadow:"{overlay.modal.shadow}"},header:{padding:"{overlay.modal.padding}"},title:{fontSize:"1.5rem",fontWeight:"600"},content:{padding:"0 {overlay.modal.padding} {overlay.modal.padding} {overlay.modal.padding}"}},Ay={toolbar:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}"},toolbarItem:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}",padding:"{list.padding}"},overlayOption:{focusBackground:"{list.option.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},content:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"}},Ly={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",padding:"0 1.125rem 1.125rem 1.125rem",transitionDuration:"{transition.duration}"},legend:{background:"{content.background}",hoverBackground:"{content.hover.background}",color:"{content.color}",hoverColor:"{content.hover.color}",borderRadius:"{content.border.radius}",borderWidth:"1px",borderColor:"transparent",padding:"0.5rem 0.75rem",gap:"0.5rem",fontWeight:"600",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},toggleIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}"},content:{padding:"0"}},Iy={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},header:{background:"transparent",color:"{text.color}",padding:"1.125rem",borderWidth:"0",borderRadius:"0",gap:"0.5rem"},content:{highlightBorderColor:"{primary.color}",padding:"0 1.125rem 1.125rem 1.125rem"},file:{padding:"1rem",gap:"1rem",borderColor:"{content.border.color}",info:{gap:"0.5rem"}},progressbar:{height:"0.25rem"},basic:{gap:"0.5rem"}},Dy={root:{color:"{form.field.float.label.color}",focusColor:"{form.field.float.label.focus.color}",invalidColor:"{form.field.float.label.invalid.color}",transitionDuration:"0.2s"}},jy={root:{borderWidth:"1px",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",transitionDuration:"{transition.duration}"},navButton:{background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.100}",hoverColor:"{surface.0}",size:"3rem",gutter:"0.5rem",prev:{borderRadius:"50%"},next:{borderRadius:"50%"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},navIcon:{size:"1.5rem"},thumbnailsContent:{background:"{content.background}",padding:"1rem 0.25rem"},thumbnailNavButton:{size:"2rem",borderRadius:"{content.border.radius}",gutter:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},thumbnailNavButtonIcon:{size:"1rem"},caption:{background:"rgba(0, 0, 0, 0.5)",color:"{surface.100}",padding:"1rem"},indicatorList:{gap:"0.5rem",padding:"1rem"},indicatorButton:{width:"1rem",height:"1rem",activeBackground:"{primary.color}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},insetIndicatorList:{background:"rgba(0, 0, 0, 0.5)"},insetIndicatorButton:{background:"rgba(255, 255, 255, 0.4)",hoverBackground:"rgba(255, 255, 255, 0.6)",activeBackground:"rgba(255, 255, 255, 0.9)"},mask:{background:"{mask.background}",color:"{mask.color}"},closeButton:{size:"3rem",gutter:"0.5rem",background:"rgba(255, 255, 255, 0.1)",hoverBackground:"rgba(255, 255, 255, 0.2)",color:"{surface.50}",hoverColor:"{surface.0}",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},closeButtonIcon:{size:"1.5rem"},colorScheme:{light:{thumbnailNavButton:{hoverBackground:"{surface.100}",color:"{surface.600}",hoverColor:"{surface.700}"},indicatorButton:{background:"{surface.200}",hoverBackground:"{surface.300}"}},dark:{thumbnailNavButton:{hoverBackground:"{surface.700}",color:"{surface.400}",hoverColor:"{surface.0}"},indicatorButton:{background:"{surface.700}",hoverBackground:"{surface.600}"}}}},My={icon:{color:"{form.field.icon.color}"}},Ny={root:{transitionDuration:"{transition.duration}"},preview:{icon:{size:"1.5rem"},mask:{background:"{mask.background}",color:"{mask.color}"}},toolbar:{position:{left:"auto",right:"1rem",top:"1rem",bottom:"auto"},blur:"8px",background:"rgba(255,255,255,0.1)",borderColor:"rgba(255,255,255,0.2)",borderWidth:"1px",borderRadius:"30px",padding:".5rem",gap:"0.5rem"},action:{hoverBackground:"rgba(255,255,255,0.1)",color:"{surface.50}",hoverColor:"{surface.0}",size:"3rem",iconSize:"1.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}},Fy={root:{padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{content.border.radius}",gap:"0.5rem"},text:{fontWeight:"500"},icon:{size:"1rem"},colorScheme:{light:{info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)"},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)"},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)"},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)"},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)"},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)"}},dark:{info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)"},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)"},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)"},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)"},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)"},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)"}}}},zy={root:{padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{transition.duration}"},display:{hoverBackground:"{content.hover.background}",hoverColor:"{content.hover.color}"}},Uy={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},chip:{borderRadius:"{border.radius.sm}"},colorScheme:{light:{chip:{focusBackground:"{surface.200}",color:"{surface.800}"}},dark:{chip:{focusBackground:"{surface.700}",color:"{surface.0}"}}}},Hy={addon:{background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.icon.color}",borderRadius:"{form.field.border.radius}"}},Vy={root:{transitionDuration:"{transition.duration}"},button:{width:"2.5rem",borderRadius:"{form.field.border.radius}",verticalPadding:"{form.field.padding.y}"},colorScheme:{light:{button:{background:"transparent",hoverBackground:"{surface.100}",activeBackground:"{surface.200}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",color:"{surface.400}",hoverColor:"{surface.500}",activeColor:"{surface.600}"}},dark:{button:{background:"transparent",hoverBackground:"{surface.800}",activeBackground:"{surface.700}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.border.color}",activeBorderColor:"{form.field.border.color}",color:"{surface.400}",hoverColor:"{surface.300}",activeColor:"{surface.200}"}}}},Wy={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}",sm:{fontSize:"0.875rem",paddingX:"0.625rem",paddingY:"0.375rem"},lg:{fontSize:"1.125rem",paddingX:"0.875rem",paddingY:"0.625rem"}}},Gy={root:{transitionDuration:"{transition.duration}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},value:{background:"{primary.color}"},range:{background:"{content.border.color}"},text:{color:"{text.muted.color}"}},Ky={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",shadow:"{form.field.shadow}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},list:{padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},checkmark:{color:"{list.option.color}",gutterStart:"-0.375rem",gutterEnd:"0.375rem"},emptyMessage:{padding:"{list.option.padding}"},colorScheme:{light:{option:{stripedBackground:"{surface.50}"}},dark:{option:{stripedBackground:"{surface.900}"}}}},qy={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",verticalOrientation:{padding:"{navigation.list.padding}",gap:"0"},horizontalOrientation:{padding:"0.5rem 0.75rem"},transitionDuration:"{transition.duration}"},baseItem:{borderRadius:"{content.border.radius}",padding:"{navigation.item.padding}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},overlay:{padding:"0",background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",shadow:"{overlay.navigation.shadow}",gap:"0.5rem"},submenu:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},submenuLabel:{padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background.}",color:"{navigation.submenu.label.color}"},submenuIcon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},separator:{borderColor:"{content.border.color}"},mobileButton:{borderRadius:"50%",size:"1.75rem",color:"{text.muted.color}",hoverColor:"{text.muted.hover.color}",hoverBackground:"{content.hover.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}},Jy={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},list:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},item:{focusBackground:"{navigation.item.focus.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}"}},submenuLabel:{padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background}",color:"{navigation.submenu.label.color}"},separator:{borderColor:"{content.border.color}"}},Yy={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",padding:"0.5rem 0.75rem",transitionDuration:"{transition.duration}"},baseItem:{borderRadius:"{content.border.radius}",padding:"{navigation.item.padding}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},submenu:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}",background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",mobileIndent:"1rem"},submenuIcon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},separator:{borderColor:"{content.border.color}"},mobileButton:{borderRadius:"50%",size:"1.75rem",color:"{text.muted.color}",hoverColor:"{text.muted.hover.color}",hoverBackground:"{content.hover.background}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}},Zy={root:{borderRadius:"{content.border.radius}",borderWidth:"1px",transitionDuration:"{transition.duration}"},content:{padding:"0.5rem 0.75rem",gap:"0.5rem"},text:{fontSize:"1rem",fontWeight:"500"},icon:{size:"1.125rem"},closeButton:{width:"1.75rem",height:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"}},closeIcon:{size:"1rem"},colorScheme:{light:{info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"{blue.100}",focusRing:{color:"{blue.600}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"{green.100}",focusRing:{color:"{green.600}",shadow:"none"}}},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"{yellow.100}",focusRing:{color:"{yellow.600}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"{red.100}",focusRing:{color:"{red.600}",shadow:"none"}}},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.200}",focusRing:{color:"{surface.600}",shadow:"none"}}},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.800}",focusRing:{color:"{surface.50}",shadow:"none"}}}},dark:{info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{blue.500}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{green.500}",shadow:"none"}}},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{yellow.500}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{red.500}",shadow:"none"}}},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.700}",focusRing:{color:"{surface.300}",shadow:"none"}}},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.100}",focusRing:{color:"{surface.950}",shadow:"none"}}}}}},Xy={root:{borderRadius:"{content.border.radius}",gap:"1rem"},meters:{background:"{content.border.color}",size:"0.5rem"},label:{gap:"0.5rem"},labelMarker:{size:"0.5rem"},labelIcon:{size:"1rem"},labelList:{verticalGap:"0.5rem",horizontalGap:"1rem"}},Qy={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}",gap:"0.5rem"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},chip:{borderRadius:"{border.radius.sm}"},emptyMessage:{padding:"{list.option.padding}"}},e1={root:{gap:"1.125rem"},controls:{gap:"0.5rem"}},t1={root:{gutter:"0.75rem",transitionDuration:"{transition.duration}"},node:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{content.border.color}",color:"{content.color}",selectedColor:"{highlight.color}",hoverColor:"{content.hover.color}",padding:"0.75rem 1rem",toggleablePadding:"0.75rem 1rem 1.25rem 1rem",borderRadius:"{content.border.radius}"},nodeToggleButton:{background:"{content.background}",hoverBackground:"{content.hover.background}",borderColor:"{content.border.color}",color:"{text.muted.color}",hoverColor:"{text.color}",size:"1.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},connector:{color:"{content.border.color}",borderRadius:"{content.border.radius}",height:"24px"}},r1={root:{outline:{width:"2px",color:"{content.background}"}}},o1={root:{padding:"0.5rem 1rem",gap:"0.25rem",borderRadius:"{content.border.radius}",background:"{content.background}",color:"{content.color}",transitionDuration:"{transition.duration}"},navButton:{background:"transparent",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedColor:"{highlight.color}",width:"2.5rem",height:"2.5rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},currentPageReport:{color:"{text.muted.color}"},jumpToPageInput:{maxWidth:"2.5rem"}},n1={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}"},header:{background:"transparent",color:"{text.color}",padding:"1.125rem",borderColor:"{content.border.color}",borderWidth:"0",borderRadius:"0"},toggleableHeader:{padding:"0.375rem 1.125rem"},title:{fontWeight:"600"},content:{padding:"0 1.125rem 1.125rem 1.125rem"},footer:{padding:"0 1.125rem 1.125rem 1.125rem"}},i1={root:{gap:"0.5rem",transitionDuration:"{transition.duration}"},panel:{background:"{content.background}",borderColor:"{content.border.color}",borderWidth:"1px",color:"{content.color}",padding:"0.25rem 0.25rem",borderRadius:"{content.border.radius}",first:{borderWidth:"1px",topBorderRadius:"{content.border.radius}"},last:{borderWidth:"1px",bottomBorderRadius:"{content.border.radius}"}},item:{focusBackground:"{navigation.item.focus.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",gap:"0.5rem",padding:"{navigation.item.padding}",borderRadius:"{content.border.radius}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}"}},submenu:{indent:"1rem"},submenuIcon:{color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}"}},s1={meter:{background:"{content.border.color}",borderRadius:"{content.border.radius}",height:".75rem"},icon:{color:"{form.field.icon.color}"},overlay:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",borderRadius:"{overlay.popover.border.radius}",color:"{overlay.popover.color}",padding:"{overlay.popover.padding}",shadow:"{overlay.popover.shadow}"},content:{gap:"0.5rem"},colorScheme:{light:{strength:{weakBackground:"{red.500}",mediumBackground:"{amber.500}",strongBackground:"{green.500}"}},dark:{strength:{weakBackground:"{red.400}",mediumBackground:"{amber.400}",strongBackground:"{green.400}"}}}},a1={root:{gap:"1.125rem"},controls:{gap:"0.5rem"}},c1={root:{background:"{overlay.popover.background}",borderColor:"{overlay.popover.border.color}",color:"{overlay.popover.color}",borderRadius:"{overlay.popover.border.radius}",shadow:"{overlay.popover.shadow}",gutter:"10px",arrowOffset:"1.25rem"},content:{padding:"{overlay.popover.padding}"}},l1={root:{background:"{content.border.color}",borderRadius:"{content.border.radius}",height:"1.25rem"},value:{background:"{primary.color}"},label:{color:"{primary.contrast.color}",fontSize:"0.75rem",fontWeight:"600"}},u1={colorScheme:{light:{root:{"color.1":"{red.500}","color.2":"{blue.500}","color.3":"{green.500}","color.4":"{yellow.500}"}},dark:{root:{"color.1":"{red.400}","color.2":"{blue.400}","color.3":"{green.400}","color.4":"{yellow.400}"}}}},d1={root:{width:"1.25rem",height:"1.25rem",background:"{form.field.background}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.border.color}",checkedBorderColor:"{primary.color}",checkedHoverBorderColor:"{primary.hover.color}",checkedFocusBorderColor:"{primary.color}",checkedDisabledBorderColor:"{form.field.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},icon:{size:"0.75rem",checkedColor:"{primary.contrast.color}",checkedHoverColor:"{primary.contrast.color}",disabledColor:"{form.field.disabled.color}"}},f1={root:{gap:"0.25rem",transitionDuration:"{transition.duration}"},icon:{size:"1rem",color:"{text.muted.color}",hoverColor:"{primary.color}",activeColor:"{primary.color}"}},p1={colorScheme:{light:{root:{background:"rgba(0,0,0,0.1)"}},dark:{root:{background:"rgba(255,255,255,0.3)"}}}},g1={root:{transitionDuration:"{transition.duration}"},bar:{size:"9px",borderRadius:"{border.radius.sm}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},colorScheme:{light:{bar:{background:"{surface.100}"}},dark:{bar:{background:"{surface.800}"}}}},h1={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},list:{padding:"{list.padding}",gap:"{list.gap}",header:{padding:"{list.header.padding}"}},option:{focusBackground:"{list.option.focus.background}",selectedBackground:"{list.option.selected.background}",selectedFocusBackground:"{list.option.selected.focus.background}",color:"{list.option.color}",focusColor:"{list.option.focus.color}",selectedColor:"{list.option.selected.color}",selectedFocusColor:"{list.option.selected.focus.color}",padding:"{list.option.padding}",borderRadius:"{list.option.border.radius}"},optionGroup:{background:"{list.option.group.background}",color:"{list.option.group.color}",fontWeight:"{list.option.group.font.weight}",padding:"{list.option.group.padding}"},clearIcon:{color:"{form.field.icon.color}"},checkmark:{color:"{list.option.color}",gutterStart:"-0.375rem",gutterEnd:"0.375rem"},emptyMessage:{padding:"{list.option.padding}"}},m1={root:{borderRadius:"{form.field.border.radius}"},colorScheme:{light:{root:{invalidBorderColor:"{form.field.invalid.border.color}"}},dark:{root:{invalidBorderColor:"{form.field.invalid.border.color}"}}}},b1={root:{borderRadius:"{content.border.radius}"},colorScheme:{light:{root:{background:"{surface.200}",animationBackground:"rgba(255,255,255,0.4)"}},dark:{root:{background:"rgba(255, 255, 255, 0.06)",animationBackground:"rgba(255, 255, 255, 0.04)"}}}},v1={root:{transitionDuration:"{transition.duration}"},track:{background:"{content.border.color}",borderRadius:"{content.border.radius}",size:"3px"},range:{background:"{primary.color}"},handle:{width:"20px",height:"20px",borderRadius:"50%",background:"{content.border.color}",hoverBackground:"{content.border.color}",content:{borderRadius:"50%",hoverBackground:"{content.background}",width:"16px",height:"16px",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.08), 0px 1px 1px 0px rgba(0, 0, 0, 0.14)"},focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},colorScheme:{light:{handle:{contentBackground:"{surface.0}"}},dark:{handle:{contentBackground:"{surface.950}"}}}},y1={root:{gap:"0.5rem",transitionDuration:"{transition.duration}"}},C1={root:{borderRadius:"{form.field.border.radius}",roundedBorderRadius:"2rem",raisedShadow:"0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12)"}},k1={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",transitionDuration:"{transition.duration}"},gutter:{background:"{content.border.color}"},handle:{size:"24px",background:"transparent",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}}},S1={root:{transitionDuration:"{transition.duration}"},separator:{background:"{content.border.color}",activeBackground:"{primary.color}",margin:"0 0 0 1.625rem",size:"2px"},step:{padding:"0.5rem",gap:"1rem"},stepHeader:{padding:"0",borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},gap:"0.5rem"},stepTitle:{color:"{text.muted.color}",activeColor:"{primary.color}",fontWeight:"500"},stepNumber:{background:"{content.background}",activeBackground:"{content.background}",borderColor:"{content.border.color}",activeBorderColor:"{content.border.color}",color:"{text.muted.color}",activeColor:"{primary.color}",size:"2rem",fontSize:"1.143rem",fontWeight:"500",borderRadius:"50%",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"},steppanels:{padding:"0.875rem 0.5rem 1.125rem 0.5rem"},steppanel:{background:"{content.background}",color:"{content.color}",padding:"0 0 0 1rem"}},x1={root:{transitionDuration:"{transition.duration}"},separator:{background:"{content.border.color}"},itemLink:{borderRadius:"{content.border.radius}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},gap:"0.5rem"},itemLabel:{color:"{text.muted.color}",activeColor:"{primary.color}",fontWeight:"500"},itemNumber:{background:"{content.background}",activeBackground:"{content.background}",borderColor:"{content.border.color}",activeBorderColor:"{content.border.color}",color:"{text.muted.color}",activeColor:"{primary.color}",size:"2rem",fontSize:"1.143rem",fontWeight:"500",borderRadius:"50%",shadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"}},_1={root:{transitionDuration:"{transition.duration}"},tablist:{borderWidth:"0 0 1px 0",background:"{content.background}",borderColor:"{content.border.color}"},item:{background:"transparent",hoverBackground:"transparent",activeBackground:"transparent",borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",hoverBorderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}",padding:"1rem 1.125rem",fontWeight:"600",margin:"0 0 -1px 0",gap:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},itemIcon:{color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},activeBar:{height:"1px",bottom:"-1px",background:"{primary.color}"}},R1={root:{transitionDuration:"{transition.duration}"},tablist:{borderWidth:"0 0 1px 0",background:"{content.background}",borderColor:"{content.border.color}"},tab:{background:"transparent",hoverBackground:"transparent",activeBackground:"transparent",borderWidth:"0 0 1px 0",borderColor:"{content.border.color}",hoverBorderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}",padding:"1rem 1.125rem",fontWeight:"600",margin:"0 0 -1px 0",gap:"0.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},tabpanel:{background:"{content.background}",color:"{content.color}",padding:"0.875rem 1.125rem 1.125rem 1.125rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"inset {focus.ring.shadow}"}},navButton:{background:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",width:"2.5rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},activeBar:{height:"1px",bottom:"-1px",background:"{primary.color}"},colorScheme:{light:{navButton:{shadow:"0px 0px 10px 50px rgba(255, 255, 255, 0.6)"}},dark:{navButton:{shadow:"0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"}}}},T1={root:{transitionDuration:"{transition.duration}"},tabList:{background:"{content.background}",borderColor:"{content.border.color}"},tab:{borderColor:"{content.border.color}",activeBorderColor:"{primary.color}",color:"{text.muted.color}",hoverColor:"{text.color}",activeColor:"{primary.color}"},tabPanel:{background:"{content.background}",color:"{content.color}"},navButton:{background:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}"},colorScheme:{light:{navButton:{shadow:"0px 0px 10px 50px rgba(255, 255, 255, 0.6)"}},dark:{navButton:{shadow:"0px 0px 10px 50px color-mix(in srgb, {content.background}, transparent 50%)"}}}},w1={root:{fontSize:"0.875rem",fontWeight:"700",padding:"0.25rem 0.5rem",gap:"0.25rem",borderRadius:"{content.border.radius}",roundedBorderRadius:"{border.radius.xl}"},icon:{size:"0.75rem"},colorScheme:{light:{primary:{background:"{primary.100}",color:"{primary.700}"},secondary:{background:"{surface.100}",color:"{surface.600}"},success:{background:"{green.100}",color:"{green.700}"},info:{background:"{sky.100}",color:"{sky.700}"},warn:{background:"{orange.100}",color:"{orange.700}"},danger:{background:"{red.100}",color:"{red.700}"},contrast:{background:"{surface.950}",color:"{surface.0}"}},dark:{primary:{background:"color-mix(in srgb, {primary.500}, transparent 84%)",color:"{primary.300}"},secondary:{background:"{surface.800}",color:"{surface.300}"},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",color:"{green.300}"},info:{background:"color-mix(in srgb, {sky.500}, transparent 84%)",color:"{sky.300}"},warn:{background:"color-mix(in srgb, {orange.500}, transparent 84%)",color:"{orange.300}"},danger:{background:"color-mix(in srgb, {red.500}, transparent 84%)",color:"{red.300}"},contrast:{background:"{surface.0}",color:"{surface.950}"}}}},P1={root:{background:"{form.field.background}",borderColor:"{form.field.border.color}",color:"{form.field.color}",height:"18rem",padding:"{form.field.padding.y} {form.field.padding.x}",borderRadius:"{form.field.border.radius}"},prompt:{gap:"0.25rem"},commandResponse:{margin:"2px 0"}},E1={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"}},O1={root:{background:"{content.background}",borderColor:"{content.border.color}",color:"{content.color}",borderRadius:"{content.border.radius}",shadow:"{overlay.navigation.shadow}",transitionDuration:"{transition.duration}"},list:{padding:"{navigation.list.padding}",gap:"{navigation.list.gap}"},item:{focusBackground:"{navigation.item.focus.background}",activeBackground:"{navigation.item.active.background}",color:"{navigation.item.color}",focusColor:"{navigation.item.focus.color}",activeColor:"{navigation.item.active.color}",padding:"{navigation.item.padding}",borderRadius:"{navigation.item.border.radius}",gap:"{navigation.item.gap}",icon:{color:"{navigation.item.icon.color}",focusColor:"{navigation.item.icon.focus.color}",activeColor:"{navigation.item.icon.active.color}"}},submenuLabel:{padding:"{navigation.submenu.label.padding}",fontWeight:"{navigation.submenu.label.font.weight}",background:"{navigation.submenu.label.background.}",color:"{navigation.submenu.label.color}"},submenuIcon:{size:"{navigation.submenu.icon.size}",color:"{navigation.submenu.icon.color}",focusColor:"{navigation.submenu.icon.focus.color}",activeColor:"{navigation.submenu.icon.active.color}"},separator:{borderColor:"{content.border.color}"}},$1={event:{minHeight:"5rem"},horizontal:{eventContent:{padding:"1rem 0"}},vertical:{eventContent:{padding:"0 1rem"}},eventMarker:{size:"1.125rem",borderRadius:"50%",borderWidth:"2px",background:"{content.background}",borderColor:"{content.border.color}",content:{borderRadius:"50%",size:"0.375rem",background:"{primary.color}",insetShadow:"0px 0.5px 0px 0px rgba(0, 0, 0, 0.06), 0px 1px 1px 0px rgba(0, 0, 0, 0.12)"}},eventConnector:{color:"{content.border.color}",size:"2px"}},B1={root:{width:"25rem",borderRadius:"{content.border.radius}",borderWidth:"1px",transitionDuration:"{transition.duration}"},icon:{size:"1.125rem"},content:{padding:"{overlay.popover.padding}",gap:"0.5rem"},text:{gap:"0.5rem"},summary:{fontWeight:"500",fontSize:"1rem"},detail:{fontWeight:"500",fontSize:"0.875rem"},closeButton:{width:"1.75rem",height:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",offset:"{focus.ring.offset}"}},closeIcon:{size:"1rem"},colorScheme:{light:{blur:"1.5px",info:{background:"color-mix(in srgb, {blue.50}, transparent 5%)",borderColor:"{blue.200}",color:"{blue.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"{blue.100}",focusRing:{color:"{blue.600}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.50}, transparent 5%)",borderColor:"{green.200}",color:"{green.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"{green.100}",focusRing:{color:"{green.600}",shadow:"none"}}},warn:{background:"color-mix(in srgb,{yellow.50}, transparent 5%)",borderColor:"{yellow.200}",color:"{yellow.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"{yellow.100}",focusRing:{color:"{yellow.600}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.50}, transparent 5%)",borderColor:"{red.200}",color:"{red.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"{red.100}",focusRing:{color:"{red.600}",shadow:"none"}}},secondary:{background:"{surface.100}",borderColor:"{surface.200}",color:"{surface.600}",detailColor:"{surface.700}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.200}",focusRing:{color:"{surface.600}",shadow:"none"}}},contrast:{background:"{surface.900}",borderColor:"{surface.950}",color:"{surface.50}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.800}",focusRing:{color:"{surface.50}",shadow:"none"}}}},dark:{blur:"10px",info:{background:"color-mix(in srgb, {blue.500}, transparent 84%)",borderColor:"color-mix(in srgb, {blue.700}, transparent 64%)",color:"{blue.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {blue.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{blue.500}",shadow:"none"}}},success:{background:"color-mix(in srgb, {green.500}, transparent 84%)",borderColor:"color-mix(in srgb, {green.700}, transparent 64%)",color:"{green.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {green.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{green.500}",shadow:"none"}}},warn:{background:"color-mix(in srgb, {yellow.500}, transparent 84%)",borderColor:"color-mix(in srgb, {yellow.700}, transparent 64%)",color:"{yellow.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {yellow.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{yellow.500}",shadow:"none"}}},error:{background:"color-mix(in srgb, {red.500}, transparent 84%)",borderColor:"color-mix(in srgb, {red.700}, transparent 64%)",color:"{red.500}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {red.500}, transparent 96%)",closeButton:{hoverBackground:"rgba(255, 255, 255, 0.05)",focusRing:{color:"{red.500}",shadow:"none"}}},secondary:{background:"{surface.800}",borderColor:"{surface.700}",color:"{surface.300}",detailColor:"{surface.0}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.500}, transparent 96%)",closeButton:{hoverBackground:"{surface.700}",focusRing:{color:"{surface.300}",shadow:"none"}}},contrast:{background:"{surface.0}",borderColor:"{surface.100}",color:"{surface.950}",detailColor:"{surface.950}",shadow:"0px 4px 8px 0px color-mix(in srgb, {surface.950}, transparent 96%)",closeButton:{hoverBackground:"{surface.100}",focusRing:{color:"{surface.950}",shadow:"none"}}}}}},A1={root:{padding:"0.5rem 1rem",borderRadius:"{content.border.radius}",gap:"0.5rem",fontWeight:"500",disabledBackground:"{form.field.disabled.background}",disabledBorderColor:"{form.field.disabled.background}",disabledColor:"{form.field.disabled.color}",invalidBorderColor:"{form.field.invalid.border.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},icon:{disabledColor:"{form.field.disabled.color}"},content:{left:"0.25rem",top:"0.25rem",checkedShadow:"0px 1px 2px 0px rgba(0, 0, 0, 0.02), 0px 1px 2px 0px rgba(0, 0, 0, 0.04)"},colorScheme:{light:{root:{background:"{surface.100}",checkedBackground:"{surface.100}",hoverBackground:"{surface.100}",borderColor:"{surface.100}",color:"{surface.500}",hoverColor:"{surface.700}",checkedColor:"{surface.900}",checkedBorderColor:"{surface.100}"},content:{checkedBackground:"{surface.0}"},icon:{color:"{surface.500}",hoverColor:"{surface.700}",checkedColor:"{surface.900}"}},dark:{root:{background:"{surface.950}",checkedBackground:"{surface.950}",hoverBackground:"{surface.950}",borderColor:"{surface.950}",color:"{surface.400}",hoverColor:"{surface.300}",checkedColor:"{surface.0}",checkedBorderColor:"{surface.950}"},content:{checkedBackground:"{surface.800}"},icon:{color:"{surface.400}",hoverColor:"{surface.300}",checkedColor:"{surface.0}"}}}},L1={root:{width:"2.5rem",height:"1.5rem",borderRadius:"30px",gap:"0.25rem",shadow:"{form.field.shadow}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"},borderWidth:"1px",borderColor:"transparent",hoverBorderColor:"transparent",checkedBorderColor:"transparent",checkedHoverBorderColor:"transparent",invalidBorderColor:"{form.field.invalid.border.color}",transitionDuration:"{form.field.transition.duration}",slideDuration:"0.2s",disabledBackground:"{form.field.disabled.background}"},handle:{borderRadius:"50%",size:"1rem",disabledBackground:"{form.field.disabled.color}"},colorScheme:{light:{root:{background:"{surface.300}",hoverBackground:"{surface.400}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}"},handle:{background:"{surface.0}",hoverBackground:"{surface.0}",checkedBackground:"{surface.0}",checkedHoverBackground:"{surface.0}"}},dark:{root:{background:"{surface.700}",hoverBackground:"{surface.600}",checkedBackground:"{primary.color}",checkedHoverBackground:"{primary.hover.color}"},handle:{background:"{surface.400}",hoverBackground:"{surface.300}",checkedBackground:"{surface.900}",checkedHoverBackground:"{surface.900}"}}}},I1={root:{background:"{content.background}",borderColor:"{content.border.color}",borderRadius:"{content.border.radius}",color:"{content.color}",gap:"0.5rem",padding:"0.75rem"}},D1={root:{maxWidth:"12.5rem",gutter:"0.25rem",shadow:"{overlay.popover.shadow}",padding:"0.5rem 0.75rem",borderRadius:"{overlay.popover.border.radius}"},colorScheme:{light:{root:{background:"{surface.700}",color:"{surface.0}"}},dark:{root:{background:"{surface.700}",color:"{surface.0}"}}}},j1={root:{background:"{content.background}",color:"{content.color}",padding:"1rem",gap:"2px",indent:"1rem",transitionDuration:"{transition.duration}"},node:{padding:"0.25rem 0.5rem",borderRadius:"{content.border.radius}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{text.color}",hoverColor:"{text.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"},gap:"0.25rem"},nodeIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedColor:"{highlight.color}"},nodeToggleButton:{borderRadius:"50%",size:"1.75rem",hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}",selectedHoverColor:"{primary.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},loadingIcon:{size:"2rem"}},M1={root:{background:"{form.field.background}",disabledBackground:"{form.field.disabled.background}",filledBackground:"{form.field.filled.background}",filledFocusBackground:"{form.field.filled.focus.background}",borderColor:"{form.field.border.color}",hoverBorderColor:"{form.field.hover.border.color}",focusBorderColor:"{form.field.focus.border.color}",invalidBorderColor:"{form.field.invalid.border.color}",color:"{form.field.color}",disabledColor:"{form.field.disabled.color}",placeholderColor:"{form.field.placeholder.color}",shadow:"{form.field.shadow}",paddingX:"{form.field.padding.x}",paddingY:"{form.field.padding.y}",borderRadius:"{form.field.border.radius}",focusRing:{width:"{form.field.focus.ring.width}",style:"{form.field.focus.ring.style}",color:"{form.field.focus.ring.color}",offset:"{form.field.focus.ring.offset}",shadow:"{form.field.focus.ring.shadow}"},transitionDuration:"{form.field.transition.duration}"},dropdown:{width:"2.5rem",color:"{form.field.icon.color}"},overlay:{background:"{overlay.select.background}",borderColor:"{overlay.select.border.color}",borderRadius:"{overlay.select.border.radius}",color:"{overlay.select.color}",shadow:"{overlay.select.shadow}"},tree:{padding:"{list.padding}"},emptyMessage:{padding:"{list.option.padding}"},chip:{borderRadius:"{border.radius.sm}"}},N1={root:{transitionDuration:"{transition.duration}"},header:{background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},headerCell:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",borderColor:"{treetable.border.color}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",gap:"0.5rem",padding:"0.75rem 1rem",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},columnTitle:{fontWeight:"600"},row:{background:"{content.background}",hoverBackground:"{content.hover.background}",selectedBackground:"{highlight.background}",color:"{content.color}",hoverColor:"{content.hover.color}",selectedColor:"{highlight.color}",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"-1px",shadow:"{focus.ring.shadow}"}},bodyCell:{borderColor:"{treetable.border.color}",padding:"0.75rem 1rem",gap:"0.5rem"},footerCell:{background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",padding:"0.75rem 1rem"},columnFooter:{fontWeight:"600"},footer:{background:"{content.background}",borderColor:"{treetable.border.color}",color:"{content.color}",borderWidth:"0 0 1px 0",padding:"0.75rem 1rem"},columnResizerWidth:"0.5rem",resizeIndicator:{width:"1px",color:"{primary.color}"},sortIcon:{color:"{text.muted.color}",hoverColor:"{text.hover.muted.color}"},loadingIcon:{size:"2rem"},nodeToggleButton:{hoverBackground:"{content.hover.background}",selectedHoverBackground:"{content.background}",color:"{text.muted.color}",hoverColor:"{text.color}",selectedHoverColor:"{primary.color}",size:"1.75rem",borderRadius:"50%",focusRing:{width:"{focus.ring.width}",style:"{focus.ring.style}",color:"{focus.ring.color}",offset:"{focus.ring.offset}",shadow:"{focus.ring.shadow}"}},paginatorTop:{borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},paginatorBottom:{borderColor:"{content.border.color}",borderWidth:"0 0 1px 0"},colorScheme:{light:{root:{borderColor:"{content.border.color}"},bodyCell:{selectedBorderColor:"{primary.100}"}},dark:{root:{borderColor:"{surface.800}"},bodyCell:{selectedBorderColor:"{primary.900}"}}}},F1={loader:{mask:{background:"{content.background}",color:"{text.muted.color}"},icon:{size:"2rem"}}},z1={primitive:{borderRadius:{none:"0",xs:"2px",sm:"4px",md:"6px",lg:"8px",xl:"12px"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b",950:"#022c22"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d",950:"#052e16"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314",950:"#1a2e05"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d",950:"#450a0a"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12",950:"#431407"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f",950:"#451a03"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12",950:"#422006"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a",950:"#042f2e"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63",950:"#083344"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e",950:"#082f49"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a",950:"#172554"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81",950:"#1e1b4b"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95",950:"#2e1065"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87",950:"#3b0764"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75",950:"#4a044e"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843",950:"#500724"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337",950:"#4c0519"},slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a",950:"#020617"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827",950:"#030712"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b",950:"#09090b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717",950:"#0a0a0a"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917",950:"#0c0a09"}},semantic:{transitionDuration:"0.2s",focusRing:{width:"1px",style:"solid",color:"{primary.color}",offset:"2px",shadow:"none"},disabledOpacity:"0.6",iconSize:"1rem",anchorGutter:"2px",primary:{50:"{emerald.50}",100:"{emerald.100}",200:"{emerald.200}",300:"{emerald.300}",400:"{emerald.400}",500:"{emerald.500}",600:"{emerald.600}",700:"{emerald.700}",800:"{emerald.800}",900:"{emerald.900}",950:"{emerald.950}"},formField:{paddingX:"0.75rem",paddingY:"0.5rem",borderRadius:"{border.radius.md}",focusRing:{width:"0",style:"none",color:"transparent",offset:"0",shadow:"none"},transitionDuration:"{transition.duration}"},list:{padding:"0.25rem 0.25rem",gap:"2px",header:{padding:"0.5rem 1rem 0.25rem 1rem"},option:{padding:"0.5rem 0.75rem",borderRadius:"{border.radius.sm}"},optionGroup:{padding:"0.5rem 0.75rem",fontWeight:"600"}},content:{borderRadius:"{border.radius.md}"},mask:{transitionDuration:"0.15s"},navigation:{list:{padding:"0.25rem 0.25rem",gap:"2px"},item:{padding:"0.5rem 0.75rem",borderRadius:"{border.radius.sm}",gap:"0.5rem"},submenuLabel:{padding:"0.5rem 0.75rem",fontWeight:"600"},submenuIcon:{size:"0.875rem"}},overlay:{select:{borderRadius:"{border.radius.md}",shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"},popover:{borderRadius:"{border.radius.md}",padding:"0.75rem",shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"},modal:{borderRadius:"{border.radius.xl}",padding:"1.25rem",shadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)"},navigation:{shadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)"}},colorScheme:{light:{surface:{0:"#ffffff",50:"{slate.50}",100:"{slate.100}",200:"{slate.200}",300:"{slate.300}",400:"{slate.400}",500:"{slate.500}",600:"{slate.600}",700:"{slate.700}",800:"{slate.800}",900:"{slate.900}",950:"{slate.950}"},primary:{color:"{primary.500}",contrastColor:"#ffffff",hoverColor:"{primary.600}",activeColor:"{primary.700}"},highlight:{background:"{primary.50}",focusBackground:"{primary.100}",color:"{primary.700}",focusColor:"{primary.800}"},mask:{background:"rgba(0,0,0,0.4)",color:"{surface.200}"},formField:{background:"{surface.0}",disabledBackground:"{surface.200}",filledBackground:"{surface.50}",filledFocusBackground:"{surface.50}",borderColor:"{surface.300}",hoverBorderColor:"{surface.400}",focusBorderColor:"{primary.color}",invalidBorderColor:"{red.400}",color:"{surface.700}",disabledColor:"{surface.500}",placeholderColor:"{surface.500}",floatLabelColor:"{surface.500}",floatLabelFocusColor:"{surface.500}",floatLabelInvalidColor:"{red.400}",iconColor:"{surface.400}",shadow:"0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05)"},text:{color:"{surface.700}",hoverColor:"{surface.800}",mutedColor:"{surface.500}",hoverMutedColor:"{surface.600}"},content:{background:"{surface.0}",hoverBackground:"{surface.100}",borderColor:"{surface.200}",color:"{text.color}",hoverColor:"{text.hover.color}"},overlay:{select:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"},popover:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"},modal:{background:"{surface.0}",borderColor:"{surface.200}",color:"{text.color}"}},list:{option:{focusBackground:"{surface.100}",selectedBackground:"{highlight.background}",selectedFocusBackground:"{highlight.focus.background}",color:"{text.color}",focusColor:"{text.hover.color}",selectedColor:"{highlight.color}",selectedFocusColor:"{highlight.focus.color}",icon:{color:"{surface.400}",focusColor:"{surface.500}"}},optionGroup:{background:"transparent",color:"{text.muted.color}"}},navigation:{item:{focusBackground:"{surface.100}",activeBackground:"{surface.100}",color:"{text.color}",focusColor:"{text.hover.color}",activeColor:"{text.hover.color}",icon:{color:"{surface.400}",focusColor:"{surface.500}",activeColor:"{surface.500}"}},submenuLabel:{background:"transparent",color:"{text.muted.color}"},submenuIcon:{color:"{surface.400}",focusColor:"{surface.500}",activeColor:"{surface.500}"}}},dark:{surface:{0:"#ffffff",50:"{zinc.50}",100:"{zinc.100}",200:"{zinc.200}",300:"{zinc.300}",400:"{zinc.400}",500:"{zinc.500}",600:"{zinc.600}",700:"{zinc.700}",800:"{zinc.800}",900:"{zinc.900}",950:"{zinc.950}"},primary:{color:"{primary.400}",contrastColor:"{surface.900}",hoverColor:"{primary.300}",activeColor:"{primary.200}"},highlight:{background:"color-mix(in srgb, {primary.400}, transparent 84%)",focusBackground:"color-mix(in srgb, {primary.400}, transparent 76%)",color:"rgba(255,255,255,.87)",focusColor:"rgba(255,255,255,.87)"},mask:{background:"rgba(0,0,0,0.6)",color:"{surface.200}"},formField:{background:"{surface.950}",disabledBackground:"{surface.700}",filledBackground:"{surface.800}",filledFocusBackground:"{surface.800}",borderColor:"{surface.700}",hoverBorderColor:"{surface.600}",focusBorderColor:"{primary.color}",invalidBorderColor:"{red.300}",color:"{surface.0}",disabledColor:"{surface.400}",placeholderColor:"{surface.400}",floatLabelColor:"{surface.400}",floatLabelFocusColor:"{surface.400}",floatLabelInvalidColor:"{red.300}",iconColor:"{surface.400}",shadow:"0 0 #0000, 0 0 #0000, 0 1px 2px 0 rgba(18, 18, 23, 0.05)"},text:{color:"{surface.0}",hoverColor:"{surface.0}",mutedColor:"{surface.400}",hoverMutedColor:"{surface.300}"},content:{background:"{surface.900}",hoverBackground:"{surface.800}",borderColor:"{surface.700}",color:"{text.color}",hoverColor:"{text.hover.color}"},overlay:{select:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"},popover:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"},modal:{background:"{surface.900}",borderColor:"{surface.700}",color:"{text.color}"}},list:{option:{focusBackground:"{surface.800}",selectedBackground:"{highlight.background}",selectedFocusBackground:"{highlight.focus.background}",color:"{text.color}",focusColor:"{text.hover.color}",selectedColor:"{highlight.color}",selectedFocusColor:"{highlight.focus.color}",icon:{color:"{surface.500}",focusColor:"{surface.400}"}},optionGroup:{background:"transparent",color:"{text.muted.color}"}},navigation:{item:{focusBackground:"{surface.800}",activeBackground:"{surface.800}",color:"{text.color}",focusColor:"{text.hover.color}",activeColor:"{text.hover.color}",icon:{color:"{surface.500}",focusColor:"{surface.400}",activeColor:"{surface.400}"}},submenuLabel:{background:"transparent",color:"{text.muted.color}"},submenuIcon:{color:"{surface.500}",focusColor:"{surface.400}",activeColor:"{surface.400}"}}}}},components:{accordion:uy,autocomplete:dy,avatar:fy,badge:py,blockui:gy,breadcrumb:hy,button:my,datepicker:Py,card:by,carousel:vy,cascadeselect:yy,checkbox:Cy,chip:ky,colorpicker:Sy,confirmdialog:xy,confirmpopup:_y,contextmenu:Ry,dataview:wy,datatable:Ty,dialog:Ey,divider:Oy,dock:$y,drawer:By,editor:Ay,fieldset:Ly,fileupload:Iy,floatlabel:Dy,galleria:jy,iconfield:My,image:Ny,inlinemessage:Fy,inplace:zy,inputchips:Uy,inputgroup:Hy,inputnumber:Vy,inputtext:Wy,knob:Gy,listbox:Ky,megamenu:qy,menu:Jy,menubar:Yy,message:Zy,metergroup:Xy,multiselect:Qy,orderlist:e1,organizationchart:t1,overlaybadge:r1,popover:c1,paginator:o1,password:s1,panel:n1,panelmenu:i1,picklist:a1,progressbar:l1,progressspinner:u1,radiobutton:d1,rating:f1,scrollpanel:g1,select:h1,selectbutton:m1,skeleton:b1,slider:v1,speeddial:y1,splitter:k1,splitbutton:C1,stepper:S1,steps:x1,tabmenu:_1,tabs:R1,tabview:T1,textarea:E1,tieredmenu:O1,tag:w1,terminal:P1,timeline:$1,togglebutton:A1,toggleswitch:L1,tree:j1,treeselect:M1,treetable:N1,toast:B1,toolbar:I1,virtualscroller:F1},directives:{tooltip:D1,ripple:p1}},U1=function(t){var r=t.dt;return`
.p-tooltip {
    position: absolute;
    display: none;
    max-width: `.concat(r("tooltip.max.width"),`;
}

.p-tooltip-right,
.p-tooltip-left {
    padding: 0 `).concat(r("tooltip.gutter"),`;
}

.p-tooltip-top,
.p-tooltip-bottom {
    padding: `).concat(r("tooltip.gutter"),` 0;
}

.p-tooltip-text {
    white-space: pre-line;
    word-break: break-word;
    background: `).concat(r("tooltip.background"),`;
    color: `).concat(r("tooltip.color"),`;
    padding: `).concat(r("tooltip.padding"),`;
    box-shadow: `).concat(r("tooltip.shadow"),`;
    border-radius: `).concat(r("tooltip.border.radius"),`;
}

.p-tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    scale: 2;
}

.p-tooltip-right .p-tooltip-arrow {
    margin-top: calc(-1 * `).concat(r("tooltip.gutter"),`);
    border-width: `).concat(r("tooltip.gutter")," ").concat(r("tooltip.gutter")," ").concat(r("tooltip.gutter"),` 0;
    border-right-color: `).concat(r("tooltip.background"),`;
}

.p-tooltip-left .p-tooltip-arrow {
    margin-top: calc(-1 * `).concat(r("tooltip.gutter"),`);
    border-width: `).concat(r("tooltip.gutter")," 0 ").concat(r("tooltip.gutter")," ").concat(r("tooltip.gutter"),`;
    border-left-color: `).concat(r("tooltip.background"),`;
}

.p-tooltip-top .p-tooltip-arrow {
    margin-left: calc(-1 * `).concat(r("tooltip.gutter"),`);
    border-width: `).concat(r("tooltip.gutter")," ").concat(r("tooltip.gutter")," 0 ").concat(r("tooltip.gutter"),`;
    border-top-color: `).concat(r("tooltip.background"),`;
    border-bottom-color: `).concat(r("tooltip.background"),`;
}

.p-tooltip-bottom .p-tooltip-arrow {
    margin-left: calc(-1 * `).concat(r("tooltip.gutter"),`);
    border-width: 0 `).concat(r("tooltip.gutter")," ").concat(r("tooltip.gutter")," ").concat(r("tooltip.gutter"),`;
    border-top-color: `).concat(r("tooltip.background"),`;
    border-bottom-color: `).concat(r("tooltip.background"),`;
}
`)},H1={root:"p-tooltip p-component",arrow:"p-tooltip-arrow",text:"p-tooltip-text"},V1=Ce.extend({name:"tooltip-directive",theme:U1,classes:H1}),W1=Z.extend({style:V1});function G1(e,t){return Y1(e)||J1(e,t)||q1(e,t)||K1()}function K1(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function q1(e,t){if(e){if(typeof e=="string")return Oc(e,t);var r={}.toString.call(e).slice(8,-1);return r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set"?Array.from(e):r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Oc(e,t):void 0}}function Oc(e,t){(t==null||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function J1(e,t){var r=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(r!=null){var o,n,i,s,a=[],c=!0,u=!1;try{if(i=(r=r.call(e)).next,t!==0)for(;!(c=(o=i.call(r)).done)&&(a.push(o.value),a.length!==t);c=!0);}catch(l){u=!0,n=l}finally{try{if(!c&&r.return!=null&&(s=r.return(),Object(s)!==s))return}finally{if(u)throw n}}return a}}function Y1(e){if(Array.isArray(e))return e}function $c(e,t,r){return(t=Z1(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Z1(e){var t=X1(e,"string");return Ht(t)=="symbol"?t:t+""}function X1(e,t){if(Ht(e)!="object"||!e)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var o=r.call(e,t||"default");if(Ht(o)!="object")return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Ht(e){"@babel/helpers - typeof";return Ht=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ht(e)}var Q1=W1.extend("tooltip",{beforeMount:function(t,r){var o,n=this.getTarget(t);if(n.$_ptooltipModifiers=this.getModifiers(r),r.value){if(typeof r.value=="string")n.$_ptooltipValue=r.value,n.$_ptooltipDisabled=!1,n.$_ptooltipEscape=!0,n.$_ptooltipClass=null,n.$_ptooltipFitContent=!0,n.$_ptooltipIdAttr=rr()+"_tooltip",n.$_ptooltipShowDelay=0,n.$_ptooltipHideDelay=0,n.$_ptooltipAutoHide=!0;else if(Ht(r.value)==="object"&&r.value){if(lr(r.value.value)||r.value.value.trim()==="")return;n.$_ptooltipValue=r.value.value,n.$_ptooltipDisabled=!!r.value.disabled===r.value.disabled?r.value.disabled:!1,n.$_ptooltipEscape=!!r.value.escape===r.value.escape?r.value.escape:!0,n.$_ptooltipClass=r.value.class||"",n.$_ptooltipFitContent=!!r.value.fitContent===r.value.fitContent?r.value.fitContent:!0,n.$_ptooltipIdAttr=r.value.id||rr()+"_tooltip",n.$_ptooltipShowDelay=r.value.showDelay||0,n.$_ptooltipHideDelay=r.value.hideDelay||0,n.$_ptooltipAutoHide=!!r.value.autoHide===r.value.autoHide?r.value.autoHide:!0}}else return;n.$_ptooltipZIndex=(o=r.instance.$primevue)===null||o===void 0||(o=o.config)===null||o===void 0||(o=o.zIndex)===null||o===void 0?void 0:o.tooltip,this.bindEvents(n,r),t.setAttribute("data-pd-tooltip",!0)},updated:function(t,r){var o=this.getTarget(t);if(o.$_ptooltipModifiers=this.getModifiers(r),this.unbindEvents(o),!!r.value){if(typeof r.value=="string")o.$_ptooltipValue=r.value,o.$_ptooltipDisabled=!1,o.$_ptooltipEscape=!0,o.$_ptooltipClass=null,o.$_ptooltipIdAttr=o.$_ptooltipIdAttr||rr()+"_tooltip",o.$_ptooltipShowDelay=0,o.$_ptooltipHideDelay=0,o.$_ptooltipAutoHide=!0,this.bindEvents(o,r);else if(Ht(r.value)==="object"&&r.value)if(lr(r.value.value)||r.value.value.trim()===""){this.unbindEvents(o,r);return}else o.$_ptooltipValue=r.value.value,o.$_ptooltipDisabled=!!r.value.disabled===r.value.disabled?r.value.disabled:!1,o.$_ptooltipEscape=!!r.value.escape===r.value.escape?r.value.escape:!0,o.$_ptooltipClass=r.value.class||"",o.$_ptooltipFitContent=!!r.value.fitContent===r.value.fitContent?r.value.fitContent:!0,o.$_ptooltipIdAttr=r.value.id||o.$_ptooltipIdAttr||rr()+"_tooltip",o.$_ptooltipShowDelay=r.value.showDelay||0,o.$_ptooltipHideDelay=r.value.hideDelay||0,o.$_ptooltipAutoHide=!!r.value.autoHide===r.value.autoHide?r.value.autoHide:!0,this.bindEvents(o,r)}},unmounted:function(t,r){var o=this.getTarget(t);this.remove(o),this.unbindEvents(o,r),o.$_ptooltipScrollHandler&&(o.$_ptooltipScrollHandler.destroy(),o.$_ptooltipScrollHandler=null)},timer:void 0,methods:{bindEvents:function(t,r){var o=this,n=t.$_ptooltipModifiers;n.focus?(t.$_focusevent=function(i){return o.onFocus(i,r)},t.addEventListener("focus",t.$_focusevent),t.addEventListener("blur",this.onBlur.bind(this))):(t.$_mouseenterevent=function(i){return o.onMouseEnter(i,r)},t.addEventListener("mouseenter",t.$_mouseenterevent),t.addEventListener("mouseleave",this.onMouseLeave.bind(this)),t.addEventListener("click",this.onClick.bind(this))),t.addEventListener("keydown",this.onKeydown.bind(this))},unbindEvents:function(t){var r=t.$_ptooltipModifiers;r.focus?(t.removeEventListener("focus",t.$_focusevent),t.$_focusevent=null,t.removeEventListener("blur",this.onBlur.bind(this))):(t.removeEventListener("mouseenter",t.$_mouseenterevent),t.$_mouseenterevent=null,t.removeEventListener("mouseleave",this.onMouseLeave.bind(this)),t.removeEventListener("click",this.onClick.bind(this))),t.removeEventListener("keydown",this.onKeydown.bind(this))},bindScrollListener:function(t){var r=this;t.$_ptooltipScrollHandler||(t.$_ptooltipScrollHandler=new ub(t,function(){r.hide(t)})),t.$_ptooltipScrollHandler.bindScrollListener()},unbindScrollListener:function(t){t.$_ptooltipScrollHandler&&t.$_ptooltipScrollHandler.unbindScrollListener()},onMouseEnter:function(t,r){var o=t.currentTarget,n=o.$_ptooltipShowDelay;this.show(o,r,n)},onMouseLeave:function(t){var r=t.currentTarget,o=r.$_ptooltipHideDelay,n=r.$_ptooltipAutoHide;if(n)this.hide(r,o);else{var i=Qt(t.target,"data-pc-name")==="tooltip"||Qt(t.target,"data-pc-section")==="arrow"||Qt(t.target,"data-pc-section")==="text"||Qt(t.relatedTarget,"data-pc-name")==="tooltip"||Qt(t.relatedTarget,"data-pc-section")==="arrow"||Qt(t.relatedTarget,"data-pc-section")==="text";!i&&this.hide(r,o)}},onFocus:function(t,r){var o=t.currentTarget,n=o.$_ptooltipShowDelay;this.show(o,r,n)},onBlur:function(t){var r=t.currentTarget,o=r.$_ptooltipHideDelay;this.hide(r,o)},onClick:function(t){var r=t.currentTarget,o=r.$_ptooltipHideDelay;this.hide(r,o)},onKeydown:function(t){var r=t.currentTarget,o=r.$_ptooltipHideDelay;t.code==="Escape"&&this.hide(t.currentTarget,o)},tooltipActions:function(t,r){if(!(t.$_ptooltipDisabled||!Bd(t))){var o=this.create(t,r);this.align(t),!this.isUnstyled()&&I0(o,250);var n=this;window.addEventListener("resize",function i(){N0()||n.hide(t),window.removeEventListener("resize",i)}),o.addEventListener("mouseleave",function i(){n.hide(t),o.removeEventListener("mouseleave",i)}),this.bindScrollListener(t),Xr.set("tooltip",o,t.$_ptooltipZIndex)}},show:function(t,r,o){var n=this;o!==void 0?this.timer=setTimeout(function(){return n.tooltipActions(t,r)},o):this.tooltipActions(t,r)},tooltipRemoval:function(t){this.remove(t),this.unbindScrollListener(t)},hide:function(t,r){var o=this;clearTimeout(this.timer),r!==void 0?setTimeout(function(){return o.tooltipRemoval(t)},r):this.tooltipRemoval(t)},getTooltipElement:function(t){return document.getElementById(t.$_ptooltipId)},create:function(t){var r=t.$_ptooltipModifiers,o=Zo("div",{class:!this.isUnstyled()&&this.cx("arrow"),"p-bind":this.ptm("arrow",{context:r})}),n=Zo("div",{class:!this.isUnstyled()&&this.cx("text"),"p-bind":this.ptm("text",{context:r})});t.$_ptooltipEscape?(n.innerHTML="",n.appendChild(document.createTextNode(t.$_ptooltipValue))):n.innerHTML=t.$_ptooltipValue;var i=Zo("div",$c($c({id:t.$_ptooltipIdAttr,role:"tooltip",style:{display:"inline-block",width:t.$_ptooltipFitContent?"fit-content":void 0,pointerEvents:!this.isUnstyled()&&t.$_ptooltipAutoHide&&"none"},class:[!this.isUnstyled()&&this.cx("root"),t.$_ptooltipClass]},this.$attrSelector,""),"p-bind",this.ptm("root",{context:r})),o,n);return document.body.appendChild(i),t.$_ptooltipId=i.id,this.$el=i,i},remove:function(t){if(t){var r=this.getTooltipElement(t);r&&r.parentElement&&(Xr.clear(r),document.body.removeChild(r)),t.$_ptooltipId=null}},align:function(t){var r=t.$_ptooltipModifiers;r.top?(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignTop(t))):r.left?(this.alignLeft(t),this.isOutOfBounds(t)&&(this.alignRight(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignLeft(t))))):r.bottom?(this.alignBottom(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&this.alignBottom(t))):(this.alignRight(t),this.isOutOfBounds(t)&&(this.alignLeft(t),this.isOutOfBounds(t)&&(this.alignTop(t),this.isOutOfBounds(t)&&(this.alignBottom(t),this.isOutOfBounds(t)&&this.alignRight(t)))))},getHostOffset:function(t){var r=t.getBoundingClientRect(),o=r.left+wd(),n=r.top+Pd();return{left:o,top:n}},alignRight:function(t){this.preAlign(t,"right");var r=this.getTooltipElement(t),o=this.getHostOffset(t),n=o.left+At(t),i=o.top+(Lt(t)-Lt(r))/2;r.style.left=n+"px",r.style.top=i+"px"},alignLeft:function(t){this.preAlign(t,"left");var r=this.getTooltipElement(t),o=this.getHostOffset(t),n=o.left-At(r),i=o.top+(Lt(t)-Lt(r))/2;r.style.left=n+"px",r.style.top=i+"px"},alignTop:function(t){this.preAlign(t,"top");var r=this.getTooltipElement(t),o=this.getHostOffset(t),n=o.left+(At(t)-At(r))/2,i=o.top-Lt(r);r.style.left=n+"px",r.style.top=i+"px"},alignBottom:function(t){this.preAlign(t,"bottom");var r=this.getTooltipElement(t),o=this.getHostOffset(t),n=o.left+(At(t)-At(r))/2,i=o.top+Lt(t);r.style.left=n+"px",r.style.top=i+"px"},preAlign:function(t,r){var o=this.getTooltipElement(t);o.style.left="-999px",o.style.top="-999px",Yo(o,"p-tooltip-".concat(o.$_ptooltipPosition)),!this.isUnstyled()&&Rd(o,"p-tooltip-".concat(r)),o.$_ptooltipPosition=r,o.setAttribute("data-p-position",r);var n=pn(o,'[data-pc-section="arrow"]');n.style.top=r==="bottom"?"0":r==="right"||r==="left"||r!=="right"&&r!=="left"&&r!=="top"&&r!=="bottom"?"50%":null,n.style.bottom=r==="top"?"0":null,n.style.left=r==="right"||r!=="right"&&r!=="left"&&r!=="top"&&r!=="bottom"?"0":r==="top"||r==="bottom"?"50%":null,n.style.right=r==="left"?"0":null},isOutOfBounds:function(t){var r=this.getTooltipElement(t),o=r.getBoundingClientRect(),n=o.top,i=o.left,s=At(r),a=Lt(r),c=Td();return i+s>c.width||i<0||n<0||n+a>c.height},getTarget:function(t){return _d(t,"p-inputwrapper")?pn(t,"input"):t},getModifiers:function(t){return t.modifiers&&Object.keys(t.modifiers).length?t.modifiers:t.arg&&Ht(t.arg)==="object"?Object.entries(t.arg).reduce(function(r,o){var n=G1(o,2),i=n[0],s=n[1];return(i==="event"||i==="position")&&(r[s]=!0),r},{}):{}}}});console.log(tf.browserDetails.browser);const Br=jg(Zv);Br.use(zg());Br.use(ly,{theme:{preset:z1,options:{cssLayer:{name:"primevue",order:"tailwind-base, primevue, tailwind-utilities"}}}});Br.use(ey);Br.use(Wd);Br.directive("tooltip",Q1);Br.mount("#app");export{gC as $,dp as A,Ce as B,ub as C,ce as D,ws as E,st as F,tn as G,xe as H,pe as I,Xf as J,Re as K,rn as L,Tn as M,pC as N,fe as O,eC as P,Zd as Q,Sv as R,cs as S,xu as T,rr as U,Fv as V,Zh as W,Pr as X,fC as Y,Xr as Z,lC as _,bt as a,ls as a0,ge as a1,aC as a2,uC as a3,Le as a4,eu as a5,wr as a6,sC as a7,Nd as a8,eo as a9,vn as aa,dC as ab,iC as ac,Jp as ad,cC as ae,lr as af,oc as ag,oC as ah,nC as ai,Ro as aj,Nr as ak,Z as b,tC as c,vC as d,yC as e,bC as f,Sl as g,CC as h,_e as i,Ae as j,Zo as k,mC as l,hC as m,j0 as n,vf as o,Rd as p,Ad as q,es as r,N0 as s,rC as t,it as u,Ld as v,mt as w,Fd as x,_o as y,gi as z};
