package main

import (
	"fmt"
	"reflect"
)

type Person struct {
	Name string `json:"name"`
	Age  uint8  `json:"age"`
}

func Struct2Map() {
	person := Person{
		Name: "张三丰",
		Age:  108,
	}
	m := make(map[string]any)
	val := reflect.ValueOf(person)
	if val.Kind() == reflect.Struct {
		goto DEAL
	}
PTR:
	val = val.Elem()
	if val.Kind() == reflect.Ptr {
		goto PTR
	}
DEAL:
	realType := val.Type()
	for i := 0; i < realType.NumField(); i++ {
		name := realType.Field(i).Name
		m[name] = val.Field(i).Interface()
		fmt.Println(realType.Field(i).Tag.Get("json"))
	}
	fmt.Println(m)
}
