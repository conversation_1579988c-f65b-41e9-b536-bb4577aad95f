<!DOCTYPE html>
<html>
<head>
    <title>WebRTC Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .video-container {
            display: flex;
            gap: 20px;
        }
        video {
            width: 300px;
            height: 200px;
            background-color: #000;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC Meeting Test</h1>
        
        <div id="status" class="status disconnected">Disconnected</div>
        
        <div class="video-container">
            <div>
                <h3>Local Video</h3>
                <video id="localVideo" autoplay muted></video>
            </div>
            <div>
                <h3>Remote Video</h3>
                <video id="remoteVideo" autoplay></video>
            </div>
        </div>
        
        <div class="controls">
            <button id="connectBtn">Connect</button>
            <button id="disconnectBtn" disabled>Disconnect</button>
            <button id="createMeetingBtn" disabled>Create Meeting</button>
            <button id="joinMeetingBtn" disabled>Join Meeting</button>
            <input type="text" id="meetingIdInput" placeholder="Meeting ID" />
        </div>
        
        <div>
            <h3>Log</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // DOM elements
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const createMeetingBtn = document.getElementById('createMeetingBtn');
        const joinMeetingBtn = document.getElementById('joinMeetingBtn');
        const meetingIdInput = document.getElementById('meetingIdInput');
        const localVideo = document.getElementById('localVideo');
        const remoteVideo = document.getElementById('remoteVideo');
        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('status');

        // WebRTC variables
        let ws = null;
        let localStream = null;
        let peerConnection = null;
        let clientId = null;
        let roomId = null;

        // WebSocket connection
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);

        // Meeting controls
        createMeetingBtn.addEventListener('click', createMeeting);
        joinMeetingBtn.addEventListener('click', joinMeeting);

        // Configuration
        const configuration = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' }
            ]
        };

        // Connect to WebSocket server
        async function connect() {
            try {
                ws = new WebSocket(`ws://${window.location.host}/ws`);
                
                ws.onopen = function(event) {
                    log('Connected to WebSocket server');
                    updateStatus('Connected', true);
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    createMeetingBtn.disabled = false;
                    joinMeetingBtn.disabled = false;
                };

                ws.onmessage = function(event) {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                };

                ws.onclose = function(event) {
                    log('Disconnected from WebSocket server');
                    updateStatus('Disconnected', false);
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    createMeetingBtn.disabled = true;
                    joinMeetingBtn.disabled = true;
                };

                ws.onerror = function(error) {
                    log('WebSocket error: ' + error);
                };

                // Get local media stream
                localStream = await navigator.mediaDevices.getUserMedia({ 
                    video: true, 
                    audio: true 
                });
                localVideo.srcObject = localStream;
                
            } catch (error) {
                log('Error connecting: ' + error);
            }
        }

        // Disconnect from WebSocket server
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }
            
            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
            }
            
            localVideo.srcObject = null;
            remoteVideo.srcObject = null;
        }

        // Create a new meeting
        function createMeeting() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('Not connected to server');
                return;
            }
            
            const message = {
                type: 'create-meeting',
                data: {
                    displayName: 'Participant'
                }
            };
            
            ws.send(JSON.stringify(message));
        }

        // Join an existing meeting
        function joinMeeting() {
            const meetingId = meetingIdInput.value.trim();
            if (!meetingId) {
                log('Please enter a meeting ID');
                return;
            }
            
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('Not connected to server');
                return;
            }
            
            const message = {
                type: 'join-meeting',
                data: {
                    meetingId: meetingId,
                    displayName: 'Participant'
                }
            };
            
            ws.send(JSON.stringify(message));
        }

        // Handle incoming WebSocket messages
        function handleMessage(message) {
            log('Received: ' + JSON.stringify(message));
            
            switch (message.type) {
                case 'id':
                    clientId = message.data.id;
                    log('Assigned client ID: ' + clientId);
                    break;
                    
                case 'meeting-created':
                    roomId = message.data.meetingId;
                    meetingIdInput.value = roomId;
                    log('Created meeting: ' + roomId);
                    break;
                    
                case 'meeting-joined':
                    roomId = message.data.meetingId;
                    log('Joined meeting: ' + roomId);
                    // Initialize peer connection for WebRTC
                    initializePeerConnection();
                    break;
                    
                case 'offer':
                    // Handle WebRTC offer
                    handleOffer(message);
                    break;
                    
                case 'answer':
                    // Handle WebRTC answer
                    handleAnswer(message);
                    break;
                    
                case 'ice-candidate':
                    // Handle ICE candidate
                    handleIceCandidate(message);
                    break;
                    
                default:
                    log('Unhandled message type: ' + message.type);
            }
        }

        // Initialize WebRTC peer connection
        function initializePeerConnection() {
            peerConnection = new RTCPeerConnection(configuration);
            
            // Add local stream to peer connection
            if (localStream) {
                localStream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, localStream);
                });
            }
            
            // Handle ICE candidates
            peerConnection.onicecandidate = function(event) {
                if (event.candidate) {
                    const message = {
                        type: 'room-ice-candidate',
                        data: {
                            candidate: event.candidate
                        }
                    };
                    ws.send(JSON.stringify(message));
                }
            };
            
            // Handle remote stream
            peerConnection.ontrack = function(event) {
                remoteVideo.srcObject = event.streams[0];
            };
            
            // Create offer if we're the meeting creator
            if (clientId && roomId) {
                createOffer();
            }
        }

        // Create WebRTC offer
        async function createOffer() {
            try {
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                
                const message = {
                    type: 'room-offer',
                    data: {
                        sdp: offer.sdp
                    }
                };
                ws.send(JSON.stringify(message));
            } catch (error) {
                log('Error creating offer: ' + error);
            }
        }

        // Handle WebRTC offer
        async function handleOffer(message) {
            try {
                const offer = new RTCSessionDescription({
                    type: 'offer',
                    sdp: message.data.sdp
                });
                
                await peerConnection.setRemoteDescription(offer);
                
                const answer = await peerConnection.createAnswer();
                await peerConnection.setLocalDescription(answer);
                
                const response = {
                    type: 'room-answer',
                    data: {
                        sdp: answer.sdp
                    }
                };
                ws.send(JSON.stringify(response));
            } catch (error) {
                log('Error handling offer: ' + error);
            }
        }

        // Handle WebRTC answer
        async function handleAnswer(message) {
            try {
                const answer = new RTCSessionDescription({
                    type: 'answer',
                    sdp: message.data.sdp
                });
                
                await peerConnection.setRemoteDescription(answer);
            } catch (error) {
                log('Error handling answer: ' + error);
            }
        }

        // Handle ICE candidate
        async function handleIceCandidate(message) {
            try {
                const candidate = new RTCIceCandidate(message.data.candidate);
                await peerConnection.addIceCandidate(candidate);
            } catch (error) {
                log('Error handling ICE candidate: ' + error);
            }
        }

        // Log messages to the UI
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Update connection status
        function updateStatus(message, connected) {
            statusElement.textContent = message;
            statusElement.className = 'status ' + (connected ? 'connected' : 'disconnected');
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            disconnect();
        });
    </script>
</body>
</html>