package service

import (
	"meeting/internal/mcp"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// MCPService MCP服务接口
type MCPService interface {
	RegisterRoutes(r *gin.Engine)
	StartStdioServer() error
	IsEnabled() bool
}

// mcpService MCP服务实现
type mcpService struct {
	server *mcp.Server
	config *mcp.Config
	logger *zap.SugaredLogger
}

// NewMCPService 创建MCP服务
func NewMCPService(logger *zap.SugaredLogger) MCPService {
	config := mcp.DefaultConfig()
	server := mcp.NewServer(config)

	return &mcpService{
		server: server,
		config: config,
		logger: logger,
	}
}

// RegisterRoutes 注册MCP路由
func (s *mcpService) RegisterRoutes(r *gin.Engine) {
	if !s.config.Enabled {
		s.logger.Info("MCP service is disabled")
		return
	}

	s.logger.Info("Registering MCP routes")
	s.server.RegisterRoutes(r)
}

// StartStdioServer 启动标准输入输出服务器
func (s *mcpService) StartStdioServer() error {
	s.logger.Info("Starting MCP stdio server")
	return s.server.StartStdioServer()
}

// IsEnabled 检查MCP服务是否启用
func (s *mcpService) IsEnabled() bool {
	return s.config.Enabled
}
