import{B as S,l as w,L as r,F as n,I as c,P as i,U as $,V as b,W as B,a as d,ab as P,a1 as C,D as m,u as o,a5 as y,a6 as D,ac as h,H as L,a9 as T,G as V,ad as I,ae as N}from"./index-L9KCizOD.js";import{s as k}from"./index-C4camBIb.js";var U=({dt:e})=>`
.p-progressspinner {
    position: relative;
    margin: 0 auto;
    width: 100px;
    height: 100px;
    display: inline-block;
}

.p-progressspinner::before {
    content: "";
    display: block;
    padding-top: 100%;
}

.p-progressspinner-spin {
    height: 100%;
    transform-origin: center center;
    width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    animation: p-progressspinner-rotate 2s linear infinite;
}

.p-progressspinner-circle {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: 0;
    stroke: ${e("progressspinner.colorOne")};
    animation: p-progressspinner-dash 1.5s ease-in-out infinite, p-progressspinner-color 6s ease-in-out infinite;
    stroke-linecap: round;
}

@keyframes p-progressspinner-rotate {
    100% {
        transform: rotate(360deg);
    }
}
@keyframes p-progressspinner-dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35px;
    }
    100% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124px;
    }
}
@keyframes p-progressspinner-color {
    100%,
    0% {
        stroke: ${e("progressspinner.colorOne")};
    }
    40% {
        stroke: ${e("progressspinner.colorTwo")};
    }
    66% {
        stroke: ${e("progressspinner.colorThree")};
    }
    80%,
    90% {
        stroke: ${e("progressspinner.colorFour")};
    }
}
`,W={root:"p-progressspinner",spin:"p-progressspinner-spin",circle:"p-progressspinner-circle"},_=S.extend({name:"progressspinner",style:U,classes:W}),j={name:"BaseProgressSpinner",extends:w,props:{strokeWidth:{type:String,default:"2"},fill:{type:String,default:"none"},animationDuration:{type:String,default:"2s"}},style:_,provide:function(){return{$pcProgressSpinner:this,$parentInstance:this}}},v={name:"ProgressSpinner",extends:j,inheritAttrs:!1,computed:{svgStyle:function(){return{"animation-duration":this.animationDuration}}}},F=["fill","stroke-width"];function M(e,g,u,a,p,l){return n(),r("div",c({class:e.cx("root"),role:"progressbar"},e.ptmi("root")),[(n(),r("svg",c({class:e.cx("spin"),viewBox:"25 25 50 50",style:l.svgStyle},e.ptm("spin")),[i("circle",c({class:e.cx("circle"),cx:"50",cy:"50",r:"20",fill:e.fill,"stroke-width":e.strokeWidth,strokeMiterlimit:"10"},e.ptm("circle")),null,16,F)],16))],16)}v.render=M;const R={class:"w-full h-full flex justify-center items-center flex-col gap-6"},O={key:1,class:"flex justify-center items-center gap-2"},A={class:"flex flex-col justify-between bg-gray-100 w-48 h-32 rounded-md p-4"},H=$({__name:"LoginView",setup(e){b();const g=B(),u=I();d("");const a=d(!0),p=d([]);P().then(t=>{p.value=t}),C(()=>{setTimeout(()=>{a.value=!1},200)});const l=()=>{const s=new URL(window.location.href).searchParams.get("redirect_uri");N(s)};function x(t){u.push(`/meeting/${t}`)}return(t,s)=>(n(),r("div",R,[s[1]||(s[1]=i("p",{class:"text-2xl"},"多人实时音视频在线会议",-1)),s[2]||(s[2]=i("p",{class:"text-sm text-gray-500"}," 多人会议需使用多账号！ ",-1)),s[3]||(s[3]=i("p",{class:"text-sm text-gray-500"},"点击下方会议加入",-1)),a.value?(n(),m(o(v),{key:0,style:{width:"50px",height:"50px"},strokeWidth:"4",fill:"transparent",animationDuration:".5s","aria-label":"Custom ProgressSpinner"})):(n(),r(y,{key:1},[o(g).info.id?(n(),r("div",O,[(n(!0),r(y,null,D(p.value,f=>(n(),r("div",A,[h(T(f.Name)+" ",1),L(o(k),{onClick:()=>x(f.ID)},{default:V(()=>s[0]||(s[0]=[h("加入")])),_:2},1032,["onClick"])]))),256))])):(n(),m(o(k),{key:0,label:"登录",severity:"secondary",onClick:l}))],64))]))}});export{H as default};
