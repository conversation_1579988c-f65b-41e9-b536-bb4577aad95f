package tests

import (
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

type ClientWithLock struct {
	lastWriteTime time.Time
	mu            sync.Mutex
}

func (c *ClientWithLock) UpdateLastWriteTime() {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.lastWriteTime = time.Now()
}

func (c *ClientWithLock) GetLastWriteTime() time.Time {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.lastWriteTime
}

type ClientWithAtomic struct {
	lastWriteTime int64
}

func (c *ClientWithAtomic) UpdateLastWriteTime() {
	atomic.StoreInt64(&c.lastWriteTime, time.Now().UnixNano())
}

func (c *ClientWithAtomic) GetLastWriteTime() time.Time {
	return time.Unix(0, atomic.LoadInt64(&c.lastWriteTime))
}

func BenchmarkUpdateLastWriteTimeWithLock(b *testing.B) {
	client := &ClientWithLock{}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		client.UpdateLastWriteTime()
	}
}

func BenchmarkGetLastWriteTimeWithLock(b *testing.B) {
	client := &ClientWithLock{}
	client.UpdateLastWriteTime() // 确保有数据
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		client.GetLastWriteTime()
	}
}

func BenchmarkUpdateLastWriteTimeWithAtomic(b *testing.B) {
	client := &ClientWithAtomic{}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		client.UpdateLastWriteTime()
	}
}

func BenchmarkGetLastWriteTimeWithAtomic(b *testing.B) {
	client := &ClientWithAtomic{}
	client.UpdateLastWriteTime() // 确保有数据
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		client.GetLastWriteTime()
	}
}
