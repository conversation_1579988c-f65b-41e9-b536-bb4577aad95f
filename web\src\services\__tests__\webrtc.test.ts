import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { WebRTCService } from '../webrtc'
import type { ConnectionConfig } from '@/types/meeting'

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  readyState = MockWebSocket.CONNECTING
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null

  constructor(public url: string) {
    // Simulate connection opening
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN
      this.onopen?.(new Event('open'))
    }, 10)
  }

  send(data: string) {
    // Mock send implementation
  }

  close() {
    this.readyState = MockWebSocket.CLOSED
    this.onclose?.(new CloseEvent('close'))
  }
}

// Mock RTCPeerConnection
class MockRTCPeerConnection {
  onicecandidate: ((event: RTCPeerConnectionIceEvent) => void) | null = null
  ontrack: ((event: RTCTrackEvent) => void) | null = null
  ondatachannel: ((event: RTCDataChannelEvent) => void) | null = null
  onconnectionstatechange: (() => void) | null = null
  connectionState: RTCPeerConnectionState = 'new'

  constructor(public config: RTCConfiguration) {}

  async createOffer(): Promise<RTCSessionDescriptionInit> {
    return { type: 'offer', sdp: 'mock-offer-sdp' }
  }

  async createAnswer(): Promise<RTCSessionDescriptionInit> {
    return { type: 'answer', sdp: 'mock-answer-sdp' }
  }

  async setLocalDescription(description: RTCSessionDescriptionInit): Promise<void> {
    // Mock implementation
  }

  async setRemoteDescription(description: RTCSessionDescriptionInit): Promise<void> {
    // Mock implementation
  }

  async addIceCandidate(candidate: RTCIceCandidate): Promise<void> {
    // Mock implementation
  }

  addTrack(track: MediaStreamTrack, stream: MediaStream): RTCRtpSender {
    return {} as RTCRtpSender
  }

  getSenders(): RTCRtpSender[] {
    return []
  }

  createDataChannel(label: string, options?: RTCDataChannelInit): RTCDataChannel {
    return {
      label,
      readyState: 'open',
      send: vi.fn(),
      close: vi.fn(),
      onopen: null,
      onclose: null,
      onmessage: null,
      onerror: null
    } as any
  }

  close(): void {
    this.connectionState = 'closed'
    this.onconnectionstatechange?.()
  }
}

// Setup global mocks
global.WebSocket = MockWebSocket as any
global.RTCPeerConnection = MockRTCPeerConnection as any

describe('WebRTC Service', () => {
  let service: WebRTCService
  let config: ConnectionConfig
  const localDeviceId = 'test-device-123'

  beforeEach(() => {
    config = {
      signalServerUrl: 'ws://localhost:8989/ws',
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
      reconnectAttempts: 3,
      reconnectDelay: 1000
    }
    service = new WebRTCService(config, localDeviceId)
  })

  afterEach(() => {
    service.disconnect()
    vi.clearAllMocks()
  })

  it('should initialize with correct config', () => {
    expect(service).toBeDefined()
    expect(service.isConnected).toBe(false)
  })

  it('should connect to signal server', async () => {
    const onConnected = vi.fn()
    service.onSignalServerConnected = onConnected

    await service.connectToSignalServer()

    expect(service.isConnected).toBe(true)
    expect(onConnected).toHaveBeenCalled()
  })

  it('should handle signal server disconnection', async () => {
    const onDisconnected = vi.fn()
    service.onSignalServerDisconnected = onDisconnected

    await service.connectToSignalServer()
    service.disconnect()

    expect(onDisconnected).toHaveBeenCalled()
  })

  it('should send signal messages', async () => {
    await service.connectToSignalServer()
    
    const sendSpy = vi.spyOn(service as any, 'signalServer.send')
    const message = { type: 'test', data: 'test-data' }

    service.sendSignalMessage(message)

    // Note: In a real test, we'd verify the message was sent
    // This is a simplified test due to mocking limitations
  })

  it('should create peer connection', async () => {
    const participantId = 'participant-123'
    const mockStream = new MediaStream()

    const peerConnection = await service.createPeerConnection(participantId, mockStream)

    expect(peerConnection).toBeDefined()
    expect(service.getPeerConnection(participantId)).toBe(peerConnection)
  })

  it('should create offer for peer connection', async () => {
    const participantId = 'participant-123'
    await service.createPeerConnection(participantId)

    const createOfferSpy = vi.fn().mockResolvedValue({ type: 'offer', sdp: 'mock-sdp' })
    const peerConnection = service.getPeerConnection(participantId)!
    peerConnection.createOffer = createOfferSpy

    await service.createOffer(participantId)

    expect(createOfferSpy).toHaveBeenCalled()
  })

  it('should send data channel messages', async () => {
    const participantId = 'participant-123'
    await service.createPeerConnection(participantId)

    const dataChannel = service.getDataChannel(participantId)
    const sendSpy = vi.spyOn(dataChannel!, 'send')

    const message = { type: 'chat', content: 'Hello' }
    service.sendDataChannelMessage(participantId, message)

    expect(sendSpy).toHaveBeenCalledWith(JSON.stringify(message))
  })

  it('should send binary data through data channel', async () => {
    const participantId = 'participant-123'
    await service.createPeerConnection(participantId)

    const dataChannel = service.getDataChannel(participantId)
    const sendSpy = vi.spyOn(dataChannel!, 'send')

    const binaryData = new ArrayBuffer(8)
    service.sendDataChannelBinary(participantId, binaryData)

    expect(sendSpy).toHaveBeenCalledWith(binaryData)
  })

  it('should cleanup peer connection', async () => {
    const participantId = 'participant-123'
    await service.createPeerConnection(participantId)

    expect(service.getPeerConnection(participantId)).toBeDefined()
    expect(service.getDataChannel(participantId)).toBeDefined()

    service.cleanupPeerConnection(participantId)

    expect(service.getPeerConnection(participantId)).toBeUndefined()
    expect(service.getDataChannel(participantId)).toBeUndefined()
  })

  it('should handle errors gracefully', () => {
    const onError = vi.fn()
    service.onError = onError

    // Simulate an error condition
    service.sendSignalMessage({ type: 'test' })

    // In a disconnected state, this should trigger an error
    expect(onError).toHaveBeenCalledWith('Signal server not connected')
  })

  it('should disconnect and cleanup all connections', async () => {
    const participantId1 = 'participant-1'
    const participantId2 = 'participant-2'

    await service.connectToSignalServer()
    await service.createPeerConnection(participantId1)
    await service.createPeerConnection(participantId2)

    expect(service.isConnected).toBe(true)
    expect(service.getPeerConnection(participantId1)).toBeDefined()
    expect(service.getPeerConnection(participantId2)).toBeDefined()

    service.disconnect()

    expect(service.isConnected).toBe(false)
    expect(service.getPeerConnection(participantId1)).toBeUndefined()
    expect(service.getPeerConnection(participantId2)).toBeUndefined()
  })
})
