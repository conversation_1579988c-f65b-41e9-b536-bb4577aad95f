package cache

import "context"

type MemoryAdapter struct {
	cache map[string]any
}

func NewMemoryAdapter() *MemoryAdapter {
	return &MemoryAdapter{
		cache: make(map[string]any),
	}
}

func (m *MemoryAdapter) Get(ctx context.Context, key string) (any, error) {
	return m.cache[key], nil
}

func (m *MemoryAdapter) Set(ctx context.Context, key string, value any) error {
	m.cache[key] = value
	return nil
}

func (m *MemoryAdapter) Increment(ctx context.Context, key string) error {
	m.cache[key] = m.cache[key].(int) + 1
	return nil
}

func (m *MemoryAdapter) Decrement(ctx context.Context, key string) error {
	m.cache[key] = m.cache[key].(int) - 1
	return nil
}

func (m *MemoryAdapter) Delete(ctx context.Context, key string) error {
	delete(m.cache, key)
	return nil
}

func (m *MemoryAdapter) Flush(ctx context.Context) error {
	m.cache = make(map[string]any)
	return nil
}

func (m *MemoryAdapter) GetMulti(ctx context.Context, keys []string) (KeyValue, error) {
	values := make(KeyValue, len(keys))
	for _, key := range keys {
		values[key] = m.cache[key]
	}
	return values, nil
}

func (m *MemoryAdapter) SetMulti(ctx context.Context, values KeyValue) error {
	for key, value := range values {
		m.cache[key] = value
	}
	return nil
}

func (m *MemoryAdapter) DeleteMulti(ctx context.Context, keys []string) error {
	m.cache = make(map[string]any)
	return nil
}
