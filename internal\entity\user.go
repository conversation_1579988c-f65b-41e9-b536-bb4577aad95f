package entity

import (
	"gorm.io/gorm"
	"time"
)

// User represents the user entity mapped to the [users](file://D:\Workspace\web\owner\meeting\web\src\views\MeetingView.vue#L46-L46) table.
type User struct {
	ID              uint            `gorm:"primaryKey;autoIncrement" json:"id"`
	UUID            string          `gorm:"type:char(36);uniqueIndex" json:"uuid"`
	Name            string          `gorm:"type:varchar(255);not null;default:''" json:"name"`
	Nickname        string          `gorm:"type:varchar(255);not null;default:''" json:"nickname"`
	Gender          uint8           `gorm:"type:tinyint(3) unsigned;not null;default:1" json:"gender"`
	Email           string          `gorm:"type:varchar(255);uniqueIndex" json:"email,omitempty"`
	EmailVerifiedAt *time.Time      `gorm:"type:timestamp" json:"email_verified_at,omitempty"`
	Avatar          string          `gorm:"type:varchar(255);not null;default:'';comment:头像" json:"avatar"`
	Password        string          `gorm:"type:varchar(255);default:''" json:"password"`
	DisabledUntil   *time.Time      `gorm:"type:timestamp" json:"disabled_until,omitempty"`
	RememberToken   *string         `gorm:"type:varchar(100)" json:"remember_token,omitempty"`
	CreatedAt       *time.Time      `gorm:"type:timestamp" json:"created_at,omitempty"`
	UpdatedAt       *time.Time      `gorm:"type:timestamp" json:"updated_at,omitempty"`
	DeletedAt       *gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at,omitempty"`
	
	// 用户收藏
	Favorites []Favorite `gorm:"foreignKey:UserID" json:"favorites,omitempty"`
}