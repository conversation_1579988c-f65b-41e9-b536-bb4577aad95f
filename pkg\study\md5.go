package main

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
)

func md5V(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}
func main() {
	var str []byte
	min, max := 33, 36
	rec(str, min, max)
}

// 33， 34， 35

// 9033e0e305f247c0c3c80d0c7848c8b3
// ef9fcdb53e4e10b12bfcd5e9e78135dc
// 6dd075556effaa6e7f1e3e3ba9fdc5fa
// 98abe3a28383501f4bfd2d9077820f11

func rec(data []byte, min, max int) {
	// 两数之差
	dl := len(data)
	l := max - min
	if dl <= l {
		for i := min; i < max; i++ {
			data = append(data, byte(i))
			fmt.Println(data, md5V(string(data)))
			rec(data, min, i)
		}
	}
}
