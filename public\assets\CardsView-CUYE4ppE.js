import{_ as e}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{D as c,K as p,O as a,ad as t}from"./index-BauktehD.js";const o={},s={class:"flex-1 h-full overflow-y-auto overflow-x-clip overflow-hidden border border-surface rounded-2xl p-6"},i=a("div",{class:"text-color text-2xl font-medium leading-8"},"Cards",-1),n=a("div",{class:"mt-1 text-muted-color leading-6"},"You can make cards using Aura like below 👇",-1),l=a("div",{class:"mt-6 flex flex-wrap items-start gap-6"},[a("div",{class:"flex-1 flex flex-col gap-6"},[a("div",{class:"border border-surface rounded-3xl p-6 flex flex-col gap-6"},[a("div",{class:"flex items-center gap-3"},[a("div",{class:"p-overlaybadge w-fit","data-pc-name":"overlaybadge","data-pc-section":"root",pc1736:""},[a("div",{class:"p-avatar p-component p-avatar-image p-avatar-lg rounded-lg overflow-hidden flex","data-pc-name":"avatar","data-pc-section":"root",pc1735:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/main-avatar.png","data-pc-section":"image"})]),a("span",{class:"p-badge p-component p-badge-dot p-badge-danger","data-pc-name":"pcbadge","data-pc-extend":"badge","data-pc-section":"root"})]),a("div",null,[a("div",{class:"font-medium text-color leading-6"},"Jacob Jones"),a("div",{class:"mt-1 text-muted-color leading-5"},"<EMAIL>")])]),a("div",{class:"flex items-center gap-6"},[a("div",{class:"text-sm leading-5 text-color"},[t("14.k "),a("span",{class:"text-muted-color"},"Followers")]),a("div",{class:"text-sm leading-5 text-color"},[t("359 "),a("span",{class:"text-muted-color"},"Following")])]),a("p",{class:"text-sm text-muted-color leading-5 mb-0"},"Meet Jacob Jones, the whimsical adventurer on a quest for life's quirks. From sock mysteries to subway adventures, join him for a laughter-filled journey!"),a("div",{class:"flex items-center justify-between gap-2"},[a("div",{class:"text-sm leading-5 text-color font-medium"},"Mutual Friends"),a("div",{class:"p-avatar-group p-component","data-pc-name":"avatargroup","data-pc-section":"root",pc1743:""},[a("div",{class:"p-avatar p-component p-avatar-image rounded-lg overflow-hidden","data-pc-name":"avatar","data-pc-section":"root",pc1737:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar1.png","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image rounded-lg overflow-hidden","data-pc-name":"avatar","data-pc-section":"root",pc1738:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar9.jpg","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image rounded-lg overflow-hidden","data-pc-name":"avatar","data-pc-section":"root",pc1739:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image rounded-lg overflow-hidden","data-pc-name":"avatar","data-pc-section":"root",pc1740:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar13.jpg","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image rounded-lg overflow-hidden","data-pc-name":"avatar","data-pc-section":"root",pc1741:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component rounded-lg overflow-hidden text-xs","data-pc-name":"avatar","data-pc-section":"root",pc1742:""},[a("span",{class:"p-avatar-label","data-pc-section":"label"},"+99")])])]),a("div",{class:"p-selectbutton p-component w-full",role:"group","data-pc-name":"selectbutton","data-pc-section":"root",pc1744:""},[a("button",{type:"button",class:"p-togglebutton p-component p-togglebutton-checked flex-1","aria-pressed":"true","data-pc-name":"pcbutton","data-pc-extend":"togglebutton","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false"},[a("span",{class:"p-togglebutton-content","data-pc-section":"content"},[a("span",{class:"p-togglebutton-label","data-pc-section":"label"},"Joined")])]),a("button",{type:"button",class:"p-togglebutton p-component flex-1","aria-pressed":"false","data-pc-name":"pcbutton","data-pc-extend":"togglebutton","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false"},[a("span",{class:"p-togglebutton-content","data-pc-section":"content"},[a("span",{class:"p-togglebutton-label","data-pc-section":"label"},"Hosted")])])]),a("div",{class:"flex flex-col gap-4"},[a("div",{class:"p-2 rounded-2xl flex items-center gap-3 bg-emphasis"},[a("div",{class:"p-overlaybadge w-fit","data-pc-name":"overlaybadge","data-pc-section":"root",pc1746:""},[a("div",{class:"p-avatar p-component p-avatar-image rounded-lg overflow-hidden w-10 h-10 block","data-pc-name":"avatar","data-pc-section":"root",pc1745:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/main-avatar.png","data-pc-section":"image"})]),a("span",{class:"p-badge p-component p-badge-dot p-badge-danger","data-pc-name":"pcbadge","data-pc-extend":"badge","data-pc-section":"root"})]),a("div",{class:"flex-1"},[a("div",{class:"text-color text-sm font-medium leading-5"},"Jacob Jones"),a("div",{class:"mt-1 text-muted-color text-xs leading-4"},"<EMAIL>")]),a("button",{class:"p-button p-component",type:"button","aria-label":"Join","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1747:""},[a("span",{class:"p-button-label","data-pc-section":"label"},"Join")])]),a("div",{class:"p-2 rounded-2xl flex items-center gap-3 bg-emphasis"},[a("div",{class:"p-overlaybadge w-fit","data-pc-name":"overlaybadge","data-pc-section":"root",pc1749:""},[a("div",{class:"p-avatar p-component p-avatar-image rounded-lg overflow-hidden w-10 h-10 flex","data-pc-name":"avatar","data-pc-section":"root",pc1748:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar9.jpg","data-pc-section":"image"})]),a("span",{class:"p-badge p-component p-badge-dot p-badge-danger","data-pc-name":"pcbadge","data-pc-extend":"badge","data-pc-section":"root"})]),a("div",{class:"flex-1"},[a("div",{class:"text-color text-sm font-medium leading-5"},"Courtney Henry"),a("div",{class:"mt-1 text-muted-color text-xs leading-4"},"<EMAIL>")]),a("button",{class:"p-button p-component",type:"button","aria-label":"Join","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1750:""},[a("span",{class:"p-button-label","data-pc-section":"label"},"Join")])])])]),a("div",{class:"border border-surface rounded-3xl p-6"},[a("div",{class:"flex items-center gap-3 p-3 border border-surface rounded-xl shadow-[0px_1px_2px_0px_rgba(18,18,23,0.05)]"},[a("div",{class:"p-avatar p-component p-avatar-image rounded-lg overflow-hidden w-14 h-14","data-pc-name":"avatar","data-pc-section":"root",pc1751:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/main-avatar.png","data-pc-section":"image"})]),a("div",{class:"flex-1"},[a("div",{class:"text-color font-medium leading-7"},"Jacob Jones"),a("div",{class:"text-muted-color text-sm mt-1"},"<EMAIL>")]),a("button",{class:"p-button p-component p-button-icon-only p-button-contrast p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-p-severity":"contrast","data-pc-section":"root",pc1752:""},[a("span",{class:"p-button-icon pi pi-bell","data-pc-section":"icon"}),a("span",{class:"p-button-label","data-pc-section":"label"}," ")])]),a("div",{class:"mt-4 flex flex-col gap-1"},[a("button",{class:"w-full flex items-center gap-2 text-color p-2 bg-transparent hover:bg-emphasis active:bg-surface-200 dark:active:bg-surface-700 cursor-pointer rounded-lg transition-all select-none"},[a("i",{class:"pi pi-envelope text-lg w-7 h-7 flex items-center justify-center"}),a("div",{class:"font-medium leading-normal flex-1 text-left"},"Messages")]),a("button",{class:"w-full flex items-center gap-2 text-color p-2 bg-transparent hover:bg-emphasis active:bg-surface-200 dark:active:bg-surface-700 cursor-pointer rounded-lg transition-all select-none"},[a("i",{class:"pi pi-cog text-lg w-7 h-7 flex items-center justify-center"}),a("div",{class:"font-medium leading-normal flex-1 text-left"},"Settings")]),a("button",{class:"w-full flex items-center gap-2 text-color p-2 bg-transparent hover:bg-emphasis active:bg-surface-200 dark:active:bg-surface-700 cursor-pointer rounded-lg transition-all select-none"},[a("i",{class:"pi pi-sync text-lg w-7 h-7 flex items-center justify-center"}),a("div",{class:"font-medium leading-normal flex-1 text-left"},"Switch Accounts")]),a("button",{class:"w-full flex items-center gap-2 text-color p-2 bg-transparent hover:bg-emphasis active:bg-surface-200 dark:active:bg-surface-700 cursor-pointer rounded-lg transition-all select-none"},[a("i",{class:"pi pi-sign-in text-lg w-7 h-7 flex items-center justify-center"}),a("div",{class:"font-medium leading-normal flex-1 text-left"},"Log out")])]),a("div",{class:"p-divider p-component p-divider-horizontal p-divider-solid p-divider-left",role:"separator","aria-orientation":"horizontal","data-pc-name":"divider","data-pc-section":"root",pc1753:"",style:{"justify-content":"center"}}),a("div",{class:"w-full flex items-center gap-2 text-color p-2 bg-transparent cursor-pointer rounded-lg transition-all select-none"},[a("i",{class:"pi text-lg w-7 h-7 flex items-center justify-center pi-moon"}),a("div",{class:"font-medium leading-normal flex-1 text-left"},"Switch to Dark"),a("div",{class:"p-toggleswitch p-component","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1754:"",style:{position:"relative"}},[a("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input","aria-checked":"false","data-pc-section":"input"}),a("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])])]),a("div",{class:"border border-surface rounded-3xl"},[a("div",{class:"pt-6 px-6 flex flex-col gap-6"},[a("div",{class:"flex items-start gap-2 justify-between"},[a("div",null,[a("div",{class:"text-2xl text-color font-medium"},"Data Analyst"),a("div",{class:"mt-2 text-color"},"Data Insights Ltd.")]),a("button",{class:"p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-outlined",type:"button","data-pc-name":"button","data-p-disabled":"false","data-p-severity":"secondary","data-pc-section":"root",pc1755:""},[a("span",{class:"p-button-icon pi pi-bookmark","data-pc-section":"icon"}),a("span",{class:"p-button-label","data-pc-section":"label"}," ")])]),a("div",{class:"flex flex-wrap gap-1 items-center justify-between"},[a("div",{class:"flex items-center gap-2 whitespace-nowrap text-muted-color"},[a("i",{class:"pi pi-users text-xl"}),a("div",{class:"font-medium leading-none"},"Senior")]),a("div",{class:"flex items-center gap-2 whitespace-nowrap text-muted-color"},[a("i",{class:"pi pi-stopwatch text-xl"}),a("div",{class:"font-medium leading-none"},"Full-Time")]),a("div",{class:"flex items-center gap-2 whitespace-nowrap text-muted-color"},[a("i",{class:"pi pi-money-bill text-xl"}),a("div",{class:"font-medium leading-none"},"$80,000")])]),a("p",{class:"leading-6 text-muted-color mb-0"},"Expert in data analysis? Join Data Insights Ltd. as a senior data analyst. Lead in the world of data with us!"),a("div",{class:"flex flex-wrap gap-2 items-center"},[a("span",{class:"p-tag p-component p-tag-rounded font-normal","data-pc-name":"tag","data-pc-section":"root",pc1756:""},[a("span",{class:"p-tag-label","data-pc-section":"label"},"Data Analysis")]),a("span",{class:"p-tag p-component p-tag-rounded font-normal","data-pc-name":"tag","data-pc-section":"root",pc1757:""},[a("span",{class:"p-tag-label","data-pc-section":"label"},"Analytics")]),a("span",{class:"p-tag p-component p-tag-rounded font-normal","data-pc-name":"tag","data-pc-section":"root",pc1758:""},[a("span",{class:"p-tag-label","data-pc-section":"label"},"Big Data")])])]),a("div",{class:"p-1 mt-4"},[a("button",{class:"p-4 rounded-3xl w-full bg-emphasis transition-all text-color hover:text-color-emphasis flex items-center gap-2 justify-between cursor-pointer"},[a("div",{class:"flex items-center [&>*]:-mr-2"},[a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1759:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1760:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar10.jpg","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1761:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar12.jpg","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1762:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar9.jpg","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1763:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png","data-pc-section":"image"})])]),a("div",{class:"flex items-center gap-2"},[a("div",{class:"font-medium leading-6"},"12 Applicants"),a("i",{class:"pi pi-arrow-right"})])])])])]),a("div",{class:"flex-1 flex flex-col gap-6"},[a("div",{class:"border border-surface rounded-3xl p-6 flex flex-col gap-6"},[a("div",{class:"flex items-center gap-2 text-color"},[a("i",{class:"pi pi-cloud-upload text-xl"}),a("div",{class:"flex-1 font-medium leading-6"},"Upload Files"),a("button",{class:"p-button p-component p-button-icon-only p-button-rounded p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1764:""},[a("span",{class:"p-button-icon pi pi-times","data-pc-section":"icon"}),a("span",{class:"p-button-label","data-pc-section":"label"}," ")])]),a("div",null,[a("label",{for:"document-name",class:"text-color font-medium leading-6"},"Document Name "),a("input",{type:"text",class:"p-inputtext p-component p-filled mt-2 w-full",id:"document-name","data-pc-name":"inputtext","data-pc-section":"root",value:"Aura Theme",pc1765:""})]),a("div",null,[a("label",{class:"text-color font-medium leading-6"},"Upload Files"),a("div",{class:"p-fileupload p-fileupload-advanced p-component bg-transparent border-dashed mt-2","data-pc-name":"fileupload","data-pc-section":"root",pc1766:""},[a("input",{type:"file",accept:"image/*","data-pc-section":"input"}),a("div",{class:"p-fileupload-header","data-pc-section":"header"},[a("div",{class:"flex flex-col items-center justify-center p-6 cursor-pointer"},[a("i",{class:"pi pi-cloud-upload text-4xl text-color"}),a("div",{class:"text-sm text-color font-medium mt-2"},[t("Click to upload "),a("span",{class:"text-muted-color"},"or and drop")]),a("p",{class:"mt-2 mb-0 text-sm text-muted-color text-center"},"PDF, JPG, PNG, JPEG, DOC, CSV, XML, XMLX, XLS, XLSX (max 10MB)")])]),a("div",{class:"p-fileupload-content p-0","data-pc-section":"content","data-p-highlight":"false"})])]),a("div",null,[a("label",{class:"text-color font-medium leading-6"},"Tag (Optional)"),a("div",{class:"p-autocomplete p-component p-inputwrapper p-inputwrapper-filled w-full mt-2","data-pc-name":"autocomplete","data-pc-section":"root",pc1767:"",style:{position:"relative"}},[a("ul",{class:"p-autocomplete-input-multiple",tabindex:"-1",role:"listbox","aria-orientation":"horizontal","data-pc-section":"inputmultiple"},[a("li",{id:"pv_id_57_multiple_option_0",class:"p-autocomplete-chip-item",role:"option","aria-label":"ui","aria-selected":"true","aria-setsize":"3","aria-posinset":"1","data-pc-section":"chipitem"},[a("div",{class:"p-chip p-component p-autocomplete-chip","aria-label":"ui","data-pc-name":"pcchip","data-pc-extend":"chip","data-pc-section":"root"},[a("div",{class:"p-chip-label","data-pc-section":"label"},"ui"),a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"p-icon p-chip-remove-icon","aria-hidden":"true",tabindex:"0","data-pc-section":"removeicon"},[a("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7 14C5.61553 14 4.26215 13.5895 3.11101 12.8203C1.95987 12.0511 1.06266 10.9579 0.532846 9.67879C0.00303296 8.3997 -0.13559 6.99224 0.134506 5.63437C0.404603 4.2765 1.07129 3.02922 2.05026 2.05026C3.02922 1.07129 4.2765 0.404603 5.63437 0.134506C6.99224 -0.13559 8.3997 0.00303296 9.67879 0.532846C10.9579 1.06266 12.0511 1.95987 12.8203 3.11101C13.5895 4.26215 14 5.61553 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM7 1.16667C5.84628 1.16667 4.71846 1.50879 3.75918 2.14976C2.79989 2.79074 2.05222 3.70178 1.61071 4.76768C1.16919 5.83358 1.05367 7.00647 1.27876 8.13803C1.50384 9.26958 2.05941 10.309 2.87521 11.1248C3.69102 11.9406 4.73042 12.4962 5.86198 12.7212C6.99353 12.9463 8.16642 12.8308 9.23232 12.3893C10.2982 11.9478 11.2093 11.2001 11.8502 10.2408C12.4912 9.28154 12.8333 8.15373 12.8333 7C12.8333 5.45291 12.2188 3.96918 11.1248 2.87521C10.0308 1.78125 8.5471 1.16667 7 1.16667ZM4.66662 9.91668C4.58998 9.91704 4.51404 9.90209 4.44325 9.87271C4.37246 9.84333 4.30826 9.8001 4.2544 9.74557C4.14516 9.6362 4.0838 9.48793 4.0838 9.33335C4.0838 9.17876 4.14516 9.0305 4.2544 8.92113L6.17553 7L4.25443 5.07891C4.15139 4.96832 4.09529 4.82207 4.09796 4.67094C4.10063 4.51982 4.16185 4.37563 4.26872 4.26876C4.3756 4.16188 4.51979 4.10066 4.67091 4.09799C4.82204 4.09532 4.96829 4.15142 5.07887 4.25446L6.99997 6.17556L8.92106 4.25446C9.03164 4.15142 9.1779 4.09532 9.32903 4.09799C9.48015 4.10066 9.62434 4.16188 9.73121 4.26876C9.83809 4.37563 9.89931 4.51982 9.90198 4.67094C9.90464 4.82207 9.84855 4.96832 9.74551 5.07891L7.82441 7L9.74554 8.92113C9.85478 9.0305 9.91614 9.17876 9.91614 9.33335C9.91614 9.48793 9.85478 9.6362 9.74554 9.74557C9.69168 9.8001 9.62748 9.84333 9.55669 9.87271C9.4859 9.90209 9.40996 9.91704 9.33332 9.91668C9.25668 9.91704 9.18073 9.90209 9.10995 9.87271C9.03916 9.84333 8.97495 9.8001 8.9211 9.74557L6.99997 7.82444L5.07884 9.74557C5.02499 9.8001 4.96078 9.84333 4.88999 9.87271C4.81921 9.90209 4.74326 9.91704 4.66662 9.91668Z",fill:"currentColor"})])])]),a("li",{id:"pv_id_57_multiple_option_1",class:"p-autocomplete-chip-item",role:"option","aria-label":"redesign","aria-selected":"true","aria-setsize":"3","aria-posinset":"2","data-pc-section":"chipitem"},[a("div",{class:"p-chip p-component p-autocomplete-chip","aria-label":"redesign","data-pc-name":"pcchip","data-pc-extend":"chip","data-pc-section":"root"},[a("div",{class:"p-chip-label","data-pc-section":"label"},"redesign"),a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"p-icon p-chip-remove-icon","aria-hidden":"true",tabindex:"0","data-pc-section":"removeicon"},[a("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7 14C5.61553 14 4.26215 13.5895 3.11101 12.8203C1.95987 12.0511 1.06266 10.9579 0.532846 9.67879C0.00303296 8.3997 -0.13559 6.99224 0.134506 5.63437C0.404603 4.2765 1.07129 3.02922 2.05026 2.05026C3.02922 1.07129 4.2765 0.404603 5.63437 0.134506C6.99224 -0.13559 8.3997 0.00303296 9.67879 0.532846C10.9579 1.06266 12.0511 1.95987 12.8203 3.11101C13.5895 4.26215 14 5.61553 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM7 1.16667C5.84628 1.16667 4.71846 1.50879 3.75918 2.14976C2.79989 2.79074 2.05222 3.70178 1.61071 4.76768C1.16919 5.83358 1.05367 7.00647 1.27876 8.13803C1.50384 9.26958 2.05941 10.309 2.87521 11.1248C3.69102 11.9406 4.73042 12.4962 5.86198 12.7212C6.99353 12.9463 8.16642 12.8308 9.23232 12.3893C10.2982 11.9478 11.2093 11.2001 11.8502 10.2408C12.4912 9.28154 12.8333 8.15373 12.8333 7C12.8333 5.45291 12.2188 3.96918 11.1248 2.87521C10.0308 1.78125 8.5471 1.16667 7 1.16667ZM4.66662 9.91668C4.58998 9.91704 4.51404 9.90209 4.44325 9.87271C4.37246 9.84333 4.30826 9.8001 4.2544 9.74557C4.14516 9.6362 4.0838 9.48793 4.0838 9.33335C4.0838 9.17876 4.14516 9.0305 4.2544 8.92113L6.17553 7L4.25443 5.07891C4.15139 4.96832 4.09529 4.82207 4.09796 4.67094C4.10063 4.51982 4.16185 4.37563 4.26872 4.26876C4.3756 4.16188 4.51979 4.10066 4.67091 4.09799C4.82204 4.09532 4.96829 4.15142 5.07887 4.25446L6.99997 6.17556L8.92106 4.25446C9.03164 4.15142 9.1779 4.09532 9.32903 4.09799C9.48015 4.10066 9.62434 4.16188 9.73121 4.26876C9.83809 4.37563 9.89931 4.51982 9.90198 4.67094C9.90464 4.82207 9.84855 4.96832 9.74551 5.07891L7.82441 7L9.74554 8.92113C9.85478 9.0305 9.91614 9.17876 9.91614 9.33335C9.91614 9.48793 9.85478 9.6362 9.74554 9.74557C9.69168 9.8001 9.62748 9.84333 9.55669 9.87271C9.4859 9.90209 9.40996 9.91704 9.33332 9.91668C9.25668 9.91704 9.18073 9.90209 9.10995 9.87271C9.03916 9.84333 8.97495 9.8001 8.9211 9.74557L6.99997 7.82444L5.07884 9.74557C5.02499 9.8001 4.96078 9.84333 4.88999 9.87271C4.81921 9.90209 4.74326 9.91704 4.66662 9.91668Z",fill:"currentColor"})])])]),a("li",{id:"pv_id_57_multiple_option_2",class:"p-autocomplete-chip-item",role:"option","aria-label":"dashboard","aria-selected":"true","aria-setsize":"3","aria-posinset":"3","data-pc-section":"chipitem"},[a("div",{class:"p-chip p-component p-autocomplete-chip","aria-label":"dashboard","data-pc-name":"pcchip","data-pc-extend":"chip","data-pc-section":"root"},[a("div",{class:"p-chip-label","data-pc-section":"label"},"dashboard"),a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"p-icon p-chip-remove-icon","aria-hidden":"true",tabindex:"0","data-pc-section":"removeicon"},[a("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7 14C5.61553 14 4.26215 13.5895 3.11101 12.8203C1.95987 12.0511 1.06266 10.9579 0.532846 9.67879C0.00303296 8.3997 -0.13559 6.99224 0.134506 5.63437C0.404603 4.2765 1.07129 3.02922 2.05026 2.05026C3.02922 1.07129 4.2765 0.404603 5.63437 0.134506C6.99224 -0.13559 8.3997 0.00303296 9.67879 0.532846C10.9579 1.06266 12.0511 1.95987 12.8203 3.11101C13.5895 4.26215 14 5.61553 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14ZM7 1.16667C5.84628 1.16667 4.71846 1.50879 3.75918 2.14976C2.79989 2.79074 2.05222 3.70178 1.61071 4.76768C1.16919 5.83358 1.05367 7.00647 1.27876 8.13803C1.50384 9.26958 2.05941 10.309 2.87521 11.1248C3.69102 11.9406 4.73042 12.4962 5.86198 12.7212C6.99353 12.9463 8.16642 12.8308 9.23232 12.3893C10.2982 11.9478 11.2093 11.2001 11.8502 10.2408C12.4912 9.28154 12.8333 8.15373 12.8333 7C12.8333 5.45291 12.2188 3.96918 11.1248 2.87521C10.0308 1.78125 8.5471 1.16667 7 1.16667ZM4.66662 9.91668C4.58998 9.91704 4.51404 9.90209 4.44325 9.87271C4.37246 9.84333 4.30826 9.8001 4.2544 9.74557C4.14516 9.6362 4.0838 9.48793 4.0838 9.33335C4.0838 9.17876 4.14516 9.0305 4.2544 8.92113L6.17553 7L4.25443 5.07891C4.15139 4.96832 4.09529 4.82207 4.09796 4.67094C4.10063 4.51982 4.16185 4.37563 4.26872 4.26876C4.3756 4.16188 4.51979 4.10066 4.67091 4.09799C4.82204 4.09532 4.96829 4.15142 5.07887 4.25446L6.99997 6.17556L8.92106 4.25446C9.03164 4.15142 9.1779 4.09532 9.32903 4.09799C9.48015 4.10066 9.62434 4.16188 9.73121 4.26876C9.83809 4.37563 9.89931 4.51982 9.90198 4.67094C9.90464 4.82207 9.84855 4.96832 9.74551 5.07891L7.82441 7L9.74554 8.92113C9.85478 9.0305 9.91614 9.17876 9.91614 9.33335C9.91614 9.48793 9.85478 9.6362 9.74554 9.74557C9.69168 9.8001 9.62748 9.84333 9.55669 9.87271C9.4859 9.90209 9.40996 9.91704 9.33332 9.91668C9.25668 9.91704 9.18073 9.90209 9.10995 9.87271C9.03916 9.84333 8.97495 9.8001 8.9211 9.74557L6.99997 7.82444L5.07884 9.74557C5.02499 9.8001 4.96078 9.84333 4.88999 9.87271C4.81921 9.90209 4.74326 9.91704 4.66662 9.91668Z",fill:"currentColor"})])])]),a("li",{class:"p-autocomplete-input-chip",role:"option","data-pc-section":"inputchip"},[a("input",{id:"multiple-ac-2",type:"text",class:"",tabindex:"0",autocomplete:"off",role:"combobox","aria-haspopup":"listbox","aria-autocomplete":"list","aria-expanded":"false","aria-controls":"pv_id_57_list","data-pc-section":"input"})])]),a("span",{role:"status","aria-live":"polite",class:"p-hidden-accessible","data-pc-section":"hiddensearchresult","data-p-hidden-accessible":"true"},"No results found")])]),a("div",{class:"flex items-center gap-2"},[a("label",{for:"E",class:"cursor-pointer flex-1 flex items-center gap-1 p-2 rounded-border border border-surface hover:bg-emphasis transition-all select-none"},[a("i",{class:"text-color pi pi-globe"}),a("div",{class:"flex-1 text-sm leading-5 text-color"},"Everyone"),a("div",{class:"p-radiobutton p-component p-radiobutton-checked p-variant-filled","data-pc-name":"radiobutton","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false",pc1768:""},[a("input",{id:"E",type:"radio",class:"p-radiobutton-input",name:"dynamic",checked:"","data-pc-section":"input",value:"Everyone"}),a("div",{class:"p-radiobutton-box","data-pc-section":"box"},[a("div",{class:"p-radiobutton-icon","data-pc-section":"icon"})])])]),a("label",{for:"A",class:"cursor-pointer flex-1 flex items-center gap-1 p-2 rounded-border border border-surface hover:bg-emphasis transition-all select-none"},[a("i",{class:"text-color pi pi-users"}),a("div",{class:"flex-1 text-sm leading-5 text-color"},"Admins only"),a("div",{class:"p-radiobutton p-component p-variant-filled","data-pc-name":"radiobutton","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1769:""},[a("input",{id:"A",type:"radio",class:"p-radiobutton-input",name:"dynamic","data-pc-section":"input",value:"Admins only"}),a("div",{class:"p-radiobutton-box","data-pc-section":"box"},[a("div",{class:"p-radiobutton-icon","data-pc-section":"icon"})])])])]),a("div",{class:"flex items-center gap-2"},[a("button",{class:"p-button p-component p-button-outlined flex-1",type:"button","aria-label":"Cancel","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1770:""},[a("span",{class:"p-button-label","data-pc-section":"label"},"Cancel")]),a("button",{class:"p-button p-component flex-1",type:"button","aria-label":"Upload","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1771:""},[a("span",{class:"p-button-label","data-pc-section":"label"},"Upload")])])]),a("div",{class:"border border-surface rounded-3xl p-6"},[a("div",{class:"flex items-start justify-between gap-1"},[a("div",{class:"flex items-center gap-x-2 gap-y-1 flex-wrap flex-1"},[a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1772:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar1.png","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1773:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar2.png","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1774:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar9.jpg","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1775:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg","data-pc-section":"image"})]),a("div",{class:"w-full"}),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1776:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar5.png","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1777:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar13.jpg","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1778:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar7.png","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1779:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar8.png","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1780:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar10.jpg","data-pc-section":"image"})]),a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle","data-pc-name":"avatar","data-pc-section":"root",pc1781:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar12.jpg","data-pc-section":"image"})])]),a("button",{class:"p-button p-component p-button-icon-only p-button-rounded p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1782:""},[a("span",{class:"p-button-icon pi pi-arrow-up-right","data-pc-section":"icon"}),a("span",{class:"p-button-label","data-pc-section":"label"}," ")])]),a("div",{class:"text-2xl font-medium text-color mt-6 leading-8"},"That's your avatar"),a("div",{class:"leading-6 text-muted-color mt-2"},"Easy to use! place it, watch it.")]),a("div",{class:"border border-surface rounded-3xl p-6 flex flex-col gap-6"},[a("div",{class:"flex items-center justify-between gap-2"},[a("div",{class:"text-2xl font-medium leading-8 flex-1"},"Add Member"),a("button",{class:"p-button p-component p-button-icon-only p-button-secondary p-button-rounded p-button-text",type:"button","data-pc-name":"button","data-p-disabled":"false","data-p-severity":"secondary","data-pc-section":"root",pc1783:""},[a("span",{class:"p-button-icon pi pi-times","data-pc-section":"icon"}),a("span",{class:"p-button-label","data-pc-section":"label"}," ")])]),a("div",null,[a("div",{class:"text-muted-color leading-6"},"Email"),a("div",{class:"flex items-start gap-3 mt-2"},[a("div",{class:"p-autocomplete p-component p-inputwrapper p-inputwrapper-filled flex-1","data-pc-name":"autocomplete","data-pc-section":"root",pc1784:"",style:{position:"relative"}},[a("ul",{class:"p-autocomplete-input-multiple",tabindex:"-1",role:"listbox","aria-orientation":"horizontal","data-pc-section":"inputmultiple"},[a("li",{class:"p-autocomplete-input-chip",role:"option","data-pc-section":"inputchip"},[a("input",{id:"multiple-ac-2",type:"text",class:"",tabindex:"0",autocomplete:"off",role:"combobox","aria-haspopup":"listbox","aria-autocomplete":"list","aria-expanded":"false","aria-controls":"pv_id_58_list","data-pc-section":"input"})])]),a("span",{role:"status","aria-live":"polite",class:"p-hidden-accessible","data-pc-section":"hiddensearchresult","data-p-hidden-accessible":"true"},"No results found")]),a("button",{class:"p-button p-component",type:"button","aria-label":"Invite","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1785:""},[a("span",{class:"p-button-label","data-pc-section":"label"},"Invite")])])]),a("div",null,[a("div",{class:"font-medium leading-6 text-muted-color"},"Members"),a("div",{class:"mt-4 flex flex-col gap-4"},[a("div",{class:"flex items-center gap-2 justify-between"},[a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle w-9 h-9","data-pc-name":"avatar","data-pc-section":"root",pc1786:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar8.png","data-pc-section":"image"})]),a("div",{class:"flex-1"},[a("div",{class:"text-sm font-medium text-color leading-5"},"Brook Simmons"),a("div",{class:"text-sm text-muted-color leading-5 line-clamp-4"}," <EMAIL>")]),a("div",{class:"p-select p-component p-inputwrapper p-inputwrapper-filled !w-16 !border-0 !shadow-none","data-pc-name":"select","data-pc-section":"root",pc1787:"",id:"pv_id_59"},[a("span",{class:"p-select-label p-placeholder !p-0 !text-muted-color !text-sm",tabindex:"0",role:"combobox","aria-label":"Select","aria-haspopup":"listbox","aria-expanded":"false","aria-controls":"pv_id_59_list","aria-disabled":"false","data-pc-section":"label"},"Select"),a("div",{class:"p-select-dropdown !p-0 !w-auto","data-pc-section":"dropdown"},[a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"p-icon p-select-dropdown-icon !w-3 !h-3","aria-hidden":"true","data-pc-section":"dropdownicon"},[a("path",{d:"M7.01744 10.398C6.91269 10.3985 6.8089 10.378 6.71215 10.3379C6.61541 10.2977 6.52766 10.2386 6.45405 10.1641L1.13907 4.84913C1.03306 4.69404 0.985221 4.5065 1.00399 4.31958C1.02276 4.13266 1.10693 3.95838 1.24166 3.82747C1.37639 3.69655 1.55301 3.61742 1.74039 3.60402C1.92777 3.59062 2.11386 3.64382 2.26584 3.75424L7.01744 8.47394L11.769 3.75424C11.9189 3.65709 12.097 3.61306 12.2748 3.62921C12.4527 3.64535 12.6199 3.72073 12.7498 3.84328C12.8797 3.96582 12.9647 4.12842 12.9912 4.30502C13.0177 4.48162 12.9841 4.662 12.8958 4.81724L7.58083 10.1322C7.50996 10.2125 7.42344 10.2775 7.32656 10.3232C7.22968 10.3689 7.12449 10.3944 7.01744 10.398Z",fill:"currentColor"})])])])]),a("div",{class:"flex items-center gap-2 justify-between"},[a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle w-9 h-9","data-pc-name":"avatar","data-pc-section":"root",pc1788:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar13.jpg","data-pc-section":"image"})]),a("div",{class:"flex-1"},[a("div",{class:"text-sm font-medium text-color leading-5"},"Dianne Russell"),a("div",{class:"text-sm text-muted-color leading-5 line-clamp-4"},"<EMAIL> ")]),a("div",{class:"p-select p-component p-inputwrapper p-inputwrapper-filled !w-16 !border-0 !shadow-none","data-pc-name":"select","data-pc-section":"root",pc1789:"",id:"pv_id_60"},[a("span",{class:"p-select-label p-placeholder !p-0 !text-muted-color !text-sm",tabindex:"0",role:"combobox","aria-label":"Select","aria-haspopup":"listbox","aria-expanded":"false","aria-controls":"pv_id_60_list","aria-disabled":"false","data-pc-section":"label"},"Select"),a("div",{class:"p-select-dropdown !p-0 !w-auto","data-pc-section":"dropdown"},[a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"p-icon p-select-dropdown-icon !w-3 !h-3","aria-hidden":"true","data-pc-section":"dropdownicon"},[a("path",{d:"M7.01744 10.398C6.91269 10.3985 6.8089 10.378 6.71215 10.3379C6.61541 10.2977 6.52766 10.2386 6.45405 10.1641L1.13907 4.84913C1.03306 4.69404 0.985221 4.5065 1.00399 4.31958C1.02276 4.13266 1.10693 3.95838 1.24166 3.82747C1.37639 3.69655 1.55301 3.61742 1.74039 3.60402C1.92777 3.59062 2.11386 3.64382 2.26584 3.75424L7.01744 8.47394L11.769 3.75424C11.9189 3.65709 12.097 3.61306 12.2748 3.62921C12.4527 3.64535 12.6199 3.72073 12.7498 3.84328C12.8797 3.96582 12.9647 4.12842 12.9912 4.30502C13.0177 4.48162 12.9841 4.662 12.8958 4.81724L7.58083 10.1322C7.50996 10.2125 7.42344 10.2775 7.32656 10.3232C7.22968 10.3689 7.12449 10.3944 7.01744 10.398Z",fill:"currentColor"})])])])]),a("div",{class:"flex items-center gap-2 justify-between"},[a("div",{class:"p-avatar p-component p-avatar-image p-avatar-circle w-9 h-9","data-pc-name":"avatar","data-pc-section":"root",pc1790:""},[a("img",{src:"https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar12.jpg","data-pc-section":"image"})]),a("div",{class:"flex-1"},[a("div",{class:"text-sm font-medium text-color leading-5"},"Jacob Jones"),a("div",{class:"text-sm text-muted-color leading-5 line-clamp-4"},"<EMAIL> ")]),a("div",{class:"p-select p-component p-inputwrapper p-inputwrapper-filled !w-16 !border-0 !shadow-none","data-pc-name":"select","data-pc-section":"root",pc1791:"",id:"pv_id_61"},[a("span",{class:"p-select-label p-placeholder !p-0 !text-muted-color !text-sm",tabindex:"0",role:"combobox","aria-label":"Select","aria-haspopup":"listbox","aria-expanded":"false","aria-controls":"pv_id_61_list","aria-disabled":"false","data-pc-section":"label"},"Select"),a("div",{class:"p-select-dropdown !p-0 !w-auto","data-pc-section":"dropdown"},[a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"p-icon p-select-dropdown-icon !w-3 !h-3","aria-hidden":"true","data-pc-section":"dropdownicon"},[a("path",{d:"M7.01744 10.398C6.91269 10.3985 6.8089 10.378 6.71215 10.3379C6.61541 10.2977 6.52766 10.2386 6.45405 10.1641L1.13907 4.84913C1.03306 4.69404 0.985221 4.5065 1.00399 4.31958C1.02276 4.13266 1.10693 3.95838 1.24166 3.82747C1.37639 3.69655 1.55301 3.61742 1.74039 3.60402C1.92777 3.59062 2.11386 3.64382 2.26584 3.75424L7.01744 8.47394L11.769 3.75424C11.9189 3.65709 12.097 3.61306 12.2748 3.62921C12.4527 3.64535 12.6199 3.72073 12.7498 3.84328C12.8797 3.96582 12.9647 4.12842 12.9912 4.30502C13.0177 4.48162 12.9841 4.662 12.8958 4.81724L7.58083 10.1322C7.50996 10.2125 7.42344 10.2775 7.32656 10.3232C7.22968 10.3689 7.12449 10.3944 7.01744 10.398Z",fill:"currentColor"})])])])])])]),a("div",null,[a("div",{class:"font-medium leading-6 text-muted-color"},"Copy Link"),a("div",{class:"flex items-center gap-3 mt-2"},[a("input",{type:"text",class:"p-inputtext p-component p-filled flex-1",readonly:"","data-pc-name":"inputtext","data-pc-section":"root",value:"https://www.example.com/shared-files/user123/document-collection/file12345",pc1792:""}),a("button",{class:"p-button p-component p-button-secondary",type:"button","aria-label":"Copy","data-pc-name":"button","data-p-disabled":"false","data-p-severity":"secondary","data-pc-section":"root",pc1793:""},[a("span",{class:"p-button-label","data-pc-section":"label"},"Copy")])])])])]),a("div",{class:"flex-1 flex flex-wrap gap-6"},[a("div",{class:"flex-1 border border-surface rounded-3xl p-6"},[a("div",{class:"text-color font-medium leading-6 mb-4"},"User Profiles"),a("div",{class:"p-selectbutton p-component w-full",role:"group","data-pc-name":"selectbutton","data-pc-section":"root",pc1794:""},[a("button",{type:"button",class:"p-togglebutton p-component p-togglebutton-checked flex-1","aria-pressed":"true","data-pc-name":"pcbutton","data-pc-extend":"togglebutton","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false"},[a("span",{class:"p-togglebutton-content","data-pc-section":"content"},[a("span",{class:"p-togglebutton-label","data-pc-section":"label"},"Chilling")])]),a("button",{type:"button",class:"p-togglebutton p-component flex-1","aria-pressed":"false","data-pc-name":"pcbutton","data-pc-extend":"togglebutton","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false"},[a("span",{class:"p-togglebutton-content","data-pc-section":"content"},[a("span",{class:"p-togglebutton-label","data-pc-section":"label"},"Do Not Disturb")])])]),a("div",{class:"flex flex-col gap-4 mt-6"},[a("div",{class:"flex items-center gap-3"},[a("i",{class:"pi pi-volume-down text-color text-xl"}),a("div",{class:"leading-6 text-color flex-1"},"Sound"),a("div",{class:"p-toggleswitch p-component p-toggleswitch-checked","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false",pc1795:"",style:{position:"relative"}},[a("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input",checked:"","aria-checked":"true","data-pc-section":"input"}),a("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])]),a("div",{class:"flex items-center gap-3"},[a("i",{class:"pi pi-wifi text-color text-xl"}),a("div",{class:"leading-6 text-color flex-1"},"Wi-Fi"),a("div",{class:"p-toggleswitch p-component p-toggleswitch-checked","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false",pc1796:"",style:{position:"relative"}},[a("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input",checked:"","aria-checked":"true","data-pc-section":"input"}),a("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])]),a("div",{class:"flex items-center gap-3"},[a("i",{class:"pi pi-moon text-color text-xl"}),a("div",{class:"leading-6 text-color flex-1"},"Dark Mode"),a("div",{class:"p-toggleswitch p-component","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1797:"",style:{position:"relative"}},[a("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input","aria-checked":"false","data-pc-section":"input"}),a("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])]),a("div",{class:"flex items-center gap-3"},[a("i",{class:"pi pi-map-marker text-color text-xl"}),a("div",{class:"leading-6 text-color flex-1"},"Location Services"),a("div",{class:"p-toggleswitch p-component","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1798:"",style:{position:"relative"}},[a("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input","aria-checked":"false","data-pc-section":"input"}),a("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])]),a("div",{class:"flex items-center gap-3"},[a("i",{class:"pi pi-shield text-color text-xl"}),a("div",{class:"leading-6 text-color flex-1"},"Privacy Settings"),a("div",{class:"p-toggleswitch p-component p-toggleswitch-checked","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false",pc1799:"",style:{position:"relative"}},[a("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input",checked:"","aria-checked":"true","data-pc-section":"input"}),a("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])]),a("div",{class:"flex items-center gap-3"},[a("i",{class:"pi pi-sync text-color text-xl"}),a("div",{class:"leading-6 text-color flex-1"},"Auto Update"),a("div",{class:"p-toggleswitch p-component","data-pc-name":"toggleswitch","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1800:"",style:{position:"relative"}},[a("input",{type:"checkbox",role:"switch",class:"p-toggleswitch-input","aria-checked":"false","data-pc-section":"input"}),a("span",{class:"p-toggleswitch-slider","data-pc-section":"slider"})])])])]),a("div",{class:"flex-1 border border-surface rounded-3xl p-6 flex flex-col gap-6"},[a("div",{class:"font-medium text-color text-2xl text-center"},"Forgot Password"),a("div",null,[a("div",{class:"text-muted-color text-lg text-center leading-snug"},[t(" Verification code "),a("br"),t(" has been sent to email ")]),a("div",{class:"rounded-full px-4 py-1 bg-surface-200 dark:bg-surface-800 w-fit mx-auto mt-4 text-color text-lg leading-relaxed"}," u*******<EMAIL>")]),a("div",{class:"flex items-center justify-center"},[a("button",{class:"p-button p-component p-button-text",type:"button","aria-label":"Resend","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1801:""},[a("span",{class:"p-button-label","data-pc-section":"label"},"Resend")])]),a("div",{class:"flex items-center justify-center"},[a("div",{class:"p-inputotp p-component w-full [&>*]:flex-1 [&>*]:min-h-14 [&>*]:text-2xl","data-pc-name":"inputotp","data-pc-section":"root",pc1802:""},[a("input",{type:"text",class:"p-inputtext p-component p-inputotp-input",inputmode:"numeric","data-pc-name":"pcinput","data-pc-extend":"inputtext","data-pc-section":"root",value:"0"}),a("input",{type:"text",class:"p-inputtext p-component p-inputotp-input",inputmode:"numeric","data-pc-name":"pcinput","data-pc-extend":"inputtext","data-pc-section":"root",value:"2"}),a("input",{type:"text",class:"p-inputtext p-component p-inputotp-input",inputmode:"numeric","data-pc-name":"pcinput","data-pc-extend":"inputtext","data-pc-section":"root",value:"3"}),a("input",{type:"text",class:"p-inputtext p-component p-inputotp-input",inputmode:"numeric","data-pc-name":"pcinput","data-pc-extend":"inputtext","data-pc-section":"root"}),a("input",{type:"text",class:"p-inputtext p-component p-inputotp-input",inputmode:"numeric","data-pc-name":"pcinput","data-pc-extend":"inputtext","data-pc-section":"root"}),a("input",{type:"text",class:"p-inputtext p-component p-inputotp-input",inputmode:"numeric","data-pc-name":"pcinput","data-pc-extend":"inputtext","data-pc-section":"root"})])]),a("button",{class:"p-button p-component",type:"button","aria-label":"Change password","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1803:""},[a("span",{class:"p-button-label","data-pc-section":"label"},"Change password")])]),a("div",{class:"border border-surface rounded-3xl p-6"},[a("div",{class:"text-color font-medium leading-6 mb-6"},"Price Range"),a("div",null,[a("div",{class:"p-slider p-component p-slider-horizontal","data-pc-name":"slider","data-pc-section":"root","data-p-sliding":"false",pc1804:""},[a("span",{class:"p-slider-range","data-pc-section":"range",style:{position:"absolute",left:"0%",width:"66.6667%"}}),a("span",{class:"p-slider-handle",tabindex:"0",role:"slider","aria-valuemin":"0","aria-valuenow":"0","aria-valuemax":"15000","aria-orientation":"horizontal","data-pc-section":"starthandler",style:{position:"absolute",left:"0%"}}),a("span",{class:"p-slider-handle",tabindex:"0",role:"slider","aria-valuemin":"0","aria-valuenow":"10000","aria-valuemax":"15000","aria-orientation":"horizontal","data-pc-section":"endhandler",style:{position:"absolute",left:"66.6667%"}})])]),a("div",{class:"mt-4 flex gap-2"},[a("div",{class:"flex-1"},[a("label",{for:"price-min-val",class:"leading-6 text-color"},"Min Value"),a("span",{class:"p-inputnumber p-component p-inputwrapper p-inputwrapper-filled [&>*]:w-full mt-2","data-pc-name":"inputnumber","data-pc-section":"root",pc1805:""},[a("input",{type:"text",class:"p-inputtext p-component p-inputnumber-input",id:"price-min-val",role:"spinbutton","aria-valuemin":"0","aria-valuenow":"0",inputmode:"decimal","data-pc-name":"pcinput","data-pc-extend":"inputtext","data-pc-section":"root",value:"$0.00"})])]),a("div",{class:"flex-1"},[a("label",{for:"price-max-val",class:"leading-6 text-color"},"Max Value"),a("span",{class:"p-inputnumber p-component p-inputwrapper p-inputwrapper-filled [&>*]:w-full mt-2","data-pc-name":"inputnumber","data-pc-section":"root",pc1806:""},[a("input",{type:"text",class:"p-inputtext p-component p-inputnumber-input",id:"price-max-val",role:"spinbutton","aria-valuenow":"10000",inputmode:"decimal","data-pc-name":"pcinput","data-pc-extend":"inputtext","data-pc-section":"root",value:"$10,000.00"})])])]),a("div",{class:"mt-4"},[a("div",{class:"text-color font-medium leading-6 mb-6"},"Popular specs"),a("div",{class:"flex items-center gap-4 flex-wrap"},[a("div",{class:"flex align-items-center"},[a("div",{class:"p-checkbox p-component p-checkbox-checked","data-pc-name":"checkbox","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false",pc1807:""},[a("input",{id:"Furnished",type:"checkbox",class:"p-checkbox-input",name:"Furnished",checked:"","data-pc-section":"input",value:"Furnished"}),a("div",{class:"p-checkbox-box","data-pc-section":"box"},[a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"p-icon p-checkbox-icon","aria-hidden":"true","data-pc-section":"icon"},[a("path",{d:"M4.86199 11.5948C4.78717 11.5923 4.71366 11.5745 4.64596 11.5426C4.57826 11.5107 4.51779 11.4652 4.46827 11.4091L0.753985 7.69483C0.683167 7.64891 0.623706 7.58751 0.580092 7.51525C0.536478 7.44299 0.509851 7.36177 0.502221 7.27771C0.49459 7.19366 0.506156 7.10897 0.536046 7.03004C0.565935 6.95111 0.613367 6.88 0.674759 6.82208C0.736151 6.76416 0.8099 6.72095 0.890436 6.69571C0.970973 6.67046 1.05619 6.66385 1.13966 6.67635C1.22313 6.68886 1.30266 6.72017 1.37226 6.76792C1.44186 6.81567 1.4997 6.8786 1.54141 6.95197L4.86199 10.2503L12.6397 2.49483C12.7444 2.42694 12.8689 2.39617 12.9932 2.40745C13.1174 2.41873 13.2343 2.47141 13.3251 2.55705C13.4159 2.64268 13.4753 2.75632 13.4938 2.87973C13.5123 3.00315 13.4888 3.1292 13.4271 3.23768L5.2557 11.4091C5.20618 11.4652 5.14571 11.5107 5.07801 11.5426C5.01031 11.5745 4.9368 11.5923 4.86199 11.5948Z",fill:"currentColor"})])])]),a("label",{for:"Furnished",class:"ml-2"},"Furnished")]),a("div",{class:"flex align-items-center"},[a("div",{class:"p-checkbox p-component","data-pc-name":"checkbox","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1808:""},[a("input",{id:"Unfurnished",type:"checkbox",class:"p-checkbox-input",name:"Unfurnished","data-pc-section":"input",value:"Unfurnished"}),a("div",{class:"p-checkbox-box","data-pc-section":"box"})]),a("label",{for:"Unfurnished",class:"ml-2"},"Unfurnished")]),a("div",{class:"flex align-items-center"},[a("div",{class:"p-checkbox p-component p-checkbox-checked","data-pc-name":"checkbox","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false",pc1809:""},[a("input",{id:"Detached",type:"checkbox",class:"p-checkbox-input",name:"Detached",checked:"","data-pc-section":"input",value:"Detached"}),a("div",{class:"p-checkbox-box","data-pc-section":"box"},[a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"p-icon p-checkbox-icon","aria-hidden":"true","data-pc-section":"icon"},[a("path",{d:"M4.86199 11.5948C4.78717 11.5923 4.71366 11.5745 4.64596 11.5426C4.57826 11.5107 4.51779 11.4652 4.46827 11.4091L0.753985 7.69483C0.683167 7.64891 0.623706 7.58751 0.580092 7.51525C0.536478 7.44299 0.509851 7.36177 0.502221 7.27771C0.49459 7.19366 0.506156 7.10897 0.536046 7.03004C0.565935 6.95111 0.613367 6.88 0.674759 6.82208C0.736151 6.76416 0.8099 6.72095 0.890436 6.69571C0.970973 6.67046 1.05619 6.66385 1.13966 6.67635C1.22313 6.68886 1.30266 6.72017 1.37226 6.76792C1.44186 6.81567 1.4997 6.8786 1.54141 6.95197L4.86199 10.2503L12.6397 2.49483C12.7444 2.42694 12.8689 2.39617 12.9932 2.40745C13.1174 2.41873 13.2343 2.47141 13.3251 2.55705C13.4159 2.64268 13.4753 2.75632 13.4938 2.87973C13.5123 3.00315 13.4888 3.1292 13.4271 3.23768L5.2557 11.4091C5.20618 11.4652 5.14571 11.5107 5.07801 11.5426C5.01031 11.5745 4.9368 11.5923 4.86199 11.5948Z",fill:"currentColor"})])])]),a("label",{for:"Detached",class:"ml-2"},"Detached")]),a("div",{class:"flex align-items-center"},[a("div",{class:"p-checkbox p-component","data-pc-name":"checkbox","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1810:""},[a("input",{id:"Underfloor heating",type:"checkbox",class:"p-checkbox-input",name:"Underfloor heating","data-pc-section":"input",value:"Underfloor heating"}),a("div",{class:"p-checkbox-box","data-pc-section":"box"})]),a("label",{for:"Underfloor heating",class:"ml-2"},"Underfloor heating")]),a("div",{class:"flex align-items-center"},[a("div",{class:"p-checkbox p-component p-checkbox-checked","data-pc-name":"checkbox","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false",pc1811:""},[a("input",{id:"Balcony",type:"checkbox",class:"p-checkbox-input",name:"Balcony",checked:"","data-pc-section":"input",value:"Balcony"}),a("div",{class:"p-checkbox-box","data-pc-section":"box"},[a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"p-icon p-checkbox-icon","aria-hidden":"true","data-pc-section":"icon"},[a("path",{d:"M4.86199 11.5948C4.78717 11.5923 4.71366 11.5745 4.64596 11.5426C4.57826 11.5107 4.51779 11.4652 4.46827 11.4091L0.753985 7.69483C0.683167 7.64891 0.623706 7.58751 0.580092 7.51525C0.536478 7.44299 0.509851 7.36177 0.502221 7.27771C0.49459 7.19366 0.506156 7.10897 0.536046 7.03004C0.565935 6.95111 0.613367 6.88 0.674759 6.82208C0.736151 6.76416 0.8099 6.72095 0.890436 6.69571C0.970973 6.67046 1.05619 6.66385 1.13966 6.67635C1.22313 6.68886 1.30266 6.72017 1.37226 6.76792C1.44186 6.81567 1.4997 6.8786 1.54141 6.95197L4.86199 10.2503L12.6397 2.49483C12.7444 2.42694 12.8689 2.39617 12.9932 2.40745C13.1174 2.41873 13.2343 2.47141 13.3251 2.55705C13.4159 2.64268 13.4753 2.75632 13.4938 2.87973C13.5123 3.00315 13.4888 3.1292 13.4271 3.23768L5.2557 11.4091C5.20618 11.4652 5.14571 11.5107 5.07801 11.5426C5.01031 11.5745 4.9368 11.5923 4.86199 11.5948Z",fill:"currentColor"})])])]),a("label",{for:"Balcony",class:"ml-2"},"Balcony")]),a("div",{class:"flex align-items-center"},[a("div",{class:"p-checkbox p-component","data-pc-name":"checkbox","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1812:""},[a("input",{id:"Duplex",type:"checkbox",class:"p-checkbox-input",name:"Duplex","data-pc-section":"input",value:"Duplex"}),a("div",{class:"p-checkbox-box","data-pc-section":"box"})]),a("label",{for:"Duplex",class:"ml-2"},"Duplex")]),a("div",{class:"flex align-items-center"},[a("div",{class:"p-checkbox p-component","data-pc-name":"checkbox","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1813:""},[a("input",{id:"Triplex",type:"checkbox",class:"p-checkbox-input",name:"Triplex","data-pc-section":"input",value:"Triplex"}),a("div",{class:"p-checkbox-box","data-pc-section":"box"})]),a("label",{for:"Triplex",class:"ml-2"},"Triplex")]),a("div",{class:"flex align-items-center"},[a("div",{class:"p-checkbox p-component","data-pc-name":"checkbox","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1814:""},[a("input",{id:"Garden",type:"checkbox",class:"p-checkbox-input",name:"Garden","data-pc-section":"input",value:"Garden"}),a("div",{class:"p-checkbox-box","data-pc-section":"box"})]),a("label",{for:"Garden",class:"ml-2"},"Garden")]),a("div",{class:"flex align-items-center"},[a("div",{class:"p-checkbox p-component","data-pc-name":"checkbox","data-pc-section":"root","data-p-checked":"false","data-p-disabled":"false",pc1815:""},[a("input",{id:"Central location",type:"checkbox",class:"p-checkbox-input",name:"Central location","data-pc-section":"input",value:"Central location"}),a("div",{class:"p-checkbox-box","data-pc-section":"box"})]),a("label",{for:"Central location",class:"ml-2"},"Central location")]),a("div",{class:"flex align-items-center"},[a("div",{class:"p-checkbox p-component p-checkbox-checked","data-pc-name":"checkbox","data-pc-section":"root","data-p-checked":"true","data-p-disabled":"false",pc1816:""},[a("input",{id:"Sea view",type:"checkbox",class:"p-checkbox-input",name:"Sea view",checked:"","data-pc-section":"input",value:"Sea view"}),a("div",{class:"p-checkbox-box","data-pc-section":"box"},[a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",class:"p-icon p-checkbox-icon","aria-hidden":"true","data-pc-section":"icon"},[a("path",{d:"M4.86199 11.5948C4.78717 11.5923 4.71366 11.5745 4.64596 11.5426C4.57826 11.5107 4.51779 11.4652 4.46827 11.4091L0.753985 7.69483C0.683167 7.64891 0.623706 7.58751 0.580092 7.51525C0.536478 7.44299 0.509851 7.36177 0.502221 7.27771C0.49459 7.19366 0.506156 7.10897 0.536046 7.03004C0.565935 6.95111 0.613367 6.88 0.674759 6.82208C0.736151 6.76416 0.8099 6.72095 0.890436 6.69571C0.970973 6.67046 1.05619 6.66385 1.13966 6.67635C1.22313 6.68886 1.30266 6.72017 1.37226 6.76792C1.44186 6.81567 1.4997 6.8786 1.54141 6.95197L4.86199 10.2503L12.6397 2.49483C12.7444 2.42694 12.8689 2.39617 12.9932 2.40745C13.1174 2.41873 13.2343 2.47141 13.3251 2.55705C13.4159 2.64268 13.4753 2.75632 13.4938 2.87973C13.5123 3.00315 13.4888 3.1292 13.4271 3.23768L5.2557 11.4091C5.20618 11.4652 5.14571 11.5107 5.07801 11.5426C5.01031 11.5745 4.9368 11.5923 4.86199 11.5948Z",fill:"currentColor"})])])]),a("label",{for:"Sea view",class:"ml-2"},"Sea view")])])]),a("div",{class:"flex items-center gap-3 flex-wrap [&>*]:flex-1 mt-6"},[a("button",{class:"p-button p-component p-button-outlined",type:"button","aria-label":"Undo","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1817:""},[a("span",{class:"p-button-label","data-pc-section":"label"},"Undo")]),a("button",{class:"p-button p-component",type:"button","aria-label":"Random","data-pc-name":"button","data-p-disabled":"false","data-pc-section":"root",pc1818:""},[a("span",{class:"p-button-label","data-pc-section":"label"},"Random")])])])])],-1),d=[i,n,l];function r(m,u){return c(),p("div",s,d)}const b=e(o,[["render",r]]);export{b as default};
