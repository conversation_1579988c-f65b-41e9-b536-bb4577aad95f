<template>
  <div class="flex-1 p-4 overflow-hidden">
    <div :class="['grid gap-4 h-full', getVideoGridClass()]">
      <!-- Local Video -->
      <div class="relative bg-gray-800 rounded-lg overflow-hidden group">
        <video
          ref="localVideoRef"
          autoplay
          playsinline
          muted
          class="w-full h-full object-cover"
        ></video>

        <!-- Local Video Overlay -->
        <div class="absolute inset-0 flex flex-col justify-between p-3">
          <div class="flex justify-between items-start">
            <div class="bg-black bg-opacity-60 text-white text-sm px-2 py-1 rounded">
              {{ localParticipantName || $t('tools.webRtcMeeting.you') }}
            </div>
            <div class="flex space-x-1">
              <div v-if="!localMediaState.microphone" class="bg-red-500 p-1 rounded">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06L3.28 2.22z"
                  />
                </svg>
              </div>
              <div v-if="!localMediaState.camera" class="bg-red-500 p-1 rounded">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06L3.28 2.22z"
                  />
                </svg>
              </div>
            </div>
          </div>

          <!-- Screen Share Indicator -->
          <div
            v-if="localMediaState.screenSharing"
            class="bg-purple-500 bg-opacity-80 text-white text-xs px-2 py-1 rounded self-start"
          >
            {{ $t('tools.webRtcMeeting.video.sharingScreen') }}
          </div>
        </div>

        <!-- No Video Placeholder -->
        <div
          v-if="!localMediaState.camera && !localMediaState.screenSharing"
          class="absolute inset-0 flex items-center justify-center bg-gray-700"
        >
          <div class="text-center">
            <div class="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-2">
              <span class="text-2xl text-white font-medium">
                {{ getInitials(localParticipantName || 'You') }}
              </span>
            </div>
            <p class="text-gray-300 text-sm">{{ $t('tools.webRtcMeeting.video.cameraOff') }}</p>
          </div>
        </div>
      </div>

      <!-- Remote Videos -->
      <div
        v-for="stream in remoteStreams"
        :key="stream.id"
        class="relative bg-gray-800 rounded-lg overflow-hidden group"
      >
        <video
          :ref="(el) => setRemoteVideoRef(el, stream.id)"
          autoplay
          playsinline
          class="w-full h-full object-cover"
        ></video>

        <!-- Remote Video Overlay -->
        <div class="absolute inset-0 flex flex-col justify-between p-3">
          <div class="flex justify-between items-start">
            <div class="bg-black bg-opacity-60 text-white text-sm px-2 py-1 rounded">
              {{ getParticipantName(stream.participantId) }}
            </div>
            <div class="flex space-x-1">
              <!-- Audio/Video status indicators -->
              <div
                v-if="!getParticipantMediaState(stream.participantId, 'microphone')"
                class="bg-red-500 p-1 rounded"
              >
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06L3.28 2.22z"
                  />
                </svg>
              </div>
              <div
                v-if="!getParticipantMediaState(stream.participantId, 'camera')"
                class="bg-red-500 p-1 rounded"
              >
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M3.28 2.22a.75.75 0 00-1.06 1.06l14.5 14.5a.75.75 0 101.06-1.06L3.28 2.22z"
                  />
                </svg>
              </div>
            </div>
          </div>

          <!-- Screen Share Indicator -->
          <div
            v-if="getParticipantMediaState(stream.participantId, 'screenSharing')"
            class="bg-purple-500 bg-opacity-80 text-white text-xs px-2 py-1 rounded self-start"
          >
            {{ $t('tools.webRtcMeeting.video.sharingScreen') }}
          </div>
        </div>

        <!-- No Video Placeholder -->
        <div
          v-if="!getParticipantMediaState(stream.participantId, 'camera') && !getParticipantMediaState(stream.participantId, 'screenSharing')"
          class="absolute inset-0 flex items-center justify-center bg-gray-700"
        >
          <div class="text-center">
            <div class="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-2">
              <span class="text-2xl text-white font-medium">
                {{ getInitials(getParticipantName(stream.participantId)) }}
              </span>
            </div>
            <p class="text-gray-300 text-sm">{{ $t('tools.webRtcMeeting.video.cameraOff') }}</p>
          </div>
        </div>

        <!-- Connection Status -->
        <div
          v-if="getParticipantConnectionState(stream.participantId) !== 'connected'"
          class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50"
        >
          <div class="text-center">
            <div v-if="getParticipantConnectionState(stream.participantId) === 'connecting'" class="mb-2">
              <div class="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin mx-auto"></div>
            </div>
            <p class="text-white text-sm">
              {{ getConnectionStatusText(getParticipantConnectionState(stream.participantId)) }}
            </p>
          </div>
        </div>
      </div>

      <!-- Empty Slots for Better Layout -->
      <div
        v-for="n in getEmptySlots()"
        :key="`empty-${n}`"
        class="bg-gray-900 rounded-lg border-2 border-dashed border-gray-600 flex items-center justify-center"
      >
        <div class="text-center text-gray-500">
          <svg class="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
            <path
              d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"
            />
          </svg>
          <p class="text-sm">{{ $t('tools.webRtcMeeting.video.waitingForParticipant') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { RemoteStream, MediaState, Participant } from '@/types/meeting'

interface Props {
  remoteStreams: RemoteStream[]
  localMediaState: MediaState
  localParticipantName: string
  participants: Participant[]
  localStream?: MediaStream | null
}

interface Emits {
  (e: 'streamAttached', streamId: string, element: HTMLVideoElement): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

// Refs
const localVideoRef = ref<HTMLVideoElement>()
const remoteVideoRefs = ref<Record<string, HTMLVideoElement>>({})

// Methods
const getVideoGridClass = () => {
  const totalVideos = props.remoteStreams.length + 1 // +1 for local video

  if (totalVideos === 1) return 'grid-cols-1'
  if (totalVideos === 2) return 'grid-cols-2'
  if (totalVideos <= 4) return 'grid-cols-2 grid-rows-2'
  if (totalVideos <= 6) return 'grid-cols-3 grid-rows-2'
  if (totalVideos <= 9) return 'grid-cols-3 grid-rows-3'
  return 'grid-cols-4 grid-rows-3'
}

const getEmptySlots = () => {
  const totalVideos = props.remoteStreams.length + 1
  const gridSizes = [1, 2, 4, 6, 9, 12]
  const currentGridSize = gridSizes.find(size => size >= totalVideos) || 12
  return Math.max(0, currentGridSize - totalVideos)
}

const setRemoteVideoRef = (el: any, streamId: string) => {
  if (el && el instanceof HTMLVideoElement) {
    remoteVideoRefs.value[streamId] = el

    // Find the stream and attach it
    const streamEntry = props.remoteStreams.find(s => s.id === streamId)
    if (streamEntry) {
      el.srcObject = streamEntry.stream
      emit('streamAttached', streamId, el)
    }
  }
}

const getParticipantName = (participantId: string) => {
  const participant = props.participants.find(p => p.id === participantId)
  return participant?.name || participantId
}

const getParticipantMediaState = (participantId: string, mediaType: keyof MediaState) => {
  const participant = props.participants.find(p => p.id === participantId)
  return participant?.mediaState[mediaType] ?? true
}

const getParticipantConnectionState = (participantId: string) => {
  const participant = props.participants.find(p => p.id === participantId)
  return participant?.connectionState ?? 'disconnected'
}

const getConnectionStatusText = (status: string) => {
  switch (status) {
    case 'connecting':
      return t('tools.webRtcMeeting.status.connecting')
    case 'disconnected':
      return t('tools.webRtcMeeting.status.disconnected')
    default:
      return ''
  }
}

const getInitials = (name: string) => {
  return name.charAt(0).toUpperCase()
}

// Watch for local stream changes
watch(() => props.localStream, (newStream) => {
  if (localVideoRef.value && newStream) {
    localVideoRef.value.srcObject = newStream
  }
}, { immediate: true })

// Watch for remote stream changes
watch(() => props.remoteStreams, (newStreams) => {
  nextTick(() => {
    newStreams.forEach(streamEntry => {
      const videoElement = remoteVideoRefs.value[streamEntry.id]
      if (videoElement && videoElement.srcObject !== streamEntry.stream) {
        videoElement.srcObject = streamEntry.stream
        emit('streamAttached', streamEntry.id, videoElement)
      }
    })
  })
}, { deep: true })

// Expose refs for parent component
defineExpose({
  localVideoRef,
  remoteVideoRefs
})
</script>
