import { useEffect, useRef, VideoHTMLAttributes } from "react";

type PropsType = VideoHTMLAttributes<HTMLVideoElement> & {
  srcObject: MediaStream;
};

export default function Video({ srcObject, ...props }: PropsType) {
  const refVideo = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (refVideo.current) {
      refVideo.current.srcObject = srcObject;
    }
  }, [srcObject]);

  return (
    <video
      autoPlay={true}
      muted={false}
      playsInline={true}
      ref={refVideo}
      {...props}
    />
  );
}
