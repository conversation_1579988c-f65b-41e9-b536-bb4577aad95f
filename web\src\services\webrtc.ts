import type { ConnectionConfig } from '@/types/meeting'
import type { SignalMessage, WebRTCMessage } from '@/types/message'

export class WebRTCService {
  private signalServer: WebSocket | null = null
  private peerConnections = new Map<string, RTCPeerConnection>()
  private dataChannels = new Map<string, RTCDataChannel>()
  private config: ConnectionConfig
  private localDeviceId: string
  private reconnectAttempts = 0

  // Event handlers
  public onSignalServerConnected?: () => void
  public onSignalServerDisconnected?: () => void
  public onSignalServerMessage?: (message: SignalMessage) => void
  public onPeerConnected?: (participantId: string) => void
  public onPeerDisconnected?: (participantId: string) => void
  public onRemoteStream?: (participantId: string, stream: MediaStream) => void
  public onDataChannelMessage?: (participantId: string, message: any) => void
  public onError?: (error: string) => void

  constructor(config: ConnectionConfig, localDeviceId: string) {
    this.config = config
    this.localDeviceId = localDeviceId
  }

  // Signal server connection
  async connectToSignalServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.signalServer = new WebSocket(this.config.signalServerUrl)

        this.signalServer.onopen = () => {
          this.reconnectAttempts = 0
          this.onSignalServerConnected?.()
          resolve()
        }

        this.signalServer.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data) as SignalMessage
            this.handleSignalMessage(message)
          } catch (error) {
            this.onError?.(`Failed to parse signal message: ${error}`)
          }
        }

        this.signalServer.onclose = () => {
          this.onSignalServerDisconnected?.()
          this.attemptReconnect()
        }

        this.signalServer.onerror = (error) => {
          this.onError?.(`Signal server error: ${error}`)
          reject(error)
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.config.reconnectAttempts) {
      this.reconnectAttempts++
      setTimeout(() => {
        this.connectToSignalServer().catch(() => {
          // Reconnection failed, will try again
        })
      }, this.config.reconnectDelay * this.reconnectAttempts)
    }
  }

  sendSignalMessage(message: SignalMessage): void {
    if (this.signalServer?.readyState === WebSocket.OPEN) {
      this.signalServer.send(JSON.stringify(message))
    } else {
      this.onError?.('Signal server not connected')
    }
  }

  private handleSignalMessage(message: SignalMessage): void {
    switch (message.type) {
      case 'offer':
      case 'answer':
      case 'ice-candidate':
        this.handleWebRTCMessage(message as WebRTCMessage)
        break
      default:
        this.onSignalServerMessage?.(message)
    }
  }

  // Peer connection management
  async createPeerConnection(participantId: string, localStream?: MediaStream): Promise<RTCPeerConnection> {
    if (this.peerConnections.has(participantId)) {
      return this.peerConnections.get(participantId)!
    }

    const peerConnection = new RTCPeerConnection({
      iceServers: this.config.iceServers
    })

    this.peerConnections.set(participantId, peerConnection)

    // Handle ICE candidates
    peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendSignalMessage({
          type: 'ice-candidate',
          candidate: event.candidate,
          target: participantId,
          source: this.localDeviceId
        })
      }
    }

    // Handle remote stream
    peerConnection.ontrack = (event) => {
      const stream = event.streams[0]
      this.onRemoteStream?.(participantId, stream)
    }

    // Handle connection state changes
    peerConnection.onconnectionstatechange = () => {
      switch (peerConnection.connectionState) {
        case 'connected':
          this.onPeerConnected?.(participantId)
          break
        case 'disconnected':
        case 'failed':
        case 'closed':
          this.onPeerDisconnected?.(participantId)
          this.cleanupPeerConnection(participantId)
          break
      }
    }

    // Add local stream tracks
    if (localStream) {
      localStream.getTracks().forEach(track => {
        peerConnection.addTrack(track, localStream)
      })
    }

    // Create data channel for the initiator
    if (this.localDeviceId < participantId) {
      const dataChannel = peerConnection.createDataChannel('messages', { ordered: true })
      this.dataChannels.set(participantId, dataChannel)
      this.setupDataChannelHandlers(participantId, dataChannel)
    }

    // Handle incoming data channels
    peerConnection.ondatachannel = (event) => {
      const channel = event.channel
      this.dataChannels.set(participantId, channel)
      this.setupDataChannelHandlers(participantId, channel)
    }

    return peerConnection
  }

  private setupDataChannelHandlers(participantId: string, dataChannel: RTCDataChannel): void {
    dataChannel.onopen = () => {
      console.log(`Data channel opened with ${participantId}`)
    }

    dataChannel.onclose = () => {
      console.log(`Data channel closed with ${participantId}`)
    }

    dataChannel.onerror = (error) => {
      this.onError?.(`Data channel error with ${participantId}: ${error}`)
    }

    dataChannel.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        this.onDataChannelMessage?.(participantId, message)
      } catch (error) {
        // Handle binary data (file chunks)
        this.onDataChannelMessage?.(participantId, event.data)
      }
    }
  }

  private async handleWebRTCMessage(message: WebRTCMessage): Promise<void> {
    const peerConnection = this.peerConnections.get(message.source)
    if (!peerConnection) {
      this.onError?.(`No peer connection found for ${message.source}`)
      return
    }

    try {
      switch (message.type) {
        case 'offer':
          await peerConnection.setRemoteDescription(message.offer!)
          const answer = await peerConnection.createAnswer()
          await peerConnection.setLocalDescription(answer)
          
          this.sendSignalMessage({
            type: 'answer',
            answer: answer,
            target: message.source,
            source: this.localDeviceId
          })
          break

        case 'answer':
          await peerConnection.setRemoteDescription(message.answer!)
          break

        case 'ice-candidate':
          await peerConnection.addIceCandidate(message.candidate!)
          break
      }
    } catch (error) {
      this.onError?.(`WebRTC message handling error: ${error}`)
    }
  }

  async createOffer(participantId: string): Promise<void> {
    const peerConnection = this.peerConnections.get(participantId)
    if (!peerConnection) {
      throw new Error(`No peer connection found for ${participantId}`)
    }

    try {
      const offer = await peerConnection.createOffer()
      await peerConnection.setLocalDescription(offer)

      this.sendSignalMessage({
        type: 'offer',
        offer: offer,
        target: participantId,
        source: this.localDeviceId
      })
    } catch (error) {
      this.onError?.(`Failed to create offer for ${participantId}: ${error}`)
      throw error
    }
  }

  sendDataChannelMessage(participantId: string, message: any): void {
    const dataChannel = this.dataChannels.get(participantId)
    if (dataChannel?.readyState === 'open') {
      dataChannel.send(JSON.stringify(message))
    } else {
      this.onError?.(`Data channel not open for ${participantId}`)
    }
  }

  sendDataChannelBinary(participantId: string, data: ArrayBuffer): void {
    const dataChannel = this.dataChannels.get(participantId)
    if (dataChannel?.readyState === 'open') {
      dataChannel.send(data)
    } else {
      this.onError?.(`Data channel not open for ${participantId}`)
    }
  }

  cleanupPeerConnection(participantId: string): void {
    const peerConnection = this.peerConnections.get(participantId)
    if (peerConnection) {
      peerConnection.close()
      this.peerConnections.delete(participantId)
    }

    const dataChannel = this.dataChannels.get(participantId)
    if (dataChannel) {
      dataChannel.close()
      this.dataChannels.delete(participantId)
    }
  }

  disconnect(): void {
    // Close all peer connections
    this.peerConnections.forEach((pc, participantId) => {
      this.cleanupPeerConnection(participantId)
    })

    // Close signal server connection
    if (this.signalServer) {
      this.signalServer.close()
      this.signalServer = null
    }
  }

  // Getters
  get isConnected(): boolean {
    return this.signalServer?.readyState === WebSocket.OPEN
  }

  getPeerConnection(participantId: string): RTCPeerConnection | undefined {
    return this.peerConnections.get(participantId)
  }

  getDataChannel(participantId: string): RTCDataChannel | undefined {
    return this.dataChannels.get(participantId)
  }
}
