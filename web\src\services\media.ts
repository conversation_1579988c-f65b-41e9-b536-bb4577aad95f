import type { MediaState } from '@/types/meeting'

export class MediaService {
  private localStream: MediaStream | null = null
  private screenStream: MediaStream | null = null
  private mediaRecorder: MediaRecorder | null = null
  private recordedChunks: Blob[] = []

  // Event handlers
  public onMediaStateChanged?: (mediaState: MediaState) => void
  public onStreamChanged?: (stream: MediaStream | null) => void
  public onError?: (error: string) => void

  constructor() {
    // Initialize
  }

  // Local media stream management
  async startLocalStream(constraints: MediaStreamConstraints = { video: true, audio: true }): Promise<MediaStream> {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia(constraints)
      this.onStreamChanged?.(this.localStream)
      
      // Notify about initial media state
      this.notifyMediaStateChange()
      
      return this.localStream
    } catch (error) {
      const errorMessage = `Failed to start local media stream: ${error}`
      this.onError?.(errorMessage)
      throw new Error(errorMessage)
    }
  }

  stopLocalStream(): void {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop())
      this.localStream = null
      this.onStreamChanged?.(null)
      this.notifyMediaStateChange()
    }
  }

  // Camera control
  async toggleCamera(): Promise<boolean> {
    if (!this.localStream) {
      await this.startLocalStream()
      return true
    }

    const videoTrack = this.localStream.getVideoTracks()[0]
    if (videoTrack) {
      videoTrack.enabled = !videoTrack.enabled
      this.notifyMediaStateChange()
      return videoTrack.enabled
    }

    return false
  }

  async enableCamera(): Promise<void> {
    if (!this.localStream) {
      await this.startLocalStream({ video: true, audio: this.isMicrophoneEnabled() })
      return
    }

    const videoTrack = this.localStream.getVideoTracks()[0]
    if (videoTrack) {
      videoTrack.enabled = true
    } else {
      // Add video track if not present
      try {
        const videoStream = await navigator.mediaDevices.getUserMedia({ video: true })
        const videoTrack = videoStream.getVideoTracks()[0]
        this.localStream.addTrack(videoTrack)
      } catch (error) {
        this.onError?.(`Failed to enable camera: ${error}`)
        throw error
      }
    }
    
    this.notifyMediaStateChange()
  }

  disableCamera(): void {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.enabled = false
        this.notifyMediaStateChange()
      }
    }
  }

  // Microphone control
  async toggleMicrophone(): Promise<boolean> {
    if (!this.localStream) {
      await this.startLocalStream()
      return true
    }

    const audioTrack = this.localStream.getAudioTracks()[0]
    if (audioTrack) {
      audioTrack.enabled = !audioTrack.enabled
      this.notifyMediaStateChange()
      return audioTrack.enabled
    }

    return false
  }

  async enableMicrophone(): Promise<void> {
    if (!this.localStream) {
      await this.startLocalStream({ video: this.isCameraEnabled(), audio: true })
      return
    }

    const audioTrack = this.localStream.getAudioTracks()[0]
    if (audioTrack) {
      audioTrack.enabled = true
    } else {
      // Add audio track if not present
      try {
        const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true })
        const audioTrack = audioStream.getAudioTracks()[0]
        this.localStream.addTrack(audioTrack)
      } catch (error) {
        this.onError?.(`Failed to enable microphone: ${error}`)
        throw error
      }
    }
    
    this.notifyMediaStateChange()
  }

  disableMicrophone(): void {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = false
        this.notifyMediaStateChange()
      }
    }
  }

  // Screen sharing
  async startScreenShare(): Promise<MediaStream> {
    try {
      this.screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      })

      // Handle when screen sharing stops (user clicks stop sharing)
      this.screenStream.getVideoTracks()[0].onended = () => {
        this.stopScreenShare()
      }

      this.onStreamChanged?.(this.screenStream)
      this.notifyMediaStateChange()
      
      return this.screenStream
    } catch (error) {
      const errorMessage = `Failed to start screen sharing: ${error}`
      this.onError?.(errorMessage)
      throw new Error(errorMessage)
    }
  }

  stopScreenShare(): void {
    if (this.screenStream) {
      this.screenStream.getTracks().forEach(track => track.stop())
      this.screenStream = null
      
      // Switch back to camera stream
      this.onStreamChanged?.(this.localStream)
      this.notifyMediaStateChange()
    }
  }

  async toggleScreenShare(): Promise<boolean> {
    if (this.isScreenSharing()) {
      this.stopScreenShare()
      return false
    } else {
      await this.startScreenShare()
      return true
    }
  }

  // Recording
  async startRecording(): Promise<void> {
    const streamToRecord = this.getCurrentStream()
    if (!streamToRecord) {
      throw new Error('No stream available for recording')
    }

    try {
      const options = { mimeType: 'video/webm; codecs=vp9' }
      this.mediaRecorder = new MediaRecorder(streamToRecord, options)
      this.recordedChunks = []

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data)
        }
      }

      this.mediaRecorder.onstop = () => {
        this.downloadRecording()
      }

      this.mediaRecorder.start()
    } catch (error) {
      const errorMessage = `Failed to start recording: ${error}`
      this.onError?.(errorMessage)
      throw new Error(errorMessage)
    }
  }

  stopRecording(): void {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop()
    }
  }

  private downloadRecording(): void {
    if (this.recordedChunks.length === 0) return

    const blob = new Blob(this.recordedChunks, { type: 'video/webm' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `meeting-recording-${Date.now()}.webm`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    this.recordedChunks = []
  }

  // Track replacement for peer connections
  async replaceVideoTrack(peerConnection: RTCPeerConnection, newStream: MediaStream): Promise<void> {
    const senders = peerConnection.getSenders()
    const videoSender = senders.find(sender => sender.track && sender.track.kind === 'video')
    
    if (videoSender) {
      const newVideoTrack = newStream.getVideoTracks()[0]
      if (newVideoTrack) {
        try {
          await videoSender.replaceTrack(newVideoTrack)
        } catch (error) {
          this.onError?.(`Failed to replace video track: ${error}`)
          throw error
        }
      }
    }
  }

  // State getters
  isCameraEnabled(): boolean {
    if (!this.localStream) return false
    const videoTrack = this.localStream.getVideoTracks()[0]
    return videoTrack ? videoTrack.enabled : false
  }

  isMicrophoneEnabled(): boolean {
    if (!this.localStream) return false
    const audioTrack = this.localStream.getAudioTracks()[0]
    return audioTrack ? audioTrack.enabled : false
  }

  isScreenSharing(): boolean {
    return this.screenStream !== null
  }

  isRecording(): boolean {
    return this.mediaRecorder?.state === 'recording'
  }

  getCurrentStream(): MediaStream | null {
    return this.screenStream || this.localStream
  }

  getLocalStream(): MediaStream | null {
    return this.localStream
  }

  getScreenStream(): MediaStream | null {
    return this.screenStream
  }

  getCurrentMediaState(): MediaState {
    return {
      camera: this.isCameraEnabled(),
      microphone: this.isMicrophoneEnabled(),
      screenSharing: this.isScreenSharing()
    }
  }

  private notifyMediaStateChange(): void {
    this.onMediaStateChanged?.(this.getCurrentMediaState())
  }

  // Cleanup
  cleanup(): void {
    this.stopLocalStream()
    this.stopScreenShare()
    this.stopRecording()
  }

  // Device enumeration
  async getAvailableDevices(): Promise<MediaDeviceInfo[]> {
    try {
      return await navigator.mediaDevices.enumerateDevices()
    } catch (error) {
      this.onError?.(`Failed to enumerate devices: ${error}`)
      return []
    }
  }

  async switchCamera(deviceId: string): Promise<void> {
    try {
      const newStream = await navigator.mediaDevices.getUserMedia({
        video: { deviceId: { exact: deviceId } },
        audio: this.isMicrophoneEnabled()
      })

      // Stop old stream
      if (this.localStream) {
        this.localStream.getVideoTracks().forEach(track => track.stop())
      }

      this.localStream = newStream
      this.onStreamChanged?.(this.localStream)
      this.notifyMediaStateChange()
    } catch (error) {
      this.onError?.(`Failed to switch camera: ${error}`)
      throw error
    }
  }

  async switchMicrophone(deviceId: string): Promise<void> {
    try {
      const newStream = await navigator.mediaDevices.getUserMedia({
        video: this.isCameraEnabled(),
        audio: { deviceId: { exact: deviceId } }
      })

      // Stop old stream
      if (this.localStream) {
        this.localStream.getAudioTracks().forEach(track => track.stop())
      }

      this.localStream = newStream
      this.onStreamChanged?.(this.localStream)
      this.notifyMediaStateChange()
    } catch (error) {
      this.onError?.(`Failed to switch microphone: ${error}`)
      throw error
    }
  }
}
