// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"fmt"
	"meeting/api/router"
	"meeting/internal/webrtc"
	"meeting/pkg/config"
	"meeting/pkg/services"
	"net/http"
)

// Injectors from wire.go:

// InitializeApplication 初始化整个应用程序
func InitializeApplication(configPath string) (*Application, error) {
	tomlConfig, err := config.NewConfig(configPath)
	if err != nil {
		return nil, err
	}
	hub := webrtc.NewHub()
	server := NewHTTPServer(tomlConfig, hub)
	service := webrtc.NewService(hub)
	application := NewApplication(server, tomlConfig, service)
	return application, nil
}

// wire.go:

// Application 应用程序结构体
type Application struct {
	Server *http.Server
	Config *config.TomlConfig
	WebRTC *webrtc.Service
}

// NewApplication 创建应用程序实例
func NewApplication(server *http.Server, config2 *config.TomlConfig, webrtcService *webrtc.Service) *Application {
	return &Application{
		Server: server,
		Config: config2,
		WebRTC: webrtcService,
	}
}

// NewHTTPServer 创建HTTP服务器
func NewHTTPServer(cfg *config.TomlConfig, webrtcHub *webrtc.Hub) *http.Server {
	host := cfg.App.Host
	if host == "" {
		host = "0.0.0.0"
	}
	port := cfg.App.Port
	if port == 0 {
		port = 8989
	}

	mcpService := services.GetServiceManager().GetMCPService()
	return &http.Server{
		Addr:    fmt.Sprintf("%s:%d", host, port),
		Handler: router.NewRouterWithServices(mcpService, webrtcHub),
	}
}
