<template>
  <div class="flex h-screen bg-gray-900 text-white">
    <!-- Meeting Entry Modal -->
    <MeetingEntry
      v-if="!meetingStore.inMeeting"
      :connection-state="meetingStore.connectionState"
      @create-meeting="handleCreateMeeting"
      @join-meeting="handleJoinMeeting"
      ref="meetingEntryRef"
    />

    <!-- Main Meeting Interface -->
    <div v-if="meetingStore.inMeeting" class="flex flex-1 h-full">
      <!-- Video Grid Area -->
      <div class="flex-1 flex flex-col">
        <!-- Meeting Header -->
        <div class="bg-gray-800 text-white p-4 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h2 class="font-semibold text-lg">{{ $t('tools.webRtcMeeting.meeting.title') }}</h2>
            <span class="text-sm text-gray-300 font-mono">{{ meetingStore.currentMeeting?.id }}</span>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <span class="text-sm"
                >{{ meetingStore.participantCount }} {{ $t('tools.webRtcMeeting.participants.count') }}</span
              >
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <!-- Recording Status -->
            <div
              v-if="meetingStore.isRecording"
              class="flex items-center space-x-2 px-3 py-1 bg-red-600 rounded-full"
            >
              <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              <span class="text-sm font-medium">{{
                $t('tools.webRtcMeeting.recording.recording')
              }}</span>
            </div>

            <!-- Chat Toggle -->
            <button
              @click="meetingStore.showChatPanel = !meetingStore.showChatPanel"
              class="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm relative"
              :title="$t('tools.webRtcMeeting.chat.toggleChat')"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"
                />
                <path
                  d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"
                />
              </svg>
              <div
                v-if="meetingStore.unreadMessages > 0"
                class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs flex items-center justify-center"
              >
                {{ meetingStore.unreadMessages > 9 ? '9+' : meetingStore.unreadMessages }}
              </div>
            </button>

            <!-- Participants Panel Toggle -->
            <button
              @click="meetingStore.showParticipantsPanel = !meetingStore.showParticipantsPanel"
              class="px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm"
              :title="$t('tools.webRtcMeeting.participants.togglePanel')"
            >
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"
                />
              </svg>
            </button>

            <!-- Leave Meeting Button -->
            <button
              @click="handleLeaveMeeting"
              class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm font-medium"
            >
              {{ $t('tools.webRtcMeeting.meeting.leave') }}
            </button>
          </div>
        </div>

        <!-- Video Grid -->
        <VideoGrid
          :remote-streams="meetingStore.remoteStreams"
          :local-media-state="meetingStore.localMediaState"
          :local-participant-name="meetingStore.displayName"
          :participants="meetingStore.participants"
          :local-stream="mediaService?.getCurrentStream()"
          @stream-attached="handleStreamAttached"
        />

        <!-- Meeting Controls Bar -->
        <MeetingControls
          :media-state="meetingStore.localMediaState"
          :is-host="meetingStore.isHost"
          :is-recording="meetingStore.isRecording"
          :unread-messages="meetingStore.unreadMessages"
          @toggle-microphone="handleToggleMicrophone"
          @toggle-camera="handleToggleCamera"
          @toggle-screen-share="handleToggleScreenShare"
          @toggle-recording="handleToggleRecording"
          @toggle-chat="handleToggleChat"
          @toggle-participants="handleToggleParticipants"
          @copy-meeting-link="handleCopyMeetingLink"
          @mute-all="handleMuteAll"
          @leave-meeting="handleLeaveMeeting"
          @open-settings="handleOpenSettings"
        />
      </div>

      <!-- Chat Panel (Slide-out) -->
      <ChatPanel
        v-if="meetingStore.showChatPanel"
        :messages="meetingStore.chatMessages"
        :local-user-id="meetingStore.localDeviceId"
        @close="handleToggleChat"
        @send-message="handleSendMessage"
        @send-file="handleSendFile"
        ref="chatPanelRef"
      />

      <!-- Participants Panel (Slide-out) -->
      <ParticipantsPanel
        v-if="meetingStore.showParticipantsPanel"
        :participants="meetingStore.participants"
        :local-user-id="meetingStore.localDeviceId"
        :is-host="meetingStore.isHost"
        @close="handleToggleParticipants"
        @mute-participant="handleMuteParticipant"
        @kick-participant="handleKickParticipant"
        @mute-all="handleMuteAll"
        @copy-invite-link="handleCopyMeetingLink"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useMeetingStore } from '@/stores/meeting'
import { WebRTCService } from '@/services/webrtc'
import { MediaService } from '@/services/media'
import type { ConnectionConfig } from '@/types/meeting'
import type { ChatMessage, FileMetadata } from '@/types/message'

// Components
import MeetingEntry from '@/components/meeting/MeetingEntry.vue'
import VideoGrid from '@/components/meeting/VideoGrid.vue'
import MeetingControls from '@/components/meeting/MeetingControls.vue'
import ChatPanel from '@/components/meeting/ChatPanel.vue'
import ParticipantsPanel from '@/components/meeting/ParticipantsPanel.vue'

const { t } = useI18n()
const meetingStore = useMeetingStore()

// Services
let webrtcService: WebRTCService | null = null
let mediaService: MediaService | null = null

// Component refs
const meetingEntryRef = ref()
const chatPanelRef = ref()

// Configuration
const connectionConfig: ConnectionConfig = {
  signalServerUrl: 'ws://localhost:8989/ws',
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' }
  ],
  reconnectAttempts: 5,
  reconnectDelay: 3000
}

// Initialize services
const initializeServices = async () => {
  try {
    // Generate device ID
    meetingStore.localDeviceId = Math.random().toString(36).substring(2, 11)

    // Initialize WebRTC service
    webrtcService = new WebRTCService(connectionConfig, meetingStore.localDeviceId)
    setupWebRTCEventHandlers()

    // Initialize Media service
    mediaService = new MediaService()
    setupMediaEventHandlers()

    // Connect to signal server
    meetingStore.setConnectionState('connecting')
    await webrtcService.connectToSignalServer()

  } catch (error) {
    console.error('Failed to initialize services:', error)
    meetingStore.setConnectionState('disconnected')
    meetingEntryRef.value?.setError(t('tools.webRtcMeeting.errors.connectionFailed'))
  }
}

// Setup WebRTC event handlers
const setupWebRTCEventHandlers = () => {
  if (!webrtcService) return

  webrtcService.onSignalServerConnected = () => {
    meetingStore.setConnectionState('connected')
  }

  webrtcService.onSignalServerDisconnected = () => {
    meetingStore.setConnectionState('disconnected')
  }

  webrtcService.onSignalServerMessage = (message) => {
    handleSignalMessage(message)
  }

  webrtcService.onRemoteStream = (participantId, stream) => {
    const streamId = `${participantId}-${stream.id}`
    meetingStore.addRemoteStream({
      id: streamId,
      participantId,
      stream,
      type: 'camera'
    })
  }

  webrtcService.onDataChannelMessage = (participantId, message) => {
    handleDataChannelMessage(participantId, message)
  }

  webrtcService.onError = (error) => {
    console.error('WebRTC Error:', error)
  }
}

// Setup Media event handlers
const setupMediaEventHandlers = () => {
  if (!mediaService) return

  mediaService.onMediaStateChanged = (mediaState) => {
    meetingStore.localMediaState = mediaState
    broadcastMediaStateChange()
  }

  mediaService.onStreamChanged = (stream) => {
    // Update peer connections with new stream
    if (webrtcService && stream) {
      updatePeerConnectionStreams(stream)
    }
  }

  mediaService.onError = (error) => {
    console.error('Media Error:', error)
  }
}

// Signal message handlers
const handleSignalMessage = (message: any) => {
  switch (message.type) {
    case 'meeting-created':
      meetingStore.setMeeting({
        id: message.meetingId,
        name: message.meetingName || message.meetingId,
        hostId: meetingStore.localDeviceId,
        participants: [],
        createdAt: Date.now(),
        isActive: true
      })
      meetingStore.isHost = true
      meetingStore.addParticipant({
        id: meetingStore.localDeviceId,
        name: meetingStore.displayName,
        role: 'host',
        mediaState: meetingStore.localMediaState,
        connectionState: 'connected',
        joinedAt: Date.now()
      })
      initializeMeetingMedia()
      break

    case 'meeting-joined':
      meetingStore.setMeeting({
        id: message.meetingId,
        name: message.meetingName || message.meetingId,
        hostId: message.hostId,
        participants: [],
        createdAt: Date.now(),
        isActive: true
      })
      meetingStore.isHost = message.isHost || false

      // Add all participants
      if (message.participants) {
        message.participants.forEach((p: any) => {
          meetingStore.addParticipant({
            id: p.id,
            name: p.name,
            role: p.role || 'member',
            mediaState: p.mediaState || { microphone: true, camera: true, screenSharing: false },
            connectionState: 'connected',
            joinedAt: Date.now()
          })
        })
      }
      initializeMeetingMedia()
      break

    case 'participant-joined':
      meetingStore.addParticipant({
        id: message.participantId,
        name: message.name,
        role: message.role || 'member',
        mediaState: { microphone: true, camera: true, screenSharing: false },
        connectionState: 'connecting',
        joinedAt: Date.now()
      })

      meetingStore.addChatMessage({
        id: Date.now().toString(),
        type: 'system',
        content: `${message.name} joined the meeting`,
        senderId: 'system',
        senderName: 'System',
        timestamp: Date.now()
      })

      // Initialize connection with new participant
      if (webrtcService && mediaService) {
        initializeConnectionWithParticipant(message.participantId)
      }
      break

    case 'participant-left':
      const leftParticipant = meetingStore.participants.find(p => p.id === message.participantId)
      meetingStore.removeParticipant(message.participantId)

      if (leftParticipant) {
        meetingStore.addChatMessage({
          id: Date.now().toString(),
          type: 'system',
          content: `${leftParticipant.name} left the meeting`,
          senderId: 'system',
          senderName: 'System',
          timestamp: Date.now()
        })
      }

      // Clean up connection
      webrtcService?.cleanupPeerConnection(message.participantId)
      meetingStore.remoteStreams = meetingStore.remoteStreams.filter(
        s => !s.id.startsWith(message.participantId)
      )
      break

    case 'meeting-message':
      if (message.messageType === 'chat' && message.message) {
        meetingStore.addChatMessage(message.message)
      } else if (message.messageType === 'media-state') {
        meetingStore.updateParticipantMediaState(message.senderId, {
          [message.mediaType]: message.enabled
        })
      }
      break

    case 'meeting-error':
      console.error('Meeting error:', message.error)
      meetingEntryRef.value?.setError(message.error)
      break
  }
}

// Data channel message handlers
const handleDataChannelMessage = (participantId: string, message: any) => {
  if (typeof message === 'string') {
    try {
      const parsedMessage = JSON.parse(message)
      switch (parsedMessage.type) {
        case 'chat':
          meetingStore.addChatMessage(parsedMessage)
          break
        case 'file-metadata':
          handleFileMetadata(parsedMessage, participantId)
          break
      }
    } catch (error) {
      console.error('Failed to parse data channel message:', error)
    }
  } else {
    // Handle binary data (file chunks)
    handleFileChunk(message, participantId)
  }
}

// Initialize meeting media
const initializeMeetingMedia = async () => {
  if (!mediaService) return

  try {
    await mediaService.startLocalStream()
    meetingStore.localMediaState = mediaService.getCurrentMediaState()
  } catch (error) {
    console.error('Failed to initialize media:', error)
  }
}

// Initialize connection with participant
const initializeConnectionWithParticipant = async (participantId: string) => {
  if (!webrtcService || !mediaService) return

  try {
    const localStream = mediaService.getCurrentStream()
    const peerConnection = await webrtcService.createPeerConnection(participantId, localStream || undefined)

    // Create offer if we're the initiator
    if (meetingStore.localDeviceId < participantId) {
      await webrtcService.createOffer(participantId)
    }
  } catch (error) {
    console.error(`Failed to initialize connection with ${participantId}:`, error)
  }
}

// Update peer connection streams
const updatePeerConnectionStreams = async (newStream: MediaStream) => {
  if (!webrtcService) return

  for (const participant of meetingStore.participants) {
    if (participant.id !== meetingStore.localDeviceId) {
      const peerConnection = webrtcService.getPeerConnection(participant.id)
      if (peerConnection && mediaService) {
        try {
          await mediaService.replaceVideoTrack(peerConnection, newStream)
        } catch (error) {
          console.error(`Failed to update stream for ${participant.id}:`, error)
        }
      }
    }
  }
}

// Broadcast media state changes
const broadcastMediaStateChange = () => {
  if (!webrtcService || !meetingStore.inMeeting) return

  webrtcService.sendSignalMessage({
    type: 'meeting-message',
    meetingId: meetingStore.currentMeeting?.id,
    messageType: 'media-state',
    mediaState: meetingStore.localMediaState,
    senderId: meetingStore.localDeviceId
  })
}

// File transfer handlers
let currentFileMetadata: any = null
let receivedChunks: ArrayBuffer[] = []
let receivedSize = 0

const handleFileMetadata = (metadata: any, participantId: string) => {
  currentFileMetadata = metadata
  receivedChunks = []
  receivedSize = 0

  const participant = meetingStore.participants.find(p => p.id === participantId)
  const senderName = participant?.name || participantId

  meetingStore.addChatMessage({
    id: Date.now().toString(),
    type: 'file',
    content: `Receiving file: ${metadata.name}`,
    senderId: participantId,
    senderName,
    timestamp: Date.now(),
    fileName: metadata.name,
    fileSize: metadata.size
  })
}

const handleFileChunk = (chunk: ArrayBuffer, participantId: string) => {
  if (!currentFileMetadata) return

  receivedChunks.push(chunk)
  receivedSize += chunk.byteLength

  if (receivedSize >= currentFileMetadata.size) {
    const completeFile = new Blob(receivedChunks, { type: currentFileMetadata.mimeType })
    const url = URL.createObjectURL(completeFile)

    // Update the file message with download URL
    const fileMessageIndex = meetingStore.chatMessages.findIndex(
      m => m.type === 'file' &&
           m.fileName === currentFileMetadata.name &&
           m.senderId === participantId &&
           !m.fileUrl
    )

    if (fileMessageIndex !== -1) {
      meetingStore.chatMessages[fileMessageIndex].fileUrl = url
      meetingStore.chatMessages[fileMessageIndex].content = `File received: ${currentFileMetadata.name}`
    }

    // Reset for next file
    currentFileMetadata = null
    receivedChunks = []
    receivedSize = 0
  }
}

// Event handlers for components
const handleCreateMeeting = (displayName: string) => {
  meetingStore.displayName = displayName

  if (!webrtcService) return

  webrtcService.sendSignalMessage({
    type: 'create-meeting',
    deviceId: meetingStore.localDeviceId,
    displayName: displayName
  })
}

const handleJoinMeeting = (meetingId: string, displayName: string) => {
  meetingStore.displayName = displayName

  if (!webrtcService) return

  webrtcService.sendSignalMessage({
    type: 'join-meeting',
    meetingId: meetingId,
    deviceId: meetingStore.localDeviceId,
    displayName: displayName
  })
}

const handleLeaveMeeting = () => {
  if (!webrtcService) return

  webrtcService.sendSignalMessage({
    type: 'leave-meeting',
    meetingId: meetingStore.currentMeeting?.id,
    deviceId: meetingStore.localDeviceId
  })

  // Clean up
  meetingStore.resetMeeting()
  mediaService?.cleanup()
  webrtcService?.disconnect()
}

const handleToggleMicrophone = async () => {
  if (!mediaService) return

  try {
    await mediaService.toggleMicrophone()
  } catch (error) {
    console.error('Failed to toggle microphone:', error)
  }
}

const handleToggleCamera = async () => {
  if (!mediaService) return

  try {
    await mediaService.toggleCamera()
  } catch (error) {
    console.error('Failed to toggle camera:', error)
  }
}

const handleToggleScreenShare = async () => {
  if (!mediaService) return

  try {
    await mediaService.toggleScreenShare()
  } catch (error) {
    console.error('Failed to toggle screen share:', error)
  }
}

const handleToggleRecording = async () => {
  if (!mediaService) return

  try {
    if (mediaService.isRecording()) {
      mediaService.stopRecording()
    } else {
      await mediaService.startRecording()
    }
    meetingStore.isRecording = mediaService.isRecording()
  } catch (error) {
    console.error('Failed to toggle recording:', error)
  }
}

const handleToggleChat = () => {
  meetingStore.showChatPanel = !meetingStore.showChatPanel
  if (meetingStore.showChatPanel) {
    meetingStore.clearChatUnread()
  }
}

const handleToggleParticipants = () => {
  meetingStore.showParticipantsPanel = !meetingStore.showParticipantsPanel
}

const handleCopyMeetingLink = async () => {
  const meetingLink = `${window.location.origin}${window.location.pathname}?meeting=${meetingStore.currentMeeting?.id}`

  try {
    await navigator.clipboard.writeText(meetingLink)
    console.log('Meeting link copied to clipboard')
  } catch (error) {
    console.error('Failed to copy meeting link:', error)
  }
}

const handleMuteAll = () => {
  if (!webrtcService || !meetingStore.isHost) return

  webrtcService.sendSignalMessage({
    type: 'meeting-action',
    meetingId: meetingStore.currentMeeting?.id,
    action: 'mute-all'
  })
}

const handleMuteParticipant = (participantId: string) => {
  if (!webrtcService || !meetingStore.isHost) return

  webrtcService.sendSignalMessage({
    type: 'meeting-action',
    meetingId: meetingStore.currentMeeting?.id,
    action: 'mute',
    targetId: participantId
  })
}

const handleKickParticipant = (participantId: string) => {
  if (!webrtcService || !meetingStore.isHost) return

  webrtcService.sendSignalMessage({
    type: 'meeting-action',
    meetingId: meetingStore.currentMeeting?.id,
    action: 'kick',
    targetId: participantId
  })
}

const handleOpenSettings = () => {
  // TODO: Implement settings modal
  console.log('Open settings')
}

// Chat and file handlers
const handleSendMessage = (message: string) => {
  if (!webrtcService || !meetingStore.inMeeting) return

  const chatMessage: ChatMessage = {
    id: Date.now().toString(),
    type: 'text',
    content: message,
    senderId: meetingStore.localDeviceId,
    senderName: meetingStore.displayName || meetingStore.localDeviceId,
    timestamp: Date.now()
  }

  // Add to local chat
  meetingStore.addChatMessage(chatMessage)

  // Send to other participants
  webrtcService.sendSignalMessage({
    type: 'meeting-message',
    meetingId: meetingStore.currentMeeting?.id,
    messageType: 'chat',
    message: chatMessage
  })
}

const handleSendFile = async (file: File) => {
  if (!webrtcService || !meetingStore.inMeeting) return

  chatPanelRef.value?.setFileUploadStatus(true)

  try {
    const fileMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'file',
      content: `Sending file: ${file.name}`,
      senderId: meetingStore.localDeviceId,
      senderName: meetingStore.displayName || meetingStore.localDeviceId,
      timestamp: Date.now(),
      fileName: file.name,
      fileSize: file.size
    }

    meetingStore.addChatMessage(fileMessage)

    // Send file to all participants
    for (const participant of meetingStore.participants) {
      if (participant.id !== meetingStore.localDeviceId) {
        const dataChannel = webrtcService.getDataChannel(participant.id)
        if (dataChannel?.readyState === 'open') {
          // Send file metadata
          const metadata: FileMetadata = {
            name: file.name,
            size: file.size,
            mimeType: file.type,
            senderId: meetingStore.localDeviceId,
            senderName: meetingStore.displayName || meetingStore.localDeviceId
          }

          webrtcService.sendDataChannelMessage(participant.id, {
            type: 'file-metadata',
            ...metadata
          })

          // Send file in chunks
          const chunkSize = 16384 // 16KB chunks
          let offset = 0

          while (offset < file.size) {
            const slice = file.slice(offset, offset + chunkSize)
            const arrayBuffer = await slice.arrayBuffer()

            webrtcService.sendDataChannelBinary(participant.id, arrayBuffer)

            offset += chunkSize
            const progress = Math.min(100, Math.floor((offset / file.size) * 100))
            chatPanelRef.value?.setFileUploadProgress(progress)
          }
        }
      }
    }
  } catch (error) {
    console.error('Failed to send file:', error)
  } finally {
    chatPanelRef.value?.setFileUploadStatus(false)
  }
}

const handleStreamAttached = (streamId: string, element: HTMLVideoElement) => {
  // Stream attachment is handled by the VideoGrid component
  console.log(`Stream ${streamId} attached to video element`)
}

// Initialize on mount
onMounted(async () => {
  await initializeServices()
})

// Cleanup on unmount
onUnmounted(() => {
  mediaService?.cleanup()
  webrtcService?.disconnect()
})
</script>