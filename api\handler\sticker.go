package handler

import (
	"meeting/api/dto"
	"meeting/pkg/api"
	"meeting/pkg/auth"
	"meeting/pkg/services"
	"meeting/pkg/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

// StickerIndex 贴纸列表接口，实现与PHP版本相同的功能
func StickerIndex(ctx *gin.Context) {
	// 获取分页参数
	page, _, limit := api.PageParamsFromCtx(ctx, 21, 21)
	// 获取查询参数
	keyword := ctx.Query("keyword")
	categoryID := ctx.Query("category_id")

	// 获取服务
	stickerService := services.GetServiceManager().GetStickerService()

	// 获取贴纸列表
	stickerList, err := stickerService.GetStickerList(ctx, page, limit, keyword, categoryID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, api.Fail(api.WithMessage("获取贴纸列表失败")))
		return
	}

	// 转换为DTO
	stickerDTOs := make([]dto.StickerDTO, len(stickerList.Data))
	for i, sticker := range stickerList.Data {
		stickerDTO := dto.StickerDTO{
			UUID:  sticker.UUID,
			Name:  sticker.Name,
			Image: sticker.Image,
			Views: sticker.Views,
		}

		// 如果有关联的贴纸包，转换为DTO
		if sticker.Pack != nil {
			stickerDTO.Pack = &dto.StickerPackDTO{
				UUID:  sticker.Pack.UUID,
				Name:  sticker.Pack.Name,
				Image: sticker.Pack.Image,
			}
		}

		stickerDTOs[i] = stickerDTO
	}

	// 返回分页结果
	response := dto.StickerListResponse{
		Data:        stickerDTOs,
		CurrentPage: stickerList.CurrentPage,
		PerPage:     stickerList.PerPage,
	}

	ctx.JSON(http.StatusOK, api.Okay(api.WithData(response)))
}

// StickerShow 获取单个贴纸详情
func StickerShow(ctx *gin.Context) {
	// 从路由参数获取贴纸UUID
	uuid := ctx.Param("id")

	// 获取服务
	stickerService := services.GetServiceManager().GetStickerService()

	// 获取贴纸详情
	stickerDetail, err := stickerService.GetStickerDetail(ctx, uuid, auth.User(ctx))
	if err != nil {
		ctx.JSON(http.StatusNotFound, api.Fail(api.WithMessage("贴纸未找到")))
		return
	}

	// 转换为DTO
	stickerDTO := dto.StickerDTO{
		UUID:      stickerDetail.UUID,
		Name:      stickerDetail.Name,
		Image:     stickerDetail.Image,
		Views:     stickerDetail.Views,
		Favorited: stickerDetail.Favorited,
	}

	// 如果有关联的贴纸包，转换为DTO
	if stickerDetail.Pack != nil {
		stickerDTO.Pack = &dto.StickerPackDTO{
			UUID:  stickerDetail.Pack.UUID,
			Name:  stickerDetail.Pack.Name,
			Image: stickerDetail.Pack.Image,
		}
	}

	// 返回结果
	ctx.JSON(http.StatusOK, api.Okay(api.WithData(stickerDTO)))
}

// StickerFavorite 贴纸收藏/取消收藏
func StickerFavorite(ctx *gin.Context) {
	// 从路由参数获取贴纸UUID
	uuid := ctx.Param("id")
	// 获取当前用户
	currentUser := auth.User(ctx)

	// 获取服务
	stickerService := services.GetServiceManager().GetStickerService()

	// 根据请求方法决定是收藏还是取消收藏
	isDelete := ctx.Request.Method == "DELETE"

	err := stickerService.FavoriteSticker(ctx, uuid, currentUser, isDelete)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, api.Fail(api.WithMessage("操作失败")))
		return
	}

	ctx.JSON(http.StatusOK, api.Okay())
}

// StickerRandom 获取随机贴纸
func StickerRandom(ctx *gin.Context) {
	// 获取分类ID参数
	categoryID := ctx.Query("category_id")

	// 获取服务
	stickerService := services.GetServiceManager().GetStickerService()

	stickerDetail, err := stickerService.GetRandomSticker(ctx, categoryID)

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, api.Fail(api.WithMessage("获取随机贴纸失败")))
		return
	}

	if stickerDetail == nil {
		ctx.JSON(http.StatusNotFound, api.Fail(api.WithMessage("未找到贴纸")))
		return
	}

	if utils.WantJson(ctx) {
		stickerDTO := dto.StickerDTO{
			UUID:  stickerDetail.UUID,
			Name:  stickerDetail.Name,
			Image: stickerDetail.Image,
			Views: stickerDetail.Views,
		}
		ctx.JSON(http.StatusOK, api.Okay(api.WithData(stickerDTO)))
		return
	}

	ctx.Redirect(http.StatusFound, stickerDetail.Image)
}
