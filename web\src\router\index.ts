import { createRouter, createWebHistory } from 'vue-router'
import { getToken } from '@/utils/helper'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/meeting/:id',
      component: () => import('@/WebRtcMeeting.vue'),
      meta: {
        requireAuth: true
      }
    },
    {
      path: '/login',
      component: () => import('@/views/LoginView.vue')
    },
    {
      path: '/',
      component: () => import('@/views/HomeView.vue')
    },
    {
      path: '/chats',
      component: () => import('@/views/ChatView.vue'),
      meta: {
        requireAuth: true
      }
    },
    {
      path: '/inbox',
      component: () => import('@/views/InboxView.vue')
    },
    {
      path: '/customers',
      component: () => import('@/views/CustomersView.vue')
    },
    {
      path: '/cards',
      component: () => import('@/views/CardsView.vue')
    },
    {
      path: '/movies',
      component: () => import('@/views/MoviesView.vue')
    }
  ]
})

router.beforeEach((to, from, next) => {
  if (to.meta?.requireAuth && !getToken()) {
    window.location.href = `/login?redirect_uri=${encodeURIComponent(window.location.href)}`
    return
  }
  next()
})

export default router