<script lang="ts" setup></script>

<template>
    <div class="flex gap-4 h-full flex-1 w-full overflow-auto">
        <div class="w-64 h-full overflow-hidden border border-surface rounded-2xl flex flex-col">
            <div class="flex items-center justify-between gap-2 p-4 border-b border-surface">
                <div class="text-xl font-medium leading-7 text-color">Mails</div><button
                    class="p-button p-component p-button-icon-only w-8 h-8" type="button" data-pc-name="button"
                    data-p-disabled="false" data-pc-section="root" pc1539=""><span class="p-button-icon pi pi-plus"
                        data-pc-section="icon"></span><span class="p-button-label"
                        data-pc-section="label">&nbsp;</span><!----></button>
            </div>
            <div class="flex-1 flex flex-col overflow-auto justify-between gap-4 pt-4 pb-4 px-4">
                <div class="flex-1 overflow-auto flex flex-col gap-2">
                    <div class="flex flex-col gap-2">
                        <div class="text-sm font-medium leading-5 text-surface-400 dark:text-surface-500">Navigation
                        </div><button
                            class="text-color bg-emphasis px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-inbox"></i><span class="font-medium">Inbox</span></button><button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-star"></i><span class="font-medium">Starry</span></button><button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-file-o"></i><span class="font-medium">Drafts</span></button><button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-file-import"></i><span class="font-medium">Important</span></button><button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-send"></i><span class="font-medium">Sent</span></button><button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-inbox"></i><span class="font-medium">Archive</span></button><button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-info-circle"></i><span class="font-medium">Spam</span></button><button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-trash"></i><span class="font-medium">Trash</span></button>
                    </div>
                    <div class="flex flex-col gap-2">
                        <div class="text-sm font-medium leading-5 text-surface-400 dark:text-surface-500">Other</div>
                        <button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-tag"></i><span class="font-medium">Security</span></button><button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-tag"></i><span class="font-medium">Update</span></button><button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-tag"></i><span class="font-medium">Marketing</span></button><button
                            class="text-muted-color bg-transparent px-4 py-2 rounded-lg flex items-center gap-2 cursor-pointer hover:bg-emphasis transition-all"><i
                                class="pi pi-tag"></i><span class="font-medium">HR</span></button>
                    </div>
                </div>
                <div>
                    <div class="border border-surface rounded-border px-4 pb-4 pt-3 mb-4">
                        <div class="font-medium text-color mb-4">Free Version</div>
                        <div role="progressbar" class="p-progressbar p-component p-progressbar-determinate"
                            aria-valuemin="0" aria-valuenow="75" aria-valuemax="100" data-pc-name="progressbar"
                            data-pc-section="root" pc1540="">
                            <div class="p-progressbar-value bg-red-600" data-pc-section="value"
                                style="width: 75%; display: flex;">
                                <div class="p-progressbar-label" data-pc-section="label"><span
                                        class="w-full text-center text-sm font-normal text-surface-0 leading-5 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">4
                                        days left</span></div>
                            </div>
                        </div>
                    </div><button class="p-button p-component p-button-outlined w-full" type="button"
                        aria-label="Upgrade to PRO 🚀" data-pc-name="button" data-p-disabled="false"
                        data-pc-section="root" pc1541=""><!----><span class="p-button-label"
                            data-pc-section="label">Upgrade to PRO 🚀</span><!----></button>
                </div>
            </div>
        </div>
        <div class="flex-1 h-full overflow-hidden flex border border-surface rounded-2xl">
            <div class="p-datatable p-component p-datatable-hoverable p-datatable-scrollable w-full flex-1 overflow-x-auto"
                data-scrollselectors=".p-datatable-wrapper" data-pc-name="datatable" data-pc-section="root" pc1538=""
                pv_id_56=""><!----><!----><!----><!----><!----><!----><!---->
                <div class="p-datatable-header sticky top-0 z-10" data-pc-section="header">
                    <div class="flex xl:items-center justify-between gap-2 flex-col xl:flex-row">
                        <div class="flex items-center gap-2">
                            <div class="p-checkbox p-component mr-1" data-pc-name="checkbox" data-pc-section="root"
                                data-p-checked="false" data-p-disabled="false" pc1542=""><input type="checkbox"
                                    class="p-checkbox-input" data-pc-section="input">
                                <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                            </div><button
                                class="p-button p-component p-button-icon-only p-button-secondary p-button-outlined"
                                type="button" data-pc-name="button" data-p-disabled="false" data-p-severity="secondary"
                                data-pc-section="root" pc1543=""><span class="p-button-icon pi pi-envelope"
                                    data-pc-section="icon"></span><span class="p-button-label"
                                    data-pc-section="label">&nbsp;</span><!----></button><button
                                class="p-button p-component p-button-icon-only p-button-secondary p-button-outlined"
                                type="button" data-pc-name="button" data-p-disabled="false" data-p-severity="secondary"
                                data-pc-section="root" pc1544=""><span class="p-button-icon pi pi-exclamation-circle"
                                    data-pc-section="icon"></span><span class="p-button-label"
                                    data-pc-section="label">&nbsp;</span><!----></button><button
                                class="p-button p-component p-button-icon-only p-button-secondary p-button-outlined"
                                type="button" data-pc-name="button" data-p-disabled="false" data-p-severity="secondary"
                                data-pc-section="root" pc1545=""><span class="p-button-icon pi pi-tag"
                                    data-pc-section="icon"></span><span class="p-button-label"
                                    data-pc-section="label">&nbsp;</span><!----></button><button
                                class="p-button p-component p-button-secondary p-button-outlined" type="button"
                                aria-label="Archive" data-pc-name="button" data-p-disabled="false"
                                data-p-severity="secondary" data-pc-section="root" pc1546=""><span
                                    class="p-button-icon p-button-icon-left pi pi-inbox"
                                    data-pc-section="icon"></span><span class="p-button-label"
                                    data-pc-section="label">Archive</span><!----></button><button
                                class="p-button p-component p-button-secondary p-button-outlined" type="button"
                                aria-label="Trash" data-pc-name="button" data-p-disabled="false"
                                data-p-severity="secondary" data-pc-section="root" pc1547=""><span
                                    class="p-button-icon p-button-icon-left pi pi-trash"
                                    data-pc-section="icon"></span><span class="p-button-label"
                                    data-pc-section="label">Trash</span><!----></button>
                        </div>
                        <div class="flex items-center gap-2">
                            <div class="p-iconfield w-6/12 xl:max-w-36" iconposition="left" data-pc-name="iconfield"
                                data-pc-section="root" pc1550=""><span class="p-inputicon pi pi-search"
                                    data-pc-name="inputicon" data-pc-section="root" pc1548=""></span><input type="text"
                                    class="p-inputtext p-component w-full" placeholder="Search" data-pc-name="inputtext"
                                    data-pc-section="root" value="" pc1549=""></div><button
                                class="p-button p-component p-button-icon-only p-button-secondary p-button-outlined"
                                type="button" data-pc-name="button" data-p-disabled="false" data-p-severity="secondary"
                                data-pc-section="root" pc1551=""><span class="p-button-icon pi pi-filter"
                                    data-pc-section="icon"></span><span class="p-button-label"
                                    data-pc-section="label">&nbsp;</span><!----></button>
                            <div class="p-divider p-component p-divider-vertical p-divider-solid p-divider-center m-0"
                                role="separator" aria-orientation="vertical" data-pc-name="divider"
                                data-pc-section="root" pc1552="" style="align-items: center;"><!----></div><button
                                class="p-button p-component p-button-icon-only p-button-secondary p-button-outlined"
                                type="button" data-pc-name="button" data-p-disabled="false" data-p-severity="secondary"
                                data-pc-section="root" pc1553=""><span class="p-button-icon pi pi-refresh"
                                    data-pc-section="icon"></span><span class="p-button-label"
                                    data-pc-section="label">&nbsp;</span><!----></button><button
                                class="p-button p-component p-button-secondary p-button-outlined !whitespace-nowrap"
                                type="button" aria-label="1 of 15" data-pc-name="button" data-p-disabled="false"
                                data-p-severity="secondary" data-pc-section="root" pc1554=""><!----><span
                                    class="p-button-label" data-pc-section="label">1 of 15</span><!----></button><button
                                class="p-button p-component p-button-icon-only p-button-secondary p-button-outlined"
                                type="button" data-pc-name="button" data-p-disabled="false" data-p-severity="secondary"
                                data-pc-section="root" pc1555=""><span class="p-button-icon pi pi-chevron-left"
                                    data-pc-section="icon"></span><span class="p-button-label"
                                    data-pc-section="label">&nbsp;</span><!----></button><button
                                class="p-button p-component p-button-icon-only p-button-secondary p-button-outlined"
                                type="button" data-pc-name="button" data-p-disabled="false" data-p-severity="secondary"
                                data-pc-section="root" pc1556=""><span class="p-button-icon pi pi-chevron-right"
                                    data-pc-section="icon"></span><span class="p-button-label"
                                    data-pc-section="label">&nbsp;</span><!----></button>
                        </div>
                    </div>
                </div><!---->
                <div class="p-datatable-table-container" data-pc-section="tablecontainer" style="overflow: auto;">
                    <table role="table" class="p-datatable-table p-datatable-scrollable-table" data-pc-section="table">
                        <thead class="p-datatable-thead hidden" role="rowgroup" data-pc-section="thead"
                            style="position: sticky;">
                            <tr role="row" data-pc-section="headerrow">
                                <th class="p-datatable-header-cell" role="columnheader" data-pc-section="headercell"
                                    data-pc-name="headercell" data-p-resizable-column="false"
                                    data-p-filter-column="false" data-p-reorderable-column="false" first="0" pc1557=""
                                    style="width: 1rem;"><!---->
                                    <div class="p-datatable-column-header-content"
                                        data-pc-section="columnheadercontent"><!----><!----><!----><!---->
                                        <div class="p-checkbox p-component" data-pc-name="pcheadercheckbox"
                                            data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                            data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                                aria-label="All items unselected" data-pc-section="input">
                                            <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                        </div><!---->
                                    </div>
                                </th>
                                <th class="p-datatable-header-cell" role="columnheader" data-pc-section="headercell"
                                    data-pc-name="headercell" data-p-resizable-column="false"
                                    data-p-filter-column="false" data-p-reorderable-column="false" first="0" pc1558=""
                                    style="width: 1rem; padding: 0.5rem;"><!---->
                                    <div class="p-datatable-column-header-content"
                                        data-pc-section="columnheadercontent"><!----><!----><!----><!----><!----><!---->
                                    </div>
                                </th>
                                <th class="p-datatable-header-cell" role="columnheader" data-pc-section="headercell"
                                    data-pc-name="headercell" data-p-resizable-column="false"
                                    data-p-filter-column="false" data-p-reorderable-column="false" first="0" pc1559="">
                                    <!---->
                                    <div class="p-datatable-column-header-content"
                                        data-pc-section="columnheadercontent"><!----><!----><!----><!----><!----><!---->
                                    </div>
                                </th>
                                <th class="p-datatable-header-cell" role="columnheader" data-pc-section="headercell"
                                    data-pc-name="headercell" data-p-resizable-column="false"
                                    data-p-filter-column="false" data-p-reorderable-column="false" first="0" pc1560=""
                                    style="min-width: 14rem; max-width: 20rem;"><!---->
                                    <div class="p-datatable-column-header-content"
                                        data-pc-section="columnheadercontent"><!----><!----><!----><!----><!----><!---->
                                    </div>
                                </th>
                                <th class="p-datatable-header-cell" role="columnheader" data-pc-section="headercell"
                                    data-pc-name="headercell" data-p-resizable-column="false"
                                    data-p-filter-column="false" data-p-reorderable-column="false" first="0" pc1561=""
                                    style="width: 4rem;"><!---->
                                    <div class="p-datatable-column-header-content"
                                        data-pc-section="columnheadercontent"><!----><!----><!----><!----><!----><!---->
                                    </div>
                                </th>
                                <th class="p-datatable-header-cell" role="columnheader" data-pc-section="headercell"
                                    data-pc-name="headercell" data-p-resizable-column="false"
                                    data-p-filter-column="false" data-p-reorderable-column="false" first="0" pc1562=""
                                    style="width: 4rem;"><!---->
                                    <div class="p-datatable-column-header-content"
                                        data-pc-section="columnheadercontent"><!----><!----><!----><!----><!----><!---->
                                    </div>
                                </th>
                            </tr><!---->
                        </thead><!---->
                        <tbody class="p-datatable-tbody" role="rowgroup" data-pc-section="tbody"><!---->
                            <tr class="p-datatable-selectable-row p-row-even" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="0"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1563="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1564=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1567="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1695="">
                                            <div class="p-avatar p-component p-avatar-image rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1694=""><img
                                                    src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar12.jpg"
                                                    data-pc-section="image"></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Brook Simmons</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1568=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Important Account
                                            Update</span><span class="text-muted-color leading-5 text-sm">Dear customer,
                                            we've made updates to enhance your account security. Please log in to review
                                            and complete the necessary steps. Thank you for choosing ABC
                                            Corporation.</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1570=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1696=""><!----><span
                                            class="p-tag-label" data-pc-section="label">Security</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1571=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">3:24 PM</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-odd" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="1"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1572="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1573=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1576="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1698="">
                                            <div class="p-avatar p-component p-avatar-image rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1697=""><img
                                                    src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar2.png"
                                                    data-pc-section="image"></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Dianne Russell</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1577=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Weekly Project
                                            Update</span><span class="text-muted-color leading-5 text-sm">Hi team,
                                            attached is the weekly project update. Kindly review the progress and come
                                            prepared for our discussion in the upcoming meeting on [Date and
                                            Time].</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1579=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1699=""><!----><span
                                            class="p-tag-label" data-pc-section="label">Update</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1580=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">11:24 AM</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-even" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="2"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1581="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1582=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark-fill"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1585="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1701="">
                                            <div class="p-avatar p-component p-avatar-image rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1700=""><img
                                                    src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar13.jpg"
                                                    data-pc-section="image"></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Amy Elsner</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1586=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Urgent: Security Alert
                                            - Account Compromise</span><span
                                            class="text-muted-color leading-5 text-sm">Dear user, we detected
                                            unauthorized access to your account. Take immediate action to secure your
                                            account. Follow the provided link to reset your password. Thank you.</span>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1588=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1702=""><!----><span
                                            class="p-tag-label" data-pc-section="label">Security</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1589=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">9:24 AM</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-odd" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="3"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1590="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1591=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1594="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1704="">
                                            <div class="p-avatar p-component p-avatar-image rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1703=""><img
                                                    src="https://www.primefaces.org/cdn/primevue/images/landing/apps/main-avatar.png"
                                                    data-pc-section="image"></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Jacob Jones</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1595=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Exclusive Offer Inside
                                            - Limited Time Only</span><span
                                            class="text-muted-color leading-5 text-sm">Greetings, check out our
                                            exclusive offer! Don't miss this limited-time deal. Details enclosed in the
                                            attached flyer. Act fast; the offer expires on [Date].</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1597=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1705=""><!----><span
                                            class="p-tag-label" data-pc-section="label">Marketing</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1598=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">Jan 21</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-even" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="4"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1599="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1600=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1603="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1707="">
                                            <div class="p-avatar p-component bg-violet-100 text-violet-950 text-xs font-medium rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1706=""><span
                                                    class="p-avatar-label" data-pc-section="label">CW</span></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Cameron Watson</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1604=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Employee Appreciation
                                            Event - Save the Date</span><span
                                            class="text-muted-color leading-5 text-sm">Hello team, mark your calendars
                                            for our upcoming Employee Appreciation Event on [Date]. Stay tuned for more
                                            details and get ready for a day of celebration!</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1606=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1708=""><!----><span
                                            class="p-tag-label" data-pc-section="label">HR</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1607=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">Jan 15</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-odd" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="5"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1608="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1609=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark-fill"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1612="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1710="">
                                            <div class="p-avatar p-component bg-violet-100 text-violet-950 text-xs font-medium rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1709=""><span
                                                    class="p-avatar-label" data-pc-section="label">WW</span></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Wade Warren</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1613=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Your Recent Purchase -
                                            Order Confirmation</span><span class="text-muted-color leading-5 text-sm">Hi
                                            Wade Warren, secure your spot at the XYZ Conference 2024 with early bird
                                            registration. Enjoy discounted rates until [Date].</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1615=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1711=""><!----><span
                                            class="p-tag-label" data-pc-section="label">Invoice</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1616=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">Jan 12</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-even" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="6"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1617="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1618=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1621="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1713="">
                                            <div class="p-avatar p-component p-avatar-image rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1712=""><img
                                                    src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar7.png"
                                                    data-pc-section="image"></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Guy Hawkins</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1622=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Early Bird
                                            Registration Open - XYZ Conference 2024</span><span
                                            class="text-muted-color leading-5 text-sm"> Attention users, we have
                                            scheduled system maintenance on Jan 17. Expect minimal service disruption
                                            during this period. Thank you for your understanding.</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1624=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1714=""><!----><span
                                            class="p-tag-label" data-pc-section="label">Events</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1625=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">Jan 11</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-odd" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="7"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1626="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1627=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1630="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1716="">
                                            <div class="p-avatar p-component p-avatar-image rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1715=""><img
                                                    src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar8.png"
                                                    data-pc-section="image"></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Annette Black</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1631=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Upcoming System
                                            Maintenance Notice</span><span
                                            class="text-muted-color leading-5 text-sm">Dear valued customer, as a token
                                            of appreciation, we're offering exclusive discounts for VIP customers.
                                            Explore the savings in the attached catalog. Expires [Date].</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1632=""
                                    style="width: 4rem;"><!----></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1633=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">Jan 8</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-even" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="8"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1634="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1635=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark-fill"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1638="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1718="">
                                            <div class="p-avatar p-component p-avatar-image rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1717=""><img
                                                    src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar10.jpg"
                                                    data-pc-section="image"></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Darrell Steward</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1639=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Special Discounts for
                                            VIP Customers</span><span class="text-muted-color leading-5 text-sm">Hello
                                            Darrell Steward, stay updated with our latest news and highlights in the
                                            January edition of our newsletter. Enjoy the read!</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1641=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1719=""><!----><span
                                            class="p-tag-label" data-pc-section="label">Discount</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1642=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">Jan 4</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-odd" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="9"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1643="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1644=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark-fill"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1647="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1721="">
                                            <div class="p-avatar p-component bg-violet-100 text-violet-950 text-xs font-medium rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1720=""><span
                                                    class="p-avatar-label" data-pc-section="label">JB</span></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Jerome Bell</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1648=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Monthly Newsletter -
                                            January Edition</span><span class="text-muted-color leading-5 text-sm">Dear
                                            user, we've updated our Terms of Service. Please review the changes to
                                            ensure compliance. Your continued use of our services implies acceptance.
                                            Thank you.</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1650=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1722=""><!----><span
                                            class="p-tag-label" data-pc-section="label">Newsletter</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1651=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">Jan 2</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-even" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="10"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1652="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1653=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1656="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1724="">
                                            <div class="p-avatar p-component p-avatar-image rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1723=""><img
                                                    src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar11.jpg"
                                                    data-pc-section="image"></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Onyama Limba</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1657=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Exclusive Travel
                                            Packages for You</span><span
                                            class="text-muted-color leading-5 text-sm">Greetings traveler, explore our
                                            exclusive travel packages tailored just for you. Plan your next adventure
                                            with XYZ Travel. Offers valid until [Date].</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1658=""
                                    style="width: 4rem;"><!----></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1659=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">Jan 2</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-odd" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="11"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1660="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1661=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1664="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1726="">
                                            <div class="p-avatar p-component bg-violet-100 text-violet-950 text-xs font-medium rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1725=""><span
                                                    class="p-avatar-label" data-pc-section="label">RF</span></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Robert Fox</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1665=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Invitation to
                                            Amsterdam</span><span class="text-muted-color leading-5 text-sm">Hello
                                            Robert Fox, you're invited to our upcoming webinar on Amsterdam. Join us on
                                            [Date and Time] for an insightful session. Reserve your spot now!</span>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1667=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1727=""><!----><span
                                            class="p-tag-label" data-pc-section="label">Invitation</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1668=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">12.12.2023</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-even" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="12"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1669="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1670=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark-fill"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1673="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1729="">
                                            <div class="p-avatar p-component bg-violet-100 text-violet-950 text-xs font-medium rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1728=""><span
                                                    class="p-avatar-label" data-pc-section="label">CH</span></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Courtney Henry</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1674=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">New Arrivals - Check
                                            Out the Latest Books</span><span
                                            class="text-muted-color leading-5 text-sm">Book enthusiasts, discover our
                                            latest arrivals! Explore the attached catalog and dive into the world of new
                                            releases. Available for purchase starting [Date].</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1675=""
                                    style="width: 4rem;"><!----></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1676=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">12.09.2023</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-odd" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="13"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1677="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1678=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark-fill"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1681="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1731="">
                                            <div class="p-avatar p-component p-avatar-image rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1730=""><img
                                                    src="https://www.primefaces.org/cdn/primevue/images/landing/apps/avatar9.jpg"
                                                    data-pc-section="image"></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Arlene McCoy</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1682=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">New Product
                                            Demo</span><span class="text-muted-color leading-5 text-sm">Exclusive demo
                                            of our latest product on Thursday.</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1683=""
                                    style="width: 4rem;"><!----></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1684=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">12.04.2023</div>
                                </td>
                            </tr><!----><!----><!---->
                            <tr class="p-datatable-selectable-row p-row-even" tabindex="-1" role="row"
                                aria-selected="false" data-pc-section="bodyrow" data-p-index="14"
                                data-p-selectable-row="true" data-p-selected="false">
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="true" data-p-cell-editing="false"
                                    pc1685="" style="width: 1rem;">
                                    <div class="p-checkbox p-component" data-pc-name="pcrowcheckbox"
                                        data-pc-extend="checkbox" data-pc-section="root" data-p-checked="false"
                                        data-p-disabled="false"><input type="checkbox" class="p-checkbox-input"
                                            aria-label="Row Unselected" data-pc-section="input">
                                        <div class="p-checkbox-box" data-pc-section="box"><!----></div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1686=""
                                    style="width: 1rem; padding: 0.5rem;">
                                    <div><i class="pi pi-bookmark"></i></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1689="">
                                    <div class="flex items-center">
                                        <div class="p-overlaybadge w-fit" data-pc-name="overlaybadge"
                                            data-pc-section="root" pc1733="">
                                            <div class="p-avatar p-component bg-violet-100 text-violet-950 text-xs font-medium rounded-md overflow-hidden flex"
                                                data-pc-name="avatar" data-pc-section="root" pc1732=""><span
                                                    class="p-avatar-label" data-pc-section="label">JB</span></div><span
                                                class="p-badge p-component p-badge-dot p-badge-danger"
                                                data-pc-name="pcbadge" data-pc-extend="badge"
                                                data-pc-section="root"></span>
                                        </div>
                                        <div class="ml-4 leading-6 text-color font-medium">Jerome Bell</div>
                                    </div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1690=""
                                    style="min-width: 14rem; max-width: 20rem;">
                                    <div class="truncate"><span class="text-color leading-6 mr-2">Monthly Newsletter -
                                            January Edition</span><span class="text-muted-color leading-5 text-sm">Dear
                                            user, we've updated our Terms of Service. Please review the changes to
                                            ensure compliance. Your continued use of our services implies acceptance.
                                            Thank you.</span></div>
                                </td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1692=""
                                    style="width: 4rem;"><span class="p-tag p-component p-tag-secondary font-medium"
                                        data-pc-name="tag" data-pc-section="root" pc1734=""><!----><span
                                            class="p-tag-label" data-pc-section="label">Newsletter</span></span></td>
                                <td class="!border-transparent" role="cell" data-pc-section="bodycell"
                                    data-pc-name="bodycell" data-p-selection-column="false"
                                    data-p-editable-column="false" data-p-cell-editing="false" pc1693=""
                                    style="width: 4rem;">
                                    <div class="text-right text-sm leading-5 text-muted-color">10.01.2023</div>
                                </td>
                            </tr><!----><!---->
                        </tbody><!----><!---->
                    </table>
                </div><!----><!---->
                <div class="p-datatable-column-resize-indicator" data-pc-section="columnresizeindicator"
                    style="display: none;"></div><!----><!---->
            </div>
        </div>
    </div>
</template>