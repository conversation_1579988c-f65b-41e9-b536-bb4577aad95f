<script setup lang="ts">
import WebRtcMeeting from '../WebRtcMeeting.vue'
import ToggleSwitch from 'primevue/toggleswitch'
import InputIcon from 'primevue/inputicon'
import IconField from 'primevue/iconfield'
import Textarea from 'primevue/textarea'
import <PERSON><PERSON> from 'primevue/button'
import InputText from 'primevue/inputtext'
import Select<PERSON><PERSON>on from 'primevue/selectbutton'
</script>

<template>
  <div class="w-full h-full">
    <WebRtcMeeting />
  </div>
</template>
