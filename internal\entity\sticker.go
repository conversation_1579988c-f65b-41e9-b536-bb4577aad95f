package entity

import (
	"gorm.io/gorm"
	"time"
)

// Sticker 贴纸实体
type Sticker struct {
	ID        uint            `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID    uint            `gorm:"type:bigint(36) unsigned;not null;default:0" json:"user_id"`
	UUID      string          `gorm:"type:char(36);uniqueIndex" json:"uuid"`
	PackID    uint            `gorm:"type:bigint(20) unsigned;not null;default:0" json:"pack_id"`
	Name      string          `gorm:"type:varchar(255);not null;default:''" json:"name"`
	Image     string          `gorm:"type:varchar(255);not null" json:"image"`
	Views     uint            `gorm:"type:bigint(20) unsigned;not null;default:0" json:"views"`
	CreatedAt *time.Time      `gorm:"type:timestamp" json:"created_at,omitempty"`
	UpdatedAt *time.Time      `gorm:"type:timestamp" json:"updated_at,omitempty"`
	DeletedAt *gorm.DeletedAt `gorm:"type:timestamp" json:"deleted_at,omitempty"`

	// 关联贴纸包
	Pack *StickerPack `gorm:"foreignKey:PackID" json:"pack,omitempty"`

	// 关联分类（多对多关系）
	Categories []Category `gorm:"many2many:model_has_categories;" json:"categories,omitempty"`
}
