import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createI18n } from 'vue-i18n'
import ChatPanel from '../ChatPanel.vue'
import type { ChatMessage } from '@/types/message'

const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      tools: {
        webRtcMeeting: {
          chat: {
            title: 'Chat',
            placeholder: 'Type a message...',
            send: 'Send',
            sendFile: 'Send File',
            download: 'Download',
            sendingFile: 'Sending file...'
          },
          you: 'You'
        }
      }
    }
  }
})

describe('ChatPanel', () => {
  const mockMessages: ChatMessage[] = [
    {
      id: '1',
      type: 'text',
      content: 'Hello everyone!',
      senderId: 'user1',
      senderName: '<PERSON> Do<PERSON>',
      timestamp: Date.now() - 60000
    },
    {
      id: '2',
      type: 'text',
      content: 'Hi there!',
      senderId: 'user2',
      senderName: '<PERSON>',
      timestamp: Date.now() - 30000
    },
    {
      id: '3',
      type: 'file',
      content: 'Shared a file',
      senderId: 'user1',
      senderName: '<PERSON>e',
      timestamp: Date.now(),
      fileName: 'document.pdf',
      fileSize: 1024000,
      fileUrl: 'blob:http://localhost/test'
    }
  ]

  const defaultProps = {
    messages: mockMessages,
    localUserId: 'user2'
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render chat panel correctly', () => {
    const wrapper = mount(ChatPanel, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.find('h3').text()).toBe('Chat')
    expect(wrapper.findAll('.space-y-3 > div')).toHaveLength(3)
  })

  it('should display messages correctly', () => {
    const wrapper = mount(ChatPanel, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    const messageElements = wrapper.findAll('.space-y-3 > div')
    
    // First message (from other user)
    expect(messageElements[0].classes()).toContain('justify-start')
    expect(messageElements[0].text()).toContain('John Doe')
    expect(messageElements[0].text()).toContain('Hello everyone!')

    // Second message (from local user)
    expect(messageElements[1].classes()).toContain('justify-end')
    expect(messageElements[1].text()).toContain('Hi there!')

    // Third message (file from other user)
    expect(messageElements[2].text()).toContain('document.pdf')
    expect(messageElements[2].text()).toContain('1000 KB')
  })

  it('should emit sendMessage when form is submitted', async () => {
    const wrapper = mount(ChatPanel, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    const input = wrapper.find('input[type="text"]')
    const sendButton = wrapper.find('button:last-child')

    await input.setValue('Test message')
    await sendButton.trigger('click')

    expect(wrapper.emitted('sendMessage')).toBeTruthy()
    expect(wrapper.emitted('sendMessage')?.[0]).toEqual(['Test message'])
    expect((input.element as HTMLInputElement).value).toBe('')
  })

  it('should emit sendMessage on Enter key press', async () => {
    const wrapper = mount(ChatPanel, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    const input = wrapper.find('input[type="text"]')
    await input.setValue('Test message')
    await input.trigger('keyup.enter')

    expect(wrapper.emitted('sendMessage')).toBeTruthy()
    expect(wrapper.emitted('sendMessage')?.[0]).toEqual(['Test message'])
  })

  it('should not send empty messages', async () => {
    const wrapper = mount(ChatPanel, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    const input = wrapper.find('input[type="text"]')
    const sendButton = wrapper.find('button:last-child')

    await input.setValue('   ')
    await sendButton.trigger('click')

    expect(wrapper.emitted('sendMessage')).toBeFalsy()
  })

  it('should disable send button for empty input', async () => {
    const wrapper = mount(ChatPanel, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    const sendButton = wrapper.find('button:last-child')
    expect(sendButton.attributes('disabled')).toBeDefined()

    const input = wrapper.find('input[type="text"]')
    await input.setValue('Test')
    
    expect(sendButton.attributes('disabled')).toBeUndefined()
  })

  it('should emit close when close button is clicked', async () => {
    const wrapper = mount(ChatPanel, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    const closeButton = wrapper.find('button:first-child')
    await closeButton.trigger('click')

    expect(wrapper.emitted('close')).toBeTruthy()
  })

  it('should trigger file upload when file button is clicked', async () => {
    const wrapper = mount(ChatPanel, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    const fileButton = wrapper.find('button:first-of-type')
    const fileInput = wrapper.find('input[type="file"]')
    
    const clickSpy = vi.spyOn(fileInput.element, 'click')
    await fileButton.trigger('click')

    expect(clickSpy).toHaveBeenCalled()
  })

  it('should emit sendFile when file is selected', async () => {
    const wrapper = mount(ChatPanel, {
      props: defaultProps,
      global: {
        plugins: [i18n]
      }
    })

    const fileInput = wrapper.find('input[type="file"]')
    const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' })

    Object.defineProperty(fileInput.element, 'files', {
      value: [mockFile],
      writable: false
    })

    await fileInput.trigger('change')

    expect(wrapper.emitted('sendFile')).toBeTruthy()
    expect(wrapper.emitted('sendFile')?.[0]).toEqual([mockFile])
  })

  it('should format file sizes correctly', () => {
    const wrapper = mount(ChatPanel, {
      props: {
        messages: [
          {
            id: '1',
            type: 'file',
            content: 'File',
            senderId: 'user1',
            senderName: 'John',
            timestamp: Date.now(),
            fileName: 'test.pdf',
            fileSize: 1024
          }
        ],
        localUserId: 'user2'
      },
      global: {
        plugins: [i18n]
      }
    })

    expect(wrapper.text()).toContain('1 KB')
  })

  it('should format timestamps correctly', () => {
    const now = new Date()
    const wrapper = mount(ChatPanel, {
      props: {
        messages: [
          {
            id: '1',
            type: 'text',
            content: 'Test',
            senderId: 'user1',
            senderName: 'John',
            timestamp: now.getTime()
          }
        ],
        localUserId: 'user2'
      },
      global: {
        plugins: [i18n]
      }
    })

    const expectedTime = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    expect(wrapper.text()).toContain(expectedTime)
  })

  it('should handle system messages differently', () => {
    const wrapper = mount(ChatPanel, {
      props: {
        messages: [
          {
            id: '1',
            type: 'system',
            content: 'User joined the meeting',
            senderId: 'system',
            senderName: 'System',
            timestamp: Date.now()
          }
        ],
        localUserId: 'user2'
      },
      global: {
        plugins: [i18n]
      }
    })

    const systemMessage = wrapper.find('.bg-yellow-100')
    expect(systemMessage.exists()).toBe(true)
    expect(systemMessage.text()).toContain('User joined the meeting')
  })
})
