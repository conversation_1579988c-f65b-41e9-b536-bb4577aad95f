import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { MediaService } from '../media'

// Mock MediaStream and related APIs
class MockMediaStreamTrack {
  kind: string
  enabled: boolean = true
  
  constructor(kind: string) {
    this.kind = kind
  }

  stop() {
    // Mock implementation
  }
}

class MockMediaStream {
  tracks: MockMediaStreamTrack[] = []

  constructor(tracks: MockMediaStreamTrack[] = []) {
    this.tracks = tracks
  }

  getTracks(): MockMediaStreamTrack[] {
    return this.tracks
  }

  getVideoTracks(): MockMediaStreamTrack[] {
    return this.tracks.filter(track => track.kind === 'video')
  }

  getAudioTracks(): MockMediaStreamTrack[] {
    return this.tracks.filter(track => track.kind === 'audio')
  }

  addTrack(track: MockMediaStreamTrack) {
    this.tracks.push(track)
  }
}

// Mock MediaRecorder
class MockMediaRecorder {
  state: 'inactive' | 'recording' | 'paused' = 'inactive'
  ondataavailable: ((event: any) => void) | null = null
  onstop: (() => void) | null = null

  constructor(public stream: MediaStream, public options?: any) {}

  start() {
    this.state = 'recording'
  }

  stop() {
    this.state = 'inactive'
    this.onstop?.()
  }
}

// Setup global mocks
const mockGetUserMedia = vi.fn()
const mockGetDisplayMedia = vi.fn()

global.navigator = {
  ...global.navigator,
  mediaDevices: {
    getUserMedia: mockGetUserMedia,
    getDisplayMedia: mockGetDisplayMedia,
    enumerateDevices: vi.fn().mockResolvedValue([])
  }
} as any

global.MediaStream = MockMediaStream as any
global.MediaRecorder = MockMediaRecorder as any

describe('Media Service', () => {
  let service: MediaService

  beforeEach(() => {
    service = new MediaService()
    vi.clearAllMocks()
  })

  afterEach(() => {
    service.cleanup()
  })

  it('should initialize correctly', () => {
    expect(service).toBeDefined()
    expect(service.isCameraEnabled()).toBe(false)
    expect(service.isMicrophoneEnabled()).toBe(false)
    expect(service.isScreenSharing()).toBe(false)
    expect(service.isRecording()).toBe(false)
  })

  it('should start local stream', async () => {
    const mockStream = new MockMediaStream([
      new MockMediaStreamTrack('video'),
      new MockMediaStreamTrack('audio')
    ])
    mockGetUserMedia.mockResolvedValue(mockStream)

    const onStreamChanged = vi.fn()
    service.onStreamChanged = onStreamChanged

    const stream = await service.startLocalStream()

    expect(mockGetUserMedia).toHaveBeenCalledWith({ video: true, audio: true })
    expect(stream).toBe(mockStream)
    expect(onStreamChanged).toHaveBeenCalledWith(mockStream)
    expect(service.isCameraEnabled()).toBe(true)
    expect(service.isMicrophoneEnabled()).toBe(true)
  })

  it('should handle getUserMedia errors', async () => {
    const error = new Error('Permission denied')
    mockGetUserMedia.mockRejectedValue(error)

    const onError = vi.fn()
    service.onError = onError

    await expect(service.startLocalStream()).rejects.toThrow('Failed to start local media stream')
    expect(onError).toHaveBeenCalledWith('Failed to start local media stream: Error: Permission denied')
  })

  it('should stop local stream', async () => {
    const mockVideoTrack = new MockMediaStreamTrack('video')
    const mockAudioTrack = new MockMediaStreamTrack('audio')
    const mockStream = new MockMediaStream([mockVideoTrack, mockAudioTrack])
    mockGetUserMedia.mockResolvedValue(mockStream)

    const stopSpy1 = vi.spyOn(mockVideoTrack, 'stop')
    const stopSpy2 = vi.spyOn(mockAudioTrack, 'stop')
    const onStreamChanged = vi.fn()
    service.onStreamChanged = onStreamChanged

    await service.startLocalStream()
    service.stopLocalStream()

    expect(stopSpy1).toHaveBeenCalled()
    expect(stopSpy2).toHaveBeenCalled()
    expect(onStreamChanged).toHaveBeenCalledWith(null)
    expect(service.getCurrentStream()).toBeNull()
  })

  it('should toggle camera', async () => {
    const mockVideoTrack = new MockMediaStreamTrack('video')
    const mockStream = new MockMediaStream([mockVideoTrack])
    mockGetUserMedia.mockResolvedValue(mockStream)

    await service.startLocalStream()
    expect(service.isCameraEnabled()).toBe(true)

    const result = await service.toggleCamera()
    expect(result).toBe(false)
    expect(mockVideoTrack.enabled).toBe(false)
    expect(service.isCameraEnabled()).toBe(false)
  })

  it('should toggle microphone', async () => {
    const mockAudioTrack = new MockMediaStreamTrack('audio')
    const mockStream = new MockMediaStream([mockAudioTrack])
    mockGetUserMedia.mockResolvedValue(mockStream)

    await service.startLocalStream()
    expect(service.isMicrophoneEnabled()).toBe(true)

    const result = await service.toggleMicrophone()
    expect(result).toBe(false)
    expect(mockAudioTrack.enabled).toBe(false)
    expect(service.isMicrophoneEnabled()).toBe(false)
  })

  it('should start screen sharing', async () => {
    const mockScreenTrack = new MockMediaStreamTrack('video')
    const mockScreenStream = new MockMediaStream([mockScreenTrack])
    mockGetDisplayMedia.mockResolvedValue(mockScreenStream)

    const onStreamChanged = vi.fn()
    service.onStreamChanged = onStreamChanged

    const stream = await service.startScreenShare()

    expect(mockGetDisplayMedia).toHaveBeenCalledWith({ video: true, audio: true })
    expect(stream).toBe(mockScreenStream)
    expect(service.isScreenSharing()).toBe(true)
    expect(onStreamChanged).toHaveBeenCalledWith(mockScreenStream)
  })

  it('should stop screen sharing', async () => {
    const mockScreenTrack = new MockMediaStreamTrack('video')
    const mockScreenStream = new MockMediaStream([mockScreenTrack])
    mockGetDisplayMedia.mockResolvedValue(mockScreenStream)

    const mockLocalTrack = new MockMediaStreamTrack('video')
    const mockLocalStream = new MockMediaStream([mockLocalTrack])
    mockGetUserMedia.mockResolvedValue(mockLocalStream)

    const stopSpy = vi.spyOn(mockScreenTrack, 'stop')
    const onStreamChanged = vi.fn()
    service.onStreamChanged = onStreamChanged

    // Start local stream first
    await service.startLocalStream()
    // Then start screen sharing
    await service.startScreenShare()
    // Stop screen sharing
    service.stopScreenShare()

    expect(stopSpy).toHaveBeenCalled()
    expect(service.isScreenSharing()).toBe(false)
    expect(onStreamChanged).toHaveBeenCalledWith(mockLocalStream)
  })

  it('should toggle screen sharing', async () => {
    const mockScreenStream = new MockMediaStream([new MockMediaStreamTrack('video')])
    mockGetDisplayMedia.mockResolvedValue(mockScreenStream)

    expect(service.isScreenSharing()).toBe(false)

    const result1 = await service.toggleScreenShare()
    expect(result1).toBe(true)
    expect(service.isScreenSharing()).toBe(true)

    const result2 = await service.toggleScreenShare()
    expect(result2).toBe(false)
    expect(service.isScreenSharing()).toBe(false)
  })

  it('should start recording', async () => {
    const mockStream = new MockMediaStream([new MockMediaStreamTrack('video')])
    mockGetUserMedia.mockResolvedValue(mockStream)

    await service.startLocalStream()
    await service.startRecording()

    expect(service.isRecording()).toBe(true)
  })

  it('should stop recording', async () => {
    const mockStream = new MockMediaStream([new MockMediaStreamTrack('video')])
    mockGetUserMedia.mockResolvedValue(mockStream)

    await service.startLocalStream()
    await service.startRecording()
    
    expect(service.isRecording()).toBe(true)
    
    service.stopRecording()
    
    expect(service.isRecording()).toBe(false)
  })

  it('should get current media state', async () => {
    const mockStream = new MockMediaStream([
      new MockMediaStreamTrack('video'),
      new MockMediaStreamTrack('audio')
    ])
    mockGetUserMedia.mockResolvedValue(mockStream)

    await service.startLocalStream()

    const mediaState = service.getCurrentMediaState()
    expect(mediaState).toEqual({
      camera: true,
      microphone: true,
      screenSharing: false
    })
  })

  it('should notify media state changes', async () => {
    const mockStream = new MockMediaStream([new MockMediaStreamTrack('video')])
    mockGetUserMedia.mockResolvedValue(mockStream)

    const onMediaStateChanged = vi.fn()
    service.onMediaStateChanged = onMediaStateChanged

    await service.startLocalStream()
    expect(onMediaStateChanged).toHaveBeenCalled()

    await service.toggleCamera()
    expect(onMediaStateChanged).toHaveBeenCalledTimes(2)
  })

  it('should cleanup properly', async () => {
    const mockVideoTrack = new MockMediaStreamTrack('video')
    const mockStream = new MockMediaStream([mockVideoTrack])
    mockGetUserMedia.mockResolvedValue(mockStream)

    const stopSpy = vi.spyOn(mockVideoTrack, 'stop')

    await service.startLocalStream()
    service.cleanup()

    expect(stopSpy).toHaveBeenCalled()
    expect(service.getCurrentStream()).toBeNull()
  })
})
