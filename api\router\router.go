package router

import (
	"meeting/api/handler"
	"meeting/api/middleware"
	"meeting/internal/service"
	"meeting/internal/webrtc"
	"net/http"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
)

// NewRouterWithServices 使用依赖注入创建路由器
func NewRouterWithServices(mcpService service.MCPService, webrtcHub *webrtc.Hub) *gin.Engine {
	r := gin.Default()
	r.MaxMultipartMemory = 8 << 20 // 8MiB
	r.UseH2C = true                // gin.UseH2C 开启http2
	pprof.Register(r)
	// r.LoadHTMLGlob("./storage/views/*")
	r.Use(middleware.CORS())
	r.StaticFS("/swagger", http.Dir("public/swagger"))
	r.StaticFile("/swagger.json", "./public/swagger.json")

	// WebRTC signaling endpoint
	if webrtcHub != nil {
		r.GET("/ws", webrtc.ServeWebSocket(webrtcHub))
	}

	// 集成MCP服务
	if mcpService != nil {
		mcpService.RegisterRoutes(r)
	}

	r.GET("/call", handler.MCP.CallMCP)

	withSession := r.Group("")
	withSession.Use(middleware.Session())
	{
		withSession.GET("/login", handler.Auth.Login)
		withSession.GET("/logout", handler.Auth.Logout)
		withSession.GET("/login/callback", handler.Auth.LoginCallback)
		withSession.GET("/login/token", handler.Auth.Token)
	}

	v1public := r.Group("api/v1")
	v1public.Use(middleware.APIKeyMiddleware())
	{
		v1public.GET("/stickers/random", handler.StickerRandom)
	}

	v1private := r.Group("/api/v1")
	v1private.Use(middleware.JWT())
	{
		v1private.GET("/stickers", handler.StickerIndex)
		v1private.GET("/stickers/:id", handler.StickerShow)
		v1privateWithAuth := v1private.Group("")
		v1privateWithAuth.Use(middleware.Authentication())
		{
			v1privateWithAuth.POST("/stickers/:id/favorite", handler.StickerFavorite)
			v1privateWithAuth.DELETE("/stickers/:id/favorite", handler.StickerFavorite)

			// API密钥相关路由
			v1privateWithAuth.POST("/api-keys", handler.APIKey.CreateAPIKey)
			v1privateWithAuth.GET("/api-keys", handler.APIKey.ListAPIKeys)
			v1privateWithAuth.DELETE("/api-keys/:id", handler.APIKey.DeleteAPIKey)
		}
	}

	//au.GET("/meetings", handler.Meeting.Meetings)
	//au.GET("/meetings/:id", handler.Meeting.Meeting)
	//au.POST("/user/upload", handler.User.Upload)
	//au.POST("/user/signature", handler.User.Signature)
	//r.POST("/ai", api.Meeting.AI)
	//r.POST("/lucky-bag", api.Meeting.LuckyBag)
	//r.POST("/join-lucky-bag", api.Meeting.JoinLuckyBag)
	//r.GET("/test", api.Test.Http)
	return r
}
