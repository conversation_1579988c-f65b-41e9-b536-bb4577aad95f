package cache

import "context"

type KeyValue = map[string]any

type Adapter interface {
	Get(ctx context.Context, key string) (any, error)
	Set(ctx context.Context, key string, value any) error
	Delete(ctx context.Context, key string) error
	Increment(ctx context.Context, key string) error
	Decrement(ctx context.Context, key string) error
	Flush(ctx context.Context) error
	GetMulti(ctx context.Context, keys []string) (KeyValue, error)
	SetMulti(ctx context.Context, values KeyValue) error
	DeleteMulti(ctx context.Context, keys []string) error
}
