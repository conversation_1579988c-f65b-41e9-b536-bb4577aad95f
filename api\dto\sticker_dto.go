package dto

// StickerPackDTO 贴纸包DTO
type StickerPackDTO struct {
	UUID  string `json:"uuid"`
	Name  string `json:"name"`
	Image string `json:"image"`
}

// StickerDTO 贴纸DTO
type StickerDTO struct {
	UUID      string          `json:"uuid"`
	Name      string          `json:"name"`
	Image     string          `json:"image"`
	Views     uint            `json:"views"`
	Pack      *StickerPackDTO `json:"pack,omitempty"`
	Favorited bool            `json:"favorited,omitempty"`
}

// StickerListResponse 贴纸列表响应
type StickerListResponse struct {
	Data        []StickerDTO `json:"data"`
	CurrentPage int          `json:"current_page"`
	PerPage     int          `json:"per_page"`
}
