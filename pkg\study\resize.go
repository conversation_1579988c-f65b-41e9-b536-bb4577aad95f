package main

import (
	"github.com/nfnt/resize"
	"image"
	"image/gif"
	_ "image/gif"
	"image/jpeg"
	"image/png"
	_ "image/png"
	"log"
	"os"
)

func main() {
	// open "test.jpg"
	file, err := os.Open("./q.png")
	defer file.Close()

	if err != nil {
		log.Fatal(err)
	}

	// decode jpeg into image.Image
	img, str, err := image.Decode(file)
	if err != nil {
		log.Fatal(err)
	}
	if fileinfo, err := file.Stat(); nil == err {
		if fileinfo.Size() > 1024 {
			m := resize.Resize(1156, 0, img, resize.Lanczos3)
			out, err := os.Create("test_resized2.jpg")
			if err != nil {
				log.Fatal(err)
			}
			defer out.Close()

			switch str {
			case "png":
				// write new image to file
				png.Encode(out, m)
			case "jpeg":
				jpeg.Encode(out, m, nil)
			case "gif":
				gif.Encode(out, m, nil)
			}
		}
	}

}
