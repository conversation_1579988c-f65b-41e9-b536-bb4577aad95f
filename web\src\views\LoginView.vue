<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { login } from '@/utils/helper'
import { useUserStore } from '@/stores/user'
import Button from 'primevue/button'
import { useRouter } from 'vue-router'
import ProgressSpinner from 'primevue/progressspinner'
import { getMeetingList } from '@/api'
import type { Meeting } from '@/types/meeting'

const userStore = useUserStore()
const router = useRouter()
const loading = ref(true)
const meetings = ref<Meeting[]>([])
getMeetingList().then((result: any) => {
  meetings.value = result
})
onMounted(() => {
  setTimeout(() => {
    loading.value = false
  }, 200)
})
const doLogin = () => {
  const url = new URL(window.location.href)
  const redirectURI = url.searchParams.get('redirect_uri') as string
  login(redirectURI)
}

function enter(id: number) {
  router.push(`/meeting/${id}`)
}
</script>

<template>
  <div class="w-full h-full flex justify-center items-center flex-col gap-6">
    <p class="text-2xl">多人实时音视频在线会议</p>
    <p class="text-sm text-gray-500">多人会议需使用多账号！</p>
    <p class="text-sm text-gray-500">点击下方会议加入</p>
    <ProgressSpinner
      style="width: 50px; height: 50px"
      strokeWidth="4"
      fill="transparent"
      animationDuration=".5s"
      aria-label="Custom ProgressSpinner"
      v-if="loading"
    />
    <template v-else>
      <Button v-if="!userStore.info.id" label="登录" severity="secondary" @click="doLogin" />
      <div class="flex justify-center items-center gap-2" v-else>
        <div
          class="flex flex-col justify-between bg-gray-100 w-48 h-32 rounded-md p-4"
          v-for="meeting in meetings"
        >
          {{ meeting.Name }}
          <Button @click="() => enter(meeting.ID)">加入</Button>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped></style>
