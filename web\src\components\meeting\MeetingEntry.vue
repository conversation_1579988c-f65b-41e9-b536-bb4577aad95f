<template>
  <div class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
    <div
      class="bg-white dark:bg-gray-800 rounded-xl p-8 w-96 max-w-md mx-4 text-gray-900 dark:text-white"
    >
      <div class="text-center mb-6">
        <div
          class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path
              d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
        </div>
        <h1 class="text-2xl font-bold mb-2">{{ $t('tools.webRtcMeeting.title') }}</h1>
        <p class="text-gray-600 dark:text-gray-400">{{ $t('tools.webRtcMeeting.subtitle') }}</p>
      </div>

      <!-- Connection Status -->
      <div class="mb-6 p-3 rounded-lg bg-gray-50 dark:bg-gray-700">
        <div class="flex items-center space-x-2">
          <div
            :class="[
              'w-3 h-3 rounded-full',
              connectionState === 'connected' ? 'bg-green-500' : 'bg-red-500',
            ]"
          ></div>
          <span class="text-sm">
            {{ getConnectionStatusText() }}
          </span>
        </div>
      </div>

      <!-- User Name Input -->
      <div class="mb-6">
        <label class="block mb-2 font-medium">{{ $t('tools.webRtcMeeting.displayName') }}</label>
        <input
          v-model="displayName"
          :placeholder="$t('tools.webRtcMeeting.displayNamePlaceholder')"
          class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600"
          maxlength="30"
          @keyup.enter="displayName.trim() && handleCreateMeeting()"
        />
      </div>

      <!-- Meeting Actions -->
      <div v-if="displayName.trim()" class="space-y-4">
        <button
          @click="handleCreateMeeting"
          :disabled="connectionState !== 'connected' || isLoading"
          class="w-full px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed font-medium flex items-center justify-center space-x-2"
        >
          <div v-if="isLoading && loadingAction === 'create'" class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5z" />
          </svg>
          <span>{{ $t('tools.webRtcMeeting.meeting.createMeeting') }}</span>
        </button>

        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white dark:bg-gray-800 text-gray-500">{{
              $t('tools.webRtcMeeting.or')
            }}</span>
          </div>
        </div>

        <div class="space-y-3">
          <input
            v-model="meetingIdInput"
            :placeholder="$t('tools.webRtcMeeting.meeting.meetingIdPlaceholder')"
            class="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600"
            :disabled="connectionState !== 'connected' || isLoading"
            @keyup.enter="handleJoinMeeting"
          />
          <button
            @click="handleJoinMeeting"
            :disabled="!meetingIdInput.trim() || connectionState !== 'connected' || isLoading"
            class="w-full px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed font-medium flex items-center justify-center space-x-2"
          >
            <div v-if="isLoading && loadingAction === 'join'" class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path
                d="M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"
              />
              <path
                d="M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"
              />
            </svg>
            <span>{{ $t('tools.webRtcMeeting.meeting.joinMeeting') }}</span>
          </button>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="errorMessage" class="mt-4 p-3 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded-lg">
        <div class="flex items-center space-x-2">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
          <span class="text-sm">{{ errorMessage }}</span>
        </div>
      </div>

      <!-- Quick Join from URL -->
      <div v-if="meetingIdFromUrl" class="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
        <div class="text-center">
          <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">
            {{ $t('tools.webRtcMeeting.meeting.invitedToMeeting') }}
          </p>
          <p class="font-mono text-sm text-blue-800 dark:text-blue-200 mb-3">
            {{ meetingIdFromUrl }}
          </p>
          <button
            @click="handleQuickJoin"
            :disabled="!displayName.trim() || connectionState !== 'connected' || isLoading"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
          >
            {{ $t('tools.webRtcMeeting.meeting.quickJoin') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

interface Props {
  connectionState: 'disconnected' | 'connecting' | 'connected'
}

interface Emits {
  (e: 'createMeeting', displayName: string): void
  (e: 'joinMeeting', meetingId: string, displayName: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()
const route = useRoute()

// Reactive state
const displayName = ref('')
const meetingIdInput = ref('')
const isLoading = ref(false)
const loadingAction = ref<'create' | 'join' | null>(null)
const errorMessage = ref('')

// Get meeting ID from URL if present
const meetingIdFromUrl = computed(() => {
  return route.query.meeting as string || route.params.id as string || ''
})

// Methods
const getConnectionStatusText = () => {
  switch (props.connectionState) {
    case 'connected':
      return t('tools.webRtcMeeting.status.connected')
    case 'connecting':
      return t('tools.webRtcMeeting.status.connecting')
    case 'disconnected':
    default:
      return t('tools.webRtcMeeting.status.connecting')
  }
}

const handleCreateMeeting = async () => {
  if (!displayName.value.trim() || props.connectionState !== 'connected') return
  
  isLoading.value = true
  loadingAction.value = 'create'
  errorMessage.value = ''
  
  try {
    emit('createMeeting', displayName.value.trim())
  } catch (error) {
    errorMessage.value = t('tools.webRtcMeeting.errors.createMeetingFailed')
  } finally {
    isLoading.value = false
    loadingAction.value = null
  }
}

const handleJoinMeeting = async () => {
  if (!meetingIdInput.value.trim() || !displayName.value.trim() || props.connectionState !== 'connected') return
  
  isLoading.value = true
  loadingAction.value = 'join'
  errorMessage.value = ''
  
  try {
    emit('joinMeeting', meetingIdInput.value.trim(), displayName.value.trim())
  } catch (error) {
    errorMessage.value = t('tools.webRtcMeeting.errors.joinMeetingFailed')
  } finally {
    isLoading.value = false
    loadingAction.value = null
  }
}

const handleQuickJoin = async () => {
  if (!meetingIdFromUrl.value || !displayName.value.trim() || props.connectionState !== 'connected') return
  
  isLoading.value = true
  loadingAction.value = 'join'
  errorMessage.value = ''
  
  try {
    emit('joinMeeting', meetingIdFromUrl.value, displayName.value.trim())
  } catch (error) {
    errorMessage.value = t('tools.webRtcMeeting.errors.joinMeetingFailed')
  } finally {
    isLoading.value = false
    loadingAction.value = null
  }
}

// Initialize
onMounted(() => {
  // Pre-fill meeting ID if present in URL
  if (meetingIdFromUrl.value) {
    meetingIdInput.value = meetingIdFromUrl.value
  }
  
  // Try to get display name from localStorage
  const savedDisplayName = localStorage.getItem('webrtc-meeting-display-name')
  if (savedDisplayName) {
    displayName.value = savedDisplayName
  }
})

// Save display name to localStorage when it changes
const saveDisplayName = () => {
  if (displayName.value.trim()) {
    localStorage.setItem('webrtc-meeting-display-name', displayName.value.trim())
  }
}

// Expose method to clear error
defineExpose({
  clearError: () => {
    errorMessage.value = ''
  },
  setError: (message: string) => {
    errorMessage.value = message
    isLoading.value = false
    loadingAction.value = null
  }
})
</script>
