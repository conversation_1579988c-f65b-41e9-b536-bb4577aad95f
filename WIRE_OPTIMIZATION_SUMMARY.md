# Wire 依赖注入优化总结

## 项目概述
本项目是一个基于 Go 的 Web 应用程序，使用 Gin 框架构建 RESTful API。通过引入 Google Wire 依赖注入框架，我们重构了项目的架构，提高了代码的可维护性和可测试性。

## 优化前的问题
1. **硬编码依赖**: 在 `cmd/serve.go` 中直接硬编码初始化各种组件
2. **全局变量过多**: 大量使用全局变量，不利于测试和维护
3. **紧耦合**: 各层之间耦合度高，难以进行单元测试
4. **缺乏清晰的依赖关系**: 依赖关系不明确，难以理解和维护

## 优化后的架构

### 1. 分层架构
```
├── cmd/                    # 命令行入口
├── internal/
│   ├── entity/            # 实体层
│   ├── repository/        # 仓库层 (数据访问)
│   └── service/           # 服务层 (业务逻辑)
├── api/
│   ├── handler/           # 处理器层 (HTTP处理)
│   ├── middleware/        # 中间件
│   └── router/            # 路由配置
├── pkg/
│   ├── config/            # 配置管理
│   ├── core/              # 核心组件 (数据库、日志等)
│   └── services/          # 服务管理器
└── wire.go                # Wire 配置
```

### 2. Wire 依赖注入配置

#### 配置层 (`pkg/config/config.go`)
```go
// NewConfig 创建配置实例
func NewConfig(filepath string) (*TomlConfig, error) {
    var c TomlConfig
    if _, err := toml.DecodeFile(filepath, &c); err != nil {
        return nil, err
    }
    return &c, nil
}

// ProviderSet Wire 提供者集合
var ProviderSet = wire.NewSet(NewConfig)
```

#### 核心组件层 (`pkg/core/`)
```go
// NewDB 创建数据库连接
func NewDB(cfg *config.TomlConfig) (*gorm.DB, error) {
    // 数据库初始化逻辑
}

// NewLogger 创建日志实例
func NewLogger(cfg *config.TomlConfig) (*zap.SugaredLogger, error) {
    // 日志初始化逻辑
}

// NewRedis 创建Redis连接
func NewRedis() *redis.Client {
    // Redis初始化逻辑
}

// ProviderSet Wire 提供者集合
var ProviderSet = wire.NewSet(NewDB, NewRedis)
var LoggerProviderSet = wire.NewSet(NewLogger)
```

#### 仓库层 (`internal/repository/`)
```go
// UserRepository 用户仓库接口
type UserRepository interface {
    Create(ctx context.Context, user *entity.User) error
    GetByID(ctx context.Context, id uint) (*entity.User, error)
    // ... 其他方法
}

// NewUserRepository 创建用户仓库
func NewUserRepository(db *gorm.DB) UserRepository {
    return &userRepository{db: db}
}

// ProviderSet 仓库层 Wire 提供者集合
var ProviderSet = wire.NewSet(
    NewUserRepository,
    NewStickerRepository,
)
```

#### 服务层 (`internal/service/`)
```go
// UserService 用户服务接口
type UserService interface {
    CreateUser(ctx context.Context, user *entity.User) error
    GetUser(ctx context.Context, id uint) (*entity.User, error)
    // ... 其他方法
}

// NewUserService 创建用户服务
func NewUserService(userRepo repository.UserRepository, logger *zap.SugaredLogger) UserService {
    return &userService{
        userRepo: userRepo,
        logger:   logger,
    }
}

// ProviderSet 服务层 Wire 提供者集合
var ProviderSet = wire.NewSet(
    NewUserService,
    NewStickerService,
    NewAuthService,
    NewWebSocketService,
)
```

### 3. 主要的 Wire 配置 (`wire.go`)
```go
//go:build wireinject
// +build wireinject

package main

import (
    "fmt"
    "meeting/api/router"
    "meeting/internal/repository"
    "meeting/internal/service"
    "meeting/pkg/config"
    "meeting/pkg/core"
    "net/http"

    "github.com/google/wire"
)

// Application 应用程序结构体
type Application struct {
    Server *http.Server
    Config *config.TomlConfig
}

// InitializeApplication 初始化整个应用程序
func InitializeApplication(configPath string) (*Application, error) {
    wire.Build(
        // 基础设施层
        config.NewConfig,
        
        // HTTP层
        NewHTTPServer,
        
        // 应用程序
        NewApplication,
    )
    return &Application{}, nil
}
```

### 4. 服务管理器 (`pkg/services/manager.go`)
为了处理一些遗留代码和全局访问需求，我们创建了一个服务管理器：

```go
// Manager 服务管理器
type Manager struct {
    AuthService    *service.AuthService
    StickerService service.StickerService
    UserService    service.UserService
    initialized    bool
    mu             sync.RWMutex
}

// GetServiceManager 获取服务管理器单例
func GetServiceManager() *Manager {
    once.Do(func() {
        serviceManager = &Manager{}
    })
    return serviceManager
}
```

## 优化效果

### 1. 依赖关系清晰
- 通过 Wire 的 ProviderSet，依赖关系一目了然
- 编译时检查依赖关系，避免运行时错误

### 2. 易于测试
- 每个组件都通过接口定义，便于 Mock 测试
- 依赖注入使得单元测试更加容易

### 3. 配置集中管理
- 所有配置通过 `config.TomlConfig` 统一管理
- 支持环境变量和配置文件

### 4. 代码结构清晰
- 分层架构明确，职责分离
- 每层都有明确的接口定义

### 5. 性能优化
- 单例模式避免重复创建对象
- 延迟初始化减少启动时间

## 使用方法

### 1. 生成 Wire 代码
```bash
go run github.com/google/wire/cmd/wire
```

### 2. 编译应用程序
```bash
go build -o meeting.exe .
```

### 3. 运行应用程序
```bash
# 启动服务
./meeting.exe serve

# 数据库迁移
./meeting.exe migrate

# 查看帮助
./meeting.exe --help
```

## 最佳实践

### 1. 接口优先
- 每个服务都定义接口，实现依赖倒置
- 便于测试和扩展

### 2. 单一职责
- 每个组件只负责一个职责
- 避免上帝对象

### 3. 依赖注入
- 通过构造函数注入依赖
- 避免全局变量和单例滥用

### 4. 错误处理
- 统一的错误处理机制
- 适当的日志记录

## 未来改进方向

1. **完全移除全局变量**: 逐步替换剩余的全局变量
2. **增加更多测试**: 为每个组件添加单元测试
3. **配置验证**: 添加配置参数验证
4. **性能监控**: 集成性能监控和指标收集
5. **文档完善**: 添加更详细的 API 文档

## 总结

通过引入 Wire 依赖注入框架，我们成功地重构了项目架构，提高了代码的可维护性、可测试性和可扩展性。新的架构更加清晰，依赖关系明确，为后续的开发和维护奠定了良好的基础。