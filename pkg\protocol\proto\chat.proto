syntax = 'proto3';

option go_package = './pb;pb';

service Chat {
  rpc Connect(ConnectRequest) returns (ConnectReply) {}
  rpc SendMessage(SendMessageRequest) returns (SendMessageReply) {}
}

message ConnectRequest {
  int32 role = 1;
  string name = 2;
  int32 age = 3;
}

message ConnectReply {
  string message = 1;
}

message SendMessageRequest {
  string content = 1;
}

message SendMessageReply {
  string content = 1;
}