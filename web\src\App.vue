<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ref } from 'vue'
import { getToken, setToken } from '@/utils/helper'
import { apiBaseURI } from '@/config'
import { loginToken } from '@/api'
import { useUserStore } from '@/stores/user'
import Divider from 'primevue/divider'
import Avatar from 'primevue/avatar'
import Toast from 'primevue/toast'
import { useToast } from 'primevue/usetoast'
import axios from 'axios'

const toast = useToast()
const userStore = useUserStore()
const menus = ref([
  // {
  //   name: 'Overview',
  //   path: '/',
  //   icon: 'pi pi-home',
  //   active: true
  // },
  // {
  //   name: 'Chat',
  //   path: '/chats',
  //   icon: 'pi pi-comment',
  //   active: false
  // },
  // {
  //   name: 'Inbox',
  //   path: '/inbox',
  //   icon: 'pi pi-inbox',
  //   active: false
  // },
  // {
  //   name: 'Cards',
  //   path: '/cards',
  //   icon: 'pi pi-th-large',
  //   active: false
  // },
  // {
  //   name: 'Customers',
  //   path: '/customers',
  //   icon: 'pi pi-user',
  //   active: false
  // },
  // {
  //   name: 'Movies',
  //   path: '/movies',
  //   icon: 'pi pi-video',
  //   active: false
  // },
])

const escapeLogin = ['/login/token']
axios.defaults.withCredentials = true
axios.defaults.baseURL = apiBaseURI

axios.interceptors.request.use(
  (config) => {
    config.headers['Content-Type'] = 'application/json'
    config.headers['Accept'] = 'application/json'
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)
// 添加响应拦截器
axios.interceptors.response.use(
  (response) => {
    const data = response.data
    if (data.code !== 0) {
      toast.add({
        severity: 'error',
        summary: '出错了',
        detail: data.message,
        group: 'br',
        life: 3000
      })
      return Promise.reject(new Error(data.message))
    }

    return response.data.data
  },
  (error) => {
    if (!escapeLogin.includes(error.config.url) && error.response?.status === 401) {
      window.location.href = `${apiBaseURI}/login?redirect_uri=${window.location.href}`
      return
    }
    toast.add({
      severity: 'error',
      summary: '出错了',
      detail: error.response?.data?.message || error.message,
      group: 'br',
      life: 3000
    })
    // if (error.response?.status !== 401) {
    // }
    return Promise.reject(error)
  }
)

loginToken()
  .then((result) => {
    const { user, token } = result as any
    setToken(token)
    userStore.updateInfo(user)
  })
  .catch((e) => {
    setToken('')
    userStore.updateInfo({})
  })
</script>
<template>
  <div
    class="bg-surface-0 border md:h-screen w-screen border-black/10 dark:border-white/20 dark:bg-surface-950 p-6 flex items-start gap-6 overflow-hidden flex-col md:flex-row h-screen sm:h-full"
  >
    <div
      class="hidden sm:flex w-auto rounded-2xl p-5 bg-surface-50 dark:bg-surface-900 h-full flex-col justify-between"
    >
      <div class="w-12 flex flex-col items-center">
        <div class="flex items-center gap-3">
          <div class="w-11 h-11 border border-primary rounded-xl flex items-center justify-center">
            <img src="https://www.chengyao.xyz/images/logo.png" alt="" />
          </div>
          <div class="hidden text-surface-950 dark:text-surface-0 font-medium text-3xl">Prime</div>
        </div>
        <div class="mt-10 flex flex-col gap-2">
          <router-link
            :to="menu.path"
            v-for="menu in menus"
            :key="menu.name"
            class="px-4 py-1 flex items-center gap-1 cursor-pointer text-base rounded-lg transition-all select-none w-12 justify-center py-4 hover:bg-emphasis text-muted-color bg-transparent"
            data-pd-tooltip="true"
          >
            <i :class="menu.icon"></i><span class="hidden">・</span
            ><span class="hidden">{{ menu.name }}</span>
          </router-link>
        </div>
      </div>
      <div class="w-12 flex flex-col items-center">
        <Divider />
        <div class="justify-center flex items-center">
          <Avatar class="shrink-0" size="large" shape="circle" :image="userStore.info.avatar" />
          <div>
            <div class="hidden">Robin Jonas</div>
            <div class="hidden"><EMAIL></div>
          </div>
        </div>
      </div>
    </div>
    <RouterView />
    <Toast />
  </div>
</template>

<style>
html,
body {
  margin: 0;
}
</style>