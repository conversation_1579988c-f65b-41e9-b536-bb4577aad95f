<template>
  <div class="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 flex flex-col">
    <!-- Chat <PERSON>er -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
      <h3 class="font-semibold text-gray-900 dark:text-white">
        {{ $t('tools.webRtcMeeting.chat.title') }}
      </h3>
      <button
        @click="$emit('close')"
        class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
      >
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path
            fill-rule="evenodd"
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
    </div>

    <!-- Chat Messages -->
    <div ref="messagesContainer" class="flex-1 p-4 overflow-y-auto bg-gray-50 dark:bg-gray-900">
      <div class="space-y-3">
        <div
          v-for="message in messages"
          :key="message.id"
          :class="[
            'flex',
            message.senderId === localUserId ? 'justify-end' : 'justify-start',
          ]"
        >
          <div
            :class="[
              'max-w-xs px-3 py-2 rounded-lg',
              message.senderId === localUserId
                ? 'bg-blue-500 text-white'
                : message.type === 'system'
                  ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-center w-full max-w-none'
                  : 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white border',
            ]"
          >
            <!-- Message Header -->
            <div
              v-if="message.senderId !== localUserId && message.type !== 'system'"
              class="text-xs text-gray-500 dark:text-gray-400 mb-1"
            >
              {{ message.senderName }}
            </div>

            <!-- Text Message -->
            <div v-if="message.type === 'text'" class="break-words">
              {{ message.content }}
            </div>

            <!-- File Message -->
            <div v-else-if="message.type === 'file'" class="space-y-2">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                  />
                </svg>
                <span class="text-sm">{{ message.fileName }}</span>
              </div>
              <div class="text-xs opacity-75">{{ formatFileSize(message.fileSize || 0) }}</div>
              <button
                v-if="message.fileUrl"
                @click="downloadFile(message.fileUrl, message.fileName)"
                class="text-xs underline hover:no-underline"
              >
                {{ $t('tools.webRtcMeeting.chat.download') }}
              </button>
            </div>

            <!-- System Message -->
            <div v-else-if="message.type === 'system'" class="text-sm">
              {{ message.content }}
            </div>

            <!-- Message Timestamp -->
            <div v-if="message.type !== 'system'" class="text-xs opacity-75 mt-1">
              {{ formatTime(message.timestamp) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Input -->
    <div class="p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      <div class="flex space-x-2">
        <!-- File Upload Button -->
        <button
          @click="triggerFileUpload"
          class="px-3 py-2 text-gray-500 hover:text-blue-500 border border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-500"
          :title="$t('tools.webRtcMeeting.chat.sendFile')"
          :disabled="isSendingFile"
        >
          <svg v-if="!isSendingFile" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path d="M8 2a1 1 0 000 2h2a1 1 0 100-2H8z" />
            <path
              d="M3 5a2 2 0 012-2 3 3 0 003 3h6a3 3 0 003-3 2 2 0 012 2v6h-4.586l1.293-1.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L12.586 15H17v3a2 2 0 01-2 2H5a2 2 0 01-2-2V5z"
            />
          </svg>
          <div v-else class="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </button>

        <!-- Message Input -->
        <input
          v-model="messageInput"
          @keyup.enter="sendMessage"
          :placeholder="$t('tools.webRtcMeeting.chat.placeholder')"
          class="flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700"
          maxlength="500"
          :disabled="isSendingFile"
        />

        <!-- Send Button -->
        <button
          @click="sendMessage"
          :disabled="!messageInput.trim() || isSendingFile"
          class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ $t('tools.webRtcMeeting.chat.send') }}
        </button>
      </div>

      <!-- File Upload Progress -->
      <div v-if="isSendingFile" class="mt-2">
        <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
          <span>{{ $t('tools.webRtcMeeting.chat.sendingFile') }}</span>
          <span>{{ fileUploadProgress }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
          <div
            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
            :style="{ width: `${fileUploadProgress}%` }"
          ></div>
        </div>
      </div>

      <!-- File Input (Hidden) -->
      <input ref="fileInput" type="file" @change="handleFileSelect" class="hidden" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import type { ChatMessage } from '@/types/message'

interface Props {
  messages: ChatMessage[]
  localUserId: string
}

interface Emits {
  (e: 'close'): void
  (e: 'sendMessage', message: string): void
  (e: 'sendFile', file: File): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()
const { t } = useI18n()

// Reactive state
const messageInput = ref('')
const messagesContainer = ref<HTMLElement>()
const fileInput = ref<HTMLInputElement>()
const isSendingFile = ref(false)
const fileUploadProgress = ref(0)

// Methods
const sendMessage = () => {
  if (!messageInput.value.trim()) return
  
  emit('sendMessage', messageInput.value.trim())
  messageInput.value = ''
}

const triggerFileUpload = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files[0]) {
    const file = target.files[0]
    emit('sendFile', file)
    
    // Reset file input
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
}

const downloadFile = (url: string, fileName?: string) => {
  const link = document.createElement('a')
  link.href = url
  link.download = fileName || 'download'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// Watch for new messages and scroll to bottom
watch(() => props.messages.length, () => {
  scrollToBottom()
})

// Expose methods for parent component
defineExpose({
  setFileUploadProgress: (progress: number) => {
    fileUploadProgress.value = progress
  },
  setFileUploadStatus: (status: boolean) => {
    isSendingFile.value = status
  }
})
</script>
