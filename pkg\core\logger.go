package core

import (
	"meeting/pkg/config"
	"os"
	"sync"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"github.com/google/wire"
)

// NewLogger 创建日志实例
func NewLogger(cfg *config.TomlConfig) (*zap.SugaredLogger, error) {
	file, err := os.OpenFile(cfg.App.LogFile, os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0755)
	if err != nil {
		return nil, err
	}

	writeSyncer := zapcore.AddSync(file)
	encoder := zapcore.NewJSONEncoder(zap.NewProductionEncoderConfig())
	core := zapcore.NewCore(encoder, writeSyncer, zapcore.DebugLevel)
	logger := zap.New(core)

	return logger.Sugar(), nil
}

// ProviderSet Wire 提供者集合
var LoggerProviderSet = wire.NewSet(NewLogger)

// 保持向后兼容的全局变量和函数
var loggerOnce sync.Once
var sugarLogger *zap.SugaredLogger

func InitializeLogger() {
	loggerOnce.Do(func() {
		file, _ := os.OpenFile(config.GetConfig().App.LogFile, os.O_WRONLY|os.O_CREATE|os.O_APPEND, 0755)
		writeSyncer := zapcore.AddSync(file)
		encoder := zapcore.NewJSONEncoder(zap.NewProductionEncoderConfig())
		core := zapcore.NewCore(encoder, writeSyncer, zapcore.DebugLevel)
		logger := zap.New(core)
		sugarLogger = logger.Sugar()
	})
}

func Logger() *zap.SugaredLogger {
	return sugarLogger
}
