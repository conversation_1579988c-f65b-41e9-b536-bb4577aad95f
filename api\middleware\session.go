package middleware

import (
	"github.com/gin-contrib/sessions"
	"github.com/gin-contrib/sessions/cookie"
	"github.com/gin-gonic/gin"
	"meeting/pkg/config"
	"meeting/pkg/core"
	"net/http"
)

func Session() gin.HandlerFunc {
	cfg := config.GetConfig()
	// 使用配置中的密钥，如果没有配置则使用默认值
	secretKey := cfg.App.SessionSecret
	if secretKey == "" {
		secretKey = "meeting-session-secret-key-change-in-production"
		core.Logger().Warn("使用默认Session密钥，生产环境请配置App.SessionSecret")
	}

	store := cookie.NewStore([]byte(secretKey))
	store.Options(sessions.Options{
		Path:     "/",
		MaxAge:   86400 * 7, // 7天
		HttpOnly: true,
		Secure:   cfg.App.Secure, // 根据配置决定是否只在HTTPS下使用
		SameSite: http.SameSiteLaxMode,
	})
	return sessions.Sessions("meeting_session", store)
}
