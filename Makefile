.PHONY:all
all: web build

.PHONY:build
build:
	GOOS=linux GOARCH=amd64 go build -o ./bin/main .

.PHONY:web
web:
	cd ./web && npm install && npm run build-only

.PHONY:clean
clean:
	rm -rf ./bin/linux-amd64;\
	rm -rf ./public/assets;\
	rm -rf ./public/index.html

.PHONY:deploy
deploy:
	git add -A;\
	git commit -m "update";\
	git push;\
	ssh <EMAIL> "cd /www/wwwroot/codeemo.cn && git pull"

.PHONY: swag
swag:
	swag init --parseDependency --parseInternal -o ./public ./api ./cmd

.PHONY: protocol
protocol:
	protoc --go_out=./pkg/protocol --go-grpc_out=./pkg/protocol ./pkg/protocol/proto/*.proto

.PHONY: vet
vet:
	go vet ./...

.PHONY: wire
wire:
	go run github.com/google/wire/cmd/wire